# Services 模块功能说明

本目录包含项目管理系统的核心业务服务，按功能模块分类如下：

## 📋 服务功能概览

| 文件名 | 功能描述 | 主要用途 | 核心功能 |
|--------|----------|----------|----------|
| **钉钉集成模块** |
| `dingtalk_service.py` | 钉钉API服务 | 与钉钉平台集成 | 获取access_token、拉取日志数据、API调用管理 |
| `dingtalk_log_converter.py` | 钉钉日志转换器 | 数据格式转换 | 解析项目类型、工时计算、数据标准化 |
| `dingtalk_sync_config_service.py` | 钉钉同步配置服务 | 同步任务配置管理 | 配置持久化、定时任务参数管理 |
| **项目管理模块** |
| `enhanced_project_matcher.py` | 增强项目匹配器 | 智能项目匹配 | 项目编号解析、多项目匹配、置信度评估 |
| `project_code_parser.py` | 项目编号解析器 | 项目编号智能解析 | 支持前缀、多项目、简写等复杂格式解析 |
| `project_type_normalizer.py` | 项目类型标准化器 | 项目类型规范化 | 类型变体标准化、映射规则管理 |
| `project_estimate_service.py` | 项目预估成本服务 | 项目成本预估 | 部门成本汇总、预估成本计算 |

| **成本计算模块** |
| `cost_calculation.py` | 成本计算服务 | 项目成本核算 | 人工成本、BOM成本、其他成本计算 |
| `auto_calc_config_service.py` | 自动计算配置服务 | 定时计算任务配置 | 计算参数管理、任务调度配置 |
| **部门管理模块** |
| `dept_output_service.py` | 部门产出分析服务 | 部门效率分析 | 产出计算、成本分析、绩效评估 |
| `dept_participation_service.py` | 部门参与比例服务 | 项目参与度管理 | 参与比例验证、设计部门联合验证 |
| `dept_unit_cost_service.py` | 部门单位成本服务 | 部门成本核算 | 月度工时成本、部门成本统计 |
| `dept_unit_cost_config_service.py` | 部门成本配置服务 | 成本计算任务配置 | 定时任务配置、计算参数管理 |
| **工时管理模块** |
| `working_hours_calculator.py` | 工时计算器 | 工时统计计算 | 正常/加班工时区分、节假日判断 |
| `holiday_service.py` | 假期服务 | 节假日管理 | 节假日配置、加班倍率计算 |
| `holiday_import_service.py` | 假期导入服务 | 节假日数据导入 | 批量导入、数据验证、类型识别 |
| **质量管理模块** |
| `quality_exception_processor.py` | 质量异常处理器 | 质量异常单处理 | 异常单解析、项目匹配、数据处理 |
| `quality_exception_sync_service.py` | 质量异常同步服务 | 自动同步质量异常 | 后台同步、增量同步、失败重试 |
| `manual_project_binding_service.py` | 手动项目绑定服务 | 质量异常单项目绑定 | 手动绑定、解绑、批量操作 |
| `binding_validation_service.py` | 绑定验证服务 | 绑定操作验证 | 数据验证、冲突检测、审计日志 |
| **员工管理模块** |
| `probation_service.py` | 试用期提醒服务 | 试用期管理 | 到期检查、邮件提醒、记录管理 |

## 🔧 核心业务流程

### 1. 钉钉数据同步流程
```
钉钉API → 数据拉取 → 格式转换 → 项目匹配 → 数据入库
```

### 2. 项目成本计算流程
```
项目数据 → 工时统计 → 成本核算 → 部门分摊 → 报表生成
```

### 3. 部门产出分析流程
```
项目收入 → 部门成本 → 参与比例 → 产出计算 → 绩效评估
```

### 4. 质量异常处理流程
```
异常单同步 → 项目匹配 → 手动绑定 → 数据验证 → 统计分析
```

## 📝 使用说明

1. **配置服务**：所有带 `config` 的服务负责系统配置管理
2. **计算服务**：所有带 `calculation` 或 `calculator` 的服务负责业务计算
3. **同步服务**：所有带 `sync` 的服务负责数据同步
4. **验证服务**：所有带 `validation` 的服务负责数据验证

## 🚀 开发指南

- 新增业务逻辑时，请按功能模块选择对应的服务文件
- 跨模块功能请考虑创建新的服务文件
- 所有服务都应包含完整的错误处理和日志记录
- 配置类服务统一使用字典表进行配置管理

---
*最后更新：2025-01-08*
