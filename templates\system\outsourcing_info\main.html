<!DOCTYPE html>
<html>
<head>
    <title>外协信息管理</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">外协名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="outsourcingName" placeholder="请输入外协名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">合作状态</label>
                    <div class="layui-input-inline">
                        <select name="cooperationStatus">
                            <option value="">请选择合作状态</option>
                            <option value="1">正常</option>
                            <option value="0">停用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-md" lay-submit lay-filter="outsourcing-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table id="outsourcing-table" lay-filter="outsourcing-table"></table>
    </div>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="outsourcing-toolbar">
    <div class="layui-btn-container">
        {% if authorize("system:outsourcing_info:add") %}
        <button class="layui-btn layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            新增
        </button>
        {% endif %}
        {% if authorize("system:outsourcing_info:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>
            批量删除
        </button>
        {% endif %}
    </div>
</script>

<!-- 操作栏模板 -->
<script type="text/html" id="outsourcing-bar">
    {% if authorize("system:outsourcing_info:edit") %}
    <a class="layui-btn layui-btn-xs" lay-event="edit">
        <i class="layui-icon layui-icon-edit"></i>
        编辑
    </a>
    {% endif %}
    {% if authorize("system:outsourcing_info:remove") %}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </a>
    {% endif %}
</script>

<!-- 时薪模板 -->
<script type="text/html" id="hourly-rate-tpl">
    {% raw %}{{# if(d.hourly_rate && d.hourly_rate > 0){ }}{% endraw %}
    <span style="color: #1E9FFF; font-weight: bold;">¥{% raw %}{{ d.hourly_rate }}{% endraw %}/小时</span>
    {% raw %}{{# } else { }}{% endraw %}
    <span style="color: #999;">未设置</span>
    {% raw %}{{# } }}{% endraw %}
</script>

<!-- 合作状态模板 -->
<script type="text/html" id="cooperation-status-tpl">
    <input type="checkbox" name="cooperationStatus" value="{% raw %}{{ d.id }}{% endraw %}" lay-skin="switch"
           lay-text="正常|停用" lay-filter="cooperation-status" {% raw %}{{# if(d.cooperation_status == 1){ }}{% endraw %}checked{% raw %}{{# } }}{% endraw %}>
</script>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['table', 'form', 'layer', 'popup', 'jquery'], function () {
        let table = layui.table
        let form = layui.form
        let layer = layui.layer
        let popup = layui.popup
        let $ = layui.jquery

        const MODULE_PATH = "/system/outsourcing_info/"

        let cols = [
            [
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', width: 80, title: 'ID', sort: true},
                {field: 'outsourcing_name', width: 200, title: '外协名称'},
                {field: 'contact_person', width: 120, title: '联系人'},
                {field: 'contact_phone', width: 130, title: '联系电话'},
                {field: 'contact_email', width: 180, title: '联系邮箱'},
                {field: 'company_address', width: 200, title: '公司地址'},
                {field: 'qualification_level', width: 120, title: '资质等级'},
                {field: 'hourly_rate', width: 120, title: '时薪(元)', templet: '#hourly-rate-tpl'},
                {field: 'cooperation_status', width: 120, title: '合作状态', templet: '#cooperation-status-tpl'},
                {field: 'create_at', width: 160, title: '创建时间'},
                {title: '操作', toolbar: '#outsourcing-bar', align: 'center', width: 200, fixed: 'right'}
            ]
        ]

        // 渲染表格
        table.render({
            elem: '#outsourcing-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#outsourcing-toolbar',
            text: {none: '暂无外协信息'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports']
        })

        // 查询
        form.on('submit(outsourcing-query)', function (data) {
            table.reload('outsourcing-table', {
                where: data.field,
                page: {curr: 1}
            })
            return false
        })

        // 合作状态切换
        form.on('switch(cooperation-status)', function (obj) {
            let operate = obj.elem.checked ? 'enable' : 'disable'
            let loading = layer.load()
            $.ajax({
                url: MODULE_PATH + operate,
                data: JSON.stringify({outsourcingId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg)
                    } else {
                        popup.failure(result.msg)
                        // 恢复开关状态
                        obj.elem.checked = !obj.elem.checked
                        form.render('checkbox')
                    }
                },
                error: function () {
                    layer.close(loading)
                    popup.failure('网络异常')
                    // 恢复开关状态
                    obj.elem.checked = !obj.elem.checked
                    form.render('checkbox')
                }
            })
        })

        // 工具栏事件
        table.on('toolbar(outsourcing-table)', function (obj) {
            let checkStatus = table.checkStatus(obj.config.id)
            switch (obj.event) {
                case 'add':
                    window.add()
                    break
                case 'batchRemove':
                    if (checkStatus.data.length === 0) {
                        popup.warning('请选择要删除的数据')
                        return
                    }
                    window.batchRemove(checkStatus.data)
                    break
                case 'refresh':
                    table.reload('outsourcing-table')
                    break
            }
        })

        // 行工具事件
        table.on('tool(outsourcing-table)', function (obj) {
            let data = obj.data
            switch (obj.event) {
                case 'edit':
                    window.edit(data)
                    break
                case 'remove':
                    window.remove(data)
                    break
            }
        })

        // 新增
        window.add = function () {
            layer.open({
                type: 2,
                title: '新增外协信息',
                shade: 0.1,
                area: ['700px', '550px'],
                content: MODULE_PATH + 'add',
                end: function () {
                    table.reload('outsourcing-table')
                }
            })
        }

        // 编辑
        window.edit = function (data) {
            layer.open({
                type: 2,
                title: '编辑外协信息',
                shade: 0.1,
                area: ['700px', '550px'],
                content: MODULE_PATH + 'edit?id=' + data.id,
                end: function () {
                    table.reload('outsourcing-table')
                }
            })
        }

        // 删除
        window.remove = function (data) {
            layer.confirm('确定要删除外协信息 "' + data.outsourcing_name + '" 吗？', {icon: 3, title: '提示'}, function (index) {
                let loading = layer.load()
                $.ajax({
                    url: MODULE_PATH + 'remove/' + data.id,
                    type: 'DELETE',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            popup.success(result.msg)
                            table.reload('outsourcing-table')
                        } else {
                            popup.failure(result.msg)
                        }
                    }
                })
                layer.close(index)
            })
        }

        // 批量删除
        window.batchRemove = function (data) {
            let ids = data.map(item => item.id)
            let names = data.map(item => item.outsourcing_name).join('、')
            layer.confirm('确定要删除外协信息 "' + names + '" 吗？', {icon: 3, title: '提示'}, function (index) {
                let loading = layer.load()
                let promises = ids.map(id => {
                    return $.ajax({
                        url: MODULE_PATH + 'remove/' + id,
                        type: 'DELETE'
                    })
                })
                
                Promise.all(promises).then(function(results) {
                    layer.close(loading)
                    let successCount = results.filter(r => r.success).length
                    if (successCount === ids.length) {
                        popup.success('批量删除成功')
                    } else {
                        popup.warning('部分删除失败，成功删除 ' + successCount + ' 条记录')
                    }
                    table.reload('outsourcing-table')
                }).catch(function() {
                    layer.close(loading)
                    popup.failure('批量删除失败')
                })
                layer.close(index)
            })
        }
    })
</script>
</body>
</html>
