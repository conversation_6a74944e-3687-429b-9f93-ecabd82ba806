import datetime
from applications.extensions import db
from flask_login import UserMixin


class DeptMonthlyUnitCost(db.Model, UserMixin):
    """部门月度工时单位成本数据模型"""
    __tablename__ = 'dept_monthly_unit_cost'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False, comment='部门ID')
    year_month = db.Column(db.String(7), nullable=False, comment='年月(格式: 2024-07)', index=True)
    total_should_pay = db.Column(db.Float, nullable=False, default=0, comment='该部门该月总应发工资')
    total_work_hours = db.Column(db.Float, nullable=False, default=0, comment='该部门该月总工时(含加班)')
    unit_cost_per_hour = db.Column(db.Float, nullable=False, default=0, comment='每小时单位成本(应发工资/总工时)')
    employee_count = db.Column(db.Integer, nullable=False, default=0, comment='参与计算的员工数量')
    calculation_date = db.Column(db.DateTime, default=datetime.datetime.now, comment='计算时间')
    status = db.Column(db.Integer, default=1, comment='状态(1-正常, 0-异常)')
    remark = db.Column(db.Text, comment='备注信息')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联部门表
    dept = db.relationship('Dept', backref='monthly_unit_costs', foreign_keys=[dept_id])
    
    # 添加唯一性约束，防止同一部门同一月份重复数据
    __table_args__ = (
        db.UniqueConstraint('dept_id', 'year_month', name='uix_dept_month'),
        db.Index('idx_dept_year_month', 'dept_id', 'year_month'),  # 添加复合索引提高查询性能
    )
    
    def __repr__(self):
        return f"<DeptMonthlyUnitCost {self.dept_id}-{self.year_month}>"
    
    @classmethod
    def get_unit_cost(cls, dept_id, year_month):
        """获取指定部门指定月份的工时单位成本

        Args:
            dept_id: 部门ID
            year_month: 年月字符串，格式如 '2024-07'

        Returns:
            float: 工时单位成本，如果没有数据则使用最近可用的数据，都没有返回0
        """
        # 首先尝试获取指定月份的数据
        record = cls.query.filter_by(
            dept_id=dept_id,
            year_month=year_month,
            status=1
        ).first()

        # 如果找到记录且单位成本大于0，直接返回
        if record and record.unit_cost_per_hour > 0:
            return record.unit_cost_per_hour

        # 如果指定月份没有数据或者单位成本为0，查找最近可用的非零数据
        latest_record = cls.query.filter(
            cls.dept_id == dept_id,
            cls.status == 1,
            cls.unit_cost_per_hour > 0  # 只查找非零的数据
        ).order_by(cls.year_month.desc()).first()

        return latest_record.unit_cost_per_hour if latest_record else 0
    
    @classmethod
    def batch_get_unit_costs(cls, dept_ids, year_month):
        """批量获取多个部门指定月份的工时单位成本
        
        Args:
            dept_ids: 部门ID列表
            year_month: 年月字符串，格式如 '2024-07'
            
        Returns:
            dict: {dept_id: unit_cost_per_hour}
        """
        records = cls.query.filter(
            cls.dept_id.in_(dept_ids),
            cls.year_month == year_month,
            cls.status == 1
        ).all()
        
        return {record.dept_id: record.unit_cost_per_hour for record in records}
    
    @classmethod
    def get_latest_calculation_date(cls, dept_id=None):
        """获取最新的计算时间
        
        Args:
            dept_id: 部门ID，如果为None则查询所有部门
            
        Returns:
            datetime: 最新计算时间
        """
        query = cls.query
        if dept_id:
            query = query.filter_by(dept_id=dept_id)
            
        record = query.order_by(cls.calculation_date.desc()).first()
        return record.calculation_date if record else None
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'dept_id': self.dept_id,
            'dept_name': self.dept.dept_name if self.dept else '未知部门',
            'year_month': self.year_month,
            'total_should_pay': round(self.total_should_pay, 2),
            'total_work_hours': round(self.total_work_hours, 2),
            'unit_cost_per_hour': round(self.unit_cost_per_hour, 2),
            'employee_count': self.employee_count,
            'calculation_date': self.calculation_date.strftime('%Y-%m-%d %H:%M:%S') if self.calculation_date else None,
            'status': self.status,
            'status_text': '正常' if self.status == 1 else '异常',
            'remark': self.remark,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
