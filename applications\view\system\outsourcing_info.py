from flask import Blueprint, render_template, request, jsonify
from flask_login import current_user

from applications.common import curd
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import OutsourcingInfo
from applications.schemas.outsourcing_info import OutsourcingInfoOutSchema

bp = Blueprint('outsourcing_info', __name__, url_prefix='/outsourcing_info')


@bp.get('/')
@authorize("system:outsourcing_info:main", log=True)
def main():
    """外协信息管理主页面"""
    return render_template('system/outsourcing_info/main.html')


@bp.get('/data')
@authorize("system:outsourcing_info:main", log=True)
def data():
    """获取外协信息数据"""
    # 获取查询参数
    outsourcing_name = str_escape(request.args.get('outsourcingName', type=str))
    cooperation_status = request.args.get('cooperationStatus', type=int)
    
    # 构建查询条件
    filters = []
    if outsourcing_name:
        filters.append(OutsourcingInfo.outsourcing_name.contains(outsourcing_name))
    if cooperation_status is not None:
        filters.append(OutsourcingInfo.cooperation_status == cooperation_status)
    
    # 执行查询
    if filters:
        outsourcing_query = OutsourcingInfo.query.filter(*filters).order_by(
            OutsourcingInfo.create_at.desc()
        ).layui_paginate()
    else:
        outsourcing_query = OutsourcingInfo.query.order_by(
            OutsourcingInfo.create_at.desc()
        ).layui_paginate()
    
    count = outsourcing_query.total
    data = curd.model_to_dicts(schema=OutsourcingInfoOutSchema, data=outsourcing_query.items)
    
    return table_api(data=data, count=count)


@bp.get('/add')
@authorize("system:outsourcing_info:add", log=True)
def add():
    """新增外协信息页面"""
    return render_template('system/outsourcing_info/add.html')


@bp.post('/save')
@authorize("system:outsourcing_info:add", log=True)
def save():
    """保存外协信息"""
    req_json = request.get_json(force=True)
    
    try:
        # 数据验证
        outsourcing_name = str_escape(req_json.get('outsourcingName'))
        if not outsourcing_name:
            return fail_api(msg="外协名称不能为空")
        
        # 检查名称是否重复
        existing = OutsourcingInfo.query.filter_by(outsourcing_name=outsourcing_name).first()
        if existing:
            return fail_api(msg="外协名称已存在")
        
        # 创建外协信息
        outsourcing_info = OutsourcingInfo(
            outsourcing_name=outsourcing_name,
            contact_person=str_escape(req_json.get('contactPerson', '')),
            contact_phone=str_escape(req_json.get('contactPhone', '')),
            contact_email=str_escape(req_json.get('contactEmail', '')),
            company_address=str_escape(req_json.get('companyAddress', '')),
            business_scope=str_escape(req_json.get('businessScope', '')),
            qualification_level=str_escape(req_json.get('qualificationLevel', '')),
            hourly_rate=float(req_json.get('hourlyRate', 0)) if req_json.get('hourlyRate') else None,
            cooperation_status=int(req_json.get('cooperationStatus', 1)),
            remark=str_escape(req_json.get('remark', ''))
        )
        
        db.session.add(outsourcing_info)
        db.session.commit()
        
        return success_api(msg="外协信息添加成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"添加失败: {str(e)}")


@bp.get('/edit')
@authorize("system:outsourcing_info:edit", log=True)
def edit():
    """编辑外协信息页面"""
    outsourcing_id = request.args.get("id")
    outsourcing_info = curd.get_one_by_id(model=OutsourcingInfo, id=outsourcing_id)
    
    if not outsourcing_info:
        return fail_api(msg="外协信息不存在")
    
    return render_template('system/outsourcing_info/edit.html', outsourcing_info=outsourcing_info)


@bp.put('/update')
@authorize("system:outsourcing_info:edit", log=True)
def update():
    """更新外协信息"""
    req_json = request.get_json(force=True)
    outsourcing_id = req_json.get('id')
    
    if not outsourcing_id:
        return fail_api(msg="外协信息ID不能为空")
    
    outsourcing_info = OutsourcingInfo.query.get(outsourcing_id)
    if not outsourcing_info:
        return fail_api(msg="外协信息不存在")
    
    try:
        # 检查名称是否重复（排除自己）
        outsourcing_name = str_escape(req_json.get('outsourcingName'))
        existing = OutsourcingInfo.query.filter(
            OutsourcingInfo.outsourcing_name == outsourcing_name,
            OutsourcingInfo.id != outsourcing_id
        ).first()
        if existing:
            return fail_api(msg="外协名称已存在")
        
        # 更新数据
        outsourcing_info.outsourcing_name = outsourcing_name
        outsourcing_info.contact_person = str_escape(req_json.get('contactPerson', ''))
        outsourcing_info.contact_phone = str_escape(req_json.get('contactPhone', ''))
        outsourcing_info.contact_email = str_escape(req_json.get('contactEmail', ''))
        outsourcing_info.company_address = str_escape(req_json.get('companyAddress', ''))
        outsourcing_info.business_scope = str_escape(req_json.get('businessScope', ''))
        outsourcing_info.qualification_level = str_escape(req_json.get('qualificationLevel', ''))
        outsourcing_info.hourly_rate = float(req_json.get('hourlyRate', 0)) if req_json.get('hourlyRate') else None
        outsourcing_info.cooperation_status = int(req_json.get('cooperationStatus', 1))
        outsourcing_info.remark = str_escape(req_json.get('remark', ''))
        
        db.session.commit()
        return success_api(msg="更新成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"更新失败: {str(e)}")


@bp.delete('/remove/<int:outsourcing_id>')
@authorize("system:outsourcing_info:remove", log=True)
def remove(outsourcing_id):
    """删除外协信息"""
    outsourcing_info = OutsourcingInfo.query.get(outsourcing_id)
    if not outsourcing_info:
        return fail_api(msg="外协信息不存在")
    
    # 检查是否有关联的工时记录
    if outsourcing_info.work_hours.count() > 0:
        return fail_api(msg="该外协信息下存在工时记录，无法删除")
    
    try:
        db.session.delete(outsourcing_info)
        db.session.commit()
        return success_api(msg="删除成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败: {str(e)}")


@bp.put('/enable')
@authorize("system:outsourcing_info:edit", log=True)
def enable():
    """启用外协信息"""
    req_json = request.get_json(force=True)
    outsourcing_id = req_json.get('outsourcingId')
    
    outsourcing_info = OutsourcingInfo.query.get(outsourcing_id)
    if not outsourcing_info:
        return fail_api(msg="外协信息不存在")
    
    try:
        outsourcing_info.cooperation_status = 1
        db.session.commit()
        return success_api(msg="启用成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"启用失败: {str(e)}")


@bp.put('/disable')
@authorize("system:outsourcing_info:edit", log=True)
def disable():
    """停用外协信息"""
    req_json = request.get_json(force=True)
    outsourcing_id = req_json.get('outsourcingId')
    
    outsourcing_info = OutsourcingInfo.query.get(outsourcing_id)
    if not outsourcing_info:
        return fail_api(msg="外协信息不存在")
    
    try:
        outsourcing_info.cooperation_status = 0
        db.session.commit()
        return success_api(msg="停用成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"停用失败: {str(e)}")
