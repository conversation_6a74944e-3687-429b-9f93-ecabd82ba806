<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>质量异常项目绑定管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    {% include 'system/common/header.html' %}
    <style>
        .binding-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .binding-status.bound {
            background-color: #5FB878;
            color: white;
        }
        .binding-status.unbound {
            background-color: #FF5722;
            color: white;
        }
        .binding-status.partial {
            background-color: #FFB800;
            color: white;
        }
        .confidence-bar {
            width: 60px;
            height: 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            display: inline-block;
            vertical-align: middle;
        }
        .confidence-fill {
            height: 100%;
            background-color: #5FB878;
            transition: width 0.3s ease;
        }
        .project-suggestion {
            padding: 8px;
            margin: 4px 0;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .project-suggestion:hover {
            border-color: #1E9FFF;
            background-color: #f8f9fa;
        }
        .project-suggestion.selected {
            border-color: #1E9FFF;
            background-color: #e8f4ff;
        }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">
            <span class="layui-icon layui-icon-link"></span>
            质量异常项目绑定管理
        </div>
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form" lay-filter="search-form">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_code" placeholder="请输入项目编号" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目类型</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_type" placeholder="请输入项目类型" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">绑定状态</label>
                            <div class="layui-input-block">
                                <select name="binding_status">
                                    <option value="">全部状态</option>
                                    <option value="bound">已绑定</option>
                                    <option value="unbound">未绑定</option>
                                    <option value="partial">部分绑定</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn" id="search-btn">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button type="reset" class="layui-btn layui-btn-warm">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- 工具栏 -->
            <div class="layui-row layui-col-space10" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <!-- <button type="button" class="layui-btn layui-btn-sm" id="batch-bind-btn">
                        <i class="layui-icon layui-icon-link"></i> 批量绑定
                    </button> -->
                    <!-- <button type="button" class="layui-btn layui-btn-warm layui-btn-sm" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button> -->
                    <!-- <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="binding-stats-btn">
                        <i class="layui-icon layui-icon-chart"></i> 绑定统计
                    </button> -->
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="exception-binding-table" lay-filter="exception-binding-table"></table>
        </div>
    </div>

    <!-- 项目绑定弹窗 -->
    <div id="project-binding-dialog" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="binding-form">
            <input type="hidden" name="exception_id" id="binding-exception-id">
            
            <div class="layui-form-item">
                <label class="layui-form-label">异常信息</label>
                <div class="layui-input-block">
                    <div id="exception-info" style="padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
                        <!-- 异常信息将在这里显示 -->
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">项目类型</label>
                <div class="layui-input-block">
                    <select name="projectType" lay-filter="projectType">
                        <option value="">请选择项目类型</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">项目编号</label>
                <div class="layui-input-block">
                    <select name="projectCode" lay-filter="projectCode">
                        <option value="">请先选择项目类型</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">手动搜索</label>
                <div class="layui-input-block">
                    <div class="layui-input-group">
                        <input type="text" id="project-search-input" placeholder="输入项目名称或编号搜索（可选）" class="layui-input">
                        <div class="layui-input-split layui-input-suffix">
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="search-projects-btn">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                        </div>
                    </div>
                    <div id="search-results" style="margin-top: 10px; max-height: 200px; overflow-y: auto;">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">绑定备注</label>
                <div class="layui-input-block">
                    <textarea name="notes" placeholder="请输入绑定备注（可选）" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="confirm-binding-btn">确认绑定</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancel-binding-btn">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 查看绑定弹窗 -->
    <div id="view-bindings-dialog" style="display: none; padding: 20px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="layui-icon layui-icon-list"></span>
                绑定信息详情
            </div>
            <div class="layui-card-body">
                <!-- 异常单基本信息 -->
                <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">
                    <div class="layui-col-md12">
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px;">
                            <div id="view-exception-info">
                                <!-- 异常单信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 绑定项目列表 -->
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <div id="bindings-list">
                            <!-- 绑定项目列表将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理绑定弹窗 -->
    <div id="manage-bindings-dialog" style="display: none; padding: 20px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <span class="layui-icon layui-icon-edit"></span>
                管理项目绑定
            </div>
            <div class="layui-card-body">
                <!-- 异常单基本信息 -->
                <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">
                    <div class="layui-col-md12">
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px;">
                            <div id="manage-exception-info">
                                <!-- 异常单信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 绑定项目管理列表 -->
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <div id="manage-bindings-list">
                            <!-- 可管理的绑定项目列表将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="layui-form-item" style="margin-top: 20px;display: flex;justify-content: end;">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn layui-btn-primary" id="close-manage-dialog">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <script src="{{ url_for('static', filename='system/component/pear/pear.js') }}"></script>
    <script>
        // 设置用户权限信息（从后端传递）
        window.userPermissions = [
            {% if authorize("system:dingtalk:project_binding:view") %}'system:dingtalk:project_binding:view',{% endif %}
            {% if authorize("system:dingtalk:project_binding:bind") %}'system:dingtalk:project_binding:bind',{% endif %}
            {% if authorize("system:dingtalk:project_binding:manage") %}'system:dingtalk:project_binding:manage',{% endif %}
            {% if authorize("system:dingtalk:project_binding:unbind") %}'system:dingtalk:project_binding:unbind',{% endif %}
            {% if authorize("system:dingtalk:project_binding:batch") %}'system:dingtalk:project_binding:batch',{% endif %}
            {% if authorize("system:dingtalk:project_binding:stats") %}'system:dingtalk:project_binding:stats'{% endif %}
        ].filter(Boolean); // 过滤掉空值

        layui.use(['table', 'form', 'layer', 'element', 'jquery'], function() {
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;
            var $ = layui.jquery;

            // 全局变量
            var selectedExceptionIds = [];
            var selectedProjectForBinding = null;
            var currentExceptionId = null;
            var projectTypesData = {}; // 存储项目类型数据
            var projectCodesData = {}; // 存储项目编号数据

            // 初始化表格
            var tableIns = table.render({
                elem: '#exception-binding-table',
                url: '/system/dindin/api/quality-exceptions/list',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                cols: [[
                    {type: 'checkbox', width: 60},
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'title',align: 'center', title: '异常标题', width: 200},
                    {field: 'project_code', align: 'center',title: '项目编号', width: 120},
                    {field: 'project_type', align: 'center',title: '项目类型', width: 120},
                    {field: 'binding_count',align: 'center', title: '绑定状态', width: 180, templet: function(d) {
                        if (d.binding_count > 0) {
                            if (d.binding_count == 1) {
                                return '<span class="binding-status bound">已绑定(' + d.binding_count + ')</span>';
                            } else {
                                return '<span class="binding-status partial">多项目(' + d.binding_count + ')</span>';
                            }
                        } else {
                            return '<span class="binding-status unbound">未绑定</span>';
                        }
                    }},
                    // {field: 'avg_confidence', title: '匹配置信度', width: 120, templet: function(d) {
                    //     if (d.avg_confidence) {
                    //         var percentage = Math.round(d.avg_confidence * 100);
                    //         return '<div class="confidence-bar"><div class="confidence-fill" style="width: ' + percentage + '%"></div></div> <span style="margin-left: 5px; font-size: 12px;">' + percentage + '%</span>';
                    //     } else {
                    //         return '<span style="color: #999;">-</span>';
                    //     }
                    // }},
                    {field: 'originator_dept_name', align: 'center', title: '发起部门', width: 150},
                    {field: 'responsible_dept_name',align: 'center', title: '责任部门', width: 150},
                    {field: 'create_time', title: '创建时间', width: 160},
                    {title: '操作', width: 350, align: 'center', templet: function(d) {
                        var html = '';

                        // 检查权限（这些权限检查将在页面加载时设置）
                        var hasBindPermission = window.userPermissions && window.userPermissions.includes('system:dingtalk:project_binding:bind');
                        var hasViewPermission = window.userPermissions && window.userPermissions.includes('system:dingtalk:project_binding:view');
                        var hasManagePermission = window.userPermissions && window.userPermissions.includes('system:dingtalk:project_binding:manage');

                        // 如果未绑定且有绑定权限，显示绑定项目按钮
                        if (d.binding_count == 0 && hasBindPermission) {
                            html += '<a class="layui-btn layui-btn-xs" lay-event="bind"><i class="layui-icon layui-icon-link"></i> 绑定项目</a>';
                        }

                        // 查看绑定按钮（有查看权限时显示）
                        if (hasViewPermission) {
                            html += '<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view"><i class="layui-icon layui-icon-list"></i> 查看绑定</a>';
                        }

                        // 如果已绑定且有管理权限，显示管理绑定按钮
                        if (d.binding_count > 0 && hasManagePermission) {
                            html += '<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="manage"><i class="layui-icon layui-icon-edit"></i> 管理绑定</a>';
                        }

                        return html;
                    }}
                ]],
                done: function(res, curr, count) {
                    console.log('表格数据加载完成:', res);
                }
            });

            // 搜索功能
            $('#search-btn').click(function() {
                var formData = form.val('search-form');
                tableIns.reload({
                    where: formData,
                    page: {curr: 1}
                });
            });

            // 刷新功能
            $('#refresh-btn').click(function() {
                tableIns.reload();
            });

            // 表格行操作事件
            table.on('tool(exception-binding-table)', function(obj) {
                var data = obj.data;
                var layEvent = obj.event;

                if (layEvent === 'bind') {
                    openBindingDialog(data.id);
                } else if (layEvent === 'view') {
                    viewBindings(data.id);
                } else if (layEvent === 'manage') {
                    manageBindings(data.id);
                }
            });

            // 复选框选择事件
            table.on('checkbox(exception-binding-table)', function(obj) {
                var checkStatus = table.checkStatus('exception-binding-table');
                selectedExceptionIds = checkStatus.data.map(function(item) {
                    return item.id;
                });
                console.log('选中的异常ID:', selectedExceptionIds);
            });

            // 加载项目类型数据
            function loadProjectTypes() {
                $.get('/system/dindin/api/project-types', function(res) {
                    if (res.success === true && res.data.project_types) {
                        var options = '<option value="">请选择项目类型</option>';
                        res.data.project_types.forEach(function(type) {
                            projectTypesData[type.id] = type;
                            options += '<option value="' + type.id + '">' + type.name + '</option>';
                        });
                        $('select[name="projectType"]').html(options);
                        form.render('select');
                    }
                }).fail(function() {
                    console.error('加载项目类型失败');
                });
            }

            // 打开项目绑定对话框
            function openBindingDialog(exceptionId) {
                currentExceptionId = exceptionId;

                // 首先检查异常单的项目编号数量
                $.get('/system/dindin/api/quality-exceptions/' + exceptionId + '/binding-info', function(res) {
                    if (res.success === true) {
                        var bindingInfo = res.data;
                        var allowMultipleBinding = bindingInfo.parsed_project_count > 1;
                        var currentBindingCount = bindingInfo.current_binding_count;

                        // 检查是否允许绑定
                        if (!allowMultipleBinding && currentBindingCount >= 1) {
                            layer.msg('该异常单只有一个项目编号，不支持绑定多个项目', {icon: 2});
                            return;
                        }

                        layer.open({
                            type: 1,
                            title: '绑定项目' + (allowMultipleBinding ? ' (支持多项目绑定)' : ' (单项目绑定)'),
                            area: ['700px', '600px'],
                            content: $('#project-binding-dialog'),
                            success: function(layero, index) {
                                $('#binding-exception-id').val(exceptionId);

                                // 显示异常单信息
                                var infoHtml = '<div><strong>异常单ID:</strong> ' + exceptionId + '</div>';
                                infoHtml += '<div><strong>项目编号:</strong> ' + bindingInfo.project_code + '</div>';
                                infoHtml += '<div><strong>解析后编号:</strong> ' + bindingInfo.parsed_project_codes.join(', ') + '</div>';
                                infoHtml += '<div><strong>当前绑定数:</strong> ' + currentBindingCount + '</div>';
                                if (!allowMultipleBinding) {
                                    infoHtml += '<div style="color: #ff5722;"><strong>注意:</strong> 该异常单只支持绑定一个项目</div>';
                                }
                                $('#exception-info').html(infoHtml);

                                // 重置表单
                                $('select[name="projectType"]').val('');
                                $('select[name="projectCode"]').html('<option value="">请先选择项目类型</option>');
                                $('#project-search-input').val('');
                                $('#search-results').html('');
                                selectedProjectForBinding = null;
                                form.render('select');

                                // 加载项目类型数据
                                loadProjectTypes();
                            }
                        });
                    } else {
                        layer.msg('获取异常单信息失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                }).fail(function() {
                    layer.msg('获取异常单信息失败', {icon: 2});
                });
            }

            // 监听项目类型选择变化
            form.on('select(projectType)', function(data) {
                var projectTypeId = data.value;
                var projectCodeSelect = $('select[name="projectCode"]');

                if (projectTypeId) {
                    // 加载对应的项目编号
                    $.get('/system/dindin/api/projects/search', {
                        project_type_id: projectTypeId,
                        limit: 100
                    }, function(res) {
                        if (res.success === true && res.data.projects) {
                            var options = '<option value="">请选择项目编号</option>';
                            res.data.projects.forEach(function(project) {
                                projectCodesData[project.id] = project;
                                options += '<option value="' + project.id + '">' + project.project_code + ' - ' + project.project_name + '</option>';
                            });
                            projectCodeSelect.html(options);
                            form.render('select');
                        } else {
                            projectCodeSelect.html('<option value="">该类型下暂无项目</option>');
                            form.render('select');
                        }
                    }).fail(function() {
                        projectCodeSelect.html('<option value="">加载失败</option>');
                        form.render('select');
                    });
                } else {
                    // 清空项目编号选项
                    projectCodeSelect.html('<option value="">请先选择项目类型</option>');
                    form.render('select');
                }

                // 清空之前的选择
                selectedProjectForBinding = null;
                $('#search-results').html('');
            });

            // 监听项目编号选择变化
            form.on('select(projectCode)', function(data) {
                var projectId = data.value;
                if (projectId && projectCodesData[projectId]) {
                    selectedProjectForBinding = projectId;
                    // 清空搜索结果，显示选中的项目
                    var project = projectCodesData[projectId];
                    var html = '<div class="project-suggestion selected" data-project-id="' + project.id + '">';
                    html += '<div><strong>' + project.project_name + '</strong> (' + project.project_code + ')</div>';
                    html += '<div style="font-size: 12px; color: #666; margin-top: 4px;">' + project.project_type_name + '</div>';
                    html += '</div>';
                    $('#search-results').html(html);
                } else {
                    selectedProjectForBinding = null;
                    $('#search-results').html('');
                }
            });

            // 项目搜索
            $('#search-projects-btn').click(function() {
                var keyword = $('#project-search-input').val().trim();
                if (!keyword) {
                    layer.msg('请输入搜索关键词', {icon: 2});
                    return;
                }

                $('#search-results').html('<div style="text-align: center; padding: 10px;">搜索中...</div>');

                // 获取当前选择的项目类型，用于过滤搜索
                var selectedProjectType = $('select[name="projectType"]').val();
                var searchParams = {
                    keyword: keyword,
                    limit: 10
                };
                if (selectedProjectType) {
                    searchParams.project_type_id = selectedProjectType;
                }

                $.get('/system/dindin/api/projects/search', searchParams, function(res) {
                    if (res.success === true && res.data.projects) {
                        renderSearchResults(res.data.projects);
                    } else {
                        $('#search-results').html('<div style="text-align: center; padding: 10px; color: #999;">未找到匹配的项目</div>');
                    }
                }).fail(function() {
                    $('#search-results').html('<div style="text-align: center; padding: 10px; color: #f56c6c;">搜索失败</div>');
                });
            });

            // 渲染搜索结果
            function renderSearchResults(projects) {
                var html = '';
                projects.forEach(function(project) {
                    html += '<div class="project-suggestion" data-project-id="' + project.id + '">';
                    html += '<div><strong>' + project.project_name + '</strong> (' + project.project_code + ')</div>';
                    html += '<div style="font-size: 12px; color: #666; margin-top: 4px;">' + project.project_type_name + '</div>';
                    html += '</div>';
                });
                $('#search-results').html(html);

                // 绑定点击事件
                $('.project-suggestion').click(function() {
                    $('.project-suggestion').removeClass('selected');
                    $(this).addClass('selected');
                    selectedProjectForBinding = $(this).data('project-id');

                    // 同步更新下拉框选择（如果项目在当前类型的下拉框中）
                    var projectId = $(this).data('project-id');
                    if (projectCodesData[projectId]) {
                        $('select[name="projectCode"]').val(projectId);
                        form.render('select');
                    }
                });
            }

            // 确认绑定
            $('#confirm-binding-btn').click(function() {
                if (!selectedProjectForBinding) {
                    layer.msg('请选择要绑定的项目', {icon: 2});
                    return;
                }

                var notes = $('textarea[name="notes"]').val();
                
                $.ajax({
                    url: '/system/dindin/api/quality-exceptions/' + currentExceptionId + '/bind-project',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        project_id: selectedProjectForBinding,
                        notes: notes
                    }),
                    success: function(res) {
                        if (res.success === true) {
                            layer.msg('绑定成功', {icon: 1});
                            layer.closeAll();
                            tableIns.reload();
                        } else {
                            layer.msg(res.msg || '绑定失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('绑定失败', {icon: 2});
                    }
                });
            });

            // 取消绑定
            $('#cancel-binding-btn').click(function() {
                layer.closeAll();
            });

            // 查看绑定
            function viewBindings(exceptionId) {
                // 获取异常单基本信息
                $.get('/system/dindin/api/quality-exceptions/' + exceptionId + '/binding-info', function(res) {
                    if (res.success === true) {
                        var exceptionInfo = res.data;

                        // 显示异常单基本信息
                        var infoHtml = '<div class="layui-row layui-col-space10">';
                        infoHtml += '<div class="layui-col-md6"><strong>异常单ID:</strong> ' + exceptionId + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>项目编号:</strong> ' + (exceptionInfo.project_code || '无') + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>项目类型:</strong> ' + (exceptionInfo.project_type || '无') + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>当前绑定数:</strong> ' + exceptionInfo.current_binding_count + '</div>';
                        if (exceptionInfo.parsed_project_codes && exceptionInfo.parsed_project_codes.length > 0) {
                            infoHtml += '<div class="layui-col-md12"><strong>解析后编号:</strong> ' + exceptionInfo.parsed_project_codes.join(', ') + '</div>';
                        }
                        infoHtml += '</div>';
                        $('#view-exception-info').html(infoHtml);

                        // 获取绑定详情
                        $.get('/system/dindin/api/quality-exceptions/' + exceptionId + '/bindings', function(bindingRes) {
                            if (bindingRes.success === true) {
                                var bindings = bindingRes.data.bindings;
                                var bindingsHtml = '';

                                if (bindings.length === 0) {
                                    bindingsHtml = '<div class="layui-empty"><div class="layui-empty-icon"><i class="layui-icon layui-icon-face-cry"></i></div><div class="layui-empty-text">暂无绑定项目</div></div>';
                                } else {
                                    bindingsHtml = '<table class="layui-table">';
                                    bindingsHtml += '<thead><tr><th>项目名称</th><th>项目编号</th><th>项目类型</th><th>绑定类型</th><th>置信度</th><th>绑定时间</th><th>备注</th></tr></thead>';
                                    bindingsHtml += '<tbody>';

                                    bindings.forEach(function(binding) {
                                        bindingsHtml += '<tr>';
                                        bindingsHtml += '<td>' + (binding.project_name || '未知项目') + '</td>';
                                        bindingsHtml += '<td>' + (binding.project_code || '-') + '</td>';
                                        bindingsHtml += '<td>' + (binding.project_type_name || '未知类型') + '</td>';
                                        bindingsHtml += '<td>';
                                        if (binding.binding_type === 'manual') {
                                            bindingsHtml += '<span class="layui-badge layui-bg-blue">手动绑定</span>';
                                        } else {
                                            bindingsHtml += '<span class="layui-badge layui-bg-green">自动绑定</span>';
                                        }
                                        bindingsHtml += '</td>';
                                        bindingsHtml += '<td>';
                                        if (binding.match_confidence) {
                                            var confidence = Math.round(binding.match_confidence * 100);
                                            bindingsHtml += '<div class="layui-progress" lay-showpercent="true" style="margin: 0;"><div class="layui-progress-bar" lay-percent="' + confidence + '%"></div></div>';
                                        } else {
                                            bindingsHtml += '-';
                                        }
                                        bindingsHtml += '</td>';
                                        bindingsHtml += '<td>' + (binding.create_time || '-') + '</td>';
                                        bindingsHtml += '<td>' + (binding.notes || '-') + '</td>';
                                        bindingsHtml += '</tr>';
                                    });

                                    bindingsHtml += '</tbody></table>';
                                }

                                $('#bindings-list').html(bindingsHtml);

                                // 打开弹窗
                                layer.open({
                                    type: 1,
                                    title: '查看项目绑定',
                                    area: ['900px', '600px'],
                                    content: $('#view-bindings-dialog'),
                                    success: function(layero, index) {
                                        // 重新渲染进度条
                                        layui.use('element', function(){
                                            var element = layui.element;
                                            element.render('progress');
                                        });
                                    }
                                });

                            } else {
                                layer.msg('获取绑定信息失败: ' + (bindingRes.msg || '未知错误'), {icon: 2});
                            }
                        }).fail(function() {
                            layer.msg('获取绑定信息失败', {icon: 2});
                        });

                    } else {
                        layer.msg('获取异常单信息失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                }).fail(function() {
                    layer.msg('获取异常单信息失败', {icon: 2});
                });
            }

            // 管理绑定
            function manageBindings(exceptionId) {
                // 获取异常单基本信息
                $.get('/system/dindin/api/quality-exceptions/' + exceptionId + '/binding-info', function(res) {
                    if (res.success === true) {
                        var exceptionInfo = res.data;

                        // 显示异常单基本信息
                        var infoHtml = '<div class="layui-row layui-col-space10">';
                        infoHtml += '<div class="layui-col-md6"><strong>异常单ID:</strong> ' + exceptionId + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>项目编号:</strong> ' + (exceptionInfo.project_code || '无') + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>项目类型:</strong> ' + (exceptionInfo.project_type || '无') + '</div>';
                        infoHtml += '<div class="layui-col-md6"><strong>当前绑定数:</strong> ' + exceptionInfo.current_binding_count + '</div>';
                        if (exceptionInfo.parsed_project_codes && exceptionInfo.parsed_project_codes.length > 0) {
                            infoHtml += '<div class="layui-col-md12"><strong>解析后编号:</strong> ' + exceptionInfo.parsed_project_codes.join(', ') + '</div>';
                        }
                        infoHtml += '</div>';
                        $('#manage-exception-info').html(infoHtml);

                        // 获取绑定详情
                        $.get('/system/dindin/api/quality-exceptions/' + exceptionId + '/bindings', function(bindingRes) {
                            if (bindingRes.success === true) {
                                var bindings = bindingRes.data.bindings;
                                var bindingsHtml = '';

                                if (bindings.length === 0) {
                                    bindingsHtml = '<div class="layui-empty"><div class="layui-empty-icon"><i class="layui-icon layui-icon-face-cry"></i></div><div class="layui-empty-text">暂无绑定项目</div></div>';
                                } else {
                                    bindingsHtml = '<table class="layui-table">';
                                    bindingsHtml += '<thead><tr><th>项目名称</th><th>项目编号</th><th>项目类型</th><th>绑定类型</th><th>备注</th><th>操作</th></tr></thead>';
                                    bindingsHtml += '<tbody>';

                                    bindings.forEach(function(binding) {
                                        bindingsHtml += '<tr>';
                                        bindingsHtml += '<td>' + (binding.project_name || '未知项目') + '</td>';
                                        bindingsHtml += '<td>' + (binding.project_code || '-') + '</td>';
                                        bindingsHtml += '<td>' + (binding.project_type_name || '未知类型') + '</td>';
                                        bindingsHtml += '<td>';
                                        if (binding.binding_type === 'manual') {
                                            bindingsHtml += '<span class="layui-badge layui-bg-blue">手动绑定</span>';
                                        } else {
                                            bindingsHtml += '<span class="layui-badge layui-bg-green">自动绑定</span>';
                                        }
                                        bindingsHtml += '</td>';
                                        bindingsHtml += '<td>';
                                        bindingsHtml += '<div class="binding-notes-container">';
                                        bindingsHtml += '<span class="binding-notes-display" id="notes-display-' + binding.id + '">' + (binding.notes || '无备注') + '</span>';
                                        bindingsHtml += '<textarea class="layui-textarea binding-notes-edit" id="notes-edit-' + binding.id + '" style="display: none;">' + (binding.notes || '') + '</textarea>';
                                        bindingsHtml += '</div>';
                                        bindingsHtml += '</td>';
                                        bindingsHtml += '<td>';
                                        bindingsHtml += '<button class="layui-btn layui-btn-xs layui-btn-normal edit-notes-btn" data-binding-id="' + binding.id + '"><i class="layui-icon layui-icon-edit"></i> 编辑备注</button> ';
                                        bindingsHtml += '<button class="layui-btn layui-btn-xs layui-btn-primary save-notes-btn" data-binding-id="' + binding.id + '" style="display: none;"><i class="layui-icon layui-icon-ok"></i> 保存</button> ';
                                        bindingsHtml += '<button class="layui-btn layui-btn-xs layui-btn-primary cancel-edit-btn" data-binding-id="' + binding.id + '" style="display: none;"><i class="layui-icon layui-icon-close"></i> 取消</button> ';
                                        bindingsHtml += '<button class="layui-btn layui-btn-xs layui-btn-danger delete-binding-btn" data-binding-id="' + binding.id + '" data-project-name="' + (binding.project_name || '未知项目') + '"><i class="layui-icon layui-icon-delete"></i> 删除</button>';
                                        bindingsHtml += '</td>';
                                        bindingsHtml += '</tr>';
                                    });

                                    bindingsHtml += '</tbody></table>';
                                }

                                $('#manage-bindings-list').html(bindingsHtml);

                                // 打开弹窗
                                layer.open({
                                    type: 1,
                                    title: '管理项目绑定',
                                    area: ['1000px', '700px'],
                                    content: $('#manage-bindings-dialog'),
                                    success: function(layero, index) {
                                        currentManageDialogIndex = index;
                                        bindManageEvents(exceptionId);
                                    }
                                });

                            } else {
                                layer.msg('获取绑定信息失败: ' + (bindingRes.msg || '未知错误'), {icon: 2});
                            }
                        }).fail(function() {
                            layer.msg('获取绑定信息失败', {icon: 2});
                        });

                    } else {
                        layer.msg('获取异常单信息失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                }).fail(function() {
                    layer.msg('获取异常单信息失败', {icon: 2});
                });
            }

            // 全局变量
            var currentManageDialogIndex = null;

            // 绑定管理事件
            function bindManageEvents(exceptionId) {
                // 编辑备注按钮事件
                $(document).off('click', '.edit-notes-btn').on('click', '.edit-notes-btn', function() {
                    var bindingId = $(this).data('binding-id');
                    $('#notes-display-' + bindingId).hide();
                    $('#notes-edit-' + bindingId).show();
                    $(this).hide();
                    $('.save-notes-btn[data-binding-id="' + bindingId + '"]').show();
                    $('.cancel-edit-btn[data-binding-id="' + bindingId + '"]').show();
                });

                // 保存备注按钮事件
                $(document).off('click', '.save-notes-btn').on('click', '.save-notes-btn', function() {
                    var bindingId = $(this).data('binding-id');
                    var newNotes = $('#notes-edit-' + bindingId).val();

                    // 调用API更新备注
                    $.ajax({
                        url: '/system/dindin/api/quality-exceptions/bindings/' + bindingId + '/update-notes',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            notes: newNotes
                        }),
                        success: function(res) {
                            if (res.success === true) {
                                $('#notes-display-' + bindingId).text(newNotes || '无备注').show();
                                $('#notes-edit-' + bindingId).hide();
                                $('.edit-notes-btn[data-binding-id="' + bindingId + '"]').show();
                                $('.save-notes-btn[data-binding-id="' + bindingId + '"]').hide();
                                $('.cancel-edit-btn[data-binding-id="' + bindingId + '"]').hide();
                                layer.msg('备注更新成功', {icon: 1});
                            } else {
                                layer.msg('备注更新失败: ' + (res.msg || '未知错误'), {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('备注更新失败', {icon: 2});
                        }
                    });
                });

                // 取消编辑按钮事件
                $(document).off('click', '.cancel-edit-btn').on('click', '.cancel-edit-btn', function() {
                    var bindingId = $(this).data('binding-id');
                    $('#notes-display-' + bindingId).show();
                    $('#notes-edit-' + bindingId).hide();
                    $('.edit-notes-btn[data-binding-id="' + bindingId + '"]').show();
                    $('.save-notes-btn[data-binding-id="' + bindingId + '"]').hide();
                    $('.cancel-edit-btn[data-binding-id="' + bindingId + '"]').hide();

                    // 恢复原始值
                    var originalNotes = $('#notes-display-' + bindingId).text();
                    if (originalNotes === '无备注') {
                        $('#notes-edit-' + bindingId).val('');
                    } else {
                        $('#notes-edit-' + bindingId).val(originalNotes);
                    }
                });

                // 删除绑定按钮事件
                $(document).off('click', '.delete-binding-btn').on('click', '.delete-binding-btn', function() {
                    var bindingId = $(this).data('binding-id');
                    var projectName = $(this).data('project-name');

                    layer.confirm('确定要删除与项目 "' + projectName + '" 的绑定关系吗？', {
                        icon: 3,
                        title: '确认删除'
                    }, function(index) {
                        // 调用API删除绑定
                        $.ajax({
                            url: '/system/dindin/api/quality-exceptions/bindings/' + bindingId + '/delete',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                notes: '手动删除绑定'
                            }),
                            success: function(res) {
                                if (res.success === true) {
                                    layer.msg('绑定删除成功', {icon: 1});
                                    layer.close(index);

                                    // 刷新管理弹窗内容
                                    if (currentManageDialogIndex) {
                                        layer.close(currentManageDialogIndex);
                                    }
                                    manageBindings(exceptionId);

                                    // 刷新主表格
                                    tableIns.reload();
                                } else {
                                    layer.msg('绑定删除失败: ' + (res.msg || '未知错误'), {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('绑定删除失败', {icon: 2});
                            }
                        });
                    });
                });

                // 关闭管理弹窗按钮事件
                $('#close-manage-dialog').off('click').on('click', function() {
                    if (currentManageDialogIndex) {
                        layer.close(currentManageDialogIndex);
                        currentManageDialogIndex = null;
                    }
                });
            }

        });
    </script>
</body>
</html>
