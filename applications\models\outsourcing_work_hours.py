import datetime
from applications.extensions import db


class OutsourcingWorkHours(db.Model):
    """外协工时管理模型"""
    __tablename__ = 'outsourcing_work_hours'
    
    id = db.Column(db.Integer, primary_key=True, comment="工时记录ID")
    project_code = db.Column(db.String(50), nullable=False, comment="项目编号")
    project_type = db.Column(db.String(50), comment="项目类型")
    outsourcing_id = db.Column(db.Integer, db.ForeignKey('outsourcing_info.id'), nullable=False, comment="外协信息ID")
    work_location = db.Column(db.String(20), nullable=False, comment="工作地点(厂内/厂外)")
    work_hours = db.Column(db.Float, nullable=False, comment="工时数")
    work_start_date = db.Column(db.Date, nullable=False, comment="工作开始日期")
    work_end_date = db.Column(db.Date, nullable=False, comment="工作结束日期")
    work_description = db.Column(db.Text, comment="工作内容描述")
    hourly_rate = db.Column(db.Float, comment="时薪")
    total_cost = db.Column(db.Float, comment="总费用")
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False, comment="所属部门ID")
    creator_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False, comment="创建人ID")
    status = db.Column(db.Integer, default=1, comment="状态(1正常,0删除)")
    remark = db.Column(db.Text, comment="备注")
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联部门信息
    dept = db.relationship('Dept', backref='outsourcing_work_hours', foreign_keys=[dept_id])
    # 关联创建人信息
    creator = db.relationship('User', backref='created_work_hours', foreign_keys=[creator_id])
