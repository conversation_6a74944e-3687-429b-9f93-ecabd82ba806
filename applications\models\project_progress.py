import datetime
from applications.extensions import db


class ProjectProgress(db.Model):
    """项目进度详情表"""
    __tablename__ = 'project_progress'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.Integer, db.<PERSON>ey('import_project.id'), nullable=False, comment='项目ID')
    project_description = db.Column(db.Text, comment='项目业务简介')
    production_start_date = db.Column(db.Date, comment='生产开始日期')
    production_end_date = db.Column(db.Date, comment='生产完成日期')
    warehouse_completion_date = db.Column(db.Date, comment='入库完成日期')
    shipping_start_date = db.Column(db.Date, comment='发货开始日期')
    shipping_end_date = db.Column(db.Date, comment='发货完成日期')
    installation_start_date = db.Column(db.Date, comment='安装开始日期')
    installation_end_date = db.Column(db.Date, comment='安装完成日期')
    installation_acceptance_date = db.Column(db.Date, comment='安装验收日期')
    debugging_start_date = db.Column(db.Date, comment='调试开始日期')
    debugging_end_date = db.Column(db.Date, comment='调试完成日期')
    project_start_date = db.Column(db.Date, comment='项目开始日期')
    acceptance_date = db.Column(db.Date, comment='验收日期')
    project_end_date = db.Column(db.Date, comment='项目完成日期')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')

    # 关联项目表
    project = db.relationship('Import_project', backref='progress_details')

    def __init__(self, **kwargs):
        super(ProjectProgress, self).__init__(**kwargs)
