"""
项目预估成本服务
处理项目预估成本的汇总计算和查询
"""
import datetime
from applications.extensions import db
from applications.models.import_project import ProjectEstimate, Import_project
from applications.models.admin_dept import Dept
from applications.models.project_manage_dept import ProjectManageDept


def update_project_total_estimates(project_id):
    """
    汇总项目的所有部门预估成本，更新到Import_project表
    
    汇总规则：
    - 人工成本：所有部门人工成本相加
    - 机械BOM成本：所有部门机械BOM成本相加
    - 电气BOM成本：所有部门电气BOM成本相加
    - 总BOM成本：机械BOM成本 + 电气BOM成本
    - 其他成本：所有部门其他成本相加
    - 总预估成本：总BOM成本 + 人工成本 + 其他成本
    
    Args:
        project_id: 项目ID
        
    Returns:
        tuple: (success: bool, message: str)
    """
    try:
        # 获取项目的所有部门预估记录
        estimates = ProjectEstimate.query.filter_by(project_id=project_id).all()
        
        if not estimates:
            return True, f"项目 {project_id} 暂无部门预估数据"
        
        # 汇总各类成本（所有部门相加）
        total_labor_cost = sum(est.labor_cost or 0 for est in estimates)
        total_mechanical_bom = sum(est.mechanical_bom_cost or 0 for est in estimates)
        total_electrical_bom = sum(est.electrical_bom_cost or 0 for est in estimates)
        total_other_cost = sum(est.other_cost or 0 for est in estimates)
        
        # 计算总BOM成本（机械BOM + 电气BOM）
        total_bom_cost = total_mechanical_bom + total_electrical_bom
        
        # 计算总预估成本
        total_estimate_cost = total_bom_cost + total_labor_cost + total_other_cost
        
        # 更新项目表
        project = Import_project.query.get(project_id)
        if not project:
            return False, f"项目 {project_id} 不存在"
        
        # 更新项目预估成本字段
        project.labor_cost = total_labor_cost
        project.mechanical_bom_cost = total_mechanical_bom
        project.electrical_bom_cost = total_electrical_bom
        project.bom_cost = total_bom_cost
        project.other_cost = total_other_cost
        project.estimate_cost = total_estimate_cost
        
        db.session.commit()
        
        # 记录汇总日志
        print(f"项目 {project_id} ({project.project_name}) 预估成本汇总完成:")
        print(f"  - 人工成本: ¥{total_labor_cost:,.2f}")
        print(f"  - 机械BOM成本: ¥{total_mechanical_bom:,.2f}")
        print(f"  - 电气BOM成本: ¥{total_electrical_bom:,.2f}")
        print(f"  - 总BOM成本: ¥{total_bom_cost:,.2f}")
        print(f"  - 其他成本: ¥{total_other_cost:,.2f}")
        print(f"  - 总预估成本: ¥{total_estimate_cost:,.2f}")
        
        return True, f"项目 {project_id} 预估成本汇总完成，总预估成本: ¥{total_estimate_cost:,.2f}"
        
    except Exception as e:
        db.session.rollback()
        print(f"汇总项目 {project_id} 预估成本失败: {str(e)}")
        return False, f"汇总失败: {str(e)}"


def get_dept_estimates_by_project(project_id):
    """
    获取项目的各部门预估成本明细
    
    Args:
        project_id: 项目ID
        
    Returns:
        list: 部门预估成本明细列表
    """
    try:
        estimates = db.session.query(
            ProjectEstimate,
            Dept.dept_name,
            ProjectManageDept.dept_name.label('project_type_name')
        ).join(Dept, ProjectEstimate.dept_id == Dept.id) \
         .join(ProjectManageDept, ProjectEstimate.project_type_id == ProjectManageDept.id) \
         .filter(ProjectEstimate.project_id == project_id).all()
        
        result = []
        for estimate, dept_name, project_type_name in estimates:
            bom_cost = (estimate.mechanical_bom_cost or 0) + (estimate.electrical_bom_cost or 0)
            total_cost = (estimate.labor_cost or 0) + bom_cost + (estimate.other_cost or 0)
            
            result.append({
                'dept_name': dept_name,
                'project_type_name': project_type_name,
                'location': estimate.location,
                'estimate_hours': estimate.estimate_hours,
                'labor_cost': estimate.labor_cost or 0,
                'mechanical_bom_cost': estimate.mechanical_bom_cost or 0,
                'electrical_bom_cost': estimate.electrical_bom_cost or 0,
                'bom_cost': bom_cost,
                'other_cost': estimate.other_cost or 0,
                'total_cost': total_cost,
                'created_at': estimate.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': estimate.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return result
        
    except Exception as e:
        print(f"获取项目 {project_id} 部门预估明细失败: {str(e)}")
        return []


def batch_update_project_estimates(project_ids):
    """
    批量更新多个项目的预估成本
    
    Args:
        project_ids: 项目ID列表
        
    Returns:
        dict: 更新结果统计
    """
    results = {
        'success_count': 0,
        'error_count': 0,
        'errors': []
    }
    
    for project_id in project_ids:
        try:
            success, message = update_project_total_estimates(project_id)
            if success:
                results['success_count'] += 1
            else:
                results['error_count'] += 1
                results['errors'].append(f"项目 {project_id}: {message}")
        except Exception as e:
            results['error_count'] += 1
            results['errors'].append(f"项目 {project_id}: {str(e)}")
    
    return results


def validate_project_estimate_data(project_id, dept_id, project_type_id, location):
    """
    验证项目预估数据的有效性
    
    Args:
        project_id: 项目ID
        dept_id: 部门ID
        project_type_id: 项目类型ID
        location: 工作地点
        
    Returns:
        tuple: (is_valid: bool, message: str)
    """
    try:
        # 检查项目是否存在
        project = Import_project.query.get(project_id)
        if not project:
            return False, f"项目ID {project_id} 不存在"
        
        # 检查部门是否存在
        dept = Dept.query.get(dept_id)
        if not dept:
            return False, f"部门ID {dept_id} 不存在"
        
        # 检查项目类型是否存在
        project_type = ProjectManageDept.query.get(project_type_id)
        if not project_type:
            return False, f"项目类型ID {project_type_id} 不存在"
        
        # 检查售后部门不能导入厂内工作地点
        if dept.dept_name == '售后部' and location == '厂内':
            return False, "售后部不能导入厂内工作地点"
        
        return True, "数据验证通过"
        
    except Exception as e:
        return False, f"数据验证失败: {str(e)}"
