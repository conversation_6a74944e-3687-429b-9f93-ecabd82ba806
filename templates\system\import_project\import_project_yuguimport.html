<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>预估项目导入</title>
        {% include 'system/common/header.html' %}
        <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
        <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
        <script src="{{ url_for('static', filename='index/layui/layui.js') }}"></script>
    </head>
    <body class="pear-container">
        <!-- 导入表单 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <h3 style="margin: 0; font-size: 16px;">预估工时数据导入</h3>
            </div>
            <div class="layui-card-body">
                <!-- 智能覆盖规则说明 -->
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-left: 3px solid #FFB800; border-radius: 3px;">
                    <div style="color: #666; font-size: 13px; line-height: 1.6;">
                        <i class="layui-icon layui-icon-tips" style="color: #FFB800; margin-right: 5px;"></i>
                        <strong style="color: #333;">智能覆盖规则：</strong>
                        <br>
                        系统会自动检测数据变动，只有当预估工时或成本数据发生变化时才会覆盖更新现有记录，数据无变动时将跳过更新。
                    </div>
                </div>

                <!-- 文件上传表单 -->
                <form class="layui-form" id="uploadForm" enctype="multipart/form-data">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 80px;">选择文件</label>
                        <div class="layui-input-inline" style="width: 280px;">
                            <input type="file" name="file" id="fileInput" class="layui-input" accept=".xlsx,.xls" required>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="layui-form-item" style="margin-top: 15px;">
                        <div class="layui-input-block" style="margin-left: 80px;">
                            {% if authorize("system:import_project:yuguimport") %}
                                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="import" style="margin-right: 10px;">
                                    <i class="layui-icon layui-icon-upload"></i> 智能导入
                                </button>
                            {% endif %}
                            {% if authorize("system:import_project:yuguimport") %}
                                <a class="layui-btn layui-btn-primary" href="{{ url_for('system.import_project.yugu_download_template') }}">
                                    <i class="layui-icon layui-icon-download-circle"></i> 下载模板
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <script>
            layui.use(['form', 'upload'], function () {
                let form = layui.form;
                let upload = layui.upload;
                let $ = layui.jquery;

                // 导入表单提交
                form.on('submit(import)', function (data) {
                    var fileInput = document.getElementById('fileInput');
                    if (!fileInput.files || fileInput.files.length === 0) {
                        layer.msg('请先选择文件');
                        return false;
                    }

                    var formData = new FormData();
                    formData.append('file', fileInput.files[0]);

                    var loading = layer.load(2, {
                        content: '',
                        shade: [0.1, '#fff']
                    });

                    $.ajax({
                        url: '{{ url_for("system.import_project.import_yugu_data") }}',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code === 200) {
                                // 显示详细导入结果
                                let msg = res.msg;
                                if (res.data.duplicate > 0 || res.data.error > 0) {
                                    msg += '\n\n点击确定查看详情';
                                }

                                // 检查是否有预估成本更新信息
                                let hasUpdates = res.data.updated_projects && res.data.updated_projects > 0;
                                let icon = hasUpdates ? 1 : 6; // 成功图标或信息图标

                                // 格式化消息内容，使其更易读，并添加滚动容器
                                let formattedMsg = '<div style="padding: 20px; height: 100%; display: flex; flex-direction: column;">' +
                                                  '<div style="flex: 1; overflow-y: auto; padding: 15px; background: #f8f9fa; border-radius: 5px; line-height: 1.6; border: 1px solid #e9ecef; min-height: 200px;">' +
                                                  msg.replace(/\n/g, '<br>') +
                                                  '</div>' +
                                                  '</div>';

                                layer.open({
                                    type: 1,
                                    title: hasUpdates ? '✅ 导入成功 - 预估成本已自动更新' : '📊 导入结果',
                                    area: ['500px', '80%'],
                                    maxmin: true,
                                    content: formattedMsg,
                                    btn: ['确定', '查看详情'],
                                    btnAlign: 'c',
                                    yes: function(index) {
                                        layer.close(index);
                                    },
                                    btn2: function(index) {
                                        layer.close(index);
                                        // 显示详细信息弹窗
                                        if (res.data.duplicate > 0 || res.data.error > 0 || res.data.update > 0) {
                                            let detailContent = '<div style="padding: 20px; font-family: Arial, sans-serif;  display: flex; flex-direction: column;">';

                                            // 统计信息
                                            detailContent += '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px; flex-shrink: 0;">';
                                            detailContent += '<h4 style="margin: 0 0 10px 0; color: #333;">📊 导入统计</h4>';
                                            detailContent += '<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">';
                                            detailContent += '<div>总计处理：<strong>' + res.data.total + '</strong> 条</div>';
                                            detailContent += '<div>新增记录：<strong style="color: #28a745;">' + res.data.success + '</strong> 条</div>';
                                            if (res.data.update > 0) {
                                                detailContent += '<div>覆盖更新：<strong style="color: #007bff;">' + res.data.update + '</strong> 条</div>';
                                            }
                                            if (res.data.duplicate > 0) {
                                                detailContent += '<div>跳过记录：<strong style="color: #6c757d;">' + res.data.duplicate + '</strong> 条</div>';
                                            }
                                            if (res.data.error > 0) {
                                                detailContent += '<div>错误数据：<strong style="color: #dc3545;">' + res.data.error + '</strong> 条</div>';
                                            }
                                            if (res.data.updated_projects > 0) {
                                                detailContent += '<div style="grid-column: 1 / -1;">项目成本更新：<strong style="color: #28a745;">' + res.data.updated_projects + '</strong> 个项目</div>';
                                            }
                                            detailContent += '</div></div>';

                                            // 详细消息
                                            detailContent += '<div style="flex: 1; display: flex; flex-direction: column; min-height: 0;">';
                                            detailContent += '<h4 style="color: #333; margin-bottom: 10px; flex-shrink: 0;">📝 详细信息</h4>';
                                            detailContent += '<div style="background: white; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-y: auto; line-height: 1.6; flex: 1;">';
                                            detailContent += res.msg.replace(/\n/g, '<br>');
                                            detailContent += '</div></div>';
                                            detailContent += '</div>';

                                            layer.open({
                                                type: 1,
                                                title: '📋 导入详情报告',
                                                area: ['80%', '85%'],
                                                content: detailContent,
                                                shadeClose: true,
                                                maxmin: true,
                                                scrollbar: true,
                                                resize: true
                                            });
                                        }
                                    }
                                });

                                // 刷新父页面表格
                                parent.layui.table.reload('yugu-table');
                            } else {
                                layer.msg('导入失败：' + res.msg);
                            }
                        },
                        error: function (xhr, status, error) {
                            layer.close(loading);
                            layer.msg('请求失败：' + error);
                        }
                    });
                    return false;
                });
            });
        </script>
    </body>
</html>
