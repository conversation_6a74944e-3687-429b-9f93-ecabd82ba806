<!DOCTYPE html>
<html>
<head>
    <title>节假日日历视图</title>
    {% include 'system/common/header.html' %}
    <style>
        .calendar-container {
            padding: 20px;
        }
        .calendar-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .calendar-header .year-selector {
            display: inline-block;
            margin: 0 20px;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }
        .month-card {
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 15px;
            background: #fff;
        }
        .month-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .month-calendar {
            width: 100%;
        }
        .month-calendar table {
            width: 100%;
            border-collapse: collapse;
        }
        .month-calendar th,
        .month-calendar td {
            width: 14.28%;
            height: 30px;
            text-align: center;
            border: 1px solid #f0f0f0;
            font-size: 12px;
        }
        .month-calendar th {
            background: #f8f8f8;
            font-weight: bold;
        }
        .month-calendar td {
            cursor: pointer;
            position: relative;
        }
        .month-calendar td:hover {
            background: #f0f0f0;
        }
        .holiday-legal {
            background: #ff5722 !important;
            color: white;
        }
        .holiday-makeup {
            background: #ff9800 !important;
            color: white;
        }
        .holiday-weekend {
            background: #2196f3 !important;
            color: white;
        }
        .legend {
            margin-top: 20px;
            text-align: center;
        }
        .legend-item {
            display: inline-block;
            margin: 0 15px;
        }
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<div class="calendar-container">
    <div class="calendar-header">
        <button class="layui-btn layui-btn-sm" id="prev-year">
            <i class="layui-icon layui-icon-left"></i> 上一年
        </button>
        <span class="year-selector">
            <span id="current-year">{{ year or 2025 }}</span>年节假日日历
        </span>
        <button class="layui-btn layui-btn-sm" id="next-year">
            下一年 <i class="layui-icon layui-icon-right"></i>
        </button>
    </div>
    
    <div class="calendar-grid" id="calendar-grid">
        <!-- 12个月的日历将在这里动态生成 -->
    </div>
    
    <div class="legend">
        <div class="legend-item">
            <span class="legend-color holiday-legal"></span>
            法定节假日(3倍工资)
        </div>
        <div class="legend-item">
            <span class="legend-color holiday-makeup"></span>
            调休日(2倍工资)
        </div>
        <div class="legend-item">
            <span class="legend-color holiday-weekend"></span>
            周末(2倍工资)
        </div>
    </div>
</div>

{% include 'system/common/footer.html' %}

<script>
layui.use(['layer', 'jquery'], function () {
    let layer = layui.layer;
    let $ = layui.jquery;
    
    let currentYear = {{ year or 2025 }};
    let holidayData = {};
    
    // 月份名称
    const monthNames = [
        '一月', '二月', '三月', '四月', '五月', '六月',
        '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    
    // 星期名称
    const weekNames = ['日', '一', '二', '三', '四', '五', '六'];
    
    // 初始化
    init();
    
    function init() {
        loadHolidayData();
        bindEvents();
    }
    
    function bindEvents() {
        // 上一年按钮
        document.getElementById('prev-year').addEventListener('click', function() {
            currentYear--;
            updateYear();
            loadHolidayData();
        });
        
        // 下一年按钮
        document.getElementById('next-year').addEventListener('click', function() {
            currentYear++;
            updateYear();
            loadHolidayData();
        });
    }
    
    function updateYear() {
        document.getElementById('current-year').textContent = currentYear;
    }
    
    function loadHolidayData() {
        layer.load(2);
        $.ajax({
            url: '/system/holiday_config/calendar_data',
            type: 'GET',
            data: { year: currentYear },
            success: function(res) {
                layer.closeAll('loading');
                if (res.success) {
                    holidayData = {};
                    res.data.forEach(function(holiday) {
                        holidayData[holiday.date] = holiday;
                    });
                    renderCalendar();
                } else {
                    layer.msg('加载日历数据失败', {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('加载日历数据失败', {icon: 2});
            }
        });
    }
    
    function renderCalendar() {
        const calendarGrid = document.getElementById('calendar-grid');
        calendarGrid.innerHTML = '';
        
        for (let month = 0; month < 12; month++) {
            const monthCard = createMonthCard(month);
            calendarGrid.appendChild(monthCard);
        }
    }
    
    function createMonthCard(month) {
        const monthCard = document.createElement('div');
        monthCard.className = 'month-card';
        
        const monthTitle = document.createElement('div');
        monthTitle.className = 'month-title';
        monthTitle.textContent = monthNames[month];
        
        const monthCalendar = document.createElement('div');
        monthCalendar.className = 'month-calendar';
        monthCalendar.innerHTML = createMonthTable(month);
        
        monthCard.appendChild(monthTitle);
        monthCard.appendChild(monthCalendar);
        
        return monthCard;
    }
    
    function createMonthTable(month) {
        let html = '<table>';
        
        // 表头
        html += '<tr>';
        weekNames.forEach(function(weekName) {
            html += '<th>' + weekName + '</th>';
        });
        html += '</tr>';
        
        // 获取当月第一天和最后一天
        const firstDay = new Date(currentYear, month, 1);
        const lastDay = new Date(currentYear, month + 1, 0);
        const firstDayWeek = firstDay.getDay();
        const daysInMonth = lastDay.getDate();
        
        let day = 1;
        let weekCount = Math.ceil((daysInMonth + firstDayWeek) / 7);
        
        for (let week = 0; week < weekCount; week++) {
            html += '<tr>';
            for (let weekDay = 0; weekDay < 7; weekDay++) {
                if (week === 0 && weekDay < firstDayWeek) {
                    html += '<td></td>';
                } else if (day > daysInMonth) {
                    html += '<td></td>';
                } else {
                    const dateStr = currentYear + '-' + 
                                  String(month + 1).padStart(2, '0') + '-' + 
                                  String(day).padStart(2, '0');
                    
                    const holiday = holidayData[dateStr];
                    let className = '';
                    let title = '';
                    
                    if (holiday) {
                        className = 'holiday-' + holiday.type;
                        title = holiday.title + ' (' + holiday.rate + '倍)';
                    }
                    
                    html += '<td class="' + className + '" title="' + title + '" data-date="' + dateStr + '">' + day + '</td>';
                    day++;
                }
            }
            html += '</tr>';
        }
        
        html += '</table>';
        return html;
    }
    
    // 点击日期事件
    document.addEventListener('click', function(e) {
        if (e.target.tagName === 'TD' && e.target.dataset.date) {
            const dateStr = e.target.dataset.date;
            const holiday = holidayData[dateStr];
            
            if (holiday) {
                layer.alert(
                    '<strong>' + holiday.title + '</strong><br>' +
                    '日期：' + holiday.date + '<br>' +
                    '类型：' + getHolidayTypeText(holiday.type) + '<br>' +
                    '倍率：' + holiday.rate + '倍<br>' +
                    '说明：' + (holiday.description || '无'),
                    {
                        title: '节假日详情',
                        area: ['400px', '250px']
                    }
                );
            } else {
                // 判断是否为周末
                const date = new Date(dateStr);
                const weekDay = date.getDay();
                if (weekDay === 0 || weekDay === 6) {
                    layer.alert('这是周末，按2倍工资计算加班费', {title: '提示'});
                } else {
                    layer.alert('这是工作日，按1.5倍工资计算加班费', {title: '提示'});
                }
            }
        }
    });
    
    function getHolidayTypeText(type) {
        const typeMap = {
            'legal': '法定节假日(3倍)',
            'makeup': '调休日(2倍)',
            'weekend': '周末(2倍)'
        };
        return typeMap[type] || '未知';
    }
});
</script>
</body>
</html>
