"""
部门工时单位成本定时任务配置管理服务
"""
import datetime
import logging
from typing import Dict, Optional
from applications.extensions import db
from applications.models.admin_dict import DictType, DictData

# 配置日志
logger = logging.getLogger(__name__)


class DeptUnitCostConfigService:
    """部门工时单位成本定时任务配置管理服务类"""
    
    TYPE_CODE = 'dept_unit_cost_schedule_config'
    
    # 配置项键名
    CONFIG_KEYS = {
        'enabled': 'enabled',           # 是否启用
        'frequency': 'frequency',       # 执行频率：monthly, weekly, daily
        'hour': 'hour',                 # 执行小时
        'minute': 'minute',             # 执行分钟
        'day_of_month': 'day_of_month', # 每月执行日期（monthly时使用）
        'day_of_week': 'day_of_week',   # 每周执行日期（weekly时使用）
        'months_span': 'months_span'    # 计算月份跨度
    }
    
    # 默认配置值
    DEFAULT_CONFIG = {
        'enabled': '1',         # 1-启用, 0-禁用
        'frequency': 'monthly', # monthly-每月, weekly-每周, daily-每天
        'hour': '2',           # 凌晨2点
        'minute': '0',         # 0分
        'day_of_month': '1',   # 每月1号
        'day_of_week': '1',    # 周一
        'months_span': '2'     # 计算前2个月的数据
    }
    
    @classmethod
    def init_config_data(cls):
        """初始化配置数据（如果不存在）"""
        try:
            # 检查字典类型是否存在（防止重复创建）
            dict_type_count = DictType.query.filter_by(type_code=cls.TYPE_CODE).count()
            if dict_type_count == 0:
                # 创建字典类型
                dict_type = DictType(
                    type_name='部门工时单位成本定时任务配置',
                    type_code=cls.TYPE_CODE,
                    description='部门工时单位成本定时任务的配置参数',
                    enable=1
                )
                db.session.add(dict_type)
                db.session.flush()  # 获取ID
            elif dict_type_count > 1:
                # 如果发现重复的字典类型，记录警告
                logger.warning(f"发现重复的字典类型: {cls.TYPE_CODE} (数量: {dict_type_count})")
            
            # 检查并创建默认配置项
            for key, default_value in cls.DEFAULT_CONFIG.items():
                # 检查是否已存在配置项（防止重复创建）
                existing_count = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=key
                ).count()

                if existing_count == 0:
                    # 创建配置项
                    config_labels = {
                        'enabled': '是否启用',
                        'frequency': '执行频率',
                        'hour': '执行小时',
                        'minute': '执行分钟',
                        'day_of_month': '每月执行日期',
                        'day_of_week': '每周执行日期',
                        'months_span': '计算月份跨度'
                    }

                    dict_data = DictData(
                        data_label=config_labels.get(key, key),
                        data_value=key,
                        type_code=cls.TYPE_CODE,
                        remark=default_value,  # 将默认值存储在remark字段中
                        enable=1,
                        is_default=1 if key == 'enabled' else 0
                    )
                    db.session.add(dict_data)
                elif existing_count > 1:
                    # 如果发现重复项，记录警告
                    logger.warning(f"发现重复的配置项: {key} (数量: {existing_count})")
            
            db.session.commit()
            logger.info("部门工时单位成本定时任务配置初始化完成")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"初始化部门工时单位成本定时任务配置失败: {str(e)}")
            raise
    
    @classmethod
    def get_config(cls) -> Dict[str, str]:
        """获取当前配置

        Returns:
            Dict[str, str]: 配置字典
        """
        try:
            config = {}
            config_items = DictData.query.filter_by(
                type_code=cls.TYPE_CODE,
                enable=1
            ).all()

            for item in config_items:
                config[item.data_value] = item.remark or cls.DEFAULT_CONFIG.get(item.data_value, '')

            # 补充缺失的默认配置
            for key, default_value in cls.DEFAULT_CONFIG.items():
                if key not in config:
                    config[key] = default_value

            return config
            
        except Exception as e:
            logger.error(f"获取部门工时单位成本定时任务配置失败: {str(e)}")
            return cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def update_config(cls, config_key: str, config_value: str) -> bool:
        """更新单个配置项
        
        Args:
            config_key: 配置键
            config_value: 配置值
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if config_key not in cls.CONFIG_KEYS.values():
                logger.error(f"无效的配置键: {config_key}")
                return False
            
            # 查找配置项
            config_item = DictData.query.filter_by(
                type_code=cls.TYPE_CODE,
                data_value=config_key
            ).first()
            
            if config_item:
                # 更新现有配置
                config_item.remark = config_value
                config_item.update_time = datetime.datetime.now()
            else:
                # 创建新配置项（理论上不应该发生，因为init_config_data会创建所有默认配置）
                config_labels = {
                    'enabled': '是否启用',
                    'frequency': '执行频率',
                    'hour': '执行小时',
                    'minute': '执行分钟',
                    'day_of_month': '每月执行日期',
                    'day_of_week': '每周执行日期',
                    'months_span': '计算月份跨度'
                }
                
                config_item = DictData(
                    data_label=config_labels.get(config_key, config_key),
                    data_value=config_key,
                    type_code=cls.TYPE_CODE,
                    remark=config_value,
                    enable=1
                )
                db.session.add(config_item)
            
            db.session.commit()
            logger.info(f"更新部门工时单位成本定时任务配置成功: {config_key} = {config_value}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新部门工时单位成本定时任务配置失败: {str(e)}")
            return False
    
    @classmethod
    def batch_update_config(cls, config_updates: Dict[str, str]) -> bool:
        """批量更新配置
        
        Args:
            config_updates: 配置更新字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            for config_key, config_value in config_updates.items():
                if config_key not in cls.CONFIG_KEYS.values():
                    logger.error(f"无效的配置键: {config_key}")
                    continue
                
                # 查找配置项
                config_item = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=config_key
                ).first()
                
                if config_item:
                    config_item.remark = config_value
                    config_item.update_time = datetime.datetime.now()
            
            db.session.commit()
            logger.info(f"批量更新部门工时单位成本定时任务配置成功: {config_updates}")
            return True
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量更新部门工时单位成本定时任务配置失败: {str(e)}")
            return False
    
    @classmethod
    def is_enabled(cls) -> bool:
        """检查定时任务是否启用
        
        Returns:
            bool: 是否启用
        """
        config = cls.get_config()
        return config.get('enabled', '0') == '1'
    
    @classmethod
    def get_schedule_config(cls) -> Dict:
        """获取调度配置
        
        Returns:
            Dict: 调度配置
        """
        config = cls.get_config()
        
        try:
            hour = int(config.get('hour', '2'))
            minute = int(config.get('minute', '0'))
            frequency = config.get('frequency', 'monthly')
            day_of_month = int(config.get('day_of_month', '1'))
            day_of_week = int(config.get('day_of_week', '1'))
            months_span = int(config.get('months_span', '2'))
            
            return {
                'hour': hour,
                'minute': minute,
                'frequency': frequency,
                'day_of_month': day_of_month,
                'day_of_week': day_of_week,
                'months_span': months_span
            }
            
        except (ValueError, TypeError) as e:
            logger.error(f"解析调度配置失败: {str(e)}")
            # 返回默认配置
            return {
                'hour': 2,
                'minute': 0,
                'frequency': 'monthly',
                'day_of_month': 1,
                'day_of_week': 1,
                'months_span': 2
            }
    
    @classmethod
    def get_config_for_frontend(cls) -> Dict:
        """获取前端显示用的配置数据
        
        Returns:
            Dict: 前端配置数据
        """
        config = cls.get_config()
        schedule_config = cls.get_schedule_config()
        
        return {
            'enabled': config.get('enabled', '0') == '1',
            'frequency': config.get('frequency', 'monthly'),
            'hour': schedule_config['hour'],
            'minute': schedule_config['minute'],
            'day_of_month': schedule_config['day_of_month'],
            'day_of_week': schedule_config['day_of_week'],
            'months_span': schedule_config['months_span'],
            'frequency_options': [
                {'value': 'daily', 'label': '每天'},
                {'value': 'weekly', 'label': '每周'},
                {'value': 'monthly', 'label': '每月'}
            ],
            'day_of_week_options': [
                {'value': 1, 'label': '周一'},
                {'value': 2, 'label': '周二'},
                {'value': 3, 'label': '周三'},
                {'value': 4, 'label': '周四'},
                {'value': 5, 'label': '周五'},
                {'value': 6, 'label': '周六'},
                {'value': 0, 'label': '周日'}
            ]
        }
