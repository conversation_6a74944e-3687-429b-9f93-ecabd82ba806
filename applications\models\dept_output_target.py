"""
部门单位产出目标值数据模型
用于存储和管理各部门的年度产出目标值
"""
import datetime
from applications.extensions import db


class DeptOutputTarget(db.Model):
    """部门单位产出目标值模型"""
    __tablename__ = 'dept_output_target'
    
    id = db.Column(db.Integer, primary_key=True, comment='主键ID')
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False, comment='部门ID')
    year = db.Column(db.Integer, nullable=False, comment='年度')
    target_value = db.Column(db.DECIMAL(10, 4), nullable=False, comment='目标值')
    status = db.Column(db.Integer, default=1, comment='状态(1有效,0无效)')
    import_file = db.Column(db.String(255), comment='导入文件名')
    import_by = db.Column(db.<PERSON>, db.<PERSON>('admin_user.id'), comment='导入人ID')
    remark = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 建立唯一约束：每个部门每年只能有一个目标值
    __table_args__ = (
        db.UniqueConstraint('dept_id', 'year', name='uk_dept_year'),
    )
    
    # 建立关联关系
    dept = db.relationship('Dept', backref='output_targets', lazy=True)
    import_user = db.relationship('User', foreign_keys=[import_by], lazy=True)
    
    def __repr__(self):
        return f'<DeptOutputTarget {self.dept_id}-{self.year}: {self.target_value}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'dept_id': self.dept_id,
            'dept_name': self.dept.dept_name if self.dept else None,
            'year': self.year,
            'target_value': float(self.target_value) if self.target_value else 0,
            'status': self.status,
            'import_file': self.import_file,
            'import_by': self.import_by,
            'import_user_name': self.import_user.realname if self.import_user else None,
            'remark': self.remark,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_by_dept_year(cls, dept_id, year):
        """根据部门ID和年度获取目标值"""
        return cls.query.filter_by(dept_id=dept_id, year=year, status=1).first()
    
    @classmethod
    def get_targets_by_year(cls, year, dept_ids=None):
        """获取指定年度的目标值列表"""
        query = cls.query.filter_by(year=year, status=1)
        if dept_ids:
            query = query.filter(cls.dept_id.in_(dept_ids))
        return query.all()
    
    @classmethod
    def batch_upsert(cls, targets_data, import_by=None, import_file=None):
        """批量插入或更新目标值"""
        try:
            for data in targets_data:
                existing = cls.query.filter_by(
                    dept_id=data['dept_id'], 
                    year=data['year']
                ).first()
                
                if existing:
                    # 更新现有记录
                    existing.target_value = data['target_value']
                    existing.import_by = import_by
                    existing.import_file = import_file
                    existing.remark = data.get('remark', existing.remark)
                    existing.updated_at = datetime.datetime.now()
                else:
                    # 创建新记录
                    new_target = cls(
                        dept_id=data['dept_id'],
                        year=data['year'],
                        target_value=data['target_value'],
                        import_by=import_by,
                        import_file=import_file,
                        remark=data.get('remark', ''),
                        status=1
                    )
                    db.session.add(new_target)
            
            db.session.commit()
            return True, "目标值批量更新成功"
            
        except Exception as e:
            db.session.rollback()
            return False, f"目标值批量更新失败: {str(e)}"
