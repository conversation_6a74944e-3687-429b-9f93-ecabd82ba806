import datetime
from applications.extensions import db


class OutsourcingInfo(db.Model):
    """外协信息管理模型"""
    __tablename__ = 'outsourcing_info'
    
    id = db.Column(db.Integer, primary_key=True, comment="外协信息ID")
    outsourcing_name = db.Column(db.String(100), nullable=False, comment="外协名称")
    contact_person = db.Column(db.String(50), comment="联系人")
    contact_phone = db.Column(db.String(20), comment="联系电话")
    contact_email = db.Column(db.String(100), comment="联系邮箱")
    company_address = db.Column(db.String(255), comment="公司地址")
    business_scope = db.Column(db.Text, comment="业务范围")
    qualification_level = db.Column(db.String(50), comment="资质等级")
    hourly_rate = db.Column(db.Float, comment="时薪(元/小时)")
    cooperation_status = db.Column(db.Integer, default=1, comment="合作状态(1正常,0停用)")
    remark = db.Column(db.Text, comment="备注")
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 反向关联外协工时记录
    work_hours = db.relationship('OutsourcingWorkHours', backref='outsourcing_info', lazy='dynamic')
