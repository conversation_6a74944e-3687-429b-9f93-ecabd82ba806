from flask import Blueprint, jsonify, render_template, request, send_file
from flask_login import login_required, current_user
from sqlalchemy import desc, text
import pandas as pd
from io import BytesIO
import datetime

from applications.common import curd
from applications.common.curd import enable_status, disable_status
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import ProjectManageDept, DictType, DictData
from applications.models.import_project import Import_project, UserEstimate, ProjectEstimate
from applications.models.admin_dept import Dept
from applications.models.admin_user import User
from applications.models.project_progress import ProjectProgress
from applications.view.system.project_progress import calculate_project_status

bp = Blueprint('import_project', __name__, url_prefix='/import_project')


@bp.route('/')
@login_required
@authorize("system:import_project:info", log=True)
def index():
    status_options = get_dict_data('project_status')
    all_depts = Dept.query.filter(Dept.status == 1).order_by(text("CONVERT(dept_name USING gbk)")).all()  # 获取所有部门
    return render_template('system/import_project/import_project_info.html',
                         status_options=status_options,
                         all_depts=all_depts)

# 用户增加

@bp.get('/add')
@authorize("system:import_project:add", log=True)
def add():
    status_options = get_dict_data('project_status')
    currency_options = get_dict_data('currency_type')  # 获取币种数据
    all_depts = Dept.query.filter(Dept.status == 1).order_by(text("CONVERT(dept_name USING gbk)")).all()
    return render_template('system/import_project/import_project_add.html',
                         status_options=status_options,
                         currency_options=currency_options,  # 传递币种数据
                         all_depts=all_depts)
@bp.post('/save')
@authorize("system:import_project:add", log=True)
def save():
    req_json = request.get_json(force=True)
    project_name = str_escape(req_json.get('project_name'))
    project_code = str_escape(req_json.get('project_code'))
    dept_id = str_escape(req_json.get('dept_id'))  # 接收 dept_id
    dept_ids = req_json.get('dept_ids', [])  # 接收 dept_ids
    if isinstance(dept_ids, str):  # 如果 dept_ids 是字符串，转换为列表
        dept_ids = dept_ids.split(',')
    project_status = int(req_json.get('project_status'))
    price = float(req_json.get('price'))
    currency = str_escape(req_json.get('currency', '人民币'))  # 接收币种，默认为人民币
    machine_number = int(req_json.get('machine_number'))
    estimate_cost = float(req_json.get('estimate_cost', 0))
    bom_cost = float(req_json.get('bom_cost', 0))
    labor_cost = float(req_json.get('labor_cost', 0))
    other_cost = float(req_json.get('other_cost', 0))

    # 获取付款相关字段（实际金额）
    prepayment = float(req_json.get('prepayment', 0) or 0)
    delivery_payment = float(req_json.get('delivery_payment', 0) or 0)
    acceptance_payment = float(req_json.get('acceptance_payment', 0) or 0)
    warranty_payment = float(req_json.get('warranty_payment', 0) or 0)

    # 获取付款天数字段
    prepayment_days = int(req_json.get('prepayment_days', 0) or 0)
    delivery_payment_days = int(req_json.get('delivery_payment_days', 0) or 0)
    acceptance_payment_days = int(req_json.get('acceptance_payment_days', 0) or 0)
    warranty_payment_days = int(req_json.get('warranty_payment_days', 0) or 0)

    # 处理计划交货时间
    delivery_date_str = str_escape(req_json.get('delivery_date'))
    delivery_date = None
    if delivery_date_str:
        try:
            import datetime
            delivery_date = datetime.datetime.strptime(delivery_date_str, '%Y-%m-%d').date()
        except:
            return fail_api(msg="计划交货时间格式不正确")

    # 处理合同开始日期
    contract_start_date_str = str_escape(req_json.get('contract_start_date'))
    contract_start_date = None
    if contract_start_date_str:
        try:
            import datetime
            contract_start_date = datetime.datetime.strptime(contract_start_date_str, '%Y-%m-%d').date()
        except:
            return fail_api(msg="合同开始日期格式不正确")

    if not project_name or not project_code:
        return fail_api(msg="项目名称不得为空")

    if bool(Import_project.query.filter_by(project_name=project_name).count()):
        return fail_api(msg="项目已经存在")

    # 创建项目对象
    project_data = {
        'project_name': project_name,
        'project_code': project_code,
        'dept_id': dept_id,  # 确保 dept_id 被正确赋值
        'dept_ids': ','.join(map(str, dept_ids)),  # 确保 dept_ids 被正确赋值
        'project_status': project_status,
        'price': price,
        'machine_number': machine_number,
        'delivery_date': delivery_date,
        'contract_start_date': contract_start_date,
        'estimate_cost': estimate_cost,
        'bom_cost': bom_cost,
        'mechanical_bom_cost': 0,  # 默认值为0
        'electrical_bom_cost': 0,  # 默认值为0
        'labor_cost': labor_cost,
        'other_cost': other_cost,
        'prepayment_days': prepayment_days,
        'delivery_payment_days': delivery_payment_days,
        'acceptance_payment_days': acceptance_payment_days,
        'warranty_payment_days': warranty_payment_days
    }

    # 尝试添加币种字段，如果数据库表中有这个字段
    try:
        project_data['currency'] = currency
    except:
        pass  # 如果数据库表中没有这个字段，忽略错误

    project = Import_project(**project_data)

    # 添加部门关系
    for dept_id in dept_ids:
        dept = Dept.query.get(dept_id)
        if dept:
            project.departments.append(dept)

    db.session.add(project)
    db.session.flush()  # 刷新会话，获取项目ID

    # 创建付款记录
    from applications.models.project_payment import ProjectPayment

    # 创建单条付款记录，包含所有付款类型的金额
    if prepayment > 0 or delivery_payment > 0 or acceptance_payment > 0 or warranty_payment > 0:
        payment_record = ProjectPayment(
            project_id=project.id,
            prepayment=prepayment,
            delivery_payment=delivery_payment,
            acceptance_payment=acceptance_payment,
            warranty_payment=warranty_payment,
            payment_status=0  # 默认未付款
        )
        db.session.add(payment_record)

    # 创建或更新项目进度记录，同步合同开始日期
    try:
        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=project.id).first()

        # 如果不存在进度记录，则创建新记录
        if not progress:
            progress = ProjectProgress(
                project_id=project.id,
                project_start_date=contract_start_date  # 同步合同开始日期到项目开始时间
            )
            db.session.add(progress)
        else:
            # 如果存在进度记录，更新项目开始时间（如果提供了合同开始日期）
            if contract_start_date:
                progress.project_start_date = contract_start_date

        # 根据进度信息计算项目状态
        calculated_status = calculate_project_status(progress)
        # 更新项目状态
        project.project_status = calculated_status
    except Exception as e:
        print(f"设置新项目状态和进度时出错: {str(e)}")
        # 出错时不影响其他操作，继续执行

    db.session.commit()
    return success_api(msg="增加成功")

# 删除项目
@bp.delete('/remove/<int:id>')
@authorize("system:import_project:remove", log=True)
def delete(id):
    try:
        # 查找项目
        project = Import_project.query.filter_by(id=id).first()
        if not project:
            return fail_api(msg="项目不存在")

        # 检查并删除相关记录
        deleted_records = []

        # 1. 删除用户预估记录
        user_estimates = UserEstimate.query.filter_by(project_id=id).all()
        if user_estimates:
            for estimate in user_estimates:
                db.session.delete(estimate)
            deleted_records.append(f"用户预估记录 {len(user_estimates)} 条")

        # 2. 删除项目预估记录
        project_estimates = ProjectEstimate.query.filter_by(project_id=id).all()
        if project_estimates:
            for estimate in project_estimates:
                db.session.delete(estimate)
            deleted_records.append(f"项目预估记录 {len(project_estimates)} 条")

        # 3. 删除付款相关记录
        from applications.models.project_payment import ProjectPayment
        from applications.models.payment_history import PaymentHistory

        # 删除付款历史记录
        payment_history_records = PaymentHistory.query.filter_by(project_id=id).all()
        if payment_history_records:
            for history in payment_history_records:
                db.session.delete(history)
            deleted_records.append(f"付款历史记录 {len(payment_history_records)} 条")

        # 删除付款记录
        payment_records = ProjectPayment.query.filter_by(project_id=id).all()
        if payment_records:
            for payment in payment_records:
                db.session.delete(payment)
            deleted_records.append(f"付款记录 {len(payment_records)} 条")

        # 4. 删除项目进度记录
        progress_records = ProjectProgress.query.filter_by(project_id=id).all()
        if progress_records:
            for progress in progress_records:
                db.session.delete(progress)
            deleted_records.append(f"进度记录 {len(progress_records)} 条")

        # 5. 删除项目成本记录
        from applications.models.project_cost import ProjectActualCost, BOMCostImport, OtherCostImport, ProjectLaborCost

        # 删除实际成本记录
        actual_costs = ProjectActualCost.query.filter_by(project_id=id).all()
        if actual_costs:
            for cost in actual_costs:
                db.session.delete(cost)
            deleted_records.append(f"实际成本记录 {len(actual_costs)} 条")

        # 删除BOM成本导入记录
        bom_imports = BOMCostImport.query.filter_by(project_id=id).all()
        if bom_imports:
            for bom in bom_imports:
                db.session.delete(bom)
            deleted_records.append(f"BOM成本记录 {len(bom_imports)} 条")

        # 删除其他成本导入记录
        other_imports = OtherCostImport.query.filter_by(project_id=id).all()
        if other_imports:
            for other in other_imports:
                db.session.delete(other)
            deleted_records.append(f"其他成本记录 {len(other_imports)} 条")

        # 删除人工成本记录
        labor_costs = ProjectLaborCost.query.filter_by(project_id=id).all()
        if labor_costs:
            for labor in labor_costs:
                db.session.delete(labor)
            deleted_records.append(f"人工成本记录 {len(labor_costs)} 条")

        # 6. 清除部门关系
        if project.departments:
            dept_count = len(project.departments)
            project.departments.clear()
            deleted_records.append(f"部门关系 {dept_count} 个")

        # 7. 最后删除项目本身
        db.session.delete(project)

        # 提交所有更改
        db.session.commit()

        # 构建删除成功消息
        if deleted_records:
            detail_msg = "，同时删除了：" + "、".join(deleted_records)
        else:
            detail_msg = ""

        return success_api(msg=f"项目 '{project.project_name}' 删除成功{detail_msg}")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败：{str(e)}")


#  编辑用户
@bp.get('/edit/<int:id>')
@authorize("system:import_project:edit", log=True)
def edit(id):
    yg = curd.get_one_by_id(Import_project, id)
    if yg.dept_ids is None:
        yg.dept_ids = ''  # 确保 dept_ids 不为 None

    # 查询项目相关的付款记录
    from applications.models.project_payment import ProjectPayment
    payments = {
        'prepayment': 0,
        'delivery_payment': 0,
        'acceptance_payment': 0,
        'warranty_payment': 0
    }

    # 查询单条付款记录
    payment_record = ProjectPayment.query.filter_by(project_id=id).first()
    if payment_record:
        payments['prepayment'] = payment_record.prepayment
        payments['delivery_payment'] = payment_record.delivery_payment
        payments['acceptance_payment'] = payment_record.acceptance_payment
        payments['warranty_payment'] = payment_record.warranty_payment

    status_options = get_dict_data('project_status')
    currency_options = get_dict_data('currency_type')  # 获取币种数据
    all_depts = Dept.query.filter(Dept.status == 1).order_by(text("CONVERT(dept_name USING gbk)")).all()
    return render_template('system/import_project/import_project_edit.html',
                         user=yg,
                         status_options=status_options,
                         currency_options=currency_options,  # 传递币种数据
                         all_depts=all_depts,
                         payments=payments)

@bp.put('/update')
@authorize("system:import_project:edit", log=True)
def update():
    req_json = request.get_json(force=True)
    id = str_escape(req_json.get("id"))
    dept_id = str_escape(req_json.get("deptId"))  # 接收 deptId
    dept_ids = req_json.get('dept_ids', [])  # 接收 dept_ids
    if isinstance(dept_ids, str):  # 如果 dept_ids 是字符串，转换为列表
        dept_ids = dept_ids.split(',')

    # 获取付款相关字段（实际金额）
    prepayment = float(req_json.get('prepayment', 0) or 0)
    delivery_payment = float(req_json.get('delivery_payment', 0) or 0)
    acceptance_payment = float(req_json.get('acceptance_payment', 0) or 0)
    warranty_payment = float(req_json.get('warranty_payment', 0) or 0)

    # 获取付款天数字段
    prepayment_days = int(req_json.get('prepayment_days', 0) or 0)
    delivery_payment_days = int(req_json.get('delivery_payment_days', 0) or 0)
    acceptance_payment_days = int(req_json.get('acceptance_payment_days', 0) or 0)
    warranty_payment_days = int(req_json.get('warranty_payment_days', 0) or 0)

    # 处理计划交货时间
    delivery_date_str = str_escape(req_json.get('delivery_date'))
    delivery_date = None
    if delivery_date_str:
        try:
            import datetime
            delivery_date = datetime.datetime.strptime(delivery_date_str, '%Y-%m-%d').date()
        except:
            return fail_api(msg="计划交货时间格式不正确")

    # 处理合同开始日期
    contract_start_date_str = str_escape(req_json.get('contract_start_date'))
    contract_start_date = None
    if contract_start_date_str:
        try:
            import datetime
            contract_start_date = datetime.datetime.strptime(contract_start_date_str, '%Y-%m-%d').date()
        except:
            return fail_api(msg="合同开始日期格式不正确")

    # 先获取项目的当前值
    project = Import_project.query.get(id)
    if not project:
        return fail_api(msg="项目不存在")

    # 创建数据字典，只包含前端提供的字段
    data = {
        "project_name": str_escape(req_json.get("project_name")),
        "project_code": str_escape(req_json.get("project_code")),
        "dept_id": dept_id,  # 确保 dept_id 被正确赋值
        "dept_ids": ','.join(map(str, dept_ids)),  # 确保 dept_ids 被正确赋值
        "project_status": int(req_json.get("project_status")),
        "price": float(req_json.get("price")),
        "machine_number": int(req_json.get("machine_number")),
        "delivery_date": delivery_date,
        "contract_start_date": contract_start_date,
        "prepayment_days": prepayment_days,
        "delivery_payment_days": delivery_payment_days,
        "acceptance_payment_days": acceptance_payment_days,
        "warranty_payment_days": warranty_payment_days,
    }

    # 尝试添加币种字段，如果数据库表中有这个字段
    try:
        # 检查项目对象是否有currency属性
        if hasattr(Import_project, 'currency'):
            data["currency"] = str_escape(req_json.get("currency", "人民币"))
    except:
        pass  # 如果数据库表中没有这个字段，忽略错误

    # 只有当前端提供了预估成本相关的字段时，才更新相应的字段
    if "estimate_cost" in req_json:
        data["estimate_cost"] = float(req_json.get("estimate_cost", 0))
    if "bom_cost" in req_json:
        data["bom_cost"] = float(req_json.get("bom_cost", 0))
    if "mechanical_bom_cost" in req_json:
        data["mechanical_bom_cost"] = float(req_json.get("mechanical_bom_cost", 0))
    if "electrical_bom_cost" in req_json:
        data["electrical_bom_cost"] = float(req_json.get("electrical_bom_cost", 0))
    if "labor_cost" in req_json:
        data["labor_cost"] = float(req_json.get("labor_cost", 0))
    if "other_cost" in req_json:
        data["other_cost"] = float(req_json.get("other_cost", 0))

    # 更新部门关系
    project.departments = []
    for dept_id in dept_ids:
        dept = Dept.query.get(dept_id)
        if dept:
            project.departments.append(dept)

    # 更新其他字段
    for key, value in data.items():
        setattr(project, key, value)

    # 更新付款记录
    from applications.models.project_payment import ProjectPayment

    # 查询现有的付款记录
    payment_record = ProjectPayment.query.filter_by(project_id=id).first()

    # 如果存在付款记录，则更新；否则创建新记录
    if payment_record:
        # 只有当所有付款金额都为0时，才删除记录
        if prepayment == 0 and delivery_payment == 0 and acceptance_payment == 0 and warranty_payment == 0:
            db.session.delete(payment_record)
        else:
            # 更新付款金额
            payment_record.prepayment = prepayment
            payment_record.delivery_payment = delivery_payment
            payment_record.acceptance_payment = acceptance_payment
            payment_record.warranty_payment = warranty_payment
    elif prepayment > 0 or delivery_payment > 0 or acceptance_payment > 0 or warranty_payment > 0:
        # 创建新的付款记录
        payment_record = ProjectPayment(
            project_id=project.id,
            prepayment=prepayment,
            delivery_payment=delivery_payment,
            acceptance_payment=acceptance_payment,
            warranty_payment=warranty_payment,
            payment_status=0
        )
        db.session.add(payment_record)

    # 创建或更新项目进度记录，同步合同开始日期
    try:
        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=id).first()

        # 如果不存在进度记录，则创建新记录
        if not progress:
            progress = ProjectProgress(
                project_id=project.id,
                project_start_date=contract_start_date  # 同步合同开始日期到项目开始时间
            )
            db.session.add(progress)
        else:
            # 如果存在进度记录，更新项目开始时间（如果提供了合同开始日期）
            if contract_start_date:
                progress.project_start_date = contract_start_date

        # 根据进度信息计算项目状态
        calculated_status = calculate_project_status(progress)
        # 更新项目状态
        project.project_status = calculated_status
    except Exception as e:
        print(f"更新项目状态和进度时出错: {str(e)}")
        # 出错时不影响其他更新操作，继续执行

    db.session.commit()
    return success_api(msg="更新成功")


#   用户分页查询
@bp.get('/data')
@authorize("system:import_project:info")
def data():
    # 获取请求参数
    project_name = str_escape(request.args.get('project_name', type=str))
    project_code = str_escape(request.args.get('project_code', type=str))
    dept_id = request.args.get('deptId', type=int)
    filters = []
    if project_name:
        filters.append(Import_project.project_name.contains(project_name))
    if project_code:
        filters.append(Import_project.project_code.contains(project_code))
    if dept_id:
        filters.append(Import_project.dept_id == dept_id)

    # 查询数据
    query = db.session.query(
        Import_project,
        ProjectManageDept
    ).filter(*filters).outerjoin(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id)

    # 分页处理
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('limit', 10, type=int)
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    items = pagination.items

    # 返回数据
    return table_api(
        data=[{
            'id': import_project.id,
            'project_name': import_project.project_name,
            'project_code': import_project.project_code,
            'project_status': import_project.project_status,
            'price': import_project.price,
            'currency': getattr(import_project, 'currency', '人民币'),
            'machine_number': import_project.machine_number,
            'delivery_date': import_project.delivery_date.strftime('%Y-%m-%d') if import_project.delivery_date else None,
            'contract_start_date': import_project.contract_start_date.strftime('%Y-%m-%d') if import_project.contract_start_date else None,
            'estimate_cost': import_project.estimate_cost,
            'bom_cost': import_project.bom_cost,
            'labor_cost': import_project.labor_cost,
            'other_cost': import_project.other_cost,
            'prepayment_days': getattr(import_project, 'prepayment_days', 0),
            'delivery_payment_days': getattr(import_project, 'delivery_payment_days', 0),
            'acceptance_payment_days': getattr(import_project, 'acceptance_payment_days', 0),
            'warranty_payment_days': getattr(import_project, 'warranty_payment_days', 0),
            'create_at': import_project.create_at.strftime('%Y-%m-%d %H:%M:%S') if import_project.create_at else None,
            'dept_id': import_project.dept_id,  # 返回 dept_id
            'dept_ids': import_project.dept_ids,  # 返回 dept_ids
            'dept_name': project_manage_dept.dept_name if project_manage_dept else None,
            'update_at': import_project.update_at.strftime('%Y-%m-%d %H:%M:%S') if import_project.update_at else None,
            'departments': [{
                'id': dept.id,
                'dept_name': dept.dept_name
            } for dept in import_project.departments]  # 将部门对象转换为字典
        } for import_project, project_manage_dept in items],
        count=pagination.total
    )


def get_dict_data(type_code):
    dict_type = DictType.query.filter_by(type_code=type_code).first()
    if dict_type:
        return DictData.query.filter_by(type_code=type_code).all()
    return []

# 修改get_dept_and_children()函数
def get_dept_and_children(dept_id):
    """
    获取当前部门及其所有下级部门的ID
    """
    dept_ids = [dept_id]
    children = Dept.query.filter_by(parent_id=dept_id).all()
    for child in children:
        dept_ids.extend(get_dept_and_children(child.id))
    return dept_ids

# 修改yugu()函数
@bp.get('/yugu')
@authorize("system:import_project:yugushuruimport", log=True)
@login_required
def yugu():
    # 获取所有部门 - 修复：按拼音排序
    all_depts = Dept.query.filter(Dept.status == 1).order_by(text("CONVERT(dept_name USING gbk)")).all()

    # 获取当前用户的部门ID
    user_dept_id = current_user.dept_id
    if not user_dept_id:
        return render_template('system/import_project/import_project_yugu.html',
                             dept_name="无部门",
                             estimates=[],
                             all_depts=all_depts)

    # 获取部门名称
    dept = Dept.query.get(user_dept_id)
    dept_name = dept.dept_name if dept else "未知部门"

    # 获取当前用户部门及其所有下级部门的ID
    dept_ids = get_dept_and_children(user_dept_id)

    # 分页查询预估工时数据
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('limit', 10, type=int)
    estimates = ProjectEstimate.query.filter(ProjectEstimate.dept_id.in_(dept_ids)) \
                                     .order_by(ProjectEstimate.created_at.desc()) \
                                     .paginate(page=page, per_page=per_page, error_out=False)

    # 获取与用户部门及其下级部门相关的项目数据
    projects = db.session.query(
        Import_project,
        ProjectManageDept
    ).filter(Import_project.dept_ids.like(f'%{user_dept_id}%')) \
     .outerjoin(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id) \
     .all()

    # 构建预估工时数据
    estimate_data = []
    for e in estimates.items:
        for project, project_dept in projects:
            if e.project_id == project.id:
                # 获取部门名称
                dept = Dept.query.get(e.dept_id)
                # 获取项目类型
                project_type = ProjectManageDept.query.get(e.project_type_id)
                estimate_data.append({
                    'project_code': project.project_code,
                    'project_name': project.project_name,
                    'user_dept_name': dept.dept_name if dept else '未知部门',
                    'project_type': project_type.dept_name if project_type else '未知类型',
                    'estimate_hours': e.estimate_hours,
                    'created_at': e.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
                break

    return render_template('system/import_project/import_project_yugu.html',
                         dept_name=dept_name,
                         estimates=estimate_data,
                         pagination=estimates,
                         all_depts=all_depts)

# 修改import_project_yuguinfo()函数
@bp.get('/import_project_yuguinfo')
@authorize("system:import_project:see", log=True)
@login_required
def import_project_yuguinfo():
    # 获取当前用户的部门ID
    user_dept_id = current_user.dept_id

    # 获取部门名称
    if user_dept_id:
        dept = Dept.query.get(user_dept_id)
        dept_name = dept.dept_name if dept else "未知部门"
    else:
        dept_name = "无部门"

    # 简化：只返回页面模板，数据通过API动态获取
    return render_template('system/import_project/import_project_yuguinfo.html',
                         dept_name=dept_name)

@bp.post('/update_dept_estimate')
@login_required
def update_dept_estimate():
    try:
        req_json = request.get_json()
        if not req_json:
            return jsonify({
                'code': 400,
                'msg': 'Invalid JSON data'
            }), 400

        project_id = req_json.get('id')
        estimate_cost = req_json.get('dept_estimate_cost')
        user_id = current_user.id

        if not project_id or not estimate_cost:
            return jsonify({
                'code': 400,
                'msg': 'Missing required fields'
            }), 400

        # 查找或创建用户预估记录
        user_estimate = UserEstimate.query.filter_by(
            user_id=user_id,
            project_id=project_id
        ).first()

        if user_estimate:
            user_estimate.estimate_cost = estimate_cost
        else:
            user_estimate = UserEstimate(
                user_id=user_id,
                project_id=project_id,
                estimate_cost=estimate_cost
            )
            db.session.add(user_estimate)

        db.session.commit()
        return jsonify({
            'code': 200,
            'msg': '更新成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}'
        }), 500

@bp.get('/get_user_estimates')
@login_required
def get_user_estimates():
    user_id = current_user.id
    estimates = UserEstimate.query.filter_by(user_id=user_id).all()
    estimate_data = {e.project_id: e.estimate_cost for e in estimates}
    return jsonify({
        'code': 200,
        'data': estimate_data
    })

@bp.get('/check_table_structure')
@authorize("system:import_project:import", log=True)
@login_required
def check_table_structure():
    try:
        # 获取表的所有列
        columns = db.session.execute(text("SHOW COLUMNS FROM import_project")).fetchall()
        column_names = [column[0] for column in columns]

        # 检查是否存在所需的列
        required_columns = ['bom_cost', 'labor_cost', 'other_cost', 'mechanical_bom_cost', 'electrical_bom_cost']
        missing_columns = [col for col in required_columns if col not in column_names]

        if missing_columns:
            return jsonify({
                'code': 400,
                'msg': f'数据库表缺少以下列：{", ".join(missing_columns)}',
                'data': {
                    'columns': column_names,
                    'missing_columns': missing_columns
                }
            })
        else:
            return jsonify({
                'code': 200,
                'msg': '数据库表结构正确',
                'data': {
                    'columns': column_names
                }
            })
    except Exception as e:
        return jsonify({'code': 500, 'msg': f'检查表结构失败: {str(e)}'})

@bp.get('/add_missing_columns')
@authorize("system:import_project:import", log=True)
@login_required
def add_missing_columns():
    try:
        # 获取表的所有列
        columns = db.session.execute(text("SHOW COLUMNS FROM import_project")).fetchall()
        column_names = [column[0] for column in columns]

        # 检查是否存在所需的列
        required_columns = {
            'bom_cost': 'FLOAT',
            'labor_cost': 'FLOAT',
            'other_cost': 'FLOAT',
            'mechanical_bom_cost': 'FLOAT',
            'electrical_bom_cost': 'FLOAT',
            'currency': 'VARCHAR(50) COMMENT "币种"'
        }

        missing_columns = {col: type_ for col, type_ in required_columns.items() if col not in column_names}

        if missing_columns:
            for col, type_ in missing_columns.items():
                db.session.execute(text(f"ALTER TABLE import_project ADD COLUMN {col} {type_}"))

            db.session.commit()

            return jsonify({
                'code': 200,
                'msg': f'成功添加缺失的列：{", ".join(missing_columns.keys())}',
                'data': {
                    'added_columns': list(missing_columns.keys())
                }
            })
        else:
            return jsonify({
                'code': 200,
                'msg': '数据库表结构已经正确，无需添加列',
                'data': {
                    'columns': column_names
                }
            })
    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'msg': f'添加列失败: {str(e)}'})

@bp.post('/yugu/import')
@authorize("system:import_project:yuguimport", log=True)
@login_required
def import_yugu_data():
    try:
        # 添加日志记录，方便调试
        print("开始导入预估工时数据...")

        # 检查数据库表结构
        try:
            columns = db.session.execute(text("SHOW COLUMNS FROM import_project")).fetchall()
            column_names = [column[0] for column in columns]

            has_bom_cost = 'bom_cost' in column_names
            has_labor_cost = 'labor_cost' in column_names
            has_other_cost = 'other_cost' in column_names
            has_mechanical_bom_cost = 'mechanical_bom_cost' in column_names
            has_electrical_bom_cost = 'electrical_bom_cost' in column_names

            if not (has_bom_cost and has_labor_cost and has_other_cost and has_mechanical_bom_cost and has_electrical_bom_cost):
                missing_columns = []
                if not has_bom_cost:
                    missing_columns.append('bom_cost')
                if not has_labor_cost:
                    missing_columns.append('labor_cost')
                if not has_other_cost:
                    missing_columns.append('other_cost')
                if not has_mechanical_bom_cost:
                    missing_columns.append('mechanical_bom_cost')
                if not has_electrical_bom_cost:
                    missing_columns.append('electrical_bom_cost')

                return jsonify({
                    'code': 400,
                    'msg': f'数据库表缺少必要的列：{", ".join(missing_columns)}，请先访问 /import_project/add_missing_columns 添加缺失的列',
                    'data': {
                        'missing_columns': missing_columns
                    }
                })
        except Exception as e:
            return jsonify({'code': 500, 'msg': f'检查表结构失败: {str(e)}'})

        # 确保文件存在
        if 'file' not in request.files:
            return jsonify({'code': 400, 'msg': '未选择文件'})

        file = request.files['file']

        # 检查文件是否为空
        if file.filename == '':
            return jsonify({'code': 400, 'msg': '未选择文件'})

        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'code': 400, 'msg': '仅支持Excel文件'})

        # 读取Excel文件
        df = pd.read_excel(BytesIO(file.read()))

        # 检查必要列是否存在
        required_columns = ['部门', '项目类型', '项目编号', '工作地点', '预估工时']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'code': 400, 'msg': '文件格式不正确，缺少必要列'})

        # 初始化统计信息
        total_count = len(df)
        success_count = 0  # 新增记录数
        update_count = 0   # 覆盖更新记录数
        duplicate_count = 0  # 跳过记录数（数据无变动）
        error_count = 0
        duplicate_messages = []
        error_messages = []
        affected_projects = set()  # 用于记录受影响的项目ID

        # 处理数据并存入数据库
        for _, row in df.iterrows():
            try:
                # 查找部门
                dept = Dept.query.filter_by(dept_name=row['部门']).first()
                if not dept:
                    error_messages.append(f"部门 {row['部门']} 不存在")
                    error_count += 1
                    continue

                # 检查售后部门不能导入厂内工作地点
                if dept.dept_name == '售后部' and row['工作地点'] == '厂内':
                    error_messages.append(f"售后部不能导入厂内工作地点，项目编号: {row['项目编号']}")
                    error_count += 1
                    continue

                # 查找项目类型
                project_type = ProjectManageDept.query.filter_by(dept_name=row['项目类型']).first()
                if not project_type:
                    error_messages.append(f"项目类型 {row['项目类型']} 不存在")
                    error_count += 1
                    continue

                # 检查项目是否存在，同时检查项目编号和 dept_id
                project = Import_project.query.filter_by(
                    project_code=row['项目编号'],
                    dept_id=project_type.id
                ).first()

                if not project:
                    error_messages.append(f"项目编号 {row['项目编号']} 在项目类型 {row['项目类型']} 中不存在。请先使用项目导入功能导入该项目，然后再导入预估工时。")
                    error_count += 1
                    continue



                # 提取成本数据（对应Excel的F、G、H、I列）
                labor_cost = float(row.get('人工成本', 0)) if pd.notna(row.get('人工成本', 0)) else 0
                mechanical_bom_cost = 0
                electrical_bom_cost = 0
                other_cost = float(row.get('其他成本', 0)) if pd.notna(row.get('其他成本', 0)) else 0

                # 处理机械BOM成本
                if '机械BOM成本' in row and pd.notna(row['机械BOM成本']):
                    try:
                        mechanical_bom_cost = float(row['机械BOM成本'])
                    except (ValueError, TypeError) as e:
                        error_messages.append(f"机械BOM成本转换错误，项目编号: {row['项目编号']} - {str(e)}")
                        mechanical_bom_cost = 0

                # 处理电气BOM成本
                if '电气BOM成本' in row and pd.notna(row['电气BOM成本']):
                    try:
                        electrical_bom_cost = float(row['电气BOM成本'])
                    except (ValueError, TypeError) as e:
                        error_messages.append(f"电气BOM成本转换错误，项目编号: {row['项目编号']} - {str(e)}")
                        electrical_bom_cost = 0

                # 检查是否已经存在相同记录
                existing_estimate = ProjectEstimate.query.filter_by(
                    project_id=project.id,
                    dept_id=dept.id,
                    project_type_id=project_type.id,
                    location=row['工作地点']
                ).first()

                new_estimate_hours = float(row['预估工时'])

                if existing_estimate:
                    # 检查数据是否有变动
                    has_changes = (
                        abs(existing_estimate.estimate_hours - new_estimate_hours) > 0.01 or  # 工时变动
                        abs(existing_estimate.labor_cost - labor_cost) > 0.01 or  # 人工成本变动
                        abs(existing_estimate.mechanical_bom_cost - mechanical_bom_cost) > 0.01 or  # 机械BOM成本变动
                        abs(existing_estimate.electrical_bom_cost - electrical_bom_cost) > 0.01 or  # 电气BOM成本变动
                        abs(existing_estimate.other_cost - other_cost) > 0.01  # 其他成本变动
                    )

                    if has_changes:
                        # 数据有变动，允许覆盖更新
                        existing_estimate.estimate_hours = new_estimate_hours
                        existing_estimate.labor_cost = labor_cost
                        existing_estimate.mechanical_bom_cost = mechanical_bom_cost
                        existing_estimate.electrical_bom_cost = electrical_bom_cost
                        existing_estimate.other_cost = other_cost
                        existing_estimate.updated_at = datetime.datetime.now()

                        duplicate_messages.append(f"项目编号 {row['项目编号']} 在部门 {row['部门']} 的工作地点 {row['工作地点']} 数据有变动，已覆盖更新")
                        update_count += 1  # 算作覆盖更新
                    else:
                        # 数据无变动，不允许覆盖
                        duplicate_messages.append(f"项目编号 {row['项目编号']} 在部门 {row['部门']} 的工作地点 {row['工作地点']} 数据无变动，跳过更新")
                        duplicate_count += 1
                else:
                    # 创建新记录
                    estimate = ProjectEstimate(
                        project_id=project.id,
                        dept_id=dept.id,
                        project_type_id=project_type.id,
                        location=row['工作地点'],
                        estimate_hours=new_estimate_hours,
                        labor_cost=labor_cost,
                        mechanical_bom_cost=mechanical_bom_cost,
                        electrical_bom_cost=electrical_bom_cost,
                        other_cost=other_cost
                    )
                    db.session.add(estimate)
                    success_count += 1

                # 记录日志，方便调试
                print(f"部门 {row['部门']} 项目编号: {row['项目编号']}, 人工成本: {labor_cost}, 机械BOM成本: {mechanical_bom_cost}, 电气BOM成本: {electrical_bom_cost}, 其他成本: {other_cost}")

                # 不再直接更新 Import_project 表的成本字段

                # 确保更改被保存到数据库
                try:
                    db.session.flush()
                    print(f"成功处理项目 {row['项目编号']} 的预估数据")
                except Exception as e:
                    print(f"处理项目 {row['项目编号']} 失败: {str(e)}")
                    error_messages.append(f"处理项目 {row['项目编号']} 失败: {str(e)}")
                    error_count += 1
                    continue

            except Exception as e:
                error_messages.append(f"处理项目 {row.get('项目编号', '未知')} 时发生错误: {str(e)}")
                error_count += 1
                continue

        # 提交所有 ProjectEstimate 记录
        try:
            db.session.commit()
            print(f"成功提交事务，导入了 {success_count} 条数据")
        except Exception as e:
            db.session.rollback()
            print(f"提交事务失败: {str(e)}")
            return jsonify({'code': 500, 'msg': f'导入失败: {str(e)}'})

        # 收集所有涉及的项目，进行成本汇总
        affected_projects = set()
        for _, row in df.iterrows():
            try:
                # 查找项目类型
                project_type = ProjectManageDept.query.filter_by(dept_name=row['项目类型']).first()
                if project_type:
                    # 查找项目
                    project = Import_project.query.filter_by(
                        project_code=row['项目编号'],
                        dept_id=project_type.id
                    ).first()
                    if project:
                        affected_projects.add(project.id)
            except Exception as e:
                print(f"收集项目ID时出错: {str(e)}")

        # 批量更新项目总预估成本
        updated_projects_count = 0
        if affected_projects:
            from applications.services.project_estimate_service import update_project_total_estimates
            print(f"开始汇总 {len(affected_projects)} 个项目的预估成本...")

            for project_id in affected_projects:
                try:
                    success, message = update_project_total_estimates(project_id)
                    if success:
                        print(f"项目 {project_id} 汇总成功: {message}")
                        updated_projects_count += 1
                    else:
                        print(f"项目 {project_id} 汇总失败: {message}")
                except Exception as e:
                    print(f"项目 {project_id} 汇总异常: {str(e)}")

            print("项目预估成本汇总完成")

        # 添加调试信息
        print(f"统计结果 - 总计: {total_count}, 新增: {success_count}, 更新: {update_count}, 跳过: {duplicate_count}, 错误: {error_count}")

        # 验证统计逻辑
        processed_count = success_count + update_count + duplicate_count + error_count
        if processed_count != total_count:
            print(f"警告：统计不一致！处理总数({processed_count}) != 实际总数({total_count})")

        # 构建返回信息
        message = f"智能导入完成。总计处理 {total_count} 条数据：\n"
        message += f"新增记录 {success_count} 条\n"
        if update_count > 0:
            message += f"覆盖更新 {update_count} 条（数据有变动）\n"
        if duplicate_count > 0:
            message += f"跳过记录 {duplicate_count} 条（数据无变动）\n"
        if error_count > 0:
            message += f"错误数据 {error_count} 条\n"

        # 添加预估成本汇总信息
        if updated_projects_count > 0:
            message += f"\n\n✅ 已自动更新 {updated_projects_count} 个项目的预估成本到项目数据库中"

        if duplicate_messages:
            message += "\n重复数据详情：\n" + "\n".join([f"• {msg}" for msg in duplicate_messages])
        if error_messages:
            message += "\n错误详情：\n" + "\n".join([f"• {msg}" for msg in error_messages])

        return jsonify({
            'code': 200,
            'msg': message,
            'data': {
                'total': total_count,
                'success': success_count,
                'update': update_count,
                'duplicate': duplicate_count,
                'error': error_count,
                'updated_projects': updated_projects_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'msg': f'导入失败: {str(e)}'})

@bp.get('/yugu/data')
@authorize("system:import_project:yugushuruimport", log=True)
@login_required
def yugu_data():
    # 获取查询参数
    dept_id = request.args.get('dept_id', type=int)
    project_type = request.args.get('project_type', type=str)
    project_code = request.args.get('project_code', type=str)

    # 检查当前用户是否有人事角色
    is_hr = any(role.code == 'hr' for role in current_user.role)

    # 获取当前用户的部门ID
    user_dept_id = current_user.dept_id

    # 如果用户是人事角色，则获取所有部门数据
    if is_hr:
        dept_ids = [dept.id for dept in Dept.query.all()]
    else:
        if not user_dept_id:
            return jsonify({'code': 200, 'msg': '无部门', 'data': [], 'count': 0})
        # 获取当前用户部门及其所有下级部门的ID
        dept_ids = get_dept_and_children(user_dept_id)

    # 如果有部门筛选条件，则覆盖之前的部门ID
    if dept_id:
        dept_ids = [dept_id]

    # 构建查询条件 - 修复：使用JOIN查询
    query = db.session.query(
        ProjectEstimate,
        Import_project,
        Dept,
        ProjectManageDept
    ).join(Import_project, ProjectEstimate.project_id == Import_project.id) \
     .join(Dept, ProjectEstimate.dept_id == Dept.id) \
     .join(ProjectManageDept, ProjectEstimate.project_type_id == ProjectManageDept.id)

    # 应用筛选条件
    filters = [ProjectEstimate.dept_id.in_(dept_ids)]
    if project_type:
        filters.append(ProjectManageDept.dept_name.contains(project_type))
    if project_code:
        filters.append(Import_project.project_code.contains(project_code))

    query = query.filter(*filters)

    # 分页查询预估工时数据
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('limit', 10, type=int)
    estimates = query.order_by(ProjectEstimate.created_at.desc()) \
                    .paginate(page=page, per_page=per_page, error_out=False)

    # 构建预估工时数据 - 显示部门级的成本数据
    estimate_data = []
    for estimate, project, dept, project_type in estimates.items:
        estimate_data.append({
            'project_code': project.project_code,
            'project_name': project.project_name,
            'user_dept_name': dept.dept_name,
            'location': estimate.location,
            'project_type': project_type.dept_name,
            'estimate_hours': estimate.estimate_hours,
            'labor_cost': estimate.labor_cost or 0,  # 显示部门级的人工成本
            'mechanical_bom_cost': estimate.mechanical_bom_cost or 0,  # 显示部门级的机械BOM成本
            'electrical_bom_cost': estimate.electrical_bom_cost or 0,  # 显示部门级的电气BOM成本
            'other_cost': estimate.other_cost or 0,  # 显示部门级的其他成本
            'created_at': estimate.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify({
        'code': 200,
        'msg': '成功',
        'data': estimate_data,
        'count': estimates.total
    })

@bp.get('/import_project_yuguinfo/data')
@authorize("system:import_project:see", log=True)
@login_required
def yuguinfo_data():
    # 获取查询参数
    dept_id = request.args.get('dept_id', type=int)
    project_code = request.args.get('project_code', type=str)
    dept_name = request.args.get('dept_name', type=str)
    location = request.args.get('location', type=str)

    # 获取当前用户的部门ID
    user_dept_id = current_user.dept_id
    if not user_dept_id:
        return jsonify({'code': 200, 'msg': '无部门', 'data': [], 'count': 0})

    # 获取当前用户部门及其所有下级部门的ID
    dept_ids = get_dept_and_children(user_dept_id)

    # 如果有部门筛选条件，则覆盖之前的部门ID
    if dept_id:
        dept_ids = [dept_id]

    # 构建查询条件 - 使用JOIN查询
    query = db.session.query(
        ProjectEstimate,
        Import_project,
        Dept,
        ProjectManageDept
    ).join(Import_project, ProjectEstimate.project_id == Import_project.id) \
     .join(Dept, ProjectEstimate.dept_id == Dept.id) \
     .join(ProjectManageDept, ProjectEstimate.project_type_id == ProjectManageDept.id)

    # 应用筛选条件
    filters = [ProjectEstimate.dept_id.in_(dept_ids)]
    if project_code:
        filters.append(Import_project.project_code.contains(project_code))
    if dept_name:
        filters.append(Dept.dept_name.contains(dept_name))
    if location:
        filters.append(ProjectEstimate.location.contains(location))

    query = query.filter(*filters)

    # 分页查询预估工时数据
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('limit', 10, type=int)
    estimates = query.order_by(ProjectEstimate.created_at.desc()) \
                    .paginate(page=page, per_page=per_page, error_out=False)

    # 构建预估工时数据 - 显示部门级的详细成本数据
    estimate_data = []
    for estimate, project, dept, project_type in estimates.items:
        estimate_data.append({
            'project_code': project.project_code,
            'project_name': project.project_name,
            'dept_name': dept.dept_name,
            'project_type': project_type.dept_name,
            'location': estimate.location,
            'estimate_hours': estimate.estimate_hours,
            'labor_cost': estimate.labor_cost or 0,  # 显示部门级的人工成本
            'mechanical_bom_cost': estimate.mechanical_bom_cost or 0,  # 显示部门级的机械BOM成本
            'electrical_bom_cost': estimate.electrical_bom_cost or 0,  # 显示部门级的电气BOM成本
            'other_cost': estimate.other_cost or 0,  # 显示部门级的其他成本
            'created_at': estimate.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify({
        'code': 200,
        'msg': '成功',
        'data': estimate_data,
        'count': estimates.total
    })

@bp.get('/yugu/import_page')
@authorize("system:import_project:yuguimport", log=True)
@login_required
def yugu_import_page():
    return render_template('system/import_project/import_project_yuguimport.html')


# @bp.route('/upgrade_project_estimate_db')
# @authorize("system:import_project:admin", log=True)
# @login_required
# def upgrade_project_estimate_db():
#     """升级项目预估数据库结构"""
#     try:
#         from applications.services.project_estimate_migration import upgrade_project_estimate_table, check_project_estimate_structure

#         # 检查当前结构
#         success, message = check_project_estimate_structure()
#         if success:
#             return jsonify({
#                 'code': 200,
#                 'msg': f'数据库结构已是最新版本: {message}',
#                 'data': {'need_upgrade': False}
#             })

#         # 执行升级
#         success, message = upgrade_project_estimate_table()
#         if success:
#             return jsonify({
#                 'code': 200,
#                 'msg': f'数据库升级成功: {message}',
#                 'data': {'need_upgrade': False}
#             })
#         else:
#             return jsonify({
#                 'code': 500,
#                 'msg': f'数据库升级失败: {message}',
#                 'data': {'need_upgrade': True}
#             })

#     except Exception as e:
#         return jsonify({
#             'code': 500,
#             'msg': f'升级过程出错: {str(e)}',
#             'data': {'need_upgrade': True}
#         })

@bp.get('/import_page')
@authorize("system:import_project:import", log=True)
@login_required
def import_page():
    return render_template('system/import_project/import_project_import.html')

@bp.post('/import_project_data')
@authorize("system:import_project:import", log=True)
@login_required
def import_project_data():
    try:
        # 确保文件存在
        if 'file' not in request.files:
            return jsonify({'code': 400, 'msg': '未选择文件'})

        file = request.files['file']

        # 检查文件是否为空
        if file.filename == '':
            return jsonify({'code': 400, 'msg': '未选择文件'})

        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'code': 400, 'msg': '仅支持Excel文件'})

        # 读取Excel文件
        df = pd.read_excel(BytesIO(file.read()))

        # 检查必要列是否存在
        required_columns = ['项目名称', '项目编码', '项目类型', '项目状态', '项目价格', '币种', '机器数量', '计划交货时间']
        # 合同开始日期是可选列，不强制要求
        if not all(col in df.columns for col in required_columns):
            return jsonify({'code': 400, 'msg': '文件格式不正确，缺少必要列'})

        # 检查付款相关列是否存在（保持向后兼容）
        payment_columns = ['预付款', '发货款', '验收款', '质保金']
        has_payment_columns = all(col in df.columns for col in payment_columns)

        # 检查付款比例相关列是否存在（新的推荐方式）
        payment_percent_columns = ['预付款比例', '发货款比例', '验收款比例', '质保金比例']
        has_payment_percent_columns = all(col in df.columns for col in payment_percent_columns)

        # 检查付款天数相关列是否存在
        payment_days_columns = ['预付款天数', '发货款天数', '验收款天数', '质保金天数']
        has_payment_days_columns = all(col in df.columns for col in payment_days_columns)

        # 获取状态选项
        status_options = {option.data_label: option.data_value for option in get_dict_data('project_status')}

        # 初始化统计信息
        total_count = len(df)
        success_count = 0
        duplicate_count = 0
        error_count = 0
        duplicate_messages = []
        error_messages = []

        # 处理数据并存入数据库
        for _, row in df.iterrows():
            try:
                # 基本信息获取
                project_name = str(row['项目名称'])
                project_code = str(row['项目编码'])

                # 检查项目名称和编码是否存在
                if not project_name or not project_code:
                    error_messages.append(f"项目名称或编码不能为空: {project_name} - {project_code}")
                    error_count += 1
                    continue

                # 检查项目是否已存在
                if Import_project.query.filter_by(project_name=project_name).count():
                    duplicate_messages.append(f"项目名称已存在: {project_name}")
                    duplicate_count += 1
                    continue

                # 查找项目类型
                dept_name = str(row['项目类型'])
                project_type = ProjectManageDept.query.filter_by(dept_name=dept_name).first()
                if not project_type:
                    error_messages.append(f"项目类型不存在: {dept_name}")
                    error_count += 1
                    continue

                # 处理项目状态
                status_label = str(row['项目状态'])
                if status_label not in status_options:
                    error_messages.append(f"项目状态不存在: {status_label}")
                    error_count += 1
                    continue
                project_status = status_options[status_label]

                # 获取数值字段
                try:
                    price = float(row['项目价格'])
                    currency = str(row['币种'])
                    machine_number = int(row['机器数量'])
                    # 设置成本字段的默认值
                    estimate_cost = 0
                    bom_cost = 0
                    labor_cost = 0
                    other_cost = 0
                except (ValueError, TypeError) as e:
                    error_messages.append(f"数值字段转换错误，项目: {project_name} - {str(e)}")
                    error_count += 1
                    continue

                # 处理计划交货时间
                delivery_date = None
                if '计划交货时间' in row and pd.notna(row['计划交货时间']):
                    try:
                        delivery_date = pd.to_datetime(row['计划交货时间']).date()
                    except:
                        error_messages.append(f"项目 {project_name} 的计划交货时间格式不正确")
                        error_count += 1
                        continue

                # 处理合同开始日期
                contract_start_date = None
                if '合同开始日期' in row and pd.notna(row['合同开始日期']):
                    try:
                        contract_start_date = pd.to_datetime(row['合同开始日期']).date()
                    except:
                        error_messages.append(f"项目 {project_name} 的合同开始日期格式不正确")
                        error_count += 1
                        continue

                # 创建项目对象
                project_data = {
                    'project_name': project_name,
                    'project_code': project_code,
                    'dept_id': project_type.id,
                    'project_status': project_status,
                    'price': price,
                    'machine_number': machine_number,
                    'delivery_date': delivery_date,
                    'contract_start_date': contract_start_date,
                    'estimate_cost': estimate_cost,
                    'bom_cost': bom_cost,
                    'labor_cost': labor_cost,
                    'other_cost': other_cost
                }

                # 如果存在付款天数相关列，添加到项目数据中
                if has_payment_days_columns:
                    try:
                        prepayment_days = int(row['预付款天数']) if '预付款天数' in row and pd.notna(row['预付款天数']) else 0
                        delivery_payment_days = int(row['发货款天数']) if '发货款天数' in row and pd.notna(row['发货款天数']) else 0
                        acceptance_payment_days = int(row['验收款天数']) if '验收款天数' in row and pd.notna(row['验收款天数']) else 0
                        warranty_payment_days = int(row['质保金天数']) if '质保金天数' in row and pd.notna(row['质保金天数']) else 0

                        project_data['prepayment_days'] = prepayment_days
                        project_data['delivery_payment_days'] = delivery_payment_days
                        project_data['acceptance_payment_days'] = acceptance_payment_days
                        project_data['warranty_payment_days'] = warranty_payment_days
                    except (ValueError, TypeError) as e:
                        error_messages.append(f"付款天数字段转换错误，项目: {project_name} - {str(e)}")
                        # 不中断导入，使用默认值0

                # 尝试添加币种字段，如果数据库表中有这个字段
                try:
                    if hasattr(Import_project, 'currency'):
                        project_data['currency'] = currency
                except:
                    pass  # 如果数据库表中没有这个字段，忽略错误

                project = Import_project(**project_data)

                # 添加到数据库
                db.session.add(project)

                # 如果存在付款相关列或比例列，创建付款记录
                if has_payment_columns or has_payment_percent_columns:
                    try:
                        # 导入项目模型以获取项目ID
                        db.session.flush()

                        # 创建付款记录
                        from applications.models.project_payment import ProjectPayment

                        # 优先使用比例字段计算金额，如果没有比例字段则使用金额字段
                        if has_payment_percent_columns:
                            # 使用比例计算金额
                            prepayment_percent = float(row['预付款比例']) if '预付款比例' in row and pd.notna(row['预付款比例']) else 0
                            delivery_payment_percent = float(row['发货款比例']) if '发货款比例' in row and pd.notna(row['发货款比例']) else 0
                            acceptance_payment_percent = float(row['验收款比例']) if '验收款比例' in row and pd.notna(row['验收款比例']) else 0
                            warranty_payment_percent = float(row['质保金比例']) if '质保金比例' in row and pd.notna(row['质保金比例']) else 0

                            # 根据项目价格和比例计算实际金额
                            prepayment_amount = price * prepayment_percent / 100
                            delivery_payment_amount = price * delivery_payment_percent / 100
                            acceptance_payment_amount = price * acceptance_payment_percent / 100
                            warranty_payment_amount = price * warranty_payment_percent / 100
                        else:
                            # 使用直接金额字段
                            prepayment_amount = float(row['预付款']) if '预付款' in row and pd.notna(row['预付款']) else 0
                            delivery_payment_amount = float(row['发货款']) if '发货款' in row and pd.notna(row['发货款']) else 0
                            acceptance_payment_amount = float(row['验收款']) if '验收款' in row and pd.notna(row['验收款']) else 0
                            warranty_payment_amount = float(row['质保金']) if '质保金' in row and pd.notna(row['质保金']) else 0

                        # 创建单条付款记录，包含所有付款类型的金额
                        if prepayment_amount > 0 or delivery_payment_amount > 0 or acceptance_payment_amount > 0 or warranty_payment_amount > 0:
                            payment_record = ProjectPayment(
                                project_id=project.id,
                                prepayment=prepayment_amount,
                                delivery_payment=delivery_payment_amount,
                                acceptance_payment=acceptance_payment_amount,
                                warranty_payment=warranty_payment_amount,
                                payment_status=0  # 默认未付款
                            )
                            db.session.add(payment_record)
                    except Exception as e:
                        error_messages.append(f"创建项目 {project_name} 的付款记录时发生错误: {str(e)}")

                # 创建或更新项目进度记录，同步合同开始日期
                try:
                    # 获取项目进度详情
                    progress = ProjectProgress.query.filter_by(project_id=project.id).first()

                    # 如果不存在进度记录，则创建新记录
                    if not progress:
                        progress = ProjectProgress(
                            project_id=project.id,
                            project_start_date=contract_start_date  # 同步合同开始日期到项目开始时间
                        )
                        db.session.add(progress)
                    else:
                        # 如果存在进度记录，更新项目开始时间（如果提供了合同开始日期）
                        if contract_start_date:
                            progress.project_start_date = contract_start_date

                    # 根据进度信息计算项目状态
                    calculated_status = calculate_project_status(progress)
                    # 更新项目状态
                    project.project_status = calculated_status
                except Exception as e:
                    print(f"导入项目时设置项目状态和进度出错: {str(e)}")
                    # 出错时不影响其他操作，继续执行

                success_count += 1

            except Exception as e:
                error_messages.append(f"处理项目 {row.get('项目名称', '未知')} 时发生错误: {str(e)}")
                error_count += 1
                continue

        db.session.commit()

        # 构建返回信息
        message = f"导入完成。总计处理 {total_count} 条数据：\n"
        message += f"成功导入 {success_count} 条\n"
        if duplicate_count > 0:
            message += f"跳过重复数据 {duplicate_count} 条\n"
        if error_count > 0:
            message += f"错误数据 {error_count} 条\n"

        if duplicate_messages:
            message += "\n重复数据详情：\n" + "\n".join([f"• {msg}" for msg in duplicate_messages])
        if error_messages:
            message += "\n错误详情：\n" + "\n".join([f"• {msg}" for msg in error_messages])

        return jsonify({
            'code': 200,
            'msg': message,
            'data': {
                'total': total_count,
                'success': success_count,
                'duplicate': duplicate_count,
                'error': error_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'msg': f'导入失败: {str(e)}'})

@bp.get('/download_template')
@authorize("system:import_project:import", log=True)
@login_required
def download_template():
    try:
        # 获取状态选项，用于创建下拉选项
        status_options = [option.data_label for option in get_dict_data('project_status')]
        # 获取项目类型选项
        project_types = [dept.dept_name for dept in ProjectManageDept.query.all()]

        # 获取币种选项
        currency_options = [option.data_label for option in get_dict_data('currency_type')]

        # 创建示例数据
        data = {
            '项目名称': ['示例项目1', '示例项目2'],
            '项目编码': ['2501', '2502'],
            '项目类型': [project_types[1] if project_types else '未知类型', project_types[2] if len(project_types) > 1 else '未知类型'],
            '项目状态': [status_options[0] if status_options else '未知状态', status_options[1] if len(status_options) > 1 else '未知状态'],
            '项目价格': [10000, 20000],
            '币种': [currency_options[0] if currency_options else '人民币', currency_options[0] if currency_options else '人民币'],
            '机器数量': [1, 2],
            '计划交货时间': ['2025-07-05', '2025-08-10'],
            '合同开始日期': ['2025-01-01', '2025-02-01'],
            '预付款比例': [40, 40],  # 预付款比例(%)
            '发货款比例': [20, 20],  # 发货款比例(%)
            '验收款比例': [20, 20],  # 验收款比例(%)
            '质保金比例': [20, 20],  # 质保金比例(%)
            '预付款天数': [0, 30],  # 预付款天数
            '发货款天数': [30, 60],  # 发货款天数
            '验收款天数': [60, 90],  # 验收款天数
            '质保金天数': [365, 365]  # 质保金天数
        }

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建一个BytesIO对象
        output = BytesIO()

        # 写入Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='项目导入模板')

            # 获取worksheet
            workbook = writer.book
            worksheet = writer.sheets['项目导入模板']

            # 设置列宽
            for i, col in enumerate(df.columns):
                column_width = max(len(col) * 2, df[col].astype(str).map(len).max() * 2)
                worksheet.column_dimensions[chr(65 + i)].width = column_width

        # 设置文件指针到开始
        output.seek(0)

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name='项目导入模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return jsonify({'code': 500, 'msg': f'模板创建失败: {str(e)}'})


@bp.get('/yugu/download_template')
@authorize("system:import_project:yuguimport", log=True)
@login_required
def yugu_download_template():
    try:
        # 获取部门列表 - 修复：按拼音排序
        departments = [dept.dept_name for dept in Dept.query.filter_by(status=1).order_by(text("CONVERT(dept_name USING gbk)")).all()]

        # 获取项目类型列表
        project_types = [dept.dept_name for dept in ProjectManageDept.query.filter_by(status=1).all()]

        # 创建示例数据
        data = {
            '部门': [departments[0] if departments else '未知部门', departments[1] if len(departments) > 1 else '未知部门'],
            '项目类型': [project_types[0] if project_types else '未知类型', project_types[1] if len(project_types) > 1 else '未知类型'],
            '项目编号': ['2501', '2507'],
            '工作地点': ['厂内', '厂外'],
            '预估工时': [500, 600],
            '人工成本': [5000, 5000],
            '机械BOM成本': [2000, 5000],
            '电气BOM成本': [30000, 10000],
            '其他成本': [50000, 52000]
        }

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建一个BytesIO对象
        output = BytesIO()

        # 写入Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='预估工时导入模板')

            # 获取worksheet
            workbook = writer.book
            worksheet = writer.sheets['预估工时导入模板']

            # 设置列宽
            for i, col in enumerate(df.columns):
                column_width = max(len(col) * 2, df[col].astype(str).map(len).max() * 2)
                worksheet.column_dimensions[chr(65 + i)].width = column_width

        # 设置文件指针到开始
        output.seek(0)

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name='预估工时导入模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return jsonify({'code': 500, 'msg': f'模板创建失败: {str(e)}'})









































