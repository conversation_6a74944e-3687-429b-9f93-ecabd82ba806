<!DOCTYPE html>
<html>
<head>
    <title>项目成本汇总</title>
    {% include 'system/common/header.html' %}
    <style>
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }

        .layui-card {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .layui-form-label {
            width: 100px;
            font-weight: 500;
            color: #333;
        }

        .layui-input-block {
            margin-left: 130px;
        }

        .layui-input, .layui-select, .layui-textarea {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-input:focus, .layui-select:focus, .layui-textarea:focus {
            border-color: #1E9FFF;
            box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
        }

        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .layui-btn-normal {
            background: linear-gradient(135deg, #1E9FFF 0%, #1890ff 100%);
            border: none;
        }

        .layui-btn-normal:hover {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .layui-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .layui-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
        }
        .cost-red {
            color: #FF5722;
            font-weight: bold;
        }
        .cost-yellow {
            color: #FFB800;
            font-weight: bold;
        }
        .cost-orange {
            color: #FF9800;
            font-weight: bold;
        }
        .cost-green {
            color: #009688;
            font-weight: bold;
        }
        .bg-cost-red {
            background-color: #FF5722;
            color: #fff;
        }
        .bg-cost-yellow {
            background-color: #FFB800;
            color: #fff;
        }
        .bg-cost-orange {
            background-color: #FF9800;
            color: #fff;
        }
        .bg-cost-green {
            background-color: #009688;
            color: #fff;
        }
        /* 预估和实际值的样式 */
        .estimate-value {
            color: #1E9FFF;
            font-weight: normal;
        }
        .actual-value {
            color: #FF5722;
            font-weight: bold;
        }
        /* 表格标题注释样式 */
        .layui-table th {
            line-height: 1.2;
        }
        .layui-table th small {
            display: block;
            font-size: 11px;
            color: #999 !important;
            font-weight: normal !important;
            margin-top: 2px;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">筛选条件</div>
                <div class="layui-card-body">
                    <form class="layui-form" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">项目状态</label>
                                <div class="layui-input-inline">
                                    <select name="status">
                                        <option value="">全部状态</option>
                                        {% for status in statuses %}
                                        <option value="{{ status.data_value }}">{{ status.data_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">项目类型</label>
                                <div class="layui-input-inline">
                                    <select name="dept_id">
                                        <option value="">全部类型</option>
                                        {% for dept in project_departments %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">关键词</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="keyword" placeholder="项目名称/编号" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" lay-submit lay-filter="cost-query">
                                    <i class="layui-icon layui-icon-search"></i> 查询
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    项目成本汇总
                    <div class="layui-btn-group" style="float: right;">
                        {% if authorize('system:project_cost:export') %}
                        <button class="layui-btn layui-btn-sm layui-btn-normal" id="exportExcel">
                            <i class="layui-icon layui-icon-export"></i> 导出Excel
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="layui-card-body">
                    <table id="project-table" lay-filter="project-table"></table>
                </div>
            </div>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<!-- 表格操作 -->
<script type="text/html" id="project-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>
        </button>
    </div>
</script>

<!-- 行操作 -->
<script type="text/html" id="project-bar">
    <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">
        <i class="layui-icon layui-icon-form"></i>查看详情
    </button>
</script>

<script>
    layui.use(['table', 'form', 'jquery'], function() {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;

        // 渲染表格
        table.render({
            elem: '#project-table',
            url: '/system/project_cost/api/project_summary',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                // {type: 'numbers', title: '序号', width: 60, align: 'center'},
                {field: 'project_type_name', title: '项目类型', align: 'center', minWidth: 120},
                {field: 'project_code', title: '项目编号', align: 'center', minWidth: 120},
                {field: 'project_name', title: '项目名称', align: 'center', minWidth: 150},
                {field: 'status_name', title: '项目状态', align: 'center', minWidth: 120},
                // {field: 'price', title: '项目价格', align: 'center', templet: function(d) {
                //     return '¥' + (d.price ? d.price.toLocaleString() : '0');
                // }},
                // {field: 'machine_number', title: '机台数量', align: 'center'},
                // {field: 'delivery_date', title: '计划交货时间', align: 'center'},
                {field: 'labor_cost', title: '预估人工成本', align: 'center', minWidth: 140, templet: function(d) {
                    return '<span class="estimate-value">¥' + d.labor_cost.toLocaleString() + '</span>';
                }},
                {field: 'actual_labor_cost', title: '实际人工成本(含外协费)', align: 'center', minWidth: 190, templet: function(d) {
                    return '<span class="actual-value">¥' + d.actual_labor_cost.toLocaleString() + '</span>';
                }},
                {field: 'mechanical_bom_cost', title: '预估机械BOM成本', align: 'center', minWidth: 160, templet: function(d) {
                    return '<span class="estimate-value">¥' + d.mechanical_bom_cost.toLocaleString() + '</span>';
                }},
                {field: 'actual_mechanical_bom_cost', title: '实际机械BOM成本', align: 'center', minWidth: 160, templet: function(d) {
                    return '<span class="actual-value">¥' + d.actual_mechanical_bom_cost.toLocaleString() + '</span>';
                }},
                {field: 'electrical_bom_cost', title: '预估电气BOM成本', align: 'center', minWidth: 160, templet: function(d) {
                    return '<span class="estimate-value">¥' + d.electrical_bom_cost.toLocaleString() + '</span>';
                }},
                {field: 'actual_electrical_bom_cost', title: '实际电气BOM成本', align: 'center', minWidth: 160, templet: function(d) {
                    return '<span class="actual-value">¥' + d.actual_electrical_bom_cost.toLocaleString() + '</span>';
                }},
                {field: 'other_cost', title: '预估其他成本', align: 'center', minWidth: 140, templet: function(d) {
                    return '<span class="estimate-value">¥' + d.other_cost.toLocaleString() + '</span>';
                }},
                {field: 'actual_other_cost', title: '实际其他成本', align: 'center', minWidth: 140, templet: function(d) {
                    return '<span class="actual-value">¥' + d.actual_other_cost.toLocaleString() + '</span>';
                }},

                {field: 'estimate_cost', title: '预估总成本', align: 'center', minWidth: 140, templet: function(d) {
                    return '<span class="estimate-value">¥' + d.estimate_cost.toLocaleString() + '</span>';
                }},
                {field: 'actual_cost', title: '实际总成本', align: 'center', minWidth: 140, templet: function(d) {
                    return '<span class="actual-value">¥' + d.actual_cost.toLocaleString() + '</span>';
                }},
                {field: 'cost_percent', title: '成本占比', align: 'center', minWidth: 100, templet: function(d) {
                    // 使用API返回的颜色类别
                    return '<span class="' + d.cost_class + '">' + d.cost_percent + '%</span>';
                }},
                {title: '操作', toolbar: '#project-bar', align: 'center', width: 130}
            ]],
            toolbar: '#project-toolbar',
            defaultToolbar: ['filter', 'print', 'exports']
        });

        // 表单提交
        form.on('submit(cost-query)', function(data) {
            table.reload('project-table', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 表格事件监听
        table.on('tool(project-table)', function(obj) {
            if (obj.event === 'detail') {
                // 查看详情，打开详情页
                layer.open({
                    type: 2,
                    title: '项目成本详情',
                    area: ['80%', '90%'],
                    content: 'detail/' + obj.data.id,
                    maxmin: true
                });
            }
        });

        // 表格工具栏事件
        table.on('toolbar(project-table)', function(obj) {
            if (obj.event === 'refresh') {
                // 刷新表格
                table.reload('project-table');
            }
        });

        // 导出Excel按钮
        $('#exportExcel').click(function() {
            window.location.href = 'excel/export';
        });
    });
</script>