<!DOCTYPE html>
<html>
<head>
    <title>钉钉日志导入失败记录</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .search-form .layui-form-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .search-form .layui-form-label {
            width: 80px;
            height: 38px;
            line-height: 38px;
            padding: 0 10px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }
        .search-form .layui-input-block {
            margin-left: 90px;
            flex: 1;
            display: flex;
            align-items: center;
        }
        .search-form .layui-input {
            height: 38px;
            line-height: 38px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.3s;
            width: 100%;
        }
        .search-form .layui-input:focus {
            border-color: #009688;
        }
        .search-form .btn-group {
            display: flex;
            align-items: center;
            gap: 10px;
            height: 38px;
        }
        .search-form .btn-group .layui-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 15px;
            margin: 0;
        }
        .table-container {
            background: #fff;
            padding: 15px;
            border-radius: 2px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
        }
        .page-header {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .page-header h2 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 500;
        }
        .failure-reason {
            max-width: 300px;
            word-break: break-all;
            white-space: normal;
            cursor: pointer;
        }

        /* 表格响应式样式 */
        .table-container {
            overflow-x: auto;
            min-width: 1000px;
        }

        .layui-table {
            min-width: 100%;
        }

        /* 移动端适配 */
        @media (max-width: 1200px) {
            .table-container {
                min-width: 900px;
            }
        }

        @media (max-width: 768px) {
            .table-container {
                min-width: 700px;
            }

            .search-form .layui-col-md2 {
                width: 50% !important;
                margin-bottom: 15px;
            }

            .search-form .layui-col-md4 {
                width: 100% !important;
                margin-bottom: 15px;
            }

            .search-form .btn-group .layui-btn {
                padding: 0 10px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .search-form .layui-col-md2 {
                width: 100% !important;
            }

            .search-form .layui-col-md4 {
                width: 100% !important;
            }

            .table-container {
                min-width: 600px;
            }
        }
    </style>
</head>

<body class="pear-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h2><i class="layui-icon layui-icon-close-fill" style="color: #ff5722;"></i> 钉钉日志导入失败记录</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-form">
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md2">
                    <div class="layui-form-item">
                        <label class="layui-form-label">创建人</label>
                        <div class="layui-input-block" style="margin-left: 0;">
                            <input type="text" name="creator_name" id="creator_name" placeholder="请输入创建人姓名" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="layui-form-item">
                        <label class="layui-form-label">部门</label>
                        <div class="layui-input-block"  style="margin-left: 0;">
                            <select name="department" id="department" lay-search>
                                <option value="">请选择部门</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="layui-form-item">
                        <label class="layui-form-label">开始日期</label>
                        <div class="layui-input-block"  style="margin-left: 0;">
                            <input type="text" name="start_date" id="start_date" placeholder="请选择开始日期" class="layui-input" readonly>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="layui-form-item">
                        <label class="layui-form-label">结束日期</label>
                        <div class="layui-input-block"  style="margin-left: 0;">
                            <input type="text" name="end_date" id="end_date" placeholder="请选择结束日期" class="layui-input" readonly>
                        </div>
                    </div>
                </div>
                
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 0; padding: 0; margin: 0;"></label>
                        <div class="layui-input-block btn-group" style="margin-left: 0;">
                            <button type="button" class="layui-btn layui-btn-primary" id="search-btn">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="button" class="layui-btn layui-btn-danger" id="batch-delete-btn">
                                <i class="layui-icon layui-icon-delete"></i> 批量删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
        <table class="layui-hide" id="failed-logs-table" lay-filter="failed-logs-table"></table>
    </div>

    <!-- 工具栏模板 -->
    <script type="text/html" id="toolbar-tpl">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">
            <i class="layui-icon layui-icon-about"></i> 详情
        </a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">
            <i class="layui-icon layui-icon-delete"></i> 删除
        </a>
    </script>

    <!-- 详情模板 -->
    <script type="text/html" id="detail-tpl">
    {% raw %}
        <div style="padding: 20px; max-height: 520px; overflow-y: auto;">
            <div class="layui-form">
                <!-- 基本信息分组 -->
                <fieldset class="layui-elem-field layui-field-title" style="margin-top: 0;">
                    <legend><i class="layui-icon layui-icon-user"></i> 基本信息</legend>
                </fieldset>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">报告ID:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.report_id}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">创建人:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.creator_name}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">创建时间:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.create_time || '未知'}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">部门:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.department || '未知'}}</div>
                            </div>
                        </div>
                    </div>
                </div>
    {% endraw %}
    {% raw %}
                <!-- 项目信息分组 -->
                <fieldset class="layui-elem-field layui-field-title">
                    <legend><i class="layui-icon layui-icon-template"></i> 项目信息</legend>
                </fieldset>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工作日期:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.work_date || '未知'}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目类型:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.project_type || '未知'}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目编号:</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{{d.project_number || '未知'}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">工时:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">
                            <span class="layui-badge layui-bg-blue">{{d.total_hours || '未知'}}</span> 小时
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">工作内容:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" style="word-break: break-all; background: #f8f8f8; padding: 10px; border-radius: 4px;">{{d.work_content || '未知'}}</div>
                    </div>
                </div>
    {% endraw %}
    {% raw %}
                <!-- 失败信息分组 -->
                <fieldset class="layui-elem-field layui-field-title">
                    <legend><i class="layui-icon layui-icon-close-fill" style="color: #ff5722;"></i> 失败信息</legend>
                </fieldset>
                <div class="layui-form-item">
                    <label class="layui-form-label">失败时间:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid">{{d.failed_at}}</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">失败原因:</label>
                    <div class="layui-input-block">
                        <div class="layui-form-mid" style="color: #ff5722; word-break: break-all; background: #fff2f0; padding: 10px; border-radius: 4px; border-left: 4px solid #ff5722;">{{d.failure_reason}}</div>
                    </div>
                </div>


            </div>
        </div>
    {% endraw %}
    </script>

    {% include 'system/common/footer.html' %}
    <script>
    layui.use(['table', 'form', 'layer', 'laydate', 'laytpl', 'jquery'], function(){
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var laytpl = layui.laytpl;
        var $ = layui.jquery;

        // 初始化日期选择器
        laydate.render({
            elem: '#start_date',
            type: 'date'
        });

        laydate.render({
            elem: '#end_date',
            type: 'date'
        });

        // 加载部门列表
        loadDepartments();

        // 初始化表格
        table.render({
            elem: '#failed-logs-table',
            url: '/system/dindin/failed-logs/data',
            method: 'GET',
            cols: [[
                {type: 'checkbox', width: 50},
                {field: 'id', title: 'ID', width: '6%', minWidth: 60, sort: true},
                {field: 'creator_name', title: '创建人', width: '5%', minWidth: 100},
                {field: 'department', title: '部门', width: '8%', minWidth: 100},
                {field: 'work_date', title: '工作日期', width: '8%', minWidth: 100, sort: true},
                {field: 'project_type', title: '项目类型', width: '12%', minWidth: 100},
                {field: 'project_number', title: '项目编号', width: '5%', minWidth: 100},
                {field: 'total_hours', title: '工时', width: '8%', minWidth: 60, align: 'center'},
                {field: 'failure_reason', title: '失败原因', width: '25%', minWidth: 200, templet: function(d){
                    var reason = d.failure_reason || '';
                    if(reason.length > 30) {
                        return '<div class="failure-reason" title="' + reason + '">' + reason.substring(0, 30) + '...</div>';
                    }
                    return '<div class="failure-reason" title="' + reason + '">' + reason + '</div>';
                }},
                {field: 'failed_at', title: '失败时间', width: '15%', minWidth: 160, sort: true},
                {title: '操作', toolbar: '#toolbar-tpl', width: '12%', minWidth: 120, align: 'center'}
            ]],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            loading: true,
            text: {
                none: '暂无失败日志记录'
            }
        });

        // 加载部门列表函数
        function loadDepartments() {
            $.ajax({
                url: '/system/dindin/departments',
                type: 'GET',
                success: function(res) {
                    if (res.success === true) {
                        var departmentSelect = $('#department');
                        departmentSelect.empty();
                        departmentSelect.append('<option value="">请选择部门</option>');

                        res.data.forEach(function(dept) {
                            departmentSelect.append('<option value="' + dept.name + '">' + dept.name + '</option>');
                        });

                        // 重新渲染表单
                        form.render('select');
                    } else {
                        layer.msg('获取部门列表失败: ' + (res.msg || '未知错误'), {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('获取部门列表失败: 网络错误', {icon: 2});
                }
            });
        }

        // 搜索功能
        $('#search-btn').on('click', function(){
            var creator_name = $('#creator_name').val();
            var start_date = $('#start_date').val();
            var end_date = $('#end_date').val();
            var department = $('#department').val();

            var where = {};
            if(creator_name) where.creator_name = creator_name;
            if(start_date) where.start_date = start_date;
            if(end_date) where.end_date = end_date;
            if(department) where.department = department;

            table.reload('failed-logs-table', {
                where: where,
                page: {
                    curr: 1
                }
            });
        });

        // 批量删除功能
        $('#batch-delete-btn').on('click', function(){
            var checkStatus = table.checkStatus('failed-logs-table');
            var data = checkStatus.data;

            if(data.length === 0){
                layer.msg('请选择要删除的记录', {icon: 2});
                return;
            }

            layer.confirm('确定要删除选中的 ' + data.length + ' 条记录吗？', {
                icon: 3,
                title: '确认删除'
            }, function(index){
                var ids = data.map(function(item){
                    return item.id;
                });

                $.ajax({
                    url: '/system/dindin/failed-logs/batch-delete',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ids: ids}),
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            table.reload('failed-logs-table');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.msg('请求失败', {icon: 2});
                    }
                });

                layer.close(index);
            });
        });

        // 监听工具条
        table.on('tool(failed-logs-table)', function(obj){
            var data = obj.data;
            if(obj.event === 'detail'){
                // 显示详情
                var getTpl = $('#detail-tpl').html();
                var view = laytpl(getTpl).render(data);

                layer.open({
                    type: 1,
                    title: '失败日志详情 - ' + data.creator_name,
                    area: ['800px', '600px'],
                    content: view,
                    maxmin: true,
                    shadeClose: true,
                    shade: 0.3,
                    scrollbar: false
                });
            } else if(obj.event === 'delete'){
                // 删除单条记录
                layer.confirm('确定要删除这条失败日志吗？', {
                    icon: 3,
                    title: '确认删除'
                }, function(index){
                    $.ajax({
                        url: '/system/dindin/failed-logs/' + data.id,
                        type: 'DELETE',
                        success: function(res){
                            if(res.code === 0){
                                layer.msg(res.msg, {icon: 1});
                                table.reload('failed-logs-table');
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        },
                        error: function(){
                            layer.msg('请求失败', {icon: 2});
                        }
                    });

                    layer.close(index);
                });
            }
        });


    });
    </script>
</body>
</html>
