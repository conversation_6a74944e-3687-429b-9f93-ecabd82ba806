#!/usr/bin/env python3
"""
项目类型标准化器
用于将各种项目类型变体标准化为系统中的标准项目类型
"""

import re
import logging
from typing import Optional, Dict, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TypeNormalizationResult:
    """项目类型标准化结果"""
    original_type: str
    normalized_type: Optional[str]
    normalization_method: str
    confidence: float
    notes: str

class ProjectTypeNormalizer:
    """项目类型标准化器"""
    
    def __init__(self):
        # 前缀去除规则
        self.prefix_patterns = [
            r'^厂外',
            r'^厂内',
            r'^内部',
            r'^外部',
        ]
        
        # 后缀去除规则
        self.suffix_patterns = [
            r'部门$',
            r'组$',
            r'科$',
        ]
        
        # 直接映射规则
        self.direct_mappings = {
            '其它': '其他',
            '其他项目': '其他',
            '会议室': '会议',
            '会议管理': '会议',
        }
        
        # 标准项目类型（从数据库中获取的有效类型）
        self.standard_types = {
            'GS', 'GSBJ', 'GSCC', 'GSM', 'GSSZ', '会议', '其他'
        }
        
        # 模糊匹配规则（相似度匹配）
        self.fuzzy_mappings = {
            'GSCC': ['GSCC', 'GS-CC', 'GS_CC'],
            'GSBJ': ['GSBJ', 'GS-BJ', 'GS_BJ'],
            'GSSZ': ['GSSZ', 'GS-SZ', 'GS_SZ'],
            'GSM': ['GSM', 'GS-M', 'GS_M'],
            'GS': ['GS'],
        }
    
    def normalize_project_type(self, project_type: str) -> TypeNormalizationResult:
        """
        标准化项目类型
        
        Args:
            project_type: 原始项目类型
            
        Returns:
            TypeNormalizationResult: 标准化结果
        """
        if not project_type or not project_type.strip():
            return TypeNormalizationResult(
                original_type=project_type or '',
                normalized_type=None,
                normalization_method='empty',
                confidence=0.0,
                notes='项目类型为空'
            )
        
        original_type = project_type.strip()
        
        # 1. 检查是否已经是标准类型
        if original_type in self.standard_types:
            return TypeNormalizationResult(
                original_type=original_type,
                normalized_type=original_type,
                normalization_method='exact',
                confidence=1.0,
                notes=f'已是标准类型: {original_type}'
            )
        
        # 2. 直接映射
        if original_type in self.direct_mappings:
            mapped_type = self.direct_mappings[original_type]
            return TypeNormalizationResult(
                original_type=original_type,
                normalized_type=mapped_type,
                normalization_method='direct_mapping',
                confidence=0.95,
                notes=f'直接映射: {original_type} → {mapped_type}'
            )
        
        # 3. 前缀去除
        prefix_result = self._remove_prefixes(original_type)
        if prefix_result:
            return prefix_result
        
        # 4. 后缀去除
        suffix_result = self._remove_suffixes(original_type)
        if suffix_result:
            return suffix_result
        
        # 5. 模糊匹配
        fuzzy_result = self._fuzzy_match(original_type)
        if fuzzy_result:
            return fuzzy_result
        
        # 6. 无法标准化
        return TypeNormalizationResult(
            original_type=original_type,
            normalized_type=None,
            normalization_method='no_match',
            confidence=0.0,
            notes=f'无法标准化项目类型: {original_type}'
        )
    
    def _remove_prefixes(self, project_type: str) -> Optional[TypeNormalizationResult]:
        """去除前缀"""
        for pattern in self.prefix_patterns:
            match = re.search(pattern, project_type)
            if match:
                # 去除前缀后的类型
                remaining_type = re.sub(pattern, '', project_type).strip()
                
                if remaining_type and remaining_type in self.standard_types:
                    return TypeNormalizationResult(
                        original_type=project_type,
                        normalized_type=remaining_type,
                        normalization_method='prefix_removal',
                        confidence=0.9,
                        notes=f'去除前缀"{match.group()}"后匹配: {project_type} → {remaining_type}'
                    )
                elif remaining_type:
                    # 检查去除前缀后的类型是否为标准类型（避免递归）
                    if remaining_type in self.standard_types:
                        return TypeNormalizationResult(
                            original_type=project_type,
                            normalized_type=remaining_type,
                            normalization_method='prefix_removal_direct',
                            confidence=0.85,
                            notes=f'去除前缀"{match.group()}"后直接匹配: {project_type} → {remaining_type}'
                        )
                    # 检查直接映射
                    elif remaining_type in self.direct_mappings:
                        mapped_type = self.direct_mappings[remaining_type]
                        return TypeNormalizationResult(
                            original_type=project_type,
                            normalized_type=mapped_type,
                            normalization_method='prefix_removal_mapping',
                            confidence=0.8,
                            notes=f'去除前缀"{match.group()}"后映射匹配: {project_type} → {remaining_type} → {mapped_type}'
                        )
        return None
    
    def _remove_suffixes(self, project_type: str) -> Optional[TypeNormalizationResult]:
        """去除后缀"""
        for pattern in self.suffix_patterns:
            match = re.search(pattern, project_type)
            if match:
                # 去除后缀后的类型
                remaining_type = re.sub(pattern, '', project_type).strip()
                
                if remaining_type and remaining_type in self.standard_types:
                    return TypeNormalizationResult(
                        original_type=project_type,
                        normalized_type=remaining_type,
                        normalization_method='suffix_removal',
                        confidence=0.85,
                        notes=f'去除后缀"{match.group()}"后匹配: {project_type} → {remaining_type}'
                    )
        return None
    
    def _fuzzy_match(self, project_type: str) -> Optional[TypeNormalizationResult]:
        """模糊匹配"""
        project_type_upper = project_type.upper()
        
        for standard_type, variants in self.fuzzy_mappings.items():
            for variant in variants:
                if variant.upper() == project_type_upper:
                    return TypeNormalizationResult(
                        original_type=project_type,
                        normalized_type=standard_type,
                        normalization_method='fuzzy_match',
                        confidence=0.8,
                        notes=f'模糊匹配: {project_type} → {standard_type}'
                    )
        
        # 包含匹配
        for standard_type in self.standard_types:
            if standard_type.upper() in project_type_upper or project_type_upper in standard_type.upper():
                return TypeNormalizationResult(
                    original_type=project_type,
                    normalized_type=standard_type,
                    normalization_method='contains_match',
                    confidence=0.7,
                    notes=f'包含匹配: {project_type} → {standard_type}'
                )
        
        return None
    
    def get_normalization_stats(self, project_types: List[str]) -> Dict:
        """获取标准化统计信息"""
        stats = {
            'total': len(project_types),
            'exact': 0,
            'direct_mapping': 0,
            'prefix_removal': 0,
            'suffix_removal': 0,
            'fuzzy_match': 0,
            'no_match': 0,
            'details': []
        }
        
        for project_type in project_types:
            result = self.normalize_project_type(project_type)
            stats[result.normalization_method] += 1
            stats['details'].append({
                'original': result.original_type,
                'normalized': result.normalized_type,
                'method': result.normalization_method,
                'confidence': result.confidence,
                'notes': result.notes
            })
        
        return stats

# 全局实例
project_type_normalizer = ProjectTypeNormalizer()
