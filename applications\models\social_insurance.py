import datetime
from applications.extensions import db

class SocialInsurance(db.Model):
    __tablename__ = 'social_insurance'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    employee_name = db.Column(db.String(50), nullable=False, comment='员工姓名')
    employee_id = db.Column(db.String(20), nullable=False, comment='工号')
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), comment='部门ID')
    dept_name = db.Column(db.String(100), comment='部门名称')
    social_insurance_deduction = db.Column(db.Float, default=0.0, comment='社保单位扣款')
    housing_fund_deduction = db.Column(db.Float, default=0.0, comment='公积金单位扣款')
    month = db.Column(db.String(7), comment='月份(YYYY-MM)', index=True)
    remark = db.Column(db.Text, comment='备注')
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联部门表
    dept = db.relationship('Dept', backref='social_insurances')
    
    def __repr__(self):
        return f'<SocialInsurance {self.employee_name}({self.employee_id}) - {self.month}>'
