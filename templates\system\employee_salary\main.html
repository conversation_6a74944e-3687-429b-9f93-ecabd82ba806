<!DOCTYPE html>
<html>
<head>
    <title>员工薪资管理</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="" lay-filter="salary-query-form">
                <div class="layui-form-item">
                    <div class="layui-input-inline">
                        <input type="text" name="name" placeholder="员工姓名" class="layui-input">
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="month" placeholder="月份" class="layui-input" id="month-picker">
                    </div>
                    <div class="layui-input-inline">
                        <select name="dept_id" lay-search>
                            <option value="">选择部门</option>
                            {% for dept in depts %}
                            <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="layui-input-inline">
                        <select name="view_status">
                            <option value="">确认状态</option>
                            <option value="pending">未确认</option>
                            <option value="confirmed">已确认</option>
                  <!--           <option value="disputed">有异议</option> -->
                        </select>
                    </div>
                    <button class="layui-btn layui-btn-primary layui-btn-sm" lay-submit lay-filter="salary-query">
                        <i class="layui-icon layui-icon-search"></i>查询
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                        <i class="layui-icon layui-icon-refresh"></i>重置
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-body">
            <table id="salary-table" lay-filter="salary-table"></table>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<script type="text/html" id="salary-toolbar">
    <button class="layui-btn layui-btn-sm" lay-event="import">
        <i class="layui-icon layui-icon-upload-drag"></i>导入Excel
    </button>
    <!-- <button class="layui-btn layui-btn-sm" lay-event="export">
        <i class="layui-icon layui-icon-download-circle"></i>导出Excel
    </button> -->
</script>

<script>
layui.use(['table', 'form', 'jquery', 'popup', 'common', 'laydate'], function() {
    let table = layui.table;
    let form = layui.form;
    let $ = layui.jquery;
    let popup = layui.popup;
    let laydate = layui.laydate;
    let MODULE_PATH = "/system/employee_salary";

    // 初始化月份选择器
    laydate.render({
        elem: '#month-picker',
        type: 'month',
        format: 'yyyy-MM'  // 确保格式为YYYY-MM
    });

    // 表格列配置
    let cols = [[
        {type: 'numbers', title: '序号', width: 60},
        {field: 'name', title: '姓名', align: 'center'},
        {field: 'month', title: '月份', align: 'center'},
        {field: 'base_salary', title: '基本工资', align: 'center'},
        {field: 'performance_salary', title: '绩效工资', align: 'center'},
        {field: 'supervisor_assessment', title: '主管考核项', align: 'center'},
        {field: 'assessment_coefficient', title: '考核系数', align: 'center'},
        {field: 'position_allowance', title: '职务津贴', align: 'center'},
        {field: 'full_attendance', title: '全勤', align: 'center'},
        {field: 'overtime_pay', title: '加班费', align: 'center'},
        {field: 'housing_subsidy', title: '房补', align: 'center'},
        {field: 'project_assessment', title: '项目考核', align: 'center'},
        {field: 'high_temp_allowance', title: '高温费', align: 'center'},
        {field: 'other_allowance', title: '其他', align: 'center'},
        {field: 'leave_deduction', title: '请假扣款', align: 'center'},
        {field: 'other_deduction', title: '其他扣款', align: 'center'},
        {field: 'should_pay', title: '应发工资', align: 'center'},
        {field: 'insurance_deduction', title: '社保扣款', align: 'center'},
        {field: 'tax_deduction', title: '个税', align: 'center'},
        {field: 'year_end_tax', title: '年终奖个税扣款', align: 'center'},
        {field: 'total_deduction', title: '扣款', align: 'center'},
        {field: 'actual_salary', title: '实发工资', align: 'center'},
        // {field: 'view_status', title: '确认状态', align: 'center', templet: function(d) {
        //     if (d.view_status === 'pending') {
        //         return '<span class="layui-badge layui-bg-orange">未确认</span>';
        //     } else if (d.view_status === 'confirmed') {
        //         return '<span class="layui-badge layui-bg-green">已确认</span>';
        //     } else if (d.view_status === 'disputed') {
        //         return '<span class="layui-badge layui-bg-red">有异议</span>';
        //     } else {
        //         return '<span class="layui-badge layui-bg-gray">未知</span>';
        //     }
        // }},
        {title: '操作', align: 'center', width: 100, templet: function(d) {
            return '<button class="layui-btn layui-btn-xs" onclick="viewStatus(' + d.id + ')">查看详情</button>';
        }}
    ]];

    // 渲染表格
    table.render({
        elem: '#salary-table',
        url: MODULE_PATH + '/data',
        page: true,
        cols: cols,
        skin: 'line',
        toolbar: '#salary-toolbar',
        defaultToolbar: ['filter', 'print', 'exports'],
        text: {none: '暂无薪资信息'}
    });

    // 工具栏事件
    table.on('toolbar(salary-table)', function(obj) {
        if (obj.event === 'import') {
            layer.open({
                type: 1,
                title: '导入Excel',
                area: ['400px', '200px'],
                content: `
                    <div class="layui-form" lay-filter="import-form" style="padding: 20px;">
                        <div class="layui-form-item">
                            <input type="file" name="file" accept=".xlsx" class="layui-input">
                        </div>
                        <div class="layui-form-item">
                            <button type="button" class="layui-btn" lay-submit lay-filter="import-submit">
                                <i class="layui-icon layui-icon-upload"></i>上传
                            </button>
                        </div>
                    </div>
                `
            });
        }
        // if (obj.event === 'export') {
        //     window.location.href = MODULE_PATH + '/export';
        // }
    });

    // 查询提交
    form.on('submit(salary-query)', function(data) {
        table.reload('salary-table', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 导入表单提交
    form.on('submit(import-submit)', function(data) {
        let formData = new FormData();
        let fileInput = $('input[name="file"]')[0];
        if (fileInput.files.length === 0) {
            popup.failure('请选择文件');
            return;
        }
        formData.append('file', fileInput.files[0]);

        $.ajax({
            url: MODULE_PATH + '/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if (res.success) {
                    popup.success(res.msg);
                    table.reload('salary-table');
                    layer.closeAll();
                } else {
                    popup.failure(res.msg);
                }
            }
        });
        return false;
    });

    // 查看工资条确认状态
    window.viewStatus = function(id) {
        window.open('/system/employee_salary/status/' + id, '_blank');
    }
});
</script>
</html>