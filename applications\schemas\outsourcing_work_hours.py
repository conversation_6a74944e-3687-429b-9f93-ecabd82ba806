from flask_marshmallow.sqla import SQLAlchemyAutoSchema
from marshmallow import fields
from applications.models.outsourcing_work_hours import OutsourcingWorkHours


class OutsourcingWorkHoursSchema(SQLAlchemyAutoSchema):
    """外协工时管理序列化模式"""
    class Meta:
        model = OutsourcingWorkHours
        include_fk = True
        
    # 自定义字段格式化
    work_start_date = fields.Date(format='%Y-%m-%d')
    work_end_date = fields.Date(format='%Y-%m-%d')
    create_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    update_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')


class OutsourcingWorkHoursOutSchema(SQLAlchemyAutoSchema):
    """外协工时管理输出序列化模式"""
    class Meta:
        model = OutsourcingWorkHours
        include_fk = True
        
    id = fields.Str(attribute="id")
    project_code = fields.Str(attribute="project_code")
    project_type = fields.Str(attribute="project_type")
    outsourcing_id = fields.Str(attribute="outsourcing_id")
    work_location = fields.Str(attribute="work_location")
    work_hours = fields.Str(attribute="work_hours")
    work_start_date = fields.Date(format='%Y-%m-%d')
    work_end_date = fields.Date(format='%Y-%m-%d')
    work_description = fields.Str(attribute="work_description")
    hourly_rate = fields.Str(attribute="hourly_rate")
    total_cost = fields.Str(attribute="total_cost")
    dept_id = fields.Str(attribute="dept_id")
    creator_id = fields.Str(attribute="creator_id")
    status = fields.Str(attribute="status")
    remark = fields.Str(attribute="remark")
    create_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    update_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    
    # 关联字段
    outsourcing_name = fields.Method("get_outsourcing_name")
    dept_name = fields.Method("get_dept_name")
    creator_name = fields.Method("get_creator_name")
    
    def get_outsourcing_name(self, obj):
        return obj.outsourcing_info.outsourcing_name if obj.outsourcing_info else ""
    
    def get_dept_name(self, obj):
        return obj.dept.dept_name if obj.dept else ""
    
    def get_creator_name(self, obj):
        return obj.creator.realname if obj.creator else ""
