<!DOCTYPE html>
<html>
<head>
    <title>节假日配置管理</title>
    {% include 'system/common/header.html' %}

<style>
/* 美化工具栏按钮 */
.holiday-toolbar-btn {
    position: relative;
    margin-right: 12px;
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.holiday-toolbar-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.holiday-toolbar-btn:hover:before {
    left: 100%;
}

.holiday-toolbar-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.holiday-toolbar-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 添加按钮样式 */
.holiday-btn-add {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.holiday-btn-add:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
}

/* 导入按钮样式 */
.holiday-btn-import {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.holiday-btn-import:hover {
    background: linear-gradient(135deg, #ee82f0 0%, #f34560 100%);
    color: white;
}

/* 修正按钮样式 */
.holiday-btn-fix {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.holiday-btn-fix:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
    color: white;
}

/* 日历按钮样式 */
.holiday-btn-calendar {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.holiday-btn-calendar:hover {
    background: linear-gradient(135deg, #32d467 0%, #26e7c5 100%);
    color: white;
}

/* 日志按钮样式 */
.holiday-btn-logs {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.holiday-btn-logs:hover {
    background: linear-gradient(135deg, #f85e88 0%, #fed02e 100%);
    color: white;
}

/* 按钮图标样式 */
.holiday-toolbar-btn i {
    margin-right: 6px;
    font-size: 14px;
    vertical-align: middle;
}

/* 按钮组容器 */
.holiday-toolbar-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 0;
}

/* 分组分隔线 */
.holiday-toolbar-divider {
    width: 1px;
    height: 24px;
    background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
    margin: 0 8px;
}

/* 操作栏按钮样式 */
.holiday-action-btn {
    margin-right: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    border: none;
    font-size: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.holiday-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.holiday-action-edit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.holiday-action-edit:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
}

.holiday-action-delete {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.holiday-action-delete:hover {
    background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .holiday-toolbar-btn {
        margin-right: 8px;
        margin-bottom: 8px;
        padding: 6px 12px;
        font-size: 12px;
    }

    .holiday-toolbar-btn i {
        margin-right: 4px;
        font-size: 12px;
    }

    .holiday-action-btn {
        padding: 3px 6px;
        font-size: 11px;
    }
}
</style>
</head>
<body class="pear-container">

<!-- 查询表单 -->
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="holiday-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">年份</label>
                <div class="layui-input-inline">
                    <select name="year" lay-filter="year-filter">
                        <option value="">选择年份</option>
                        {% for year in years %}
                        <option value="{{ year }}" {% if year == 2025 %}selected{% endif %}>{{ year }}年</option>
                        {% endfor %}
                    </select>
                </div>
                
                <label class="layui-form-label">类型</label>
                <div class="layui-input-inline">
                    <select name="holiday_type" lay-filter="type-filter">
                        <option value="">节假日类型</option>
                        <option value="legal">法定节假日(3倍)</option>
                        <option value="makeup">调休日(2倍)</option>
                        <option value="weekend">周末(2倍)</option>
                        <option value="company_rest">公司调休休息日(2倍)</option>
                        <option value="company_work">公司调休工作日(1.5倍)</option>
                    </select>
                </div>
                
                <label class="layui-form-label">名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="keyword" placeholder="节假日名称" class="layui-input">
                </div>
                
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="holiday-query">
                    <i class="layui-icon layui-icon-search"></i> 搜索
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i> 重置
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 数据表格 -->
<div class="layui-card">
    <div class="layui-card-body">
        <table id="holiday-table" lay-filter="holiday-table"></table>
    </div>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="holiday-toolbar">
    <div class="holiday-toolbar-container">
        {% if authorize("system:holiday_config:add") %}
        <button class="holiday-toolbar-btn holiday-btn-add" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>添加节假日
        </button>
        {% endif %}
        {% if authorize("system:holiday_config:import") %}
        <button class="holiday-toolbar-btn holiday-btn-import" lay-event="import">
            <i class="layui-icon layui-icon-download-circle"></i>批量导入
        </button>
        {% endif %}
        {% if authorize("system:holiday_config:edit") %}
        <button class="holiday-toolbar-btn holiday-btn-fix" lay-event="fix">
            <i class="layui-icon layui-icon-refresh-1"></i>修正类型
        </button>
        {% endif %}

        <div class="holiday-toolbar-divider"></div>

        <button class="holiday-toolbar-btn holiday-btn-calendar" lay-event="calendar">
            <i class="layui-icon layui-icon-date"></i>日历视图
        </button>
        <button class="holiday-toolbar-btn holiday-btn-logs" lay-event="logs">
            <i class="layui-icon layui-icon-log"></i>操作日志
        </button>
    </div>
</script>

<!-- 操作栏模板 -->
<script type="text/html" id="holiday-bar">
    {% if authorize("system:holiday_config:edit") %}
    <button class="holiday-action-btn holiday-action-edit" lay-event="edit" title="编辑">
        <i class="layui-icon layui-icon-edit"></i>
    </button>
    {% endif %}
    {% if authorize("system:holiday_config:remove") %}
    <button class="holiday-action-btn holiday-action-delete" lay-event="remove" title="删除">
        <i class="layui-icon layui-icon-delete"></i>
    </button>
    {% endif %}
</script>

<!-- 状态模板 -->
<script type="text/html" id="holiday-status">
    {%raw%}{{# if(d.status == 1){ }}{%endraw%}
    <span class="layui-badge layui-bg-green">启用</span>
    {%raw%}{{# } else { }}{%endraw%}
    <span class="layui-badge layui-bg-gray">禁用</span>
    {%raw%}{{# } }}{%endraw%}
</script>

<!-- 类型模板 -->
<script type="text/html" id="holiday-type">
    {%raw%}{{# if(d.holiday_type == 'legal'){ }}{%endraw%}
    <span class="layui-badge layui-bg-red">法定节假日(3倍)</span>
    {%raw%}{{# } else if(d.holiday_type == 'makeup'){ }}{%endraw%}
    <span class="layui-badge layui-bg-orange">调休日(2倍)</span>
    {%raw%}{{# } else if(d.holiday_type == 'weekend'){ }}{%endraw%}
    <span class="layui-badge layui-bg-blue">周末(2倍)</span>
    {%raw%}{{# } else if(d.holiday_type == 'company_rest'){ }}{%endraw%}
    <span class="layui-badge layui-bg-cyan">公司调休休息日(2倍)</span>
    {%raw%}{{# } else if(d.holiday_type == 'company_work'){ }}{%endraw%}
    <span class="layui-badge layui-bg-green">公司调休工作日(1.5倍)</span>
    {%raw%}{{# } else { }}{%endraw%}
    <span class="layui-badge layui-bg-gray">未知类型</span>
    {%raw%}{{# } }}{%endraw%}
</script>

</body>

{% include 'system/common/footer.html' %}

<script>
layui.use(['table', 'form', 'layer', 'jquery'], function () {
    let table = layui.table;
    let form = layui.form;
    let layer = layui.layer;
    let $ = layui.jquery;
    
    const MODULE_PATH = "/system/holiday_config/";
    
    // 表格配置
    let cols = [
        [
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', width: 80, title: 'ID', sort: true},
            {field: 'holiday_date', width: 120, title: '日期', sort: true},
            {field: 'holiday_name', width: 150, title: '节假日名称'},
            {field: 'holiday_type', width: 150, title: '类型', templet: '#holiday-type'},
            {field: 'overtime_rate', width: 100, title: '倍率', sort: true},
            {field: 'year', width: 80, title: '年份', sort: true},
            {field: 'description', title: '备注'},
            {field: 'status', width: 80, title: '状态', templet: '#holiday-status'},
            {field: 'created_at', width: 160, title: '创建时间', sort: true},
            {title: '操作', toolbar: '#holiday-bar', align: 'center', width: 120}
        ]
    ];
    
    // 渲染表格
    table.render({
        elem: '#holiday-table',
        url: MODULE_PATH + 'data',
        page: true,
        cols: cols,
        skin: 'line',
        toolbar: '#holiday-toolbar',
        defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'exports']
    });
    
    // 工具栏事件
    table.on('toolbar(holiday-table)', function (obj) {
        if (obj.event === 'add') {
            layer.open({
                type: 2,
                title: '添加节假日',
                shade: 0.1,
                area: ['600px', '500px'],
                content: MODULE_PATH + 'add',
                success: function(layero, index) {
                    // 成功回调
                },
                end: function() {
                    // 关闭后刷新表格
                    table.reload('holiday-table');
                }
            });
        } else if (obj.event === 'import') {
            // 智能导入节假日对话框
            layer.open({
                type: 1,
                title: '从holidays库导入中国法定节假日',
                area: ['500px', '400px'],
                content: `
                    <div style="padding: 20px;">
                        <form class="layui-form" lay-filter="import-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择年份</label>
                                <div class="layui-input-block">
                                    <select name="year" lay-verify="required">
                                        <option value="">请选择年份</option>
                                        ${Array.from({length: 20}, (_, i) => 2025 + i).map(year =>
                                            `<option value="${year}" ${year === new Date().getFullYear() ? 'selected' : ''}>${year}年</option>`
                                        ).join('')}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <input type="checkbox" name="overwrite" title="覆盖已存在的节假日" lay-skin="primary">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="preview-btn">预览导入</button>
                                    <button type="button" class="layui-btn layui-btn-normal" id="import-btn">开始导入</button>
                                </div>
                            </div>
                        </form>
                        <div id="preview-result" style="margin-top: 15px; display: none;">
                            <div class="layui-collapse" lay-accordion="">
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">预览结果</h2>
                                    <div class="layui-colla-content layui-show">
                                        <div id="preview-content"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                success: function(layero, index) {
                    form.render();

                    // 预览按钮事件
                    $('#preview-btn').click(function() {
                        let year = $(layero).find('select[name="year"]').val();
                        if (!year) {
                            layer.msg('请选择年份', {icon: 2});
                            return;
                        }

                        layer.load(2);
                        $.ajax({
                            url: MODULE_PATH + 'preview_import?year=' + year,
                            type: 'GET',
                            success: function(res) {
                                layer.closeAll('loading');
                                if (res.success) {
                                    let data = res.data;
                                    let html = `
                                        <p><strong>年份：</strong>${data.year}</p>
                                        <p><strong>总节假日：</strong>${data.total_holidays}个</p>
                                        <p><strong>新增节假日：</strong>${data.new_holidays}个</p>
                                        <p><strong>已存在：</strong>${data.existing_holidays}个</p>
                                    `;

                                    if (data.missing_list && data.missing_list.length > 0) {
                                        html += '<h4>将要导入的节假日：</h4><ul>';
                                        data.missing_list.forEach(holiday => {
                                            html += `<li>${holiday.date} - ${holiday.name} (${holiday.type}, ${holiday.overtime_rate}倍)</li>`;
                                        });
                                        html += '</ul>';
                                    }

                                    $('#preview-content').html(html);
                                    $('#preview-result').show();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.closeAll('loading');
                                layer.msg('预览失败', {icon: 2});
                            }
                        });
                    });

                    // 导入按钮事件
                    $('#import-btn').click(function() {
                        let year = $(layero).find('select[name="year"]').val();
                        let overwrite = $(layero).find('input[name="overwrite"]').is(':checked');

                        if (!year) {
                            layer.msg('请选择年份', {icon: 2});
                            return;
                        }

                        layer.confirm('确定要导入' + year + '年的中国法定节假日吗？', {icon: 3, title: '确认导入'}, function(confirmIndex) {
                            layer.load(2);
                            $.ajax({
                                url: MODULE_PATH + 'import_from_holidays',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    year: parseInt(year),
                                    overwrite: overwrite
                                }),
                                success: function(res) {
                                    layer.closeAll('loading');
                                    if (res.success) {
                                        layer.msg(res.msg, {icon: 1});
                                        table.reload('holiday-table');
                                        layer.close(index);
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                },
                                error: function() {
                                    layer.closeAll('loading');
                                    layer.msg('导入失败', {icon: 2});
                                }
                            });
                            layer.close(confirmIndex);
                        });
                    });
                }
            });
        } else if (obj.event === 'fix') {
            // 修正节假日类型
            layer.confirm('确定要修正所有节假日的类型吗？<br><br>这将重新判断所有节假日的类型，将错误标记为法定节假日(3倍)的调休日改为调休日(2倍)。', {
                icon: 3,
                title: '确认修正',
                area: ['400px', '200px']
            }, function(confirmIndex) {
                layer.load(2);
                $.ajax({
                    url: MODULE_PATH + 'fix_holiday_types',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({}),
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.success) {
                            let message = res.message;
                            if (res.data && res.data.details && res.data.details.length > 0) {
                                message += '<br><br>修正详情：<br>';
                                res.data.details.forEach(detail => {
                                    message += `${detail.date} ${detail.name}: ${detail.old_type}(${detail.old_rate}倍) → ${detail.new_type}(${detail.new_rate}倍)<br>`;
                                });
                            }
                            layer.alert(message, {icon: 1, title: '修正完成'});
                            table.reload('holiday-table');
                        } else {
                            layer.msg(res.message, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('修正失败', {icon: 2});
                    }
                });
                layer.close(confirmIndex);
            });
        } else if (obj.event === 'calendar') {
            // 日历视图
            layer.open({
                type: 2,
                title: '节假日日历视图',
                shade: 0.1,
                area: ['90%', '80%'],
                content: MODULE_PATH + 'calendar'
            });
        } else if (obj.event === 'logs') {
            // 操作日志
            layer.open({
                type: 2,
                title: '操作日志',
                shade: 0.1,
                area: ['80%', '70%'],
                content: MODULE_PATH + 'logs'
            });
        }
    });
    
    // 行工具事件
    table.on('tool(holiday-table)', function (obj) {
        let data = obj.data;
        if (obj.event === 'edit') {
            layer.open({
                type: 2,
                title: '编辑节假日',
                shade: 0.1,
                area: ['600px', '500px'],
                content: MODULE_PATH + 'edit?id=' + data.id,
                end: function() {
                    table.reload('holiday-table');
                }
            });
        } else if (obj.event === 'remove') {
            layer.confirm('确定要删除这个节假日配置吗？', {icon: 3, title: '提示'}, function(index) {
                $.ajax({
                    url: MODULE_PATH + 'remove',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({id: data.id}),
                    success: function(res) {
                        if (res.success) {
                            layer.msg(res.msg, {icon: 1});
                            table.reload('holiday-table');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        }
    });
    
    // 搜索表单提交
    form.on('submit(holiday-query)', function (data) {
        table.reload('holiday-table', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
});
</script>
</html>
