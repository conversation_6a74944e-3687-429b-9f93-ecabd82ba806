"""
增强的项目匹配服务
结合项目编号解析和项目类型进行精准匹配
"""

import logging
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from applications.models.import_project import Import_project
from applications.models.project_manage_dept import ProjectManageDept
from applications.services.project_code_parser import ProjectCodeParser, ParsedProjectCode
from applications.services.project_type_normalizer import project_type_normalizer

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class ProjectMatchResult:
    """项目匹配结果"""
    project_id: Optional[int]  # 匹配的项目ID
    project_code: str  # 项目编号
    project_name: Optional[str]  # 项目名称
    project_type_id: Optional[int]  # 项目类型ID
    project_type_name: Optional[str]  # 项目类型名称
    match_type: str  # 匹配类型：exact, parsed, fuzzy
    confidence: float  # 匹配置信度
    notes: str  # 匹配说明


@dataclass
class MultiProjectMatchResult:
    """多项目匹配结果"""
    original_code: str  # 原始编号
    parsed_result: ParsedProjectCode  # 解析结果
    matches: List[ProjectMatchResult]  # 匹配的项目列表
    unmatched_codes: List[str]  # 未匹配的编号
    total_confidence: float  # 总体置信度
    notes: str  # 匹配说明


class EnhancedProjectMatcher:
    """增强的项目匹配器"""
    
    def __init__(self):
        self.parser = ProjectCodeParser()
    
    def match_projects(self, project_code: str, project_type: Optional[str] = None) -> MultiProjectMatchResult:
        """
        匹配项目，支持多项目编号和项目类型过滤
        
        Args:
            project_code: 项目编号（可能包含多个项目）
            project_type: 项目类型名称（可选）
            
        Returns:
            MultiProjectMatchResult: 匹配结果
        """
        logger.info(f"开始匹配项目: 编号={project_code}, 类型={project_type}")
        
        # 1. 解析项目编号
        parsed_result = self.parser.parse_project_code(project_code)
        logger.debug(f"项目编号解析结果: {parsed_result}")
        
        # 2. 确定项目类型（优先使用解析出的项目类型）
        project_type_id = None
        project_type_name = None
        normalization_notes = []

        # 优先使用解析出的标准化项目类型
        effective_project_type = None
        if parsed_result.normalized_project_type:
            effective_project_type = parsed_result.normalized_project_type
            logger.debug(f"使用解析出的标准化项目类型: {effective_project_type}")
        elif project_type:
            # 对传入的项目类型进行标准化处理
            normalization_result = project_type_normalizer.normalize_project_type(project_type)
            if normalization_result.normalized_type:
                effective_project_type = normalization_result.normalized_type
                normalization_notes.append(normalization_result.notes)
                logger.info(f"项目类型标准化: {project_type} → {effective_project_type} ({normalization_result.normalization_method})")
            else:
                effective_project_type = project_type
                normalization_notes.append(f"项目类型标准化失败: {normalization_result.notes}")
                logger.warning(f"项目类型标准化失败: {project_type} - {normalization_result.notes}")

        if effective_project_type:
            project_type_obj = self._find_project_type_by_name(effective_project_type)
            if project_type_obj:
                project_type_id = project_type_obj.id
                project_type_name = project_type_obj.dept_name
                logger.debug(f"找到项目类型: {project_type_name} (ID: {project_type_id})")
            else:
                logger.warning(f"未找到项目类型: {effective_project_type}")
        
        # 3. 匹配每个解析后的编号
        matches = []
        unmatched_codes = []
        
        for parsed_code in parsed_result.parsed_codes:
            match_result = self._match_single_project(parsed_code, project_type_id, project_type_name)
            if match_result.project_id:
                matches.append(match_result)
                logger.debug(f"成功匹配项目: {parsed_code} -> {match_result.project_name}")
            else:
                unmatched_codes.append(parsed_code)
                logger.debug(f"未匹配到项目: {parsed_code}")
        
        # 4. 计算总体置信度
        total_confidence = self._calculate_total_confidence(parsed_result, matches, unmatched_codes)
        
        # 5. 生成匹配说明
        notes = self._generate_match_notes(parsed_result, matches, unmatched_codes, normalization_notes)
        
        result = MultiProjectMatchResult(
            original_code=project_code,
            parsed_result=parsed_result,
            matches=matches,
            unmatched_codes=unmatched_codes,
            total_confidence=total_confidence,
            notes=notes
        )
        
        logger.info(f"项目匹配完成: 匹配{len(matches)}个项目，未匹配{len(unmatched_codes)}个编号")
        return result
    
    def _match_single_project(self, project_code: str, project_type_id: Optional[int] = None,
                             project_type_name: Optional[str] = None) -> ProjectMatchResult:
        """
        匹配单个项目

        Args:
            project_code: 项目编号
            project_type_id: 项目类型ID（可选）
            project_type_name: 项目类型名称（可选）

        Returns:
            ProjectMatchResult: 匹配结果
        """
        logger.debug(f"开始匹配单个项目: 编号={project_code}, 类型ID={project_type_id}, 类型名称={project_type_name}")

        # 1. 精确匹配（包含项目类型）
        if project_type_id:
            project = Import_project.query.filter_by(
                project_code=project_code,
                dept_id=project_type_id
            ).first()

            if project:
                logger.debug(f"精确匹配成功: {project_code} + {project_type_name}")
                return ProjectMatchResult(
                    project_id=project.id,
                    project_code=project.project_code,
                    project_name=project.project_name,
                    project_type_id=project.dept_id,
                    project_type_name=project_type_name,
                    match_type='exact_with_type',
                    confidence=1.0,
                    notes=f'精确匹配（含类型）: {project_code} in {project_type_name}'
                )
            else:
                logger.debug(f"精确匹配失败: 项目编号{project_code}在项目类型{project_type_name}中不存在")

        # 2. 精确匹配（不限项目类型）
        projects = Import_project.query.filter_by(project_code=project_code).all()
        logger.debug(f"查找项目编号{project_code}的所有项目: 找到{len(projects)}个")

        if len(projects) == 1:
            project = projects[0]
            # 获取项目类型名称
            project_type_obj = ProjectManageDept.query.get(project.dept_id)
            actual_type_name = project_type_obj.dept_name if project_type_obj else '未知类型'

            # 严格匹配模式：如果指定了项目类型但不匹配，则不绑定
            if project_type_name and actual_type_name != project_type_name:
                logger.warning(f"项目类型严格匹配失败: 期望{project_type_name}, 实际{actual_type_name}")

                # 返回无匹配结果，但提供详细说明
                return ProjectMatchResult(
                    project_id=None,
                    project_code=project_code,
                    project_name=None,
                    project_type_id=None,
                    project_type_name=None,
                    match_type='type_mismatch_strict',
                    confidence=0.0,
                    notes=f'严格匹配失败: 项目编号{project_code}存在但类型不匹配。期望类型:{project_type_name}, 实际类型:{actual_type_name}。如需绑定请使用手动绑定功能。'
                )
            else:
                # 类型匹配或未指定类型的情况
                confidence = 0.9 if not project_type_id else 0.7
                match_notes = f'精确匹配: {project_code} -> {actual_type_name}'

                logger.debug(f"单项目匹配成功: {match_notes}, 置信度: {confidence}")

                return ProjectMatchResult(
                    project_id=project.id,
                    project_code=project.project_code,
                    project_name=project.project_name,
                    project_type_id=project.dept_id,
                    project_type_name=actual_type_name,
                    match_type='exact_no_type' if not project_type_name else 'exact_with_type',
                    confidence=confidence,
                    notes=match_notes
                )
        elif len(projects) > 1:
            # 多个项目匹配，需要项目类型来区分
            project_types = []
            for p in projects:
                type_obj = ProjectManageDept.query.get(p.dept_id)
                type_name = type_obj.dept_name if type_obj else '未知'
                project_types.append(type_name)

            logger.warning(f"项目编号{project_code}存在多个项目: {project_types}")

            return ProjectMatchResult(
                project_id=None,
                project_code=project_code,
                project_name=None,
                project_type_id=None,
                project_type_name=None,
                match_type='ambiguous',
                confidence=0.0,
                notes=f'编号{project_code}存在多个项目，需要指定项目类型进行区分。可用类型: {", ".join(project_types)}'
            )

        # 3. 未找到匹配
        logger.warning(f"未找到项目编号{project_code}的任何项目")

        # 提供更详细的错误信息
        available_codes_query = Import_project.query.filter(
            Import_project.project_code.like(f'%{project_code}%')
        ).limit(5).all()

        similar_codes = [p.project_code for p in available_codes_query]

        error_notes = f'未找到匹配的项目编号: {project_code}'
        if similar_codes:
            error_notes += f'。相似的项目编号: {", ".join(similar_codes)}'

        if project_type_name:
            # 检查该项目类型下是否有其他项目
            type_projects = Import_project.query.filter_by(dept_id=project_type_id).limit(5).all() if project_type_id else []
            if type_projects:
                type_codes = [p.project_code for p in type_projects]
                error_notes += f'。{project_type_name}类型下的项目编号: {", ".join(type_codes)}'

        return ProjectMatchResult(
            project_id=None,
            project_code=project_code,
            project_name=None,
            project_type_id=None,
            project_type_name=None,
            match_type='no_match',
            confidence=0.0,
            notes=error_notes
        )
    
    def _find_project_type_by_name(self, type_name: str) -> Optional[ProjectManageDept]:
        """
        根据名称查找项目类型

        Args:
            type_name: 项目类型名称

        Returns:
            ProjectManageDept: 项目类型对象，未找到返回None
        """
        if not type_name:
            return None

        # 精确匹配
        project_type = ProjectManageDept.query.filter_by(dept_name=type_name.strip()).first()
        if project_type:
            return project_type

        # 模糊匹配
        project_type = ProjectManageDept.query.filter(
            ProjectManageDept.dept_name.contains(type_name.strip())
        ).first()

        return project_type
    
    def _calculate_total_confidence(self, parsed_result: ParsedProjectCode, 
                                  matches: List[ProjectMatchResult], 
                                  unmatched_codes: List[str]) -> float:
        """
        计算总体匹配置信度
        
        Args:
            parsed_result: 解析结果
            matches: 匹配的项目列表
            unmatched_codes: 未匹配的编号列表
            
        Returns:
            float: 总体置信度
        """
        if not parsed_result.parsed_codes:
            return 0.0
        
        total_codes = len(parsed_result.parsed_codes)
        matched_codes = len(matches)
        
        # 基础匹配率
        match_rate = matched_codes / total_codes
        
        # 考虑解析置信度
        parse_confidence = parsed_result.confidence
        
        # 考虑匹配质量
        if matches:
            avg_match_confidence = sum(m.confidence for m in matches) / len(matches)
        else:
            avg_match_confidence = 0.0
        
        # 综合计算
        total_confidence = (match_rate * 0.5 + parse_confidence * 0.3 + avg_match_confidence * 0.2)
        
        return min(total_confidence, 1.0)
    
    def _generate_match_notes(self, parsed_result: ParsedProjectCode,
                            matches: List[ProjectMatchResult],
                            unmatched_codes: List[str],
                            normalization_notes: List[str] = None) -> str:
        """
        生成匹配说明

        Args:
            parsed_result: 解析结果
            matches: 匹配的项目列表
            unmatched_codes: 未匹配的编号列表

        Returns:
            str: 匹配说明
        """
        notes_parts = []

        # 解析说明
        notes_parts.append(f"解析: {parsed_result.notes}")

        # 项目类型标准化说明
        if normalization_notes:
            for note in normalization_notes:
                notes_parts.append(f"类型标准化: {note}")

        # 匹配统计
        total_codes = len(parsed_result.parsed_codes)
        matched_count = len(matches)
        unmatched_count = len(unmatched_codes)

        notes_parts.append(f"匹配统计: 总计{total_codes}个编号，成功匹配{matched_count}个，未匹配{unmatched_count}个")

        # 匹配详情
        if matches:
            match_details = []
            for match in matches:
                # 根据匹配类型添加不同的标识
                if match.match_type == 'exact_with_type':
                    status_icon = "✓"
                elif match.match_type == 'exact_no_type':
                    status_icon = "○"
                elif match.match_type == 'type_mismatch':
                    status_icon = "⚠"
                else:
                    status_icon = "?"

                match_detail = f"{status_icon}{match.project_code}→{match.project_name}({match.project_type_name})"
                if match.confidence < 0.5:
                    match_detail += f"[置信度:{match.confidence:.1f}]"
                match_details.append(match_detail)
            notes_parts.append(f"匹配项目: {'; '.join(match_details)}")

        # 未匹配详情 - 包含具体原因
        if unmatched_codes:
            unmatched_details = []
            for code in unmatched_codes:
                # 查找对应的匹配结果以获取详细错误信息
                for match in matches:
                    if match.project_code == code and not match.project_id:
                        unmatched_details.append(f"{code}({match.notes})")
                        break
                else:
                    unmatched_details.append(code)

            notes_parts.append(f"未匹配编号: {'; '.join(unmatched_details)}")

        # 添加匹配建议
        if unmatched_count > 0:
            notes_parts.append("建议: 检查项目编号和项目类型是否正确，或使用手动绑定功能")

        return '; '.join(notes_parts)
