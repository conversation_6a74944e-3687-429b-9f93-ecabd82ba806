from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from applications.common.utils.rights import authorize
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.curd import model_to_dicts
from applications.services.holiday_service import HolidayService
from applications.services.holiday_import_service import HolidayImportService
from applications.models.holiday_config import HolidayConfig, HolidayOperationLog
from applications.schemas.holiday_config import (
    HolidayConfigSchema, HolidayConfigQuerySchema, 
    HolidayOperationLogSchema, HolidayImportSchema,
    HolidayBatchOperationSchema
)
from applications.extensions import db
from datetime import datetime, date
from marshmallow import ValidationError

bp = Blueprint('holiday_config', __name__, url_prefix='/holiday_config')


@bp.route('/')
@login_required
@authorize("system:holiday_config:main", log=True)
def main():
    """节假日配置主页"""
    years = list(range(2025, 2050))  # 支持2025-2049年
    return render_template('system/holiday_config/main.html', years=years)


@bp.route('/data')
@login_required
@authorize("system:holiday_config:main", log=True)
def data():
    """获取节假日配置数据"""
    try:
        # 获取查询参数
        args = request.args.to_dict()

        # 强化的参数过滤和清理
        filtered_args = {}
        for key, value in args.items():
            # 只处理我们关心的参数
            if key in ['year', 'holiday_type', 'keyword', 'status', 'page', 'limit']:
                # 过滤空字符串、None和'null'
                if value and value != '' and value.lower() != 'null':
                    filtered_args[key] = value

        # 验证查询参数
        query_schema = HolidayConfigQuerySchema()
        try:
            query_params = query_schema.load(filtered_args)
        except ValidationError as e:
            # 如果验证失败，尝试只保留基本参数
            basic_args = {}
            for key in ['page', 'limit']:
                if key in filtered_args:
                    basic_args[key] = filtered_args[key]
            try:
                query_params = query_schema.load(basic_args)
            except ValidationError:
                # 最后的兜底方案：使用默认参数
                query_params = query_schema.load({})
        
        # 构建查询
        query = HolidayConfig.query
        
        # 应用筛选条件
        if query_params.get('year'):
            query = query.filter(HolidayConfig.year == query_params['year'])
        
        if query_params.get('holiday_type'):
            query = query.filter(HolidayConfig.holiday_type == query_params['holiday_type'])
        
        if query_params.get('keyword'):
            keyword = f"%{query_params['keyword']}%"
            query = query.filter(HolidayConfig.holiday_name.like(keyword))
        
        if query_params.get('status') is not None:
            query = query.filter(HolidayConfig.status == query_params['status'])
        
        # 排序
        query = query.order_by(HolidayConfig.holiday_date.desc())
        
        # 分页
        page = query_params.get('page', 1)
        limit = query_params.get('limit', 10)
        
        pagination = query.paginate(
            page=page, 
            per_page=limit, 
            error_out=False
        )
        
        # 序列化数据
        schema = HolidayConfigSchema(many=True)
        data = []
        for item in pagination.items:
            item_dict = item.to_dict()
            data.append(item_dict)
        
        return table_api(data=data, count=pagination.total)
        
    except Exception as e:
        return fail_api(msg=f"获取数据失败: {str(e)}")


@bp.route('/add')
@login_required
@authorize("system:holiday_config:add", log=True)
def add():
    """添加节假日页面"""
    return render_template('system/holiday_config/add.html')


@bp.route('/save', methods=['POST'])
@login_required
@authorize("system:holiday_config:add", log=True)
def save():
    """保存节假日配置"""
    try:
        req_json = request.get_json(force=True)
        
        # 验证数据
        schema = HolidayConfigSchema()
        try:
            validated_data = schema.load(req_json)
        except ValidationError as e:
            return fail_api(msg=f"数据验证失败: {e.messages}")
        
        # 检查日期是否已存在
        existing = HolidayConfig.query.filter_by(
            holiday_date=validated_data['holiday_date']
        ).first()
        
        if existing:
            return fail_api(msg="该日期已存在节假日配置")
        
        # 创建新记录
        holiday_config = HolidayConfig(
            holiday_date=validated_data['holiday_date'],
            holiday_name=validated_data['holiday_name'],
            holiday_type=validated_data['holiday_type'],
            overtime_rate=validated_data['overtime_rate'],
            year=validated_data['year'],
            description=validated_data.get('description'),
            created_by=current_user.id,
            status=validated_data.get('status', 1)
        )
        
        db.session.add(holiday_config)
        db.session.commit()
        
        # 记录操作日志
        HolidayService._log_operation(
            'create', 
            holiday_config.holiday_date, 
            None, 
            holiday_config.to_dict(), 
            current_user.id,
            '手动添加节假日配置'
        )
        
        return success_api(msg="添加节假日配置成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"添加失败: {str(e)}")


@bp.route('/edit')
@login_required
@authorize("system:holiday_config:edit", log=True)
def edit():
    """编辑节假日页面"""
    holiday_id = request.args.get('id')
    if not holiday_id:
        return fail_api(msg="缺少节假日ID")
    
    holiday = HolidayConfig.query.get(holiday_id)
    if not holiday:
        return fail_api(msg="节假日配置不存在")
    
    return render_template('system/holiday_config/edit.html', holiday=holiday.to_dict())


@bp.route('/update', methods=['POST'])
@login_required
@authorize("system:holiday_config:edit", log=True)
def update():
    """更新节假日配置"""
    try:
        req_json = request.get_json(force=True)
        holiday_id = req_json.get('id')
        
        if not holiday_id:
            return fail_api(msg="缺少节假日ID")
        
        holiday = HolidayConfig.query.get(holiday_id)
        if not holiday:
            return fail_api(msg="节假日配置不存在")
        
        # 准备验证数据（移除不需要验证的字段）
        validation_data = req_json.copy()
        validation_data.pop('id', None)  # 移除id字段，因为它是dump_only的

        # 验证数据
        schema = HolidayConfigSchema()
        try:
            validated_data = schema.load(validation_data)
        except ValidationError as e:
            return fail_api(msg=f"数据验证失败: {e.messages}")
        
        # 检查日期是否与其他记录冲突
        if validated_data['holiday_date'] != holiday.holiday_date:
            existing = HolidayConfig.query.filter_by(
                holiday_date=validated_data['holiday_date']
            ).first()
            if existing:
                return fail_api(msg="该日期已存在其他节假日配置")
        
        # 记录修改前的数据
        old_data = holiday.to_dict()
        
        # 更新数据
        holiday.holiday_date = validated_data['holiday_date']
        holiday.holiday_name = validated_data['holiday_name']
        holiday.holiday_type = validated_data['holiday_type']
        holiday.overtime_rate = validated_data['overtime_rate']
        holiday.year = validated_data['year']
        holiday.description = validated_data.get('description')
        holiday.status = validated_data.get('status', 1)
        holiday.updated_by = current_user.id
        holiday.updated_at = datetime.now()
        
        db.session.commit()
        
        # 记录操作日志
        HolidayService._log_operation(
            'update', 
            holiday.holiday_date, 
            old_data, 
            holiday.to_dict(), 
            current_user.id,
            '手动更新节假日配置'
        )
        
        return success_api(msg="更新节假日配置成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"更新失败: {str(e)}")


@bp.route('/remove', methods=['POST'])
@login_required
@authorize("system:holiday_config:remove", log=True)
def remove():
    """删除节假日配置"""
    try:
        req_json = request.get_json(force=True)
        holiday_id = req_json.get('id')
        
        if not holiday_id:
            return fail_api(msg="缺少节假日ID")
        
        holiday = HolidayConfig.query.get(holiday_id)
        if not holiday:
            return fail_api(msg="节假日配置不存在")
        
        # 记录删除前的数据
        old_data = holiday.to_dict()
        
        db.session.delete(holiday)
        db.session.commit()
        
        # 记录操作日志
        HolidayService._log_operation(
            'delete', 
            holiday.holiday_date, 
            old_data, 
            None, 
            current_user.id,
            '手动删除节假日配置'
        )
        
        return success_api(msg="删除节假日配置成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败: {str(e)}")


@bp.route('/import_holidays', methods=['POST'])
@login_required
@authorize("system:holiday_config:import", log=True)
def import_holidays():
    """批量导入节假日"""
    try:
        req_json = request.get_json(force=True)
        
        # 验证导入参数
        schema = HolidayImportSchema()
        try:
            import_params = schema.load(req_json)
        except ValidationError as e:
            return fail_api(msg=f"导入参数错误: {e.messages}")
        
        # 执行导入
        result = HolidayService.import_from_holidays_lib(
            year=import_params['year'],
            operator_id=current_user.id,
            override_existing=import_params.get('override_existing', False)
        )
        
        if result['success']:
            return success_api(
                msg=f"导入完成！新增{result['imported_count']}条，更新{result['updated_count']}条，跳过{result['skipped_count']}条",
                data=result
            )
        else:
            return fail_api(msg=f"导入失败: {result['error']}")
        
    except Exception as e:
        return fail_api(msg=f"导入失败: {str(e)}")


@bp.route('/batch_operation', methods=['POST'])
@login_required
@authorize("system:holiday_config:edit", log=True)
def batch_operation():
    """批量操作节假日配置"""
    try:
        req_json = request.get_json(force=True)
        
        # 验证批量操作参数
        schema = HolidayBatchOperationSchema()
        try:
            batch_params = schema.load(req_json)
        except ValidationError as e:
            return fail_api(msg=f"批量操作参数错误: {e.messages}")
        
        # 执行批量操作
        result = HolidayService.batch_operation(
            ids=batch_params['ids'],
            operation=batch_params['operation'],
            operator_id=current_user.id
        )
        
        if result['success']:
            return success_api(msg=result['message'], data={'count': result['count']})
        else:
            return fail_api(msg=result['message'])
        
    except Exception as e:
        return fail_api(msg=f"批量操作失败: {str(e)}")


@bp.route('/logs')
@login_required
@authorize("system:holiday_config:main", log=True)
def logs():
    """操作日志页面"""
    return render_template('system/holiday_config/logs.html')


@bp.route('/logs_data')
@login_required
@authorize("system:holiday_config:main", log=True)
def logs_data():
    """获取操作日志数据"""
    try:
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)

        # 构建查询条件
        query = HolidayOperationLog.query

        # 操作类型过滤
        operation_type = request.args.get('operation_type')
        if operation_type:
            query = query.filter(HolidayOperationLog.operation_type == operation_type)

        # 操作人过滤
        operator_name = request.args.get('operator_name')
        if operator_name:
            query = query.filter(HolidayOperationLog.operator_name.like(f'%{operator_name}%'))

        # 时间范围过滤
        start_date = request.args.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
                query = query.filter(HolidayOperationLog.operation_time >= start_datetime)
            except ValueError:
                pass

        end_date = request.args.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
                query = query.filter(HolidayOperationLog.operation_time <= end_datetime)
            except ValueError:
                pass

        # 排序和分页
        query = query.order_by(HolidayOperationLog.operation_time.desc())
        pagination = query.paginate(
            page=page,
            per_page=limit,
            error_out=False
        )

        # 序列化数据
        data = []
        for log in pagination.items:
            log_dict = log.to_dict()
            # 格式化操作类型
            operation_type_map = {
                'create': '创建',
                'update': '更新',
                'delete': '删除',
                'import': '导入'
            }
            log_dict['operation_type_text'] = operation_type_map.get(log.operation_type, log.operation_type)

            # 格式化数据变更内容
            if log.old_data or log.new_data:
                log_dict['changes_html'] = format_data_changes(log.old_data, log.new_data)
            else:
                log_dict['changes_html'] = log.remark or '无详细信息'

            data.append(log_dict)

        return table_api(data=data, count=pagination.total)

    except Exception as e:
        return fail_api(msg=f"获取日志失败: {str(e)}")


def format_data_changes(old_data, new_data):
    """格式化数据变更为HTML"""
    if not old_data and not new_data:
        return "无数据变更"

    # 确保数据不为None
    old_data = old_data or {}
    new_data = new_data or {}

    html = "<div class='data-changes'>"

    if old_data and new_data:
        # 对比变更
        html += "<h5>数据变更对比：</h5>"
        html += "<table class='layui-table' lay-size='sm'>"
        html += "<thead><tr><th>字段</th><th>修改前</th><th>修改后</th></tr></thead><tbody>"

        # 合并所有字段
        all_keys = set(old_data.keys()) | set(new_data.keys())
        field_map = {
            'holiday_date': '节假日日期',
            'holiday_name': '节假日名称',
            'holiday_type': '节假日类型',
            'overtime_rate': '加班倍率',
            'description': '备注说明',
            'status': '状态'
        }

        for key in sorted(all_keys):
            if key in ['created_at', 'updated_at', 'created_by', 'updated_by']:
                continue

            old_val = old_data.get(key, '-')
            new_val = new_data.get(key, '-')

            if old_val != new_val:
                field_name = field_map.get(key, key)

                # 特殊格式化
                if key == 'holiday_type':
                    type_map = {
                        'legal': '法定节假日(3倍)',
                        'makeup': '调休日(2倍)',
                        'weekend': '周末(2倍)',
                        'company_rest': '公司调休休息日(2倍)',
                        'company_work': '公司调休工作日(1.5倍)'
                    }
                    old_val = type_map.get(str(old_val), old_val)
                    new_val = type_map.get(str(new_val), new_val)
                elif key == 'status':
                    old_val = '启用' if old_val == 1 else '禁用'
                    new_val = '启用' if new_val == 1 else '禁用'

                html += f"<tr><td>{field_name}</td><td>{old_val}</td><td><strong>{new_val}</strong></td></tr>"

        html += "</tbody></table>"
    elif new_data:
        # 新增数据
        html += "<h5>新增数据：</h5>"
        html += "<table class='layui-table' lay-size='sm'>"
        html += "<thead><tr><th>字段</th><th>值</th></tr></thead><tbody>"

        field_map = {
            'holiday_date': '节假日日期',
            'holiday_name': '节假日名称',
            'holiday_type': '节假日类型',
            'overtime_rate': '加班倍率',
            'description': '备注说明'
        }

        for key, value in new_data.items():
            if key in ['created_at', 'updated_at', 'created_by', 'updated_by', 'id']:
                continue
            field_name = field_map.get(key, key)

            if key == 'holiday_type':
                type_map = {
                    'legal': '法定节假日(3倍)',
                    'makeup': '调休日(2倍)',
                    'weekend': '周末(2倍)',
                    'company_rest': '公司调休休息日(2倍)',
                    'company_work': '公司调休工作日(1.5倍)'
                }
                value = type_map.get(str(value), value)

            html += f"<tr><td>{field_name}</td><td>{value}</td></tr>"

        html += "</tbody></table>"

    html += "</div>"
    return html


@bp.route('/calendar')
@login_required
@authorize("system:holiday_config:main", log=True)
def calendar():
    """日历视图页面"""
    year = request.args.get('year', datetime.now().year, type=int)
    return render_template('system/holiday_config/calendar.html', year=year)


@bp.route('/calendar_data')
@login_required
@authorize("system:holiday_config:main", log=True)
def calendar_data():
    """获取日历数据"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)

        # 查询指定年份的节假日配置
        holidays = HolidayConfig.query.filter_by(year=year, status=1).all()

        # 转换为日历格式
        calendar_data = []
        for holiday in holidays:
            calendar_data.append({
                'date': holiday.holiday_date.strftime('%Y-%m-%d'),
                'title': holiday.holiday_name,
                'type': holiday.holiday_type,
                'rate': float(holiday.overtime_rate),
                'description': holiday.description
            })

        return success_api(data=calendar_data)

    except Exception as e:
        return fail_api(msg=f"获取日历数据失败: {str(e)}")


@bp.route('/preview_import')
@login_required
@authorize("system:holiday_config:import", log=True)
def preview_import():
    """预览导入节假日"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)

        # 获取预览数据
        preview_data = HolidayImportService.preview_import(year)

        return success_api(data=preview_data)

    except Exception as e:
        return fail_api(msg=f"预览导入失败: {str(e)}")


@bp.route('/fix_holiday_types', methods=['POST'])
@login_required
@authorize("system:holiday_config:edit", log=True)
def fix_holiday_types():
    """修正节假日类型"""
    try:
        # 获取所有标记为法定节假日的记录
        legal_holidays = HolidayConfig.query.filter_by(holiday_type='legal').all()

        fixed_count = 0
        fixed_details = []

        for holiday in legal_holidays:
            # 重新判断节假日类型
            new_type, new_rate = HolidayImportService._determine_holiday_type(
                holiday.holiday_name, holiday.holiday_date
            )

            # 如果类型发生变化，更新记录
            if new_type != holiday.holiday_type or new_rate != holiday.overtime_rate:
                old_type = holiday.holiday_type
                old_rate = holiday.overtime_rate

                holiday.holiday_type = new_type
                holiday.overtime_rate = new_rate

                fixed_details.append({
                    'date': holiday.holiday_date.strftime('%Y-%m-%d'),
                    'name': holiday.holiday_name,
                    'old_type': old_type,
                    'old_rate': old_rate,
                    'new_type': new_type,
                    'new_rate': new_rate
                })

                fixed_count += 1

        # 提交更改
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功修正了 {fixed_count} 个节假日记录',
            'data': {
                'fixed_count': fixed_count,
                'details': fixed_details
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'修正失败: {str(e)}'
        })


@bp.route('/import_from_holidays', methods=['POST'])
@login_required
@authorize("system:holiday_config:import", log=True)
def import_from_holidays():
    """从holidays库导入节假日"""
    try:
        req_json = request.get_json(force=True)
        year = req_json.get('year', datetime.now().year)
        overwrite = req_json.get('overwrite', False)

        # 执行导入
        result = HolidayImportService.import_china_holidays(
            year=year,
            created_by=current_user.id,
            overwrite=overwrite
        )

        if result['errors'] == 0:
            return success_api(
                msg=f"导入成功！共导入{result['imported']}条节假日，跳过{result['skipped']}条",
                data=result
            )
        else:
            return fail_api(
                msg=f"导入完成，但有{result['errors']}个错误。成功{result['imported']}条，跳过{result['skipped']}条",
                data=result
            )

    except Exception as e:
        return fail_api(msg=f"导入失败: {str(e)}")


@bp.route('/get_missing_holidays')
@login_required
@authorize("system:holiday_config:main", log=True)
def get_missing_holidays():
    """获取缺失的节假日"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)

        # 获取缺失的节假日
        missing_holidays = HolidayImportService.get_missing_holidays(year)

        return success_api(data={
            'year': year,
            'missing_count': len(missing_holidays),
            'missing_holidays': missing_holidays
        })

    except Exception as e:
        return fail_api(msg=f"获取缺失节假日失败: {str(e)}")
