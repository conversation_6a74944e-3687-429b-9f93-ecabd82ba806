<!DOCTYPE html>
<html>
<head>
    <title>编辑外协信息</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <input type="hidden" name="id" value="{{ outsourcing_info.id }}">
    <div class="mainBox">
        <div class="main-container">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">外协名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="outsourcingName" lay-verify="required" autocomplete="off" 
                                   placeholder="请输入外协名称" class="layui-input" value="{{ outsourcing_info.outsourcing_name }}">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系人</label>
                        <div class="layui-input-block">
                            <input type="text" name="contactPerson" autocomplete="off" 
                                   placeholder="请输入联系人" class="layui-input" value="{{ outsourcing_info.contact_person or '' }}">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系电话</label>
                        <div class="layui-input-block">
                            <input type="text" name="contactPhone" lay-verify="phone" autocomplete="off" 
                                   placeholder="请输入联系电话" class="layui-input" value="{{ outsourcing_info.contact_phone or '' }}">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系邮箱</label>
                        <div class="layui-input-block">
                            <input type="email" name="contactEmail" lay-verify="email" autocomplete="off" 
                                   placeholder="请输入联系邮箱" class="layui-input" value="{{ outsourcing_info.contact_email or '' }}">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">公司地址</label>
                <div class="layui-input-block">
                    <input type="text" name="companyAddress" autocomplete="off" 
                           placeholder="请输入公司地址" class="layui-input" value="{{ outsourcing_info.company_address or '' }}">
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">资质等级</label>
                        <div class="layui-input-block">
                            <select name="qualificationLevel">
                                <option value="">请选择资质等级</option>
                                <option value="一级" {{ 'selected' if outsourcing_info.qualification_level == '一级' else '' }}>一级</option>
                                <option value="二级" {{ 'selected' if outsourcing_info.qualification_level == '二级' else '' }}>二级</option>
                                <option value="三级" {{ 'selected' if outsourcing_info.qualification_level == '三级' else '' }}>三级</option>
                                <option value="其他" {{ 'selected' if outsourcing_info.qualification_level == '其他' else '' }}>其他</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">时薪(元/小时)</label>
                        <div class="layui-input-block">
                            <input type="number" name="hourlyRate" autocomplete="off"
                                   placeholder="请输入时薪" class="layui-input" step="0.01" min="0"
                                   value="{{ outsourcing_info.hourly_rate or '' }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">合作状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="cooperationStatus" value="1" title="正常" {{ 'checked' if outsourcing_info.cooperation_status == 1 else '' }}>
                    <input type="radio" name="cooperationStatus" value="0" title="停用" {{ 'checked' if outsourcing_info.cooperation_status == 0 else '' }}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">业务范围</label>
                <div class="layui-input-block">
                    <textarea name="businessScope" placeholder="请输入业务范围描述" 
                              class="layui-textarea" rows="3">{{ outsourcing_info.business_scope or '' }}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" 
                              class="layui-textarea" rows="2">{{ outsourcing_info.remark or '' }}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="outsourcing-update">
                        <i class="layui-icon layui-icon-ok"></i>
                        更新
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'popup', 'jquery'], function () {
        let form = layui.form
        let popup = layui.popup
        let $ = layui.jquery

        const MODULE_PATH = "/system/outsourcing_info/"

        // 表单提交
        form.on('submit(outsourcing-update)', function (data) {
            let loading = layer.load()
            $.ajax({
                url: MODULE_PATH + 'update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg, function () {
                            let index = parent.layer.getFrameIndex(window.name)
                            parent.layer.close(index)
                        })
                    } else {
                        popup.failure(result.msg)
                    }
                },
                error: function () {
                    layer.close(loading)
                    popup.failure('网络异常')
                }
            })
            return false
        })
    })
</script>
</body>
</html>
