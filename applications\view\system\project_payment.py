from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, or_, desc, cast

from applications.common import curd
from applications.common.utils import validate
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.common.admin import admin_log
from applications.models import Import_project, DictData, Dept, ProjectProgress, ProjectPayment, PaymentHistory, PaymentApproval, ProjectManageDept
from applications.view.system.project_progress import calculate_project_status, DEFAULT_STATUS_NAMES


def calculate_acceptance_incentive(project, progress, payment, payment_approvals=None):
    """
    计算验收激励金额
    规则：以验收日期后第100天为基准
    - 提前回款：每提前1天奖励 验收款额 × 0.1‰
    - 延迟回款：每延迟1天扣除 验收款额 × 0.1‰
    - 分次回款：按每次金额和时间单独计算后累加
    """
    # 检查是否有验收日期
    if not progress or not progress.acceptance_date:
        return {'incentive_amount': 0, 'incentive_status': '未验收', 'incentive_days': 0}

    # 检查是否有验收款
    if not payment or payment.acceptance_payment <= 0:
        return {'incentive_amount': 0, 'incentive_status': '无验收款', 'incentive_days': 0}

    # 检查是否已回款
    if payment.actual_acceptance_payment <= 0:
        return {'incentive_amount': 0, 'incentive_status': '未回款', 'incentive_days': 0}

    total_incentive = 0
    incentive_details = []

    # 获取验收款相关的回款记录
    if payment_approvals:
        acceptance_approvals = [a for a in payment_approvals
                              if a.payment_type == 'acceptance_payment' and a.payment_date and a.actual_amount > 0]

        if acceptance_approvals:
            # 分次回款计算
            for approval in acceptance_approvals:
                # 确保日期是date对象
                payment_date = approval.payment_date
                if hasattr(payment_date, 'date'):
                    payment_date = payment_date.date()

                acceptance_date = progress.acceptance_date
                if hasattr(acceptance_date, 'date'):
                    acceptance_date = acceptance_date.date()

                days_diff = (payment_date - acceptance_date).days
                if days_diff < 100:  # 提前
                    incentive = approval.actual_amount * 0.0001 * (100 - days_diff)
                    incentive_details.append(f'提前{100-days_diff}天')
                elif days_diff > 100:  # 延迟
                    incentive = -approval.actual_amount * 0.0001 * (days_diff - 100)
                    incentive_details.append(f'延迟{days_diff-100}天')
                else:  # 准时
                    incentive = 0
                    incentive_details.append('准时')
                total_incentive += incentive
        else:
            # 没有详细回款记录，使用payment表的回款日期
            if payment.acceptance_payment_date:
                # 确保日期是date对象
                payment_date = payment.acceptance_payment_date
                if hasattr(payment_date, 'date'):
                    payment_date = payment_date.date()

                acceptance_date = progress.acceptance_date
                if hasattr(acceptance_date, 'date'):
                    acceptance_date = acceptance_date.date()

                days_diff = (payment_date - acceptance_date).days
                if days_diff < 100:
                    total_incentive = payment.actual_acceptance_payment * 0.0001 * (100 - days_diff)
                    incentive_details.append(f'提前{100-days_diff}天')
                elif days_diff > 100:
                    total_incentive = -payment.actual_acceptance_payment * 0.0001 * (days_diff - 100)
                    incentive_details.append(f'延迟{days_diff-100}天')
                else:
                    total_incentive = 0
                    incentive_details.append('准时')
            else:
                return {'incentive_amount': 0, 'incentive_status': '未记录回款日期', 'incentive_days': 0}
    else:
        # 没有payment_approvals，使用payment表的回款日期
        if payment.acceptance_payment_date:
            # 确保日期是date对象
            payment_date = payment.acceptance_payment_date
            if hasattr(payment_date, 'date'):
                payment_date = payment_date.date()

            acceptance_date = progress.acceptance_date
            if hasattr(acceptance_date, 'date'):
                acceptance_date = acceptance_date.date()

            days_diff = (payment_date - acceptance_date).days
            if days_diff < 100:
                total_incentive = payment.actual_acceptance_payment * 0.0001 * (100 - days_diff)
                incentive_details.append(f'提前{100-days_diff}天')
            elif days_diff > 100:
                total_incentive = -payment.actual_acceptance_payment * 0.0001 * (days_diff - 100)
                incentive_details.append(f'延迟{days_diff-100}天')
            else:
                total_incentive = 0
                incentive_details.append('准时')
        else:
            return {'incentive_amount': 0, 'incentive_status': '未记录回款日期', 'incentive_days': 0}

    # 生成状态描述
    if len(incentive_details) == 1:
        status = incentive_details[0]
    else:
        status = f'分{len(incentive_details)}次回款'

    return {
        'incentive_amount': round(total_incentive, 2),
        'incentive_status': status,
        'incentive_days': 0  # 可以后续扩展使用
    }


def calculate_payment_status(project, progress, payment, calculated_status=None):
    """
    根据项目进度信息、付款天数和实际回款金额计算项目回款状态

    参数：
    - project: 项目对象
    - progress: 项目进度对象
    - payment: 项目付款对象
    - calculated_status: 计算得到的项目状态（如果为None则使用project.project_status）

    回款状态规则：
    - 如果实际回款金额 >= 应收金额，则标记为"已回款"(1)
    - 如果实际回款金额 > 0 但 < 应收金额，则标记为"部分回款"(3)
    - 如果实际回款金额为0且已超过付款期限，则标记为"未回款"(0)
    - 如果实际回款金额为0且尚未超过付款期限，则标记为"未到期"(2)

    付款期限计算规则：
    - 预付款：从合同开始日期（project_start_date）算起，经过预付款天数后，状态变为"未回款"
    - 发货款：从发货完成日期（shipping_end_date）算起，经过发货款天数后，状态变为"未回款"
    - 验收款：从验收完成日期（acceptance_date）算起，经过验收款天数后，状态变为"未回款"
    - 质保金：从验收完成日期（acceptance_date）算起，经过质保金天数后，状态变为"未回款"

    返回值：
    {
        'prepayment_status': 0/1/2/3,  # 0: 未回款, 1: 已回款, 2: 未到期, 3: 部分回款
        'delivery_payment_status': 0/1/2/3,
        'acceptance_payment_status': 0/1/2/3,
        'warranty_payment_status': 0/1/2/3
    }
    """
    # 使用传入的计算状态，如果没有则使用项目原始状态
    project_status = calculated_status if calculated_status is not None else project.project_status
    today = datetime.now().date()
    result = {
        'prepayment_status': 0,  # 默认为未回款
        'delivery_payment_status': 2,  # 默认为未到期
        'acceptance_payment_status': 2,  # 默认为未到期
        'warranty_payment_status': 2   # 默认为未到期
    }

    # 如果有付款记录，检查各类实际回款金额
    if payment:
        # 预付款：检查实际回款金额
        if payment.prepayment <= 0:
            result['prepayment_status'] = 4  # 无需回款（应收金额为0）
        elif payment.actual_prepayment >= payment.prepayment and payment.prepayment > 0:
            result['prepayment_status'] = 1  # 已回款（全部）
        elif payment.actual_prepayment > 0:
            result['prepayment_status'] = 3  # 部分回款
        else:
            # 检查是否有合同开始日期和预付款天数
            if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
                # 计算预付款到期日
                prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
                prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
                if today > prepayment_due_date:
                    result['prepayment_status'] = 0  # 未回款（已到期）
                else:
                    result['prepayment_status'] = 2  # 未到期
            else:
                # 如果没有合同开始日期或预付款天数，则默认为未回款
                result['prepayment_status'] = 0  # 未回款

        # 发货款：检查实际回款金额
        if payment.delivery_payment <= 0:
            result['delivery_payment_status'] = 4  # 无需回款（应收金额为0）
        elif payment.actual_delivery_payment >= payment.delivery_payment and payment.delivery_payment > 0:
            result['delivery_payment_status'] = 1  # 已回款（全部）
        elif payment.actual_delivery_payment > 0:
            result['delivery_payment_status'] = 3  # 部分回款
        else:
            # 检查是否有发货完成日期和发货款天数
            if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
                # 计算发货款到期日
                delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
                delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
                if today > delivery_due_date:
                    result['delivery_payment_status'] = 0  # 未回款（已到期）
                else:
                    result['delivery_payment_status'] = 2  # 未到期
            elif project_status >= 4:  # 如果没有日期但项目状态已达到发货完成，则默认为未回款
                result['delivery_payment_status'] = 0  # 未回款
            else:
                result['delivery_payment_status'] = 2  # 未到期

        # 验收款：检查实际回款金额
        if payment.acceptance_payment <= 0:
            result['acceptance_payment_status'] = 4  # 无需回款（应收金额为0）
        elif payment.actual_acceptance_payment >= payment.acceptance_payment and payment.acceptance_payment > 0:
            result['acceptance_payment_status'] = 1  # 已回款（全部）
        elif payment.actual_acceptance_payment > 0:
            result['acceptance_payment_status'] = 3  # 部分回款
        else:
            # 检查是否有验收完成日期和验收款天数
            if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
                # 计算验收款到期日
                acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
                acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
                if today > acceptance_due_date:
                    result['acceptance_payment_status'] = 0  # 未回款（已到期）
                else:
                    result['acceptance_payment_status'] = 2  # 未到期
            elif project_status >= 11:  # 如果没有日期但项目状态已达到验收完成，则默认为未回款
                result['acceptance_payment_status'] = 0  # 未回款
            else:
                result['acceptance_payment_status'] = 2  # 未到期

        # 质保金：检查实际回款金额
        if payment.warranty_payment <= 0:
            result['warranty_payment_status'] = 4  # 无需回款（应收金额为0）
        elif payment.actual_warranty_payment >= payment.warranty_payment and payment.warranty_payment > 0:
            result['warranty_payment_status'] = 1  # 已回款（全部）
        elif payment.actual_warranty_payment > 0:
            result['warranty_payment_status'] = 3  # 部分回款
        else:
            # 检查是否有验收完成日期和质保金天数
            if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
                # 计算质保金到期日
                warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 0
                warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
                if today > warranty_due_date:
                    result['warranty_payment_status'] = 0  # 未回款（已到期）
                else:
                    result['warranty_payment_status'] = 2  # 未到期
            elif project_status >= 11 and progress and progress.acceptance_date:
                # 如果没有质保金天数但项目已完成且有验收日期，则使用默认的一年质保期
                warranty_due_date = progress.acceptance_date + timedelta(days=365)
                if today > warranty_due_date:
                    result['warranty_payment_status'] = 0  # 未回款（已到期）
                else:
                    result['warranty_payment_status'] = 2  # 未到期
            else:
                result['warranty_payment_status'] = 2  # 未到期

    return result


def calculate_payment_status_with_days(project, progress, payment, calculated_status=None):
    """
    计算带天数信息的项目回款状态显示文本

    参数：
    - project: 项目对象
    - progress: 项目进度对象
    - payment: 项目付款对象
    - calculated_status: 计算得到的项目状态（如果为None则使用project.project_status）

    返回值：
    {
        'prepayment_status_display': '未回款（已超期3天）',
        'delivery_payment_status_display': '未到期（还剩5天）',
        'acceptance_payment_status_display': '部分回款（还剩10天）',
        'warranty_payment_status_display': '已回款'
    }
    """
    # 先获取基础状态
    basic_status = calculate_payment_status(project, progress, payment, calculated_status)

    # 使用传入的计算状态，如果没有则使用项目原始状态
    project_status = calculated_status if calculated_status is not None else project.project_status
    today = datetime.now().date()

    result = {}

    # 处理预付款状态
    prepayment_status = basic_status['prepayment_status']
    if prepayment_status == 4:
        result['prepayment_status_display'] = '无需回款'
    elif prepayment_status == 1:
        result['prepayment_status_display'] = '已回款'
    elif prepayment_status == 0:
        # 未回款，计算超期天数
        if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
            prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
            prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
            overdue_days = (today - prepayment_due_date).days
            if overdue_days > 0:
                result['prepayment_status_display'] = f'未回款（已超期{overdue_days}天）'
            else:
                result['prepayment_status_display'] = '未回款'
        else:
            result['prepayment_status_display'] = '未回款'
    elif prepayment_status == 2:
        # 未到期，计算剩余天数
        if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
            prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
            prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
            remaining_days = (prepayment_due_date - today).days
            if remaining_days > 0:
                result['prepayment_status_display'] = f'未到期（还剩{remaining_days}天）'
            else:
                result['prepayment_status_display'] = '未到期'
        else:
            result['prepayment_status_display'] = '未到期'
    elif prepayment_status == 3:
        # 部分回款，判断是否超期
        if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
            prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
            prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
            days_diff = (today - prepayment_due_date).days
            if days_diff > 0:
                result['prepayment_status_display'] = f'部分回款（已超期{days_diff}天）'
            elif days_diff < 0:
                result['prepayment_status_display'] = f'部分回款（还剩{abs(days_diff)}天）'
            else:
                result['prepayment_status_display'] = '部分回款（今日到期）'
        else:
            result['prepayment_status_display'] = '部分回款'

    # 处理发货款状态
    delivery_payment_status = basic_status['delivery_payment_status']
    if delivery_payment_status == 4:
        result['delivery_payment_status_display'] = '无需回款'
    elif delivery_payment_status == 1:
        result['delivery_payment_status_display'] = '已回款'
    elif delivery_payment_status == 0:
        # 未回款，计算超期天数
        if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
            delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
            delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
            overdue_days = (today - delivery_due_date).days
            if overdue_days > 0:
                result['delivery_payment_status_display'] = f'未回款（已超期{overdue_days}天）'
            else:
                result['delivery_payment_status_display'] = '未回款'
        else:
            result['delivery_payment_status_display'] = '未回款'
    elif delivery_payment_status == 2:
        # 未到期，计算剩余天数
        if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
            delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
            delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
            remaining_days = (delivery_due_date - today).days
            if remaining_days > 0:
                result['delivery_payment_status_display'] = f'未到期（还剩{remaining_days}天）'
            else:
                result['delivery_payment_status_display'] = '未到期'
        else:
            result['delivery_payment_status_display'] = '未到期'
    elif delivery_payment_status == 3:
        # 部分回款，判断是否超期
        if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
            delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
            delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
            days_diff = (today - delivery_due_date).days
            if days_diff > 0:
                result['delivery_payment_status_display'] = f'部分回款（已超期{days_diff}天）'
            elif days_diff < 0:
                result['delivery_payment_status_display'] = f'部分回款（还剩{abs(days_diff)}天）'
            else:
                result['delivery_payment_status_display'] = '部分回款（今日到期）'
        else:
            result['delivery_payment_status_display'] = '部分回款'

    # 处理验收款状态
    acceptance_payment_status = basic_status['acceptance_payment_status']
    if acceptance_payment_status == 4:
        result['acceptance_payment_status_display'] = '无需回款'
    elif acceptance_payment_status == 1:
        result['acceptance_payment_status_display'] = '已回款'
    elif acceptance_payment_status == 0:
        # 未回款，计算超期天数
        if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
            acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
            acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
            overdue_days = (today - acceptance_due_date).days
            if overdue_days > 0:
                result['acceptance_payment_status_display'] = f'未回款（已超期{overdue_days}天）'
            else:
                result['acceptance_payment_status_display'] = '未回款'
        else:
            result['acceptance_payment_status_display'] = '未回款'
    elif acceptance_payment_status == 2:
        # 未到期，计算剩余天数
        if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
            acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
            acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
            remaining_days = (acceptance_due_date - today).days
            if remaining_days > 0:
                result['acceptance_payment_status_display'] = f'未到期（还剩{remaining_days}天）'
            else:
                result['acceptance_payment_status_display'] = '未到期'
        else:
            result['acceptance_payment_status_display'] = '未到期'
    elif acceptance_payment_status == 3:
        # 部分回款，判断是否超期
        if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
            acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
            acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
            days_diff = (today - acceptance_due_date).days
            if days_diff > 0:
                result['acceptance_payment_status_display'] = f'部分回款（已超期{days_diff}天）'
            elif days_diff < 0:
                result['acceptance_payment_status_display'] = f'部分回款（还剩{abs(days_diff)}天）'
            else:
                result['acceptance_payment_status_display'] = '部分回款（今日到期）'
        else:
            result['acceptance_payment_status_display'] = '部分回款'

    # 处理质保金状态
    warranty_payment_status = basic_status['warranty_payment_status']
    if warranty_payment_status == 4:
        result['warranty_payment_status_display'] = '无需回款'
    elif warranty_payment_status == 1:
        result['warranty_payment_status_display'] = '已回款'
    elif warranty_payment_status == 0:
        # 未回款，计算超期天数
        if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
            warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 365
            warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
            overdue_days = (today - warranty_due_date).days
            if overdue_days > 0:
                result['warranty_payment_status_display'] = f'未回款（已超期{overdue_days}天）'
            else:
                result['warranty_payment_status_display'] = '未回款'
        else:
            result['warranty_payment_status_display'] = '未回款'
    elif warranty_payment_status == 2:
        # 未到期，计算剩余天数
        if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
            warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 365
            warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
            remaining_days = (warranty_due_date - today).days
            if remaining_days > 0:
                result['warranty_payment_status_display'] = f'未到期（还剩{remaining_days}天）'
            else:
                result['warranty_payment_status_display'] = '未到期'
        else:
            result['warranty_payment_status_display'] = '未到期'
    elif warranty_payment_status == 3:
        # 部分回款，判断是否超期
        if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
            warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 365
            warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
            days_diff = (today - warranty_due_date).days
            if days_diff > 0:
                result['warranty_payment_status_display'] = f'部分回款（已超期{days_diff}天）'
            elif days_diff < 0:
                result['warranty_payment_status_display'] = f'部分回款（还剩{abs(days_diff)}天）'
            else:
                result['warranty_payment_status_display'] = '部分回款（今日到期）'
        else:
            result['warranty_payment_status_display'] = '部分回款'

    # 计算验收激励
    try:
        # 获取验收款相关的回款记录
        payment_approvals = None
        if payment:
            payment_approvals = PaymentApproval.query.filter_by(
                project_id=project.id,
                payment_type='acceptance_payment'
            ).all()

        incentive_info = calculate_acceptance_incentive(project, progress, payment, payment_approvals)
        result['acceptance_incentive_amount'] = incentive_info['incentive_amount']
        result['acceptance_incentive_status'] = incentive_info['incentive_status']

        # 生成显示文本
        if incentive_info['incentive_amount'] > 0:
            result['acceptance_incentive_display'] = f"+{incentive_info['incentive_amount']:.2f}元 ({incentive_info['incentive_status']})"
        elif incentive_info['incentive_amount'] < 0:
            result['acceptance_incentive_display'] = f"{incentive_info['incentive_amount']:.2f}元 ({incentive_info['incentive_status']})"
        else:
            result['acceptance_incentive_display'] = f"0元 ({incentive_info['incentive_status']})"
    except Exception as e:
        # 如果计算出错，设置默认值并记录错误信息
        print(f"验收激励计算出错: {str(e)}")  # 临时调试信息
        result['acceptance_incentive_amount'] = 0
        result['acceptance_incentive_status'] = f'计算错误: {str(e)}'
        result['acceptance_incentive_display'] = f'计算错误: {str(e)}'

    return result


bp = Blueprint('project_payment', __name__, url_prefix='/project_payment')


@bp.get('/')
@authorize("system:project_payment:main", log=True)
def main():
    """项目回款情况一览表页面"""
    # 获取项目状态字典数据
    project_status_options = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).order_by(cast(DictData.data_value, db.Integer)).all()

    return render_template('system/project_payment/main.html',
                         project_status_options=project_status_options)


@bp.get('/data')
@authorize("system:project_payment:main", log=True)
def data():
    """获取项目回款数据，支持分页、筛选和搜索"""
    # 获取请求参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    status = request.args.get('status', '', type=str)
    keyword = request.args.get('keyword', '', type=str)

    # 构建基础查询（不包含状态筛选，因为需要动态计算状态）
    query = db.session.query(Import_project, ProjectManageDept).outerjoin(
        ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
    )

    # 关键字搜索
    if keyword:
        query = query.filter(
            or_(
                Import_project.project_name.like(f'%{keyword}%'),
                Import_project.project_code.like(f'%{keyword}%')
            )
        )

    # 获取所有符合关键字条件的项目
    all_projects = query.all()

    # 在内存中进行状态筛选和计算
    filtered_projects = []
    for project, project_type in all_projects:
        # 获取项目进度信息
        progress = ProjectProgress.query.filter_by(project_id=project.id).first()

        # 根据项目进度信息计算项目状态
        calculated_status = calculate_project_status(progress) if progress else 0

        # 应用状态筛选条件
        if status and str(calculated_status) != status:
            continue

        filtered_projects.append((project, project_type, calculated_status))

    # 手动分页
    total_count = len(filtered_projects)
    start_index = (page - 1) * limit
    end_index = min(start_index + limit, total_count)
    paginated_projects = filtered_projects[start_index:end_index]

    # 创建分页对象模拟
    class MockPaginate:
        def __init__(self, items, total, page, per_page):
            self.items = items
            self.total = total
            self.page = page
            self.per_page = per_page
            self.pages = (total + per_page - 1) // per_page

    paginate = MockPaginate(paginated_projects, total_count, page, limit)

    # 准备返回数据
    items = []
    for project, project_type, calculated_status in paginate.items:
        # 获取项目进度信息
        progress = ProjectProgress.query.filter_by(project_id=project.id).first()

        # 获取计算后的状态名称
        status_dict = DictData.query.filter_by(type_code='project_status').all()
        status_map = {int(item.data_value): item.data_label for item in status_dict}
        calculated_status_name = status_map.get(calculated_status, '')

        # 如果状态名称为空，则使用默认名称
        if not calculated_status_name:
            calculated_status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

        # 获取项目付款信息
        payment = ProjectPayment.query.filter_by(project_id=project.id).first()

        # 计算回款状态
        payment_status = calculate_payment_status(project, progress, payment, calculated_status)

        # 计算带天数信息的回款状态显示文本
        payment_status_display = calculate_payment_status_with_days(project, progress, payment, calculated_status)

        # 计算质保天数
        warranty_days_remaining = None
        warranty_days_display = '-'

        if progress and progress.acceptance_date:
            # 获取质保金天数，如果没有则使用默认365天
            warranty_days = project.warranty_payment_days if hasattr(project, 'warranty_payment_days') and project.warranty_payment_days is not None else 365

            # 计算质保到期日期
            warranty_due_date = progress.acceptance_date + timedelta(days=warranty_days)

            # 计算剩余天数
            today = datetime.now().date()
            remaining_days = (warranty_due_date - today).days
            warranty_days_remaining = remaining_days

            if remaining_days > 0:
                warranty_days_display = f'剩余{remaining_days}天'
            elif remaining_days == 0:
                warranty_days_display = '今日到期'
            else:
                warranty_days_display = f'质保已过{abs(remaining_days)}天'
        else:
            warranty_days_display = '未验收'

        # 构建项目数据
        item = {
            'id': project.id,
            'project_name': project.project_name,
            'project_code': project.project_code,
            'project_type_name': project_type.dept_name if project_type else '',
            'project_status': calculated_status,
            'status_name': calculated_status_name,
            'price': project.price,
            'currency': project.currency if hasattr(project, 'currency') and project.currency else '人民币',  # 添加币种信息
            'prepayment': payment.prepayment if payment else 0,
            'delivery_payment': payment.delivery_payment if payment else 0,
            'acceptance_payment': payment.acceptance_payment if payment else 0,
            'warranty_payment': payment.warranty_payment if payment else 0,
            'actual_prepayment': payment.actual_prepayment if payment else 0,
            'actual_delivery_payment': payment.actual_delivery_payment if payment else 0,
            'actual_acceptance_payment': payment.actual_acceptance_payment if payment else 0,
            'actual_warranty_payment': payment.actual_warranty_payment if payment else 0,
            'prepayment_status': payment_status['prepayment_status'],
            'delivery_payment_status': payment_status['delivery_payment_status'],
            'acceptance_payment_status': payment_status['acceptance_payment_status'],
            'warranty_payment_status': payment_status['warranty_payment_status'],
            'prepayment_status_display': payment_status_display['prepayment_status_display'],
            'delivery_payment_status_display': payment_status_display['delivery_payment_status_display'],
            'acceptance_payment_status_display': payment_status_display['acceptance_payment_status_display'],
            'warranty_payment_status_display': payment_status_display['warranty_payment_status_display'],
            'warranty_days_remaining': warranty_days_remaining,
            'warranty_days_display': warranty_days_display,
            # 验收激励相关字段
            'acceptance_incentive_amount': payment_status_display.get('acceptance_incentive_amount', 0),
            'acceptance_incentive_status': payment_status_display.get('acceptance_incentive_status', '未计算'),
            'acceptance_incentive_display': payment_status_display.get('acceptance_incentive_display', '0元 (未计算)')
        }
        items.append(item)

    # 返回数据
    return table_api(
        msg='',
        count=paginate.total,
        data=items
    )


@bp.get('/statistics')
@authorize("system:project_payment:main", log=True)
def statistics():
    """获取项目回款统计数据"""
    try:
        # 获取所有项目
        projects = Import_project.query.all()

        # 初始化计数器
        total_projects = len(projects)
        paid_projects = 0
        unpaid_projects = 0
        partially_paid_projects = 0

        # 初始化各类型未付款项目计数器
        prepayment_unpaid_count = 0
        delivery_payment_unpaid_count = 0
        acceptance_payment_unpaid_count = 0
        warranty_payment_unpaid_count = 0

        # 遍历项目，统计回款情况
        for project in projects:
            # 获取项目进度信息
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress) if progress else 0

            # 获取项目付款信息
            payment = ProjectPayment.query.filter_by(project_id=project.id).first()

            if not payment:
                unpaid_projects += 1

                # 根据项目进度信息和付款天数判断各类型付款是否已到期
                today = datetime.now().date()

                # 预付款：检查是否有合同开始日期和预付款天数
                if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
                    # 计算预付款到期日
                    prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
                    prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
                    if today > prepayment_due_date:
                        prepayment_unpaid_count += 1
                else:
                    # 如果没有合同开始日期或预付款天数，则默认为未回款
                    prepayment_unpaid_count += 1

                # 发货款：检查是否有发货完成日期和发货款天数
                if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
                    # 计算发货款到期日
                    delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
                    delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
                    if today > delivery_due_date:
                        delivery_payment_unpaid_count += 1
                elif calculated_status >= 4:  # 如果没有日期但项目状态已达到发货完成，则默认为未回款
                    delivery_payment_unpaid_count += 1

                # 验收款：检查是否有验收完成日期和验收款天数
                if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
                    # 计算验收款到期日
                    acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
                    acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
                    if today > acceptance_due_date:
                        acceptance_payment_unpaid_count += 1
                elif calculated_status >= 11:  # 如果没有日期但项目状态已达到验收完成，则默认为未回款
                    acceptance_payment_unpaid_count += 1

                # 质保金：检查是否有验收完成日期和质保金天数
                if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
                    # 计算质保金到期日
                    warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 0
                    warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
                    if today > warranty_due_date:
                        warranty_payment_unpaid_count += 1
                elif calculated_status >= 11 and progress and progress.acceptance_date:
                    # 如果没有质保金天数但项目已完成且有验收日期，则使用默认的一年质保期
                    warranty_due_date = progress.acceptance_date + timedelta(days=365)
                    if today > warranty_due_date:
                        warranty_payment_unpaid_count += 1

                continue

            # 计算回款状态（使用已计算的状态）
            payment_status = calculate_payment_status(project, progress, payment, calculated_status)

            # 统计各种状态的付款类型数量
            due_payment_types = 0  # 已到期的付款类型数量
            paid_payment_types = 0  # 已回款的付款类型数量
            unpaid_payment_types = 0  # 未回款的付款类型数量
            not_due_payment_types = 0  # 未到期的付款类型数量

            # 检查各种付款类型的状态
            for status_key, status_value in payment_status.items():
                if status_value == 0 or status_value == 3:  # 未回款或部分回款
                    due_payment_types += 1
                    unpaid_payment_types += 1

                    # 统计各类型未付款项目（统计未回款和部分回款的项目，不包括未到期项目）
                    if status_key == 'prepayment_status' and payment.prepayment > 0:
                        prepayment_unpaid_count += 1
                    elif status_key == 'delivery_payment_status' and payment.delivery_payment > 0:
                        delivery_payment_unpaid_count += 1
                    elif status_key == 'acceptance_payment_status' and payment.acceptance_payment > 0:
                        acceptance_payment_unpaid_count += 1
                    elif status_key == 'warranty_payment_status' and payment.warranty_payment > 0:
                        warranty_payment_unpaid_count += 1

                elif status_value == 1:  # 已回款
                    due_payment_types += 1
                    paid_payment_types += 1
                elif status_value == 2:  # 未到期
                    not_due_payment_types += 1

            # 如果所有已到期的付款类型都已回款，则为已付款项目
            if due_payment_types > 0 and due_payment_types == paid_payment_types:
                paid_projects += 1
            # 如果有已到期的付款类型但没有任何回款，则为未付款项目
            elif due_payment_types > 0 and paid_payment_types == 0:
                unpaid_projects += 1
            # 如果有部分已到期的付款类型已回款，则为部分付款项目
            elif due_payment_types > 0 and paid_payment_types > 0 and paid_payment_types < due_payment_types:
                partially_paid_projects += 1
            # 如果所有付款类型都未到期，则为未付款项目
            elif not_due_payment_types > 0 and due_payment_types == 0:
                unpaid_projects += 1

        # 计算百分比
        paid_percentage = round(paid_projects / total_projects * 100 if total_projects > 0 else 0, 2)

        # 返回统计数据
        return jsonify({
            'success': True,
            'data': {
                'total_projects': total_projects,
                'paid_projects': paid_projects,
                'unpaid_projects': unpaid_projects,
                'partially_paid_projects': partially_paid_projects,
                'paid_percentage': paid_percentage,
                # 添加各类型未付款项目统计
                'prepayment_unpaid_count': prepayment_unpaid_count,
                'delivery_payment_unpaid_count': delivery_payment_unpaid_count,
                'acceptance_payment_unpaid_count': acceptance_payment_unpaid_count,
                'warranty_payment_unpaid_count': warranty_payment_unpaid_count
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        })


@bp.get('/api/unpaid_projects')
@authorize("system:project_payment:main", log=True)
def get_unpaid_projects():
    """获取未付款项目列表"""
    try:
        # 获取请求参数
        payment_type = request.args.get('payment_type', '')  # 付款类型：prepayment, delivery_payment, acceptance_payment, warranty_payment

        if not payment_type or payment_type not in ['prepayment', 'delivery_payment', 'acceptance_payment', 'warranty_payment']:
            return jsonify({
                'success': False,
                'message': '无效的付款类型'
            })

        # 获取所有项目及其项目类型
        projects = db.session.query(Import_project, ProjectManageDept).outerjoin(
            ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
        ).all()

        # 初始化未付款项目列表
        unpaid_projects = []

        # 遍历项目，查找未付款项目
        for project, project_type in projects:
            # 获取项目进度信息
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress) if progress else 0

            # 获取项目付款信息
            payment = ProjectPayment.query.filter_by(project_id=project.id).first()

            if not payment:
                # 获取计算后的状态名称
                status_dict = DictData.query.filter_by(type_code='project_status').all()
                status_map = {int(item.data_value): item.data_label for item in status_dict}
                status_name = status_map.get(calculated_status, '')

                # 如果状态名称为空，则使用默认名称
                if not status_name:
                    status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

                # 根据付款类型、项目进度信息和付款天数判断是否为未付款项目
                today = datetime.now().date()
                is_unpaid = False

                if payment_type == 'prepayment':
                    # 预付款：检查是否有合同开始日期和预付款天数
                    if progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
                        # 计算预付款到期日
                        prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
                        prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
                        if today > prepayment_due_date:
                            is_unpaid = True
                    else:
                        # 如果没有合同开始日期或预付款天数，则默认为未回款
                        is_unpaid = True

                elif payment_type == 'delivery_payment':
                    # 发货款：检查是否有发货完成日期和发货款天数
                    if progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
                        # 计算发货款到期日
                        delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
                        delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
                        if today > delivery_due_date:
                            is_unpaid = True
                    elif calculated_status >= 4:  # 如果没有日期但项目状态已达到发货完成，则默认为未回款
                        is_unpaid = True

                elif payment_type == 'acceptance_payment':
                    # 验收款：检查是否有验收完成日期和验收款天数
                    if progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
                        # 计算验收款到期日
                        acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
                        acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
                        if today > acceptance_due_date:
                            is_unpaid = True
                    elif calculated_status >= 11:  # 如果没有日期但项目状态已达到验收完成，则默认为未回款
                        is_unpaid = True

                elif payment_type == 'warranty_payment':
                    # 质保金：检查是否有验收完成日期和质保金天数
                    if progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
                        # 计算质保金到期日
                        warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 0
                        warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
                        if today > warranty_due_date:
                            is_unpaid = True
                    elif calculated_status >= 11 and progress and progress.acceptance_date:
                        # 如果没有质保金天数但项目已完成且有验收日期，则使用默认的一年质保期
                        warranty_due_date = progress.acceptance_date + timedelta(days=365)
                        if today > warranty_due_date:
                            is_unpaid = True

                # 如果判断为未付款，则添加到未付款项目列表
                if is_unpaid:
                    # 计算状态显示文本（没有payment记录的情况）
                    status_display_text = '未回款'
                    if payment_type == 'prepayment' and progress and progress.project_start_date and hasattr(project, 'prepayment_days'):
                        prepayment_days = project.prepayment_days if project.prepayment_days is not None else 0
                        prepayment_due_date = progress.project_start_date + timedelta(days=prepayment_days)
                        overdue_days = (today - prepayment_due_date).days
                        if overdue_days > 0:
                            status_display_text = f'未回款（已超期{overdue_days}天）'
                    elif payment_type == 'delivery_payment' and progress and progress.shipping_end_date and hasattr(project, 'delivery_payment_days'):
                        delivery_payment_days = project.delivery_payment_days if project.delivery_payment_days is not None else 0
                        delivery_due_date = progress.shipping_end_date + timedelta(days=delivery_payment_days)
                        overdue_days = (today - delivery_due_date).days
                        if overdue_days > 0:
                            status_display_text = f'未回款（已超期{overdue_days}天）'
                    elif payment_type == 'acceptance_payment' and progress and progress.acceptance_date and hasattr(project, 'acceptance_payment_days'):
                        acceptance_payment_days = project.acceptance_payment_days if project.acceptance_payment_days is not None else 0
                        acceptance_due_date = progress.acceptance_date + timedelta(days=acceptance_payment_days)
                        overdue_days = (today - acceptance_due_date).days
                        if overdue_days > 0:
                            status_display_text = f'未回款（已超期{overdue_days}天）'
                    elif payment_type == 'warranty_payment' and progress and progress.acceptance_date and hasattr(project, 'warranty_payment_days'):
                        warranty_payment_days = project.warranty_payment_days if project.warranty_payment_days is not None else 365
                        warranty_due_date = progress.acceptance_date + timedelta(days=warranty_payment_days)
                        overdue_days = (today - warranty_due_date).days
                        if overdue_days > 0:
                            status_display_text = f'未回款（已超期{overdue_days}天）'

                    unpaid_projects.append({
                        'id': project.id,
                        'project_name': project.project_name,
                        'project_code': project.project_code,
                        'project_status': calculated_status,
                        'status_name': status_name,
                        'price': project.price,
                        'currency': project.currency if hasattr(project, 'currency') and project.currency else '人民币',  # 添加币种信息
                        'expected_amount': 0,  # 应收金额
                        'actual_amount': 0,    # 实际回款金额
                        'payment_status': 0,   # 未回款
                        'payment_status_display': status_display_text  # 状态显示文本
                    })
                continue

            # 计算回款状态（使用已计算的状态）
            payment_status = calculate_payment_status(project, progress, payment, calculated_status)

            # 计算带天数信息的回款状态显示文本
            payment_status_display = calculate_payment_status_with_days(project, progress, payment, calculated_status)

            # 获取计算后的状态名称
            status_dict = DictData.query.filter_by(type_code='project_status').all()
            status_map = {int(item.data_value): item.data_label for item in status_dict}
            status_name = status_map.get(calculated_status, '')

            # 如果状态名称为空，则使用默认名称
            if not status_name:
                status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

            # 根据付款类型检查是否为未付款项目
            if payment_type == 'prepayment':
                status_key = 'prepayment_status'
                expected_amount = payment.prepayment
                actual_amount = payment.actual_prepayment
            elif payment_type == 'delivery_payment':
                status_key = 'delivery_payment_status'
                expected_amount = payment.delivery_payment
                actual_amount = payment.actual_delivery_payment
            elif payment_type == 'acceptance_payment':
                status_key = 'acceptance_payment_status'
                expected_amount = payment.acceptance_payment
                actual_amount = payment.actual_acceptance_payment
            elif payment_type == 'warranty_payment':
                status_key = 'warranty_payment_status'
                expected_amount = payment.warranty_payment
                actual_amount = payment.actual_warranty_payment

            # 如果是未回款状态或部分回款状态且应收金额大于0，则添加到未付款项目列表
            if (payment_status.get(status_key) == 0 or payment_status.get(status_key) == 3) and expected_amount > 0:
                # 获取对应的状态显示文本
                status_display_key = status_key.replace('_status', '_status_display')
                status_display_text = payment_status_display.get(status_display_key, '未知状态')

                unpaid_projects.append({
                    'id': project.id,
                    'project_name': project.project_name,
                    'project_code': project.project_code,
                    'project_type_name': project_type.dept_name if project_type else '',
                    'project_status': calculated_status,
                    'status_name': status_name,
                    'price': project.price,
                    'currency': project.currency if hasattr(project, 'currency') and project.currency else '人民币',  # 添加币种信息
                    'expected_amount': expected_amount,  # 应收金额
                    'actual_amount': actual_amount,      # 实际回款金额
                    'payment_status': payment_status.get(status_key),  # 回款状态
                    'payment_status_display': status_display_text  # 状态显示文本
                })

        # 返回未付款项目列表
        return jsonify({
            'success': True,
            'data': unpaid_projects
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取未付款项目列表失败: {str(e)}'
        })


@bp.get('/detail/<int:id>')
@authorize("system:project_payment:detail", log=True)
def detail(id):
    """项目回款详情页面"""
    project = Import_project.query.get_or_404(id)
    return render_template('system/project_payment/detail.html', project=project)


@bp.get('/api/project/<int:project_id>/payment_history')
@authorize("system:project_payment:detail", log=True)
def get_payment_history(project_id):
    """获取项目回款历史记录"""
    try:
        # 获取请求参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)

        # 查询项目回款历史记录
        query = PaymentHistory.query.filter_by(project_id=project_id).order_by(PaymentHistory.created_at.desc())

        # 执行分页查询
        paginate = query.paginate(page=page, per_page=limit, error_out=False)

        # 准备返回数据
        items = []
        for history in paginate.items:
            # 格式化日期
            payment_date = history.payment_date.strftime('%Y-%m-%d') if history.payment_date else None
            created_at = history.created_at.strftime('%Y-%m-%d %H:%M:%S') if history.created_at else None

            # 构建历史记录数据
            item = {
                'id': history.id,
                'payment_type': history.payment_type,
                'amount': history.amount,
                'payment_date': payment_date,
                'remark': history.remark,
                'created_at': created_at
            }
            items.append(item)

        # 返回数据
        return table_api(
            msg='',
            count=paginate.total,
            data=items
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取项目回款历史记录失败: {str(e)}'
        })

@bp.get('/api/project/<int:project_id>')
@authorize("system:project_payment:detail", log=True)
def get_project_payment(project_id):
    """获取项目回款详情"""
    try:
        # 获取项目基本信息及项目类型
        project = Import_project.query.get_or_404(project_id)
        project_type = ProjectManageDept.query.filter_by(id=project.dept_id).first()

        # 获取项目进度信息
        progress = ProjectProgress.query.filter_by(project_id=project_id).first()

        # 根据项目进度信息计算项目状态
        calculated_status = calculate_project_status(progress) if progress else 0

        # 获取计算后的状态名称
        status_dict = DictData.query.filter_by(type_code='project_status').all()
        status_map = {int(item.data_value): item.data_label for item in status_dict}
        status_name = status_map.get(calculated_status, '')

        # 如果状态名称为空，则使用默认名称
        if not status_name:
            status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

        # 获取项目付款信息
        payment = ProjectPayment.query.filter_by(project_id=project_id).first()

        # 查询各类型的待审批请求
        pending_approvals = {}
        payment_types = ['prepayment', 'delivery_payment', 'acceptance_payment', 'warranty_payment']

        for payment_type in payment_types:
            approval = PaymentApproval.query.filter_by(
                project_id=project_id,
                payment_type=payment_type,
                status=0  # 待审批
            ).order_by(PaymentApproval.created_at.desc()).first()

            if approval:
                pending_approvals[payment_type] = {
                    'id': approval.id,
                    'actual_amount': approval.actual_amount,
                    'payment_date': approval.payment_date.strftime('%Y-%m-%d') if approval.payment_date else None,
                    'remark': approval.remark,
                    'is_accumulate': approval.is_accumulate,
                    'created_at': approval.created_at.strftime('%Y-%m-%d %H:%M:%S') if approval.created_at else None
                }
            else:
                pending_approvals[payment_type] = None

        # 计算回款状态
        payment_status = calculate_payment_status(project, progress, payment, calculated_status)

        # 准备返回数据
        data = {
            'project': {
                'id': project.id,
                'project_name': project.project_name,
                'project_code': project.project_code,
                'project_type_name': project_type.dept_name if project_type else '',
                'project_status': calculated_status,
                'status_name': status_name,
                'price': project.price,
                'currency': project.currency if hasattr(project, 'currency') and project.currency else '人民币'  # 添加币种信息
            },
            'progress': {
                'project_start_date': progress.project_start_date.strftime('%Y-%m-%d') if progress and progress.project_start_date else None,
                'shipping_end_date': progress.shipping_end_date.strftime('%Y-%m-%d') if progress and progress.shipping_end_date else None,
                'acceptance_date': progress.acceptance_date.strftime('%Y-%m-%d') if progress and progress.acceptance_date else None
            } if progress else {},
            'payment': {
                'id': payment.id if payment else None,
                'prepayment': payment.prepayment if payment else 0,
                'delivery_payment': payment.delivery_payment if payment else 0,
                'acceptance_payment': payment.acceptance_payment if payment else 0,
                'warranty_payment': payment.warranty_payment if payment else 0,
                'actual_prepayment': payment.actual_prepayment if payment else 0,
                'actual_delivery_payment': payment.actual_delivery_payment if payment else 0,
                'actual_acceptance_payment': payment.actual_acceptance_payment if payment else 0,
                'actual_warranty_payment': payment.actual_warranty_payment if payment else 0,

                # 各类型付款日期
                'prepayment_date': payment.prepayment_date.strftime('%Y-%m-%d') if payment and payment.prepayment_date else None,
                'delivery_payment_date': payment.delivery_payment_date.strftime('%Y-%m-%d') if payment and payment.delivery_payment_date else None,
                'acceptance_payment_date': payment.acceptance_payment_date.strftime('%Y-%m-%d') if payment and payment.acceptance_payment_date else None,
                'warranty_payment_date': payment.warranty_payment_date.strftime('%Y-%m-%d') if payment and payment.warranty_payment_date else None,

                # 各类型付款备注
                'prepayment_remark': payment.prepayment_remark if payment and payment.prepayment_remark else '',
                'delivery_payment_remark': payment.delivery_payment_remark if payment and payment.delivery_payment_remark else '',
                'acceptance_payment_remark': payment.acceptance_payment_remark if payment and payment.acceptance_payment_remark else '',
                'warranty_payment_remark': payment.warranty_payment_remark if payment and payment.warranty_payment_remark else '',

                # 保留旧字段，兼容性考虑
                'payment_date': payment.payment_date.strftime('%Y-%m-%d') if payment and payment.payment_date else None,
                'payment_status': payment.payment_status if payment else 0,
                'remark': payment.remark if payment else ''
            } if payment else {},
            'payment_status': payment_status,
            'pending_approvals': pending_approvals
        }

        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取项目回款详情失败: {str(e)}'
        })


@bp.post('/api/project/<int:project_id>/update_payment_status')
@authorize("system:project_payment:edit", log=True)
def update_payment_status(project_id):
    """更新项目回款状态和实际回款金额"""
    try:
        # 获取请求数据
        req_json = request.get_json(force=True)
        payment_type = req_json.get('payment_type', '')  # 付款类型：prepayment, delivery_payment, acceptance_payment, warranty_payment
        actual_amount = float(req_json.get('actual_amount', 0))  # 实际回款金额
        remark = str_escape(req_json.get('remark', ''))
        payment_date_str = req_json.get('payment_date', '')  # 付款日期字符串

        # 将 is_accumulate 转换为布尔值
        is_accumulate_raw = req_json.get('is_accumulate', False)
        # 处理各种可能的输入值
        if isinstance(is_accumulate_raw, str):
            is_accumulate = is_accumulate_raw.lower() in ('true', 'yes', '1', 'on')
        else:
            is_accumulate = bool(is_accumulate_raw)

        # 处理付款日期
        payment_date = None
        if payment_date_str:
            try:
                payment_date = datetime.strptime(payment_date_str, '%Y-%m-%d').date()
            except ValueError:
                # 如果日期格式不正确，使用当前日期
                payment_date = datetime.now().date()

        # 获取项目付款信息
        payment = ProjectPayment.query.filter_by(project_id=project_id).first()

        if not payment:
            return jsonify({
                'success': False,
                'message': '项目付款信息不存在'
            })

        # 验证实际回款金额不能超过应收金额
        expected_amount = 0
        current_amount = 0

        # 根据付款类型获取对应的应收金额和当前已回款金额
        if payment_type == 'prepayment':
            expected_amount = payment.prepayment
            current_amount = payment.actual_prepayment
        elif payment_type == 'delivery_payment':
            expected_amount = payment.delivery_payment
            current_amount = payment.actual_delivery_payment
        elif payment_type == 'acceptance_payment':
            expected_amount = payment.acceptance_payment
            current_amount = payment.actual_acceptance_payment
        elif payment_type == 'warranty_payment':
            expected_amount = payment.warranty_payment
            current_amount = payment.actual_warranty_payment
        else:
            return jsonify({
                'success': False,
                'message': '无效的付款类型'
            })

        # 检查是否超过应收金额
        if is_accumulate:
            # 累计回款
            if (current_amount + actual_amount) > expected_amount:
                return jsonify({
                    'success': False,
                    'message': f'累计回款金额（{current_amount + actual_amount}）不能大于应收金额（{expected_amount}）'
                })
        else:
            # 覆盖回款
            if actual_amount > expected_amount:
                return jsonify({
                    'success': False,
                    'message': f'实际回款金额（{actual_amount}）不能大于应收金额（{expected_amount}）'
                })

        # 创建审批请求而不是直接更新
        payment_approval = PaymentApproval(
            project_id=project_id,
            payment_id=payment.id,
            payment_type=payment_type,
            actual_amount=actual_amount,
            payment_date=payment_date,
            remark=remark,
            is_accumulate=is_accumulate,
            status=0  # 待审批
        )
        db.session.add(payment_approval)
        db.session.commit()

        # 记录操作日志
        admin_log(
            request=request,
            is_access=True,
            desc=f'提交回款审批请求 [项目ID: {project_id}, 类型: {payment_type}, 金额: {actual_amount}]'
        )

        return jsonify({
            'success': True,
            'message': '回款请求已提交，等待审批'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'提交回款审批请求失败: {str(e)}'
        })
@bp.get('/approval')
@authorize("system:project_payment_approval:main", log=True)
def approval():
    return render_template('system/project_payment/approval.html')


@bp.get('/api/approval/statistics')
@authorize("system:project_payment_approval:main", log=True)
def get_approval_statistics():
    """获取各类型待审批项目的数量统计"""
    try:
        from sqlalchemy import func
        # 查询各类型待审批项目的数量
        prepayment_count = db.session.query(func.count(PaymentApproval.id)).filter(
            PaymentApproval.status == 0,
            PaymentApproval.payment_type == 'prepayment'
        ).scalar() or 0

        delivery_payment_count = db.session.query(func.count(PaymentApproval.id)).filter(
            PaymentApproval.status == 0,
            PaymentApproval.payment_type == 'delivery_payment'
        ).scalar() or 0

        acceptance_payment_count = db.session.query(func.count(PaymentApproval.id)).filter(
            PaymentApproval.status == 0,
            PaymentApproval.payment_type == 'acceptance_payment'
        ).scalar() or 0

        warranty_payment_count = db.session.query(func.count(PaymentApproval.id)).filter(
            PaymentApproval.status == 0,
            PaymentApproval.payment_type == 'warranty_payment'
        ).scalar() or 0

        # 返回统计数据
        return jsonify({
            'code': 0,
            'msg': '获取统计数据成功',
            'data': {
                'prepayment_count': prepayment_count,
                'delivery_payment_count': delivery_payment_count,
                'acceptance_payment_count': acceptance_payment_count,
                'warranty_payment_count': warranty_payment_count
            }
        })
    except Exception as e:
        return jsonify({
            'code': 1,
            'msg': f'获取统计数据失败: {str(e)}',
            'data': None
        })


@bp.get('/api/approval/pending')
@authorize("system:project_payment_approval:main", log=True)
def get_pending_approvals():
    """获取待审批的回款请求列表"""
    try:
        # 获取请求参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        payment_type = request.args.get('payment_type', '')  # 付款类型筛选

        # 查询待审批的请求
        query = db.session.query(
            PaymentApproval,
            Import_project.project_name,
            Import_project.project_code,
            Import_project.currency
        ).join(
            Import_project, PaymentApproval.project_id == Import_project.id
        ).filter(
            PaymentApproval.status == 0  # 待审批
        )

        # 如果指定了付款类型，则添加筛选条件
        if payment_type and payment_type in ['prepayment', 'delivery_payment', 'acceptance_payment', 'warranty_payment']:
            query = query.filter(PaymentApproval.payment_type == payment_type)

        # 按创建时间降序排序
        query = query.order_by(
            PaymentApproval.created_at.desc()
        )

        # 分页
        paginated = query.paginate(page=page, per_page=limit, error_out=False)

        # 准备返回数据
        data = []
        for approval, project_name, project_code, currency in paginated.items:
            # 获取付款类型名称
            payment_type_name = ''
            if approval.payment_type == 'prepayment':
                payment_type_name = '预付款'
            elif approval.payment_type == 'delivery_payment':
                payment_type_name = '发货款'
            elif approval.payment_type == 'acceptance_payment':
                payment_type_name = '验收款'
            elif approval.payment_type == 'warranty_payment':
                payment_type_name = '质保金'

            # 获取币种符号
            currency_symbol = '¥'  # 默认人民币符号
            if currency:
                if currency == '美元':
                    currency_symbol = '$'
                elif currency == '欧元':
                    currency_symbol = '€'
                elif currency == '英镑':
                    currency_symbol = '£'
                elif currency == '日元':
                    currency_symbol = '¥'
                elif currency == '韩元':
                    currency_symbol = '₩'
                elif currency == '港币':
                    currency_symbol = 'HK$'
                elif currency == '卢布':
                    currency_symbol = '₽'

            data.append({
                'id': approval.id,
                'project_id': approval.project_id,
                'project_name': project_name,
                'project_code': project_code,
                'payment_type': approval.payment_type,
                'payment_type_name': payment_type_name,
                'actual_amount': approval.actual_amount,
                'currency_symbol': currency_symbol,
                'payment_date': approval.payment_date.strftime('%Y-%m-%d') if approval.payment_date else '',
                'remark': approval.remark,
                'is_accumulate': approval.is_accumulate,
                'status': approval.status,
                'created_at': approval.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        return table_api(
            data=data,
            count=paginated.total,
            limit=limit
        )
    except Exception as e:
        return jsonify({
            'code': 1,
            'msg': f'获取待审批回款请求失败: {str(e)}',
            'data': None
        })


@bp.post('/api/approval/<int:approval_id>/process')
@authorize("system:project_payment_approval:approve", log=True)
def process_approval(approval_id):
    """处理回款审批请求（批准或拒绝）"""
    try:
        # 获取请求数据
        req_json = request.get_json(force=True)
        action = req_json.get('action', '')  # approve 或 reject
        remark = str_escape(req_json.get('remark', ''))

        # 验证操作类型
        if action not in ['approve', 'reject']:
            return fail_api(msg='无效的操作类型，必须是 approve 或 reject')

        # 获取审批请求
        approval = PaymentApproval.query.get_or_404(approval_id)

        # 检查审批状态
        if approval.status != 0:
            return fail_api(msg='该请求已经被处理过')

        # 更新审批信息
        approval.approver_id = current_user.id
        approval.approval_time = datetime.now()
        approval.approval_remark = remark

        if action == 'approve':
            # 批准请求
            approval.status = 1  # 已批准

            # 获取项目付款信息
            payment = ProjectPayment.query.filter_by(id=approval.payment_id).first()

            if not payment:
                return fail_api(msg='项目付款信息不存在')

            # 创建付款历史记录
            payment_history = PaymentHistory(
                project_id=approval.project_id,
                payment_id=approval.payment_id,
                payment_type=approval.payment_type,
                amount=approval.actual_amount,
                payment_date=approval.payment_date,
                remark=approval.remark
            )
            db.session.add(payment_history)

            # 根据付款类型更新实际回款金额、日期和备注
            if approval.payment_type == 'prepayment':
                if approval.is_accumulate:
                    # 累计回款
                    payment.actual_prepayment += approval.actual_amount
                else:
                    # 覆盖回款
                    payment.actual_prepayment = approval.actual_amount
                payment.prepayment_date = approval.payment_date if payment.actual_prepayment > 0 else None
                payment.prepayment_remark = approval.remark
            elif approval.payment_type == 'delivery_payment':
                if approval.is_accumulate:
                    # 累计回款
                    payment.actual_delivery_payment += approval.actual_amount
                else:
                    # 覆盖回款
                    payment.actual_delivery_payment = approval.actual_amount
                payment.delivery_payment_date = approval.payment_date if payment.actual_delivery_payment > 0 else None
                payment.delivery_payment_remark = approval.remark
            elif approval.payment_type == 'acceptance_payment':
                if approval.is_accumulate:
                    # 累计回款
                    payment.actual_acceptance_payment += approval.actual_amount
                else:
                    # 覆盖回款
                    payment.actual_acceptance_payment = approval.actual_amount
                payment.acceptance_payment_date = approval.payment_date if payment.actual_acceptance_payment > 0 else None
                payment.acceptance_payment_remark = approval.remark
            elif approval.payment_type == 'warranty_payment':
                if approval.is_accumulate:
                    # 累计回款
                    payment.actual_warranty_payment += approval.actual_amount
                else:
                    # 覆盖回款
                    payment.actual_warranty_payment = approval.actual_amount
                payment.warranty_payment_date = approval.payment_date if payment.actual_warranty_payment > 0 else None
                payment.warranty_payment_remark = approval.remark

            # 同时更新旧字段，保持兼容性
            payment.remark = approval.remark
            payment.payment_date = approval.payment_date if approval.actual_amount > 0 else None

            # 记录操作日志
            admin_log(
                request=request,
                is_access=True,
                desc=f'批准回款请求 [审批ID: {approval_id}, 项目ID: {approval.project_id}, 类型: {approval.payment_type}, 金额: {approval.actual_amount}]'
            )

            message = '回款请求已批准并更新'
        else:
            # 拒绝请求
            approval.status = 2  # 已拒绝

            # 记录操作日志
            admin_log(
                request=request,
                is_access=True,
                desc=f'拒绝回款请求 [审批ID: {approval_id}, 项目ID: {approval.project_id}, 类型: {approval.payment_type}, 金额: {approval.actual_amount}]'
            )

            message = '回款请求已拒绝'

        # 提交更改
        db.session.commit()

        return success_api(msg=message)
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f'处理回款审批请求失败: {str(e)}')