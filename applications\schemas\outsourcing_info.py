from flask_marshmallow.sqla import SQLAlchemyAutoSchema
from marshmallow import fields
from applications.models.outsourcing_info import OutsourcingInfo


class OutsourcingInfoSchema(SQLAlchemyAutoSchema):
    """外协信息序列化模式"""
    class Meta:
        model = OutsourcingInfo
        include_fk = True
        
    # 自定义字段格式化
    create_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    update_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')


class OutsourcingInfoOutSchema(SQLAlchemyAutoSchema):
    """外协信息输出序列化模式"""
    class Meta:
        model = OutsourcingInfo
        include_fk = True
        
    id = fields.Str(attribute="id")
    outsourcing_name = fields.Str(attribute="outsourcing_name")
    contact_person = fields.Str(attribute="contact_person")
    contact_phone = fields.Str(attribute="contact_phone")
    contact_email = fields.Str(attribute="contact_email")
    company_address = fields.Str(attribute="company_address")
    business_scope = fields.Str(attribute="business_scope")
    qualification_level = fields.Str(attribute="qualification_level")
    hourly_rate = fields.Str(attribute="hourly_rate")
    cooperation_status = fields.Str(attribute="cooperation_status")
    remark = fields.Str(attribute="remark")
    create_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    update_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
