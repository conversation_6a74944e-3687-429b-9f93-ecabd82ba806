<!DOCTYPE html>
<html>
<head>
    <title>项目管理</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<body class="pear-container">
{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="user-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-inline">
                    <input type="text" name="name" placeholder="姓名" class="layui-input">
                </div>
                <label class="layui-form-label">项目号</label>
                <div class="layui-input-inline">
                    <input type="text" name="projectNumber" placeholder="项目号" class="layui-input">
                </div>
                <label class="layui-form-label">审批状态</label>
                <div class="layui-input-inline">
                    <input type="text" name="status" placeholder="例如：pending" class="layui-input">
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="user-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

{# 用户表格 #}
<div class="user-main user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <table id="user-table" lay-filter="user-table"></table>
        </div>
    </div>
</div>

<div id="message-center"></div>

<div id="notification-badge" style="display: none;">
    <span class="badge">0</span>
</div>
</body>

{% include 'system/common/footer.html' %}
<!-- {# 表格操作 #}
<script type="text/html" id="user-toolbar">
    {% if authorize("system:user:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="pear-icon pear-icon-add"></i>
            新增
        </button>
    {% endif %}
    <button class="layui-btn layui-btn-sm" lay-event="collasped">
        <i class="pear-icon pear-icon-modular"></i>
        高级
    </button>
</script> -->

{# 用户修改操作 #}
<script type="text/html" id="user-bar">
    {% if authorize("system:log_info:approve") %}
        <button class="layui-btn layui-btn-xs" lay-event="approve"><i class="pear-icon pear-icon-check"> 同意</i>
        </button>
    {% endif %}
    {% if authorize("system:log_info:reject") %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="reject"><i class="pear-icon pear-icon-close"> 拒绝</i>
        </button>
    {% endif %}
</script>


<script>
    // 更新updateNotifications函数的实现
    function updateNotifications(data) {
        try {
            // 如果data是字符串，解析它
            if (typeof data.msg === 'string') {
                data = JSON.parse(data.msg);
            }
            
            // 获取通知数量
            const pendingCount = data.pending_count || 0;
            
            // 更新通知徽章
            const badgeElement = document.getElementById('notification-badge');
            if (badgeElement) {
                if (pendingCount > 0) {
                    badgeElement.style.display = 'block';
                    badgeElement.querySelector('.badge').textContent = pendingCount;
                } else {
                    badgeElement.style.display = 'none';
                }
            }
            
            // 刷新页面上的通知中心显示
            // 这里可能需要刷新右上角的通知图标，取决于页面结构
            // 如果有显示消息中心的元素，可以在这里更新
            const notificationBadge = document.querySelector('.layui-badge');
            if (notificationBadge && pendingCount > 0) {
                notificationBadge.textContent = pendingCount;
                notificationBadge.style.display = 'inline-block';
            } else if (notificationBadge) {
                notificationBadge.style.display = 'none';
            }
            
            console.log('通知更新成功: ', pendingCount, '个待处理项');
        } catch (e) {
            console.error('更新通知失败: ', e);
        }
    }

    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'messageCenter'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let messageCenter = layui.messageCenter;
        let MODULE_PATH = "{{ url_for('system.log_info.index') }}";
        
        // Define table columns
        let cols = [
            [
                {title: '编号', field: 'id', align: 'center'},
                {title: '姓名', field: 'name', align: 'center'},
                {title: '工作地点', field: 'projectLocation', align: 'center'},
                {title: '项目类型', field: 'projectPrefix', align: 'center'},
                {title: '项目编号', field: 'projectNumber', align: 'center'},
                {title: '正常工作时间', field: 'regularWorkingHours', align: 'center'},
                {title: '加班工作时间', field: 'overtimeWorkingHours', align: 'center'},
                {title: '总工作时间', field: 'totalHours', align: 'center'},
                {title: '日志内容', field: 'content', align: 'center', width: 110},
                {title: '创建时间', field: 'created_at', align: 'center'},
                {title: '状态', field: 'status',  align: 'center', templet: function(d) {
                    if (d.status === 'rejected') {
                        return '拒绝';
                    } else if (d.status === 'pending') {
                        return '审批中';
                    } else {
                        return '同意';
                    }
                }, width: 120},
                {title: '操作', align: 'center', width: 180, templet: function(d) {
                    if (d.status === 'pending') {
                        return `
                            {% if authorize("system:log_info:approve") %}
                                <button class="layui-btn layui-btn-xs" lay-event="approve"><i class="pear-icon pear-icon-check"> 同意</i></button>
                            {% endif %}
                            {% if authorize("system:log_info:reject") %}
                                <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="reject"><i class="pear-icon pear-icon-close"> 拒绝</i></button>
                            {% endif %}
                        `;
                    } else {
                        return '<span>无操作</span>'; // Display a placeholder if no buttons are shown
                    }
                }}
            ]
        ]

        // Render table
        table.render({
            elem: '#user-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#user-toolbar', /*工具栏*/
            text: {none: '暂无人员信息'},
            defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
                title: '刷新'
            },'filter', 'print', 'exports'] /*默认工具栏*/
        })

        // Handle refresh event
        table.on('toolbar(user-table)', function (obj) {
            if (obj.event === 'refresh') {
                table.reload('user-table');
            }
        });

        // Handle approve and reject events
        table.on('tool(user-table)', function (obj) {
            let data = obj.data;
            if (obj.event === 'approve') {
                console.log('Approving log:', data.id);
                $.post(MODULE_PATH + 'approve/' + data.id, function (res) {
                    console.log('Response:', res);
                    if (res.success) {
                        console.log('Approval successful, updating notifications...');
                        updateNotifications(res); // 更新通知中心
                        table.reload('user-table'); // 刷新表格
                    } else {
                        console.error('Approval failed:', res.msg);
                        popup.failure(res.msg);
                    }
                }).fail(function(err) {
                    console.error('Request failed:', err);
                    popup.failure('请求失败，请检查网络连接');
                });
            } else if (obj.event === 'reject') {
                $.post(MODULE_PATH + 'reject/' + data.id, function (res) {
                    if (res.success) {
                        console.log('Rejection successful, updating notifications...');
                        updateNotifications(res); // 更新通知中心
                        table.reload('user-table'); // 刷新表格
                    } else {
                        popup.failure(res.msg);
                    }
                });
            }
        });

        // Handle form submit event
        form.on('submit(user-query)', function(data){
            // Get form data
            let queryParams = {
                name: data.field.name,
                projectNumber: data.field.projectNumber,
                status: data.field.status
            };
        
            
            // Reload table with query parameters
            table.reload('user-table', {
                where: queryParams,
                page: {
                    curr: 1 // Reset to first page
                }
            });
            
            return false; // Prevent form submission
        });

        form.on('switch(user-enable)', function (obj) {
            let operate;
            if (obj.elem.checked) {
                operate = 'enable';
            } else {
                operate = 'disable';
            }
            
            // 先保存URL到变量
            let operateUrl = MODULE_PATH + operate;
            
            let loading = layer.load();
            $.ajax({
                url: operateUrl,
                data: JSON.stringify({userId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        popup.success(result.msg);
                    } else {
                        popup.failure(result.msg);
                    }
                }
            });
        });

        // 获取通知信息
        $.ajax({
            url: '/system/log_info/notifications',
            type: 'GET',
            success: function (data) {
                if (data.code === 0) {
                    // 调用 updateNotifications 函数
                    updateNotifications(data);
                }
            },
            error: function () {
                console.error('Failed to fetch notifications');
            }
        });
    });
</script>
</html>