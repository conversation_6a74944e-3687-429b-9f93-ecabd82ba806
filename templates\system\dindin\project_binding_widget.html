<!-- 项目绑定组件 - 可嵌入到其他页面中 -->
<style>
    .binding-widget {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        padding: 15px;
        margin: 10px 0;
        background-color: #fafafa;
    }
    .binding-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .binding-item:last-child {
        border-bottom: none;
    }
    .binding-info {
        flex: 1;
    }
    .binding-actions {
        margin-left: 10px;
    }
    .confidence-indicator {
        display: inline-block;
        width: 50px;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        vertical-align: middle;
        margin-left: 8px;
    }
    .confidence-fill {
        height: 100%;
        background-color: #5FB878;
        transition: width 0.3s ease;
    }
    .add-binding-btn {
        width: 100%;
        margin-top: 10px;
        border: 2px dashed #d9d9d9;
        background-color: transparent;
        color: #666;
        transition: all 0.3s ease;
    }
    .add-binding-btn:hover {
        border-color: #1E9FFF;
        color: #1E9FFF;
    }
</style>

<div class="binding-widget" id="project-binding-widget" data-exception-id="">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <h4 style="margin: 0; color: #333;">项目绑定</h4>
        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refresh-bindings-btn">
            <i class="layui-icon layui-icon-refresh"></i> 刷新
        </button>
    </div>
    
    <div id="binding-list">
        <!-- 绑定列表将在这里显示 -->
        <div style="text-align: center; padding: 20px; color: #999;">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
            加载中...
        </div>
    </div>
    
    <button type="button" class="layui-btn layui-btn-fluid add-binding-btn" id="add-binding-btn">
        <i class="layui-icon layui-icon-add-1"></i> 添加项目绑定
    </button>
</div>

<!-- 快速绑定弹窗 -->
<div id="quick-binding-dialog" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="quick-binding-form">
        <div class="layui-form-item">
            <label class="layui-form-label">搜索项目</label>
            <div class="layui-input-block">
                <div class="layui-input-group">
                    <input type="text" id="quick-search-input" placeholder="输入项目名称或编号" class="layui-input">
                    <div class="layui-input-split layui-input-suffix">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="quick-search-btn">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">搜索结果</label>
            <div class="layui-input-block">
                <div id="quick-search-results" style="max-height: 200px; overflow-y: auto; border: 1px solid #e6e6e6; border-radius: 4px; padding: 8px;">
                    <div style="text-align: center; padding: 20px; color: #999;">请输入关键词搜索项目</div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">绑定备注</label>
            <div class="layui-input-block">
                <textarea name="quick_notes" placeholder="请输入绑定备注（可选）" class="layui-textarea" rows="3"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="confirm-quick-binding-btn" disabled>确认绑定</button>
                <button type="button" class="layui-btn layui-btn-primary" id="cancel-quick-binding-btn">取消</button>
            </div>
        </div>
    </form>
</div>

<script>
// 项目绑定组件JavaScript
(function() {
    var currentExceptionId = null;
    var selectedProjectForQuickBinding = null;
    
    // 初始化组件
    function initProjectBindingWidget(exceptionId) {
        currentExceptionId = exceptionId;
        $('#project-binding-widget').attr('data-exception-id', exceptionId);
        loadBindings();
        bindEvents();
    }
    
    // 绑定事件
    function bindEvents() {
        // 刷新绑定
        $('#refresh-bindings-btn').off('click').on('click', function() {
            loadBindings();
        });
        
        // 添加绑定
        $('#add-binding-btn').off('click').on('click', function() {
            openQuickBindingDialog();
        });
        
        // 快速搜索
        $('#quick-search-btn').off('click').on('click', function() {
            performQuickSearch();
        });
        
        // 回车搜索
        $('#quick-search-input').off('keypress').on('keypress', function(e) {
            if (e.which === 13) {
                performQuickSearch();
            }
        });
        
        // 确认快速绑定
        $('#confirm-quick-binding-btn').off('click').on('click', function() {
            confirmQuickBinding();
        });
        
        // 取消快速绑定
        $('#cancel-quick-binding-btn').off('click').on('click', function() {
            layer.closeAll();
        });
    }
    
    // 加载绑定列表
    function loadBindings() {
        if (!currentExceptionId) return;
        
        $('#binding-list').html('<div style="text-align: center; padding: 20px; color: #999;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</div>');
        
        $.get('/system/dindin/api/quality-exceptions/' + currentExceptionId + '/bindings', function(res) {
            if (res.code === 200) {
                renderBindings(res.data.bindings);
            } else {
                $('#binding-list').html('<div style="text-align: center; padding: 20px; color: #f56c6c;">加载失败: ' + (res.msg || '未知错误') + '</div>');
            }
        }).fail(function() {
            $('#binding-list').html('<div style="text-align: center; padding: 20px; color: #f56c6c;">网络错误，加载失败</div>');
        });
    }
    
    // 渲染绑定列表
    function renderBindings(bindings) {
        var html = '';
        
        if (bindings.length === 0) {
            html = '<div style="text-align: center; padding: 20px; color: #999;">暂无绑定项目</div>';
        } else {
            bindings.forEach(function(binding) {
                html += '<div class="binding-item">';
                html += '<div class="binding-info">';
                html += '<div><strong>' + binding.project_name + '</strong> <span style="color: #666;">(' + binding.project_code + ')</span></div>';
                html += '<div style="font-size: 12px; color: #999; margin-top: 2px;">';
                html += binding.project_type_name;
                if (binding.match_confidence) {
                    html += '<span class="confidence-indicator"><span class="confidence-fill" style="width: ' + (binding.match_confidence * 100) + '%"></span></span>';
                    html += ' ' + Math.round(binding.match_confidence * 100) + '%';
                }
                html += '</div>';
                html += '</div>';
                html += '<div class="binding-actions">';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="unbindProject(' + binding.project_id + ')">';
                html += '<i class="layui-icon layui-icon-close"></i> 解绑';
                html += '</button>';
                html += '</div>';
                html += '</div>';
            });
        }
        
        $('#binding-list').html(html);
    }
    
    // 打开快速绑定对话框
    function openQuickBindingDialog() {
        selectedProjectForQuickBinding = null;
        $('#confirm-quick-binding-btn').prop('disabled', true);
        $('#quick-search-input').val('');
        $('#quick-search-results').html('<div style="text-align: center; padding: 20px; color: #999;">请输入关键词搜索项目</div>');
        $('textarea[name="quick_notes"]').val('');
        
        layer.open({
            type: 1,
            title: '快速绑定项目',
            area: ['600px', '500px'],
            content: $('#quick-binding-dialog')
        });
    }
    
    // 执行快速搜索
    function performQuickSearch() {
        var keyword = $('#quick-search-input').val().trim();
        if (!keyword) {
            layer.msg('请输入搜索关键词', {icon: 2});
            return;
        }
        
        $('#quick-search-results').html('<div style="text-align: center; padding: 20px; color: #999;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 搜索中...</div>');
        
        $.get('/system/dindin/api/projects/search', {
            keyword: keyword,
            limit: 10
        }, function(res) {
            if (res.code === 200 && res.data.projects) {
                renderQuickSearchResults(res.data.projects);
            } else {
                $('#quick-search-results').html('<div style="text-align: center; padding: 20px; color: #999;">未找到匹配的项目</div>');
            }
        }).fail(function() {
            $('#quick-search-results').html('<div style="text-align: center; padding: 20px; color: #f56c6c;">搜索失败</div>');
        });
    }
    
    // 渲染快速搜索结果
    function renderQuickSearchResults(projects) {
        var html = '';
        projects.forEach(function(project) {
            html += '<div class="project-suggestion" data-project-id="' + project.id + '" style="padding: 8px; margin: 4px 0; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer;">';
            html += '<div><strong>' + project.project_name + '</strong> <span style="color: #666;">(' + project.project_code + ')</span></div>';
            html += '<div style="font-size: 12px; color: #999; margin-top: 2px;">' + project.project_type_name + '</div>';
            html += '</div>';
        });
        $('#quick-search-results').html(html);
        
        // 绑定点击事件
        $('.project-suggestion').off('click').on('click', function() {
            $('.project-suggestion').removeClass('selected').css({
                'border-color': '#e6e6e6',
                'background-color': 'transparent'
            });
            $(this).addClass('selected').css({
                'border-color': '#1E9FFF',
                'background-color': '#e8f4ff'
            });
            selectedProjectForQuickBinding = $(this).data('project-id');
            $('#confirm-quick-binding-btn').prop('disabled', false);
        });
    }
    
    // 确认快速绑定
    function confirmQuickBinding() {
        if (!selectedProjectForQuickBinding) {
            layer.msg('请选择要绑定的项目', {icon: 2});
            return;
        }
        
        var notes = $('textarea[name="quick_notes"]').val();
        
        $.post('/system/dindin/api/quality-exceptions/' + currentExceptionId + '/bind-project', {
            project_id: selectedProjectForQuickBinding,
            notes: notes
        }, function(res) {
            if (res.code === 200) {
                layer.msg('绑定成功', {icon: 1});
                layer.closeAll();
                loadBindings();
            } else {
                layer.msg(res.msg || '绑定失败', {icon: 2});
            }
        }).fail(function() {
            layer.msg('绑定失败', {icon: 2});
        });
    }
    
    // 解绑项目
    window.unbindProject = function(projectId) {
        layer.confirm('确定要解绑此项目吗？', {
            icon: 3,
            title: '确认解绑'
        }, function(index) {
            $.ajax({
                url: '/system/dindin/api/quality-exceptions/' + currentExceptionId + '/unbind-project/' + projectId,
                type: 'DELETE',
                data: JSON.stringify({notes: '手动解绑'}),
                contentType: 'application/json',
                success: function(res) {
                    if (res.code === 200) {
                        layer.msg('解绑成功', {icon: 1});
                        loadBindings();
                    } else {
                        layer.msg(res.msg || '解绑失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('解绑失败', {icon: 2});
                }
            });
            layer.close(index);
        });
    };
    
    // 暴露初始化函数
    window.initProjectBindingWidget = initProjectBindingWidget;
})();
</script>
