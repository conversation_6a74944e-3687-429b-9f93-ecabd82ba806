import logging
from datetime import timedelta


class BaseConfig:
    # 超级管理员账号
    SUPERADMIN = 'admin'

    # 系统名称
    SYSTEM_NAME = 'GosunTec'

    # 主题面板的链接列表配置
    SYSTEM_PANEL_LINKS = [
        {
            "icon": "layui-icon layui-icon-auz",
            "title": "官方网站",
            "href": "http://www.pearadmin.com"
        },
        {
            "icon": "layui-icon layui-icon-auz",
            "title": "开发文档",
            "href": "http://www.pearadmin.com"
        },
        {
            "icon": "layui-icon layui-icon-auz",
            "title": "开源地址",
            "href": "https://gitee.com/Jmysy/Pear-Admin-Layui"
        }
    ]

    # 上传图片目标文件夹
    UPLOADED_PHOTOS_DEST = 'static/upload'
    UPLOADED_FILES_ALLOW = ['gif', 'jpg', 'jpeg', 'png', 'webp']
    UPLOADS_AUTOSERVE = True

    # JSON 配置
    JSON_AS_ASCII = False

    # 配置多个数据库连接的连接串写法示例
    # HOSTNAME: 指数据库的IP地址、USERNAME：指数据库登录的用户名、PASSWORD：指数据库登录密码、PORT：指数据库开放的端口、DATABASE：指需要连接的数据库名称
    # MSSQL:    f"mssql+pymssql://{USERNAME}:{PASSWORD}@{HOSTNAME}:{PORT}/{DATABASE}?charset=cp936"
    # MySQL:    f"mysql+pymysql://{USERNAME}:{PASSWORD}@{HOSTNAME}:{PORT}/{DATABASE}?charset=utf8mb4"
    # Oracle:   f"oracle+cx_oracle://{USERNAME}:{PASSWORD}@{HOSTNAME}:{PORT}/{DATABASE}"
    # SQLite    "sqlite:/// database.db"
    # Postgres f"postgresql+psycopg2://{USERNAME}:{PASSWORD}@{HOSTNAME}:{PORT}/{DATABASE}"
    # Oracle的第二种连接方式
    # dsnStr = cx_Oracle.makedsn({HOSTNAME}, 1521, service_name='orcl')
    # connect_str = "oracle://%s:%s@%s" % ('{USERNAME}', ' {PASSWORD}', dsnStr)

    #  在SQLALCHEMY_BINDS 中设置：'{数据库连接别名}': '{连接串}'
    # 最后在models的数据模型class中，在__tablename__前设置        __bind_key__ = '{数据库连接别名}'  即可，表示该数据模型不使用默认的数据库连接，改用“SQLALCHEMY_BINDS”中设置的其他数据库连接
    #  SQLALCHEMY_BINDS = {
    #    'testMySQL': 'mysql+pymysql://test:123456@***********:3306/test?charset=utf8',
    #    'testMsSQL': 'mssql+pymssql://test:123456@***********:1433/test?charset=cp936',
    #    'testOracle': 'oracle+cx_oracle://test:123456@***********:1521/test',
    #    'testSQLite': 'sqlite:///database.db
    # }

    # 数据库的配置信息
    # SQLALCHEMY_DATABASE_URI = 'sqlite:///../pear.db'
    MYSQL_USERNAME = "root"
    MYSQL_PASSWORD = "Gosun2020"
    MYSQL_HOST = "127.0.0.1"
    MYSQL_PORT = 3306
    MYSQL_DATABASE = "PearAdminFlask"

    # MYSQL_USERNAME = "PearAdminFlask"
    # MYSQL_PASSWORD = "Gosun2020"
    # MYSQL_HOST = "*************"
    # MYSQL_PORT = 3306
    # MYSQL_DATABASE = "pearadminflask"

    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SQLALCHEMY_ECHO = False
    SQLALCHEMY_POOL_RECYCLE = 8
    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{MYSQL_USERNAME}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"
    # 默认日志等级
    LOG_LEVEL = logging.WARN

    # 发信设置
    MAIL_SERVER = 'smtp.exmail.qq.com'
    MAIL_USE_TLS = False
    MAIL_USE_SSL = True
    MAIL_PORT = 465
    MAIL_USERNAME = '<EMAIL>'
    MAIL_PASSWORD = 'H3Cgv2dZeDqECECU'  # 生成的授权码
    MAIL_DEFAULT_SENDER = MAIL_USERNAME

    # 试用期提醒相关配置
    HR_EMAIL = '<EMAIL>'  # HR部门邮箱
    SYSTEM_URL = 'http://*************/system/yg_info/formal_management'  # 系统访问地址

    # 插件配置，填写插件的文件名名称，默认不启用插件。
    PLUGIN_ENABLE_FOLDERS = []

    # 钉钉API配置
    DINGTALK_APP_KEY = "dingy2s9zodvgmrlaypb"
    DINGTALK_APP_SECRET = "q4WqT6aLx6y06kLX-EEa1DwtvSy4MA5YA9wz2KnZ2Bwy9rnvEkFLeiO3U2rP1u4H"

    # Session 设置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    SESSION_TYPE = "filesystem"  # 默认使用文件系统来保存会话
    SESSION_PERMANENT = False  # 会话是否持久化
    SESSION_USE_SIGNER = True  # 是否对发送到浏览器上 session 的 cookie 值进行加密

    SECRET_KEY = "gosuntec"

    # 项目成本自动计算配置
    AUTO_LABOR_CALC_ENABLED = True  # 是否启用自动计算
    AUTO_LABOR_CALC_DELAY = 5  # 项目间延迟时间（秒）
    AUTO_LABOR_CALC_HOUR = 11  # 执行时间（小时）
    AUTO_LABOR_CALC_MINUTE = 30  # 执行时间（分钟）
