#!/usr/bin/env python3
"""
节假日导入服务
从holidays库导入中国法定节假日
"""

import holidays
from datetime import date, datetime
from applications.extensions import db
from applications.models.holiday_config import HolidayConfig
from sqlalchemy.exc import IntegrityError


class HolidayImportService:
    """节假日导入服务"""

    # 英文到中文的节假日名称映射字典
    HOLIDAY_NAME_MAPPING = {
        # 元旦
        "New Year's Day": "元旦",
        "New Year": "元旦",

        # 春节相关
        "Chinese New Year": "春节",
        "Spring Festival": "春节",
        "Chinese New Year's Eve": "除夕",
        "New Year's Eve": "除夕",
        "Lunar New Year": "春节",
        "Lunar New Year's Day": "春节",

        # 清明节
        "Qingming Festival": "清明节",
        "Tomb Sweeping Day": "清明节",
        "Tomb-Sweeping Day": "清明节",
        "Ching Ming Festival": "清明节",

        # 劳动节
        "Labor Day": "劳动节",
        "Labour Day": "劳动节",
        "International Workers' Day": "劳动节",
        "May Day": "劳动节",
        "Workers' Day": "劳动节",

        # 端午节
        "Dragon Boat Festival": "端午节",
        "Duanwu Festival": "端午节",
        "Double Fifth Festival": "端午节",

        # 中秋节
        "Mid-Autumn Festival": "中秋节",
        "Moon Festival": "中秋节",
        "Mooncake Festival": "中秋节",
        "Autumn Moon Festival": "中秋节",

        # 国庆节
        "National Day": "国庆节",
        "China National Day": "国庆节",
        "National Day of the People's Republic of China": "国庆节",
        "PRC National Day": "国庆节",

        # 调休和观察日相关
        "observed": "调休",
        "(observed)": "调休",
        "in lieu": "调休",
        "substitute": "调休",
        "makeup": "调休",
        "substitute holiday": "调休",
        "compensatory holiday": "调休",

        # 其他可能的表达
        "Golden Week": "黄金周",
        "Holiday": "节假日",
        "Public Holiday": "公共假期",
        "Bank Holiday": "法定假日",
    }

    @staticmethod
    def _translate_holiday_name(holiday_name):
        """
        处理节假日名称，支持英文到中文转换

        Args:
            holiday_name: 节假日名称（可能是中文或英文）

        Returns:
            str: 中文节假日名称
        """
        if not holiday_name:
            return holiday_name

        # 检查是否已经是中文名称（包含中文字符）
        if any('\u4e00' <= char <= '\u9fff' for char in holiday_name):
            # 已经是中文，直接返回
            return holiday_name

        # 英文名称转换逻辑
        # 直接匹配完整名称
        if holiday_name in HolidayImportService.HOLIDAY_NAME_MAPPING:
            return HolidayImportService.HOLIDAY_NAME_MAPPING[holiday_name]

        # 处理包含 "observed" 的情况
        if "observed" in holiday_name.lower():
            # 提取主要节假日名称
            base_name = holiday_name.replace(" (observed)", "").replace(" observed", "").strip()
            if base_name in HolidayImportService.HOLIDAY_NAME_MAPPING:
                return HolidayImportService.HOLIDAY_NAME_MAPPING[base_name] + "（调休）"
            else:
                # 如果基础名称也没有匹配，尝试部分匹配
                for eng_key, chn_value in HolidayImportService.HOLIDAY_NAME_MAPPING.items():
                    if eng_key.lower() in base_name.lower():
                        return chn_value + "（调休）"

        # 部分匹配：检查是否包含已知的节假日关键词
        # 按长度排序，优先匹配更长的关键词
        sorted_keys = sorted(HolidayImportService.HOLIDAY_NAME_MAPPING.keys(), key=len, reverse=True)
        holiday_lower = holiday_name.lower()
        for eng_key in sorted_keys:
            if eng_key.lower() in holiday_lower:
                return HolidayImportService.HOLIDAY_NAME_MAPPING[eng_key]

        # 如果都没有匹配，返回原名称
        return holiday_name
    
    @staticmethod
    def get_china_holidays(year):
        """获取指定年份的中国法定节假日"""
        try:
            # 获取中国节假日
            china_holidays = holidays.China(years=year)
            return china_holidays
        except Exception as e:
            print(f"获取中国节假日失败: {e}")
            return {}
    
    @staticmethod
    def import_china_holidays(year, created_by=1, overwrite=False):
        """
        导入指定年份的中国法定节假日
        
        Args:
            year: 年份
            created_by: 创建者ID
            overwrite: 是否覆盖已存在的节假日
            
        Returns:
            dict: 导入结果统计
        """
        result = {
            'total': 0,
            'imported': 0,
            'skipped': 0,
            'errors': 0,
            'details': []
        }
        
        try:
            # 获取中国节假日
            china_holidays = HolidayImportService.get_china_holidays(year)
            result['total'] = len(china_holidays)
            
            if not china_holidays:
                result['details'].append(f"未找到{year}年的中国节假日数据")
                return result
            
            # 遍历节假日并导入
            for holiday_date, holiday_name in china_holidays.items():
                try:
                    # 检查是否已存在
                    existing = HolidayConfig.query.filter_by(
                        holiday_date=holiday_date
                    ).first()
                    
                    if existing and not overwrite:
                        result['skipped'] += 1
                        result['details'].append(f"跳过已存在的节假日: {holiday_date} - {holiday_name}")
                        continue
                    
                    # 将英文节假日名称转换为中文
                    chinese_name = HolidayImportService._translate_holiday_name(holiday_name)

                    # 确定节假日类型和加班倍率（使用中文名称）
                    holiday_type, overtime_rate = HolidayImportService._determine_holiday_type(
                        chinese_name, holiday_date
                    )

                    if existing and overwrite:
                        # 更新已存在的记录
                        existing.holiday_name = chinese_name
                        existing.holiday_type = holiday_type
                        existing.overtime_rate = overtime_rate
                        existing.year = year
                        existing.description = f"从holidays库导入的{year}年中国法定节假日"
                        existing.status = 1
                        result['details'].append(f"更新节假日: {holiday_date} - {chinese_name}")
                    else:
                        # 创建新记录
                        new_holiday = HolidayConfig(
                            holiday_date=holiday_date,
                            holiday_name=chinese_name,
                            holiday_type=holiday_type,
                            overtime_rate=overtime_rate,
                            year=year,
                            description=f"从holidays库导入的{year}年中国法定节假日",
                            status=1,
                            created_by=created_by
                        )
                        db.session.add(new_holiday)
                        result['details'].append(f"导入新节假日: {holiday_date} - {chinese_name}")
                    
                    result['imported'] += 1
                    
                except Exception as e:
                    result['errors'] += 1
                    # 使用中文名称显示错误信息
                    chinese_name = HolidayImportService._translate_holiday_name(holiday_name)
                    result['details'].append(f"导入失败: {holiday_date} - {chinese_name}, 错误: {str(e)}")
            
            # 提交数据库更改
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            result['errors'] += 1
            result['details'].append(f"导入过程发生错误: {str(e)}")
        
        return result
    
    @staticmethod
    def _determine_holiday_type(holiday_name, holiday_date):
        """
        根据节假日名称和日期确定节假日类型和加班倍率

        Args:
            holiday_name: 节假日名称
            holiday_date: 节假日日期

        Returns:
            tuple: (holiday_type, overtime_rate)
        """
        # 真正的法定节假日关键词（只有这些才是3倍工资）
        legal_keywords = [
            '元旦', '新年',  # 元旦
            '农历除夕', '除夕',  # 除夕
            '春节',  # 春节（但不包括调休日）
            '清明节',  # 清明节
            '劳动节',  # 劳动节
            '端午节',  # 端午节
            '中秋节',  # 中秋节
            '国庆节'   # 国庆节
        ]

        # 调休日关键词（这些是2倍工资）
        makeup_keywords = [
            '调休', '补休', '观察日', '休息日', '取代', '起取代'
        ]

        # 首先检查是否是调休日
        for keyword in makeup_keywords:
            if keyword in holiday_name:
                return 'makeup', 2.0  # 调休日，2倍工资

        # 然后检查是否是真正的法定节假日
        for keyword in legal_keywords:
            if keyword in holiday_name:
                # 额外检查：如果包含调休相关词汇，仍然按调休处理
                if any(makeup_word in holiday_name for makeup_word in makeup_keywords):
                    return 'makeup', 2.0
                return 'legal', 3.0  # 法定节假日，3倍工资

        # 特殊处理：根据日期判断
        # 如果是周末且没有明确标识，可能是调休日
        weekday = holiday_date.weekday()  # 0=Monday, 6=Sunday
        if weekday >= 5:  # 周六或周日
            return 'makeup', 2.0

        # 默认按调休日处理（更安全的选择）
        return 'makeup', 2.0
    
    @staticmethod
    def get_missing_holidays(year):
        """
        获取缺失的节假日（在holidays库中有但数据库中没有的）
        
        Args:
            year: 年份
            
        Returns:
            list: 缺失的节假日列表
        """
        missing_holidays = []
        
        try:
            # 获取holidays库中的节假日
            china_holidays = HolidayImportService.get_china_holidays(year)
            
            # 获取数据库中已有的节假日
            existing_dates = set()
            existing_holidays = HolidayConfig.query.filter_by(year=year).all()
            for holiday in existing_holidays:
                existing_dates.add(holiday.holiday_date)
            
            # 找出缺失的节假日
            for holiday_date, holiday_name in china_holidays.items():
                if holiday_date not in existing_dates:
                    # 将英文节假日名称转换为中文
                    chinese_name = HolidayImportService._translate_holiday_name(holiday_name)
                    holiday_type, overtime_rate = HolidayImportService._determine_holiday_type(
                        chinese_name, holiday_date
                    )
                    missing_holidays.append({
                        'date': holiday_date,
                        'name': chinese_name,
                        'type': holiday_type,
                        'overtime_rate': overtime_rate,
                        'weekday': holiday_date.strftime('%A')
                    })
            
            # 按日期排序
            missing_holidays.sort(key=lambda x: x['date'])
            
        except Exception as e:
            print(f"获取缺失节假日失败: {e}")
        
        return missing_holidays
    
    @staticmethod
    def preview_import(year):
        """
        预览导入结果，不实际导入
        
        Args:
            year: 年份
            
        Returns:
            dict: 预览结果
        """
        preview = {
            'year': year,
            'total_holidays': 0,
            'new_holidays': 0,
            'existing_holidays': 0,
            'holiday_list': [],
            'missing_list': []
        }
        
        try:
            # 获取holidays库中的节假日
            china_holidays = HolidayImportService.get_china_holidays(year)
            preview['total_holidays'] = len(china_holidays)
            
            # 获取数据库中已有的节假日
            existing_dates = set()
            existing_holidays = HolidayConfig.query.filter_by(year=year).all()
            for holiday in existing_holidays:
                existing_dates.add(holiday.holiday_date)
            
            # 分析每个节假日
            for holiday_date, holiday_name in china_holidays.items():
                # 将英文节假日名称转换为中文
                chinese_name = HolidayImportService._translate_holiday_name(holiday_name)
                holiday_type, overtime_rate = HolidayImportService._determine_holiday_type(
                    chinese_name, holiday_date
                )

                holiday_info = {
                    'date': holiday_date.strftime('%Y-%m-%d'),
                    'name': chinese_name,
                    'type': holiday_type,
                    'overtime_rate': overtime_rate,
                    'weekday': holiday_date.strftime('%A'),
                    'exists': holiday_date in existing_dates
                }
                
                preview['holiday_list'].append(holiday_info)
                
                if holiday_date not in existing_dates:
                    preview['new_holidays'] += 1
                    preview['missing_list'].append(holiday_info)
                else:
                    preview['existing_holidays'] += 1
            
            # 按日期排序
            preview['holiday_list'].sort(key=lambda x: x['date'])
            preview['missing_list'].sort(key=lambda x: x['date'])
            
        except Exception as e:
            preview['error'] = str(e)
        
        return preview
