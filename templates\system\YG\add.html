<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <!-- <div class="layui-form-item">
                    <label class="layui-form-label">账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="username" lay-verify="title" autocomplete="off" placeholder="用户名"
                               class="layui-input">
                    </div>
                </div> -->
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" lay-verify="title" autocomplete="off" placeholder="用户姓名"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">手机号</label>
                    <div class="layui-input-block">
                        <input type="text" name="phone" lay-verify="title" autocomplete="off" placeholder="用户手机号"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">职务</label>
                    <div class="layui-input-block">
                        <input type="text" name="position" lay-verify="title" autocomplete="off" placeholder="用户职务"
                               class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="deptId" class="dtree" data-id="0"></ul>
                    </div>
                </div>

            

                <div class="layui-form-item">
                    <label class="layui-form-label">工号</label>
                    <div class="layui-input-block">
                        <input type="text" name="employee_id" lay-verify="title" autocomplete="off" placeholder="员工工号"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">入职日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="hire_date" lay-verify="date" autocomplete="off" placeholder="入职日期"
                               class="layui-input" id="hire_date">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性别</label>
                    <div class="layui-input-block">
                        <select name="gender" lay-verify="required">
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">出生日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="birth_date" lay-verify="date" autocomplete="off" placeholder="出生日期"
                               class="layui-input" id="birth_date">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">年龄</label>
                    <div class="layui-input-block">
                        <input type="number" name="age" lay-verify="number" autocomplete="off" placeholder="年龄"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">身份证号码</label>
                    <div class="layui-input-block">
                        <input type="text" name="id_card" lay-verify="identity" autocomplete="off" placeholder="身份证号码"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否正式</label>
                    <div class="layui-input-block">
                        <select name="is_formal" lay-verify="required" lay-filter="is_formal_change">
                            <option value="正式">正式员工</option>
                            <option value="试用" selected>试用期</option>
                            <option value="实习">实习期</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" id="probation_duration_item">
                    <label class="layui-form-label">试用期时长</label>
                    <div class="layui-input-block">
                        <select name="probation_duration" lay-verify="" lay-filter="probation_duration">
                            <option value="30">30天 (1个月)</option>
                            <option value="60">60天 (2个月)</option>
                            <option value="90" selected>90天 (3个月)</option>
                            <option value="120">120天 (4个月)</option>
                            <option value="180">180天 (6个月)</option>
                            <option value="365">365天 (12个月)</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" id="custom_duration_item" style="display: none;">
                    <label class="layui-form-label">自定义天数</label>
                    <div class="layui-input-block">
                        <input type="number" name="custom_probation_duration" min="1" max="1095"
                               placeholder="请输入天数 (1-1095天)" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">基本工资</label>
                    <div class="layui-input-block">
                        <input type="number" name="base_salary" lay-verify="number" autocomplete="off" placeholder="基本工资"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">绩效工资</label>
                    <div class="layui-input-block">
                        <input type="number" name="performance_salary" lay-verify="number" autocomplete="off" placeholder="绩效工资"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">主管考核项</label>
                    <div class="layui-input-block">
                        <input type="text" name="supervisor_assessment" lay-verify="title" autocomplete="off" placeholder="主管考核项"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">角色</label>
                    <div class="layui-input-block">

                        {% for role in roles %}
                            <input
                                    value="{{ role.id }}" title="{{ role.name }}" type="checkbox"
                                    name="roleIds" lay-skin="primary">
                        {% endfor %}

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="user-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery', 'dtree'], function () {
    let form = layui.form
    let $ = layui.jquery

    let dtree = layui.dtree

    dtree.renderSelect({
        elem: '#selectParent',
        url: '/system/dept/tree',
        method: 'get',
        selectInputName: {nodeId: 'deptId', context: 'deptName'},
        skin: 'layui',
        dataFormat: 'list',
        response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'}
    })

    // 监听员工类型变化
    form.on('select(is_formal_change)', function(data){
        let value = data.value;
        let durationItem = $('#probation_duration_item');
        let customItem = $('#custom_duration_item');

        if (value === '正式') {
            durationItem.hide();
            customItem.hide();
        } else {
            durationItem.show();
            // 根据类型设置默认值
            if (value === '试用') {
                $('select[name="probation_duration"]').val('90');
            } else if (value === '实习') {
                $('select[name="probation_duration"]').val('180');
            }
            form.render('select');
        }
    });

    // 监听试用期时长变化
    form.on('select(probation_duration)', function(data){
        let customItem = $('#custom_duration_item');
        if (data.value === 'custom') {
            customItem.show();
        } else {
            customItem.hide();
        }
    });

    form.on('submit(user-save)', function (data) {
      let roleIds = ''
      $('input[type=checkbox]:checked').each(function () {
        roleIds += $(this).val() + ','
      })
      roleIds = roleIds.substr(0, roleIds.length - 1)
      data.field.roleIds = roleIds

      // 处理试用期时长
      if (data.field.probation_duration === 'custom') {
          data.field.probation_duration = data.field.custom_probation_duration;
      }
      // 如果是正式员工，清空试用期时长
      if (data.field.is_formal === '正式') {
          data.field.probation_duration = null;
      }

      $.ajax({
        url: '/system/yg_info/save',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (result) {
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
              parent.layui.table.reload('yg-table')
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>

</body>
</html>