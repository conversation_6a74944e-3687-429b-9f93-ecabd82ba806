<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>部门产出比例统计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='system/component/layui/css/layui.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='system/component/pear/css/pear.css') }}">
    <style>
        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            background: #f5f7fa;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
      
        /* 主容器样式 */
        .main-container {
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面标题样式 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .page-header .icon {
            margin-right: 15px;
            font-size: 32px;
        }

        .page-header .subtitle {
            margin-top: 10px;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 卡片样式 */
        .content-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }

        /* 筛选卡片特殊处理 - 允许下拉框溢出 */
        .content-card.filter-card {
            overflow: visible;
        }

        .content-card.filter-card .card-body {
            overflow: visible;
        }

        .content-card.filter-card .filter-form {
            overflow: visible;
        }

        /* 修复所有按钮的文字对齐 */
        .layui-btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            line-height: 1 !important;
        }

        .layui-btn .layui-icon {
            margin-right: 5px;
            line-height: 1;
        }

        /* 确保按钮容器也正确对齐 */
        .action-buttons .layui-btn {
            vertical-align: middle;
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-title .layui-icon {
            margin-right: 10px;
            color: #667eea;
        }

        .card-body {
            padding: 25px;
        }

        /* 筛选表单样式 */
        .filter-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .filter-form .layui-form-item {
            margin-bottom: 15px;
        }

        .filter-form .layui-form-label {
            width: 100px;
            font-weight: 500;
            color: #495057;
        }

        .filter-form .layui-input-block {
            margin-left: 110px;
        }

        /* 操作按钮样式 */
        .action-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(86, 171, 47, 0.4);
        }

        /* 表格样式优化 */
        .layui-table-view {
            border-radius: 8px;
            overflow: hidden;
        }

        .layui-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .layui-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 提示信息样式 */
        .tips-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .tips-title {
            font-size: 16px;
            font-weight: 600;
            color: #8b4513;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .tips-title .layui-icon {
            margin-right: 8px;
            font-size: 18px;
        }

        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tips-list li {
            padding: 8px 0;
            color: #8b4513;
            font-size: 14px;
            line-height: 1.6;
        }

        .tips-list li strong {
            color: #d2691e;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .filter-form .layui-form-label {
                width: 80px;
            }
            
            .filter-form .layui-input-block {
                margin-left: 90px;
            }
        }

        /* 验证提示样式 */
        .validation-tip {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px 16px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 13px;
            color: #1976d2;
        }

        .validation-tip.warning {
            background: #fff3e0;
            border-left-color: #ff9800;
            color: #f57c00;
        }

        .validation-tip.error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #d32f2f;
        }

        /* 修复下拉框层级问题 */
        .layui-form-select .layui-anim {
            z-index: 99999 !important;
        }

        .layui-form-select dl {
            z-index: 99999 !important;
        }

        .layui-form-selectup dl {
            z-index: 99999 !important;
        }

        /* 确保下拉框容器有足够的层级 */
        .layui-form-item {
            position: relative;
            z-index: 1;
        }

        .layui-form-item.layui-form-selected {
            z-index: 99999 !important;
        }

        /* 修复筛选区域的层级问题 */
        .filter-form {
            position: relative;
            z-index: 10;
        }

        .filter-form .layui-form-item {
            overflow: visible;
        }

        /* 全局下拉框样式修复 */
        .layui-form-select {
            z-index: 99999 !important;
        }

        /* 确保弹窗内的下拉框也有正确的层级 */
        .layui-layer-content .layui-form-select .layui-anim {
            z-index: 999999 !important;
        }

        /* 弹窗表单样式 - 参考target_import.html设计 */
        .modal-form {
            padding: 30px;
            background: white;
        }

        .modal-form .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .modal-form .form-group {
            margin-bottom: 0;
        }

        .modal-form .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .modal-form .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
        }

        .modal-form .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-form .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
        }

        .modal-form .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-form .form-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .modal-form .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .modal-form .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal-form .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .modal-form .btn-secondary {
            background: #f3f4f6;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .modal-form .btn-secondary:hover {
            background: #e5e7eb;
        }

        .modal-form .required {
            color: #ef4444;
        }

        /* 修复弹窗内下拉框浮动显示问题 */
        .layui-layer-content .layui-form-select {
            position: relative !important;
        }

        .layui-layer-content .layui-form-select dl {
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 999999 !important;
            background: #fff !important;
            border: 1px solid #e6e6e6 !important;
            border-top: none !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        }

        .layui-layer-content .layui-form-selectup dl {
            top: auto !important;
            bottom: 100% !important;
            border-top: 1px solid #e6e6e6 !important;
            border-bottom: none !important;
        }

        .layui-layer-content .layui-form-select .layui-anim {
            z-index: 999999 !important;
        }

        /* 确保弹窗容器允许溢出 */
        .layui-layer-content {
            overflow: visible !important;
        }

        .layui-layer-content .modal-form {
            overflow: visible !important;
        }

        .layui-layer-content .modal-form .form-row {
            overflow: visible !important;
        }

        .layui-layer-content .modal-form .form-group {
            overflow: visible !important;
            position: relative !important;
        }

        /* 确保下拉框选项不被其他元素遮挡 */
        .layui-layer-content .layui-form-select.layui-form-selected {
            z-index: 999999 !important;
        }

        /* 修复下拉框选项的样式 */
        .layui-layer-content .layui-form-select dd {
            padding: 8px 15px !important;
            cursor: pointer !important;
        }

        .layui-layer-content .layui-form-select dd:hover {
            background-color: #f2f2f2 !important;
        }

        .layui-layer-content .layui-form-select dd.layui-this {
            background-color: #667eea !important;
            color: #fff !important;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>
                <i class="layui-icon layui-icon-chart-screen icon"></i>
                部门产出比例统计
            </h1>
            <div class="subtitle">管理各部门在项目中的参与比例，实现精准的产出分析和成本分摊</div>
        </div>

        <!-- 功能说明 -->
        <div class="content-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="layui-icon layui-icon-tips"></i>
                    功能说明
                </h3>
            </div>
            <div class="card-body">
                <div class="tips-section">
                    <div class="tips-title">
                        <i class="layui-icon layui-icon-about"></i>
                        重要说明
                    </div>
                    <ul class="tips-list">
                        <li>📊 <strong>设计开发部门特殊规则：</strong>设计开发一部、设计开发二部、设计开发三部在同一项目中的参与比例总和不能超过100%</li>
                        <li>🏢 <strong>其他部门独立管理：</strong>生产部、电控部部等其他部门可独立设置参与比例，无需与其他部门联合验证</li>
                        <li>💰 <strong>价格分摊计算：</strong>部门单位产出分析将根据设置的参与比例分摊项目价格</li>
                        <li>🔄 <strong>向后兼容：</strong>未设置参与比例的项目，部门默认按100%参与计算</li>
                        <!-- <li>⚠️ <strong>权限控制：</strong>用户只能管理有权限的部门数据</li> -->
                    </ul>
                </div>
            </div>
        </div>

        <!-- 筛选和操作区域 -->
        <div class="content-card filter-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="layui-icon layui-icon-search"></i>
                    筛选条件
                </h3>
            </div>
            <div class="card-body">
                <form class="layui-form filter-form" lay-filter="searchForm">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目类型</label>
                                <div class="layui-input-block">
                                    <select name="project_type_id" lay-filter="projectTypeSelect">
                                        <option value="">全部类型</option>
                                        {% for project_type in project_types %}
                                        {% if project_type.dept_name not in ['会议', '其他', '方案支持', '培训'] %}
                                        <option value="{{ project_type.id }}">{{ project_type.dept_name }}</option>
                                        {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目编号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="project_code" placeholder="请输入项目编号" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择部门</label>
                                <div class="layui-input-block">
                                    <select name="dept_id" lay-filter="deptSelect">
                                        <option value="">全部部门</option>
                                        {% for dept in depts %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <div class="layui-input-block" style="margin-left: 0;">
                                    <button class="layui-btn btn-primary" lay-submit lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i> 查询
                                    </button>
                                    <button type="reset" class="layui-btn layui-btn-warm">
                                        <i class="layui-icon layui-icon-refresh"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="content-card">
            <div class="card-body">
                <div class="action-buttons">
                    <button class="layui-btn btn-primary" id="addBtn">
                        <i class="layui-icon layui-icon-add-1"></i> 添加参与比例
                    </button>
                    <button class="layui-btn btn-success" id="batchImportBtn">
                        <i class="layui-icon layui-icon-upload"></i> 批量导入
                    </button>
                    <button class="layui-btn layui-btn-normal" id="exportBtn">
                        <i class="layui-icon layui-icon-download-circle"></i> 导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="content-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="layui-icon layui-icon-table"></i>
                    参与比例数据
                </h3>
            </div>
            <div class="card-body">
                <table class="layui-hide" id="participationTable" lay-filter="participationTable"></table>
            </div>
        </div>
    </div>

    <!-- 添加/编辑弹窗 -->
    <div id="addEditForm" style="display: none;">
        <div class="modal-form">
            <form class="layui-form" lay-filter="addEditForm">
                <input type="hidden" name="id" />

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">项目类型 <span class="required">*</span></label>
                        <select name="project_type_id" class="form-input" lay-filter="formProjectTypeSelect" lay-verify="required">
                            <option value="">请选择项目类型</option>
                            {% for project_type in project_types %}
                            {% if project_type.dept_name not in ['会议', '其他', '方案支持', '培训'] %}
                            <option value="{{ project_type.id }}">{{ project_type.dept_name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">项目编号 <span class="required">*</span></label>
                        <select name="project_id" class="form-input" lay-filter="formProjectSelect" lay-verify="required">
                            <option value="">请先选择项目类型</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">参与部门 <span class="required">*</span></label>
                        <select name="dept_id" class="form-input" lay-verify="required">
                            <option value="">请选择部门</option>
                            {% for dept in depts %}
                            <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">参与比例 <span class="required">*</span></label>
                        <div style="position: relative;">
                            <input type="number" name="participation_rate" placeholder="请输入参与比例(0-100)"
                                   class="form-input" lay-verify="required|number" min="0" max="100" step="0.01">
                            <span style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); color: #6b7280; font-size: 14px;">%</span>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label class="form-label">备注</label>
                        <textarea name="remark" placeholder="请输入备注信息" class="form-textarea"></textarea>
                    </div>
                </div>

                <!-- 验证提示区域 -->
                <div id="validationTip" class="validation-tip" style="display: none;"></div>

                <div class="form-buttons">
                    <button type="reset" class="btn btn-secondary">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                    <button class="btn btn-primary" lay-submit lay-filter="submitForm">
                        <i class="layui-icon layui-icon-ok"></i>
                        确认提交
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 表格操作按钮模板 -->
    <script type="text/html" id="operationTpl">
        <div class="layui-btn-group">
            <button class="layui-btn layui-btn-xs" lay-event="edit">
                <i class="layui-icon layui-icon-edit"></i> 编辑
            </button>
            <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">
                <i class="layui-icon layui-icon-delete"></i> 删除
            </button>
        </div>
    </script>

    <!-- 参与比例显示模板 -->
    <script type="text/html" id="rateTpl">
        {%raw%}
        {{# if(d.participation_rate) { }}
            <span style="font-weight: bold; color: #1890ff;">{{ d.participation_rate }}%</span>
        {{# } else { }}
            <span style="color: #999;">-</span>
        {{# } }}
        {%endraw%}
    </script>

    <!-- 部门名称显示模板（设计开发部门特殊标识） -->
    <script type="text/html" id="deptNameTpl">
        {%raw%}
        {{# if(d.dept_name && (d.dept_name.indexOf('设计开发') !== -1)) { }}
            <span style="color: #ff7875;">
                <i class="layui-icon layui-icon-group"></i> {{ d.dept_name }}
            </span>
        {{# } else { }}
            <span>{{ d.dept_name || '-' }}</span>
        {{# } }}
        {%endraw%}
    </script>

    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'jquery'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.jquery;

            // 全局变量
            var currentEditId = null;
            var projectsData = {}; // 存储项目数据

            // 修复下拉框层级问题
            $(document).on('click', '.layui-form-select', function(){
                $(this).css('z-index', '99999');
                $('.layui-form-select dl').css('z-index', '99999');
            });

            // 初始化表格
            var tableIns = table.render({
                elem: '#participationTable',
                url: '/system/dept_output/participation/data',
                method: 'GET',
                page: true,
                limits: [10, 20, 50, 100],
                limit: 20,
                loading: true,
                parseData: function(res) {
                    // 解析后端返回的数据格式
                    return {
                        "code": res.success ? 0 : 1,
                        "msg": res.msg || "",
                        "count": res.data ? res.data.total : 0,
                        "data": res.data ? res.data.items : []
                    };
                },
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'project_type_name', title: '项目类型', width: 120},
                    {field: 'project_code', title: '项目编号', width: 120},
                    {field: 'project_name', title: '项目名称', width: 240, templet: function(d){
                        return d.project_name || '-';
                    }},
                    {field: 'dept_name', title: '参与部门', width: 180, templet: '#deptNameTpl'},
                    {field: 'participation_rate', title: '参与比例', width: 180, templet: '#rateTpl'},
                    {field: 'remark', title: '备注', width: 150, templet: function(d){
                        return d.remark || '-';
                    }},
                    {field: 'creator_name', title: '创建人', width: 100, templet: function(d){
                        return d.creator_name || '-';
                    }},
                    {field: 'created_at', title: '创建时间', width: 150, sort: true},
                    {title: '操作', width: 200, align: 'center', toolbar: '#operationTpl', fixed: 'right'}
                ]],
                done: function(res, curr, count){
                    // 表格渲染完成后的回调
                    console.log('表格数据加载完成，共' + count + '条记录');
                }
            });

            // 搜索功能
            form.on('submit(search)', function(data){
                var field = data.field;

                // 重新加载表格
                tableIns.reload({
                    where: {
                        project_type_id: field.project_type_id,
                        project_code: field.project_code,
                        dept_id: field.dept_id
                    },
                    page: {
                        curr: 1 // 重新从第1页开始
                    }
                });

                return false;
            });

            // 重置表单
            $('button[type="reset"]').click(function(){
                setTimeout(function(){
                    form.render(); // 重新渲染表单
                    tableIns.reload({
                        where: {},
                        page: {
                            curr: 1
                        }
                    });
                }, 100);
            });

            // 添加按钮事件
            $('#addBtn').click(function(){
                openAddEditDialog('add');
            });

            // 批量导入按钮事件
            $('#batchImportBtn').click(function(){
                layer.msg('批量导入功能开发中...', {icon: 1});
            });

            // 导出按钮事件
            $('#exportBtn').click(function(){
                layer.msg('导出功能开发中...', {icon: 1});
            });

            // 表格行工具事件
            table.on('tool(participationTable)', function(obj){
                var data = obj.data;
                var layEvent = obj.event;

                if(layEvent === 'edit'){
                    openAddEditDialog('edit', data);
                } else if(layEvent === 'delete'){
                    deleteParticipation(data);
                }
            });

            // 项目类型选择事件（表单中）
            form.on('select(formProjectTypeSelect)', function(data){
                var projectTypeId = data.value;
                loadProjectsByType(projectTypeId);
            });

            // 参与比例输入事件（实时验证）
            $(document).on('input', 'input[name="participation_rate"]', function(){
                var rate = $(this).val();
                var projectId = $('select[name="project_id"]').val();
                var deptId = $('select[name="dept_id"]').val();

                if(rate && projectId && deptId){
                    validateParticipationRate(projectId, deptId, rate, currentEditId);
                }
            });

            // 部门选择事件（实时验证）
            form.on('select(dept_id)', function(data){
                var deptId = data.value;
                var rate = $('input[name="participation_rate"]').val();
                var projectId = $('select[name="project_id"]').val();

                if(rate && projectId && deptId){
                    validateParticipationRate(projectId, deptId, rate, currentEditId);
                }
            });

            // 表单提交事件
            form.on('submit(submitForm)', function(data){
                var field = data.field;
                var isEdit = currentEditId !== null;

                // 最终验证
                if(!validateForm(field)){
                    return false;
                }

                var url = isEdit ? '/system/dept_output/participation/edit' : '/system/dept_output/participation/save';
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});

                $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(field),
                    success: function(res){
                        layer.close(loadIndex);
                        if(res.success){
                            layer.msg(res.msg, {icon: 1});
                            layer.closeAll('page');
                            tableIns.reload(); // 刷新表格
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.close(loadIndex);
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });

                return false;
            });

            // 打开添加/编辑对话框
            function openAddEditDialog(type, data){
                var title = type === 'add' ? '添加参与比例' : '编辑参与比例';
                currentEditId = type === 'edit' ? data.id : null;

                var content = $('#addEditForm').html();

                layer.open({
                    type: 1,
                    title: title,
                    area: ['700px', '600px'],
                    content: content,
                    maxmin: true,
                    shade: 0.3,
                    shadeClose: false,
                    success: function(layero, index){
                        var form_new = layui.form;

                        // 强制重新渲染表单
                        setTimeout(function(){
                            form_new.render();

                            // 确保下拉框正确浮动显示
                            $(layero).find('.layui-form-select').each(function(){
                                var $select = $(this);
                                $select.on('click', function(){
                                    // 确保当前下拉框的选项列表在最顶层
                                    $select.find('dl').css({
                                        'position': 'absolute',
                                        'z-index': '999999',
                                        'top': '100%',
                                        'left': '0',
                                        'right': '0'
                                    });
                                });
                            });
                        }, 100);

                        if(type === 'edit' && data){
                            // 填充编辑数据
                            layero.find('input[name="id"]').val(data.id);
                            layero.find('select[name="project_type_id"]').val(data.project_type_id);
                            layero.find('select[name="dept_id"]').val(data.dept_id);
                            layero.find('input[name="participation_rate"]').val(data.participation_rate);
                            layero.find('textarea[name="remark"]').val(data.remark || '');

                            // 加载项目数据并设置选中值
                            if(data.project_type_id){
                                loadProjectsByType(data.project_type_id, function(){
                                    layero.find('select[name="project_id"]').val(data.project_id);
                                    form_new.render('select');
                                });
                            }
                        }

                        form_new.render();

                        // 重新绑定项目类型选择事件
                        form_new.on('select(formProjectTypeSelect)', function(data){
                            var projectTypeId = data.value;
                            loadProjectsByType(projectTypeId);
                        });

                        // 重新绑定表单提交事件
                        form_new.on('submit(submitForm)', function(data){
                            var field = data.field;
                            var isEdit = currentEditId !== null;

                            if(!validateForm(field)){
                                return false;
                            }

                            var url = isEdit ? '/system/dept_output/participation/edit' : '/system/dept_output/participation/save';
                            var loadIndex = layer.load(2, {shade: [0.3, '#000']});

                            $.ajax({
                                url: url,
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify(field),
                                success: function(res){
                                    layer.close(loadIndex);
                                    if(res.success){
                                        layer.msg(res.msg, {icon: 1});
                                        layer.close(index);
                                        tableIns.reload();
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                },
                                error: function(){
                                    layer.close(loadIndex);
                                    layer.msg('网络错误，请稍后重试', {icon: 2});
                                }
                            });

                            return false;
                        });
                    }
                });
            }

            // 删除参与比例
            function deleteParticipation(data){
                layer.confirm('确定要删除这条参与比例记录吗？', {
                    icon: 3,
                    title: '确认删除'
                }, function(index){
                    var loadIndex = layer.load(2, {shade: [0.3, '#000']});

                    $.ajax({
                        url: '/system/dept_output/participation/delete',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({id: data.id}),
                        success: function(res){
                            layer.close(loadIndex);
                            if(res.success){
                                layer.msg(res.msg, {icon: 1});
                                layer.close(index);
                                tableIns.reload();
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        },
                        error: function(){
                            layer.close(loadIndex);
                            layer.msg('网络错误，请稍后重试', {icon: 2});
                        }
                    });
                });
            }

            // 根据项目类型加载项目列表
            function loadProjectsByType(projectTypeId, callback){
                if(!projectTypeId){
                    $('select[name="project_id"]').html('<option value="">请先选择项目类型</option>');
                    form.render('select');
                    return;
                }

                // 如果已经缓存了数据，直接使用
                if(projectsData[projectTypeId]){
                    updateProjectSelect(projectsData[projectTypeId]);
                    if(callback) callback();
                    return;
                }

                // 调用后端API获取项目详细信息
                $.ajax({
                    url: '/system/dept_output/api/projects_by_type',
                    type: 'GET',
                    data: {project_type_id: projectTypeId},
                    success: function(res){
                        if(res.success && res.data){
                            // 缓存项目数据
                            projectsData[projectTypeId] = res.data;

                            // 更新项目选择下拉框
                            updateProjectSelect(res.data);
                            if(callback) callback();
                        } else {
                            layer.msg('加载项目列表失败: ' + (res.msg || '未知错误'), {icon: 2});
                        }
                    },
                    error: function(){
                        layer.msg('网络错误，无法加载项目列表', {icon: 2});
                    }
                });
            }

            // 更新项目选择下拉框
            function updateProjectSelect(projects){
                var html = '<option value="">请选择项目</option>';
                if(projects && projects.length > 0){
                    projects.forEach(function(project){
                        html += '<option value="' + project.id + '">' + project.project_code + ' - ' + (project.project_name || '未命名项目') + '</option>';
                    });
                } else {
                    html = '<option value="">该类型下暂无项目</option>';
                }

                $('select[name="project_id"]').html(html);
                form.render('select');
            }

            // 验证参与比例
            function validateParticipationRate(projectId, deptId, rate, excludeId){
                if(!projectId || !deptId || !rate){
                    hideValidationTip();
                    return;
                }

                $.ajax({
                    url: '/system/dept_output/participation/validate',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        project_id: projectId,
                        dept_id: deptId,
                        participation_rate: parseFloat(rate),
                        exclude_id: excludeId
                    }),
                    success: function(res){
                        if(res.success && res.data){
                            if(res.data.valid){
                                showValidationTip(res.data.message, 'success');
                            } else {
                                showValidationTip(res.data.message, 'error');
                            }
                        }
                    },
                    error: function(){
                        showValidationTip('验证失败，请检查网络连接', 'error');
                    }
                });
            }

            // 显示验证提示
            function showValidationTip(message, type){
                var tipClass = 'validation-tip';
                if(type === 'error'){
                    tipClass += ' error';
                } else if(type === 'warning'){
                    tipClass += ' warning';
                }

                var html = '<div class="' + tipClass + '">' + message + '</div>';
                $('#validationTip').html(html).show();
            }

            // 隐藏验证提示
            function hideValidationTip(){
                $('#validationTip').hide();
            }

            // 表单验证
            function validateForm(field){
                if(!field.project_id){
                    layer.msg('请选择项目', {icon: 2});
                    return false;
                }

                if(!field.dept_id){
                    layer.msg('请选择部门', {icon: 2});
                    return false;
                }

                var rate = parseFloat(field.participation_rate);
                if(isNaN(rate) || rate < 0 || rate > 100){
                    layer.msg('参与比例必须在0-100之间', {icon: 2});
                    return false;
                }

                return true;
            }
        });
    </script>
</body>
</html>
