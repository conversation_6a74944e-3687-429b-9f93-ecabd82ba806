<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工试用期到期提醒</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px;
        }
        .greeting {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
        }
        .employee-card {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .employee-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            color: #6c757d;
            min-width: 100px;
            margin-right: 10px;
        }
        .info-value {
            color: #495057;
            flex: 1;
        }
        .status-urgent {
            color: #dc3545;
            font-weight: bold;
            font-size: 18px;
        }
        .status-warning {
            color: #fd7e14;
            font-weight: bold;
            font-size: 18px;
        }
        .status-normal {
            color: #28a745;
            font-weight: bold;
        }
        .action-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        .action-section h3 {
            margin-top: 0;
            color: #856404;
        }
        .action-list {
            margin: 0;
            padding-left: 20px;
        }
        .action-list li {
            margin-bottom: 8px;
            color: #856404;
        }
        .system-link {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: bold;
        }
        .system-link:hover {
            background-color: #0056b3;
            color: white;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .priority-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 10px;
        }
        .priority-urgent {
            background-color: #dc3545;
            color: white;
        }
        .priority-warning {
            background-color: #fd7e14;
            color: white;
        }
        .priority-normal {
            background-color: #28a745;
            color: white;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .info-label {
                min-width: auto;
                margin-bottom: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon">⏰</div>
            <h1>员工试用期到期提醒</h1>
            <span class="priority-badge priority-{{ urgency_class }}">{{ priority_text }}</span>
        </div>
        
        <div class="content">
            <div class="greeting">
                您好，
            </div>
            
            <p>以下员工的<strong>{{ employee.is_formal }}</strong>即将到期，请及时处理：</p>
            
            <div class="employee-card">
                <h3>👤 员工详细信息</h3>
                
                <div class="info-row">
                    <span class="info-label">姓名：</span>
                    <span class="info-value">{{ employee.name }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">工号：</span>
                    <span class="info-value">{{ employee.employee_id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">部门：</span>
                    <span class="info-value">{{ employee.dept.dept_name if employee.dept else '未分配部门' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">职位：</span>
                    <span class="info-value">{{ employee.position or '未设置职位' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">入职日期：</span>
                    <span class="info-value">{{ employee.hire_date.strftime('%Y年%m月%d日') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">员工状态：</span>
                    <span class="info-value">{{ employee.is_formal }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">到期日期：</span>
                    <span class="info-value">{{ end_date.strftime('%Y年%m月%d日') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">剩余天数：</span>
                    <span class="info-value status-{{ urgency_class }}">
                        {% if days_remaining <= 0 %}
                            已逾期 {{ (days_remaining * -1) }} 天
                        {% else %}
                            {{ days_remaining }} 天
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div class="action-section">
                <h3>📋 需要处理的事项</h3>
                <ul class="action-list">
                    <li>完成员工绩效评估和工作表现评价</li>
                    <li>确定是否转为正式员工或延长试用期</li>
                    <li>准备相关转正手续或离职手续</li>
                    <li>更新员工系统中的状态信息</li>
                    <li>通知相关部门和员工本人</li>
                </ul>
            </div>
            
            <div style="text-align: center;">
                <a href="{{ system_url or '#' }}" class="system-link">
                    🖥️ 登录员工管理系统
                </a>
            </div>
            
            <div style="margin-top: 30px; padding: 15px; background-color: #e7f3ff; border-radius: 4px;">
                <p style="margin: 0; color: #0066cc; font-size: 14px;">
                    <strong>💡 温馨提示：</strong>
                    请在到期日前完成相关手续，避免影响员工的正常工作和薪资发放。
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>此邮件由人事管理系统自动发送，请勿直接回复此邮件。</p>
            <p>如有疑问，请联系人力资源部门。</p>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                发送时间：{{ sent_time.strftime('%Y年%m月%d日 %H:%M') }}
            </p>
        </div>
    </div>
</body>
</html>
