<!DOCTYPE html>
<html>
<head>
    <title>项目回款详情</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f7fa;
        }
        .layui-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .layui-card:hover {
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #ebeef5;
            padding: 15px 20px;
            background-color: #fff;
            border-radius: 8px 8px 0 0;
        }
        .layui-card-body {
            padding: 20px;
            background-color: #fff;
            border-radius: 0 0 8px 8px;
        }

        /* 信息卡片样式 */
        .info-card {
            padding: 20px;
        }
        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 12px;
            color: #333;
        }
        .info-item {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .info-label {
            font-weight: 600;
            display: inline-block;
            width: 120px;
            color: #606266;
        }
        .info-value {
            display: inline-block;
            color: #333;
            font-weight: 500;
        }

        /* 回款状态标签样式 */
        .payment-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            min-width: 80px;
        }
        .payment-status.status-unpaid {
            background-color: #F56C6C;
            color: #fff;
        }
        .payment-status.status-paid {
            background-color: #67C23A;
            color: #fff;
        }
        .payment-status.status-required {
            background-color: #E6A23C;
            color: #fff;
        }
        .payment-status.status-partial {
            background-color: #409EFF;
            color: #fff;
        }



        /* 回款卡片样式 */
        .payment-card {
            padding: 25px;
            margin-bottom: 15px;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .payment-card:hover {
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .payment-card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 12px;
        }
        .payment-card-amount {
            margin: 15px 0;
        }
        .payment-card-amount div {
            margin-bottom: 10px;
            font-size: 15px;
            color: #606266;
        }
        .payment-card-amount div span {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }
        .payment-card-status {
            margin: 15px 0;
        }
        .payment-card-action {
            margin-top: 20px;
        }

        /* 按钮样式 */
        .layui-btn {
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .layui-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .layui-btn-primary {
            border-color: #dcdfe6;
            color: #606266;
        }
        .layui-btn-primary:hover {
            border-color: #c6e2ff;
            color: #409eff;
            background-color: #ecf5ff;
        }
        .edit-payment-btn {
            width: 100%;
            background-color: #409EFF;
        }

        /* 响应式调整 */
        @media screen and (max-width: 768px) {
            .layui-col-md3 {
                width: 100%;
                margin-bottom: 15px;
            }
            .info-label {
                width: 100px;
            }
        }
    </style>
</head>
<body class="pear-container">

    <!-- 页面标题和操作按钮 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <h2 style="margin: 5px 0; font-size: 22px; color: #333; font-weight: 600;">
                        <i class="layui-icon layui-icon-rmb" style="margin-right: 8px; font-size: 22px; color: #409EFF;"></i>项目回款详情
                    </h2>
                </div>
                <!-- <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-primary" id="back-btn">
                        <i class="layui-icon layui-icon-return"></i> 返回列表
                    </button>
                </div> -->
            </div>
        </div>
    </div>

    <!-- 项目基本信息 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-app" style="margin-right: 8px; color: #409EFF;"></i>项目基本信息
        </div>
        <div class="layui-card-body info-card">
            <div class="layui-row layui-col-space20">
                <div class="layui-col-md6">
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-template-1" style="margin-right: 5px; font-size: 14px;"></i>项目名称：</span>
                        <span class="info-value" id="project-name">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-vercode" style="margin-right: 5px; font-size: 14px;"></i>项目编号：</span>
                        <span class="info-value" id="project-code">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-flag" style="margin-right: 5px; font-size: 14px;"></i>项目状态：</span>
                        <span class="info-value" id="project-status">--</span>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-rmb" style="margin-right: 5px; font-size: 14px;"></i>项目价格：</span>
                        <span class="info-value" id="project-price">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-date" style="margin-right: 5px; font-size: 14px;"></i>合同开始日：</span>
                        <span class="info-value" id="project-start-date">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label"><i class="layui-icon layui-icon-ok" style="margin-right: 5px; font-size: 14px;"></i>验收完成日：</span>
                        <span class="info-value" id="acceptance-date">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回款信息 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-rmb" style="margin-right: 8px; color: #409EFF;"></i>回款信息
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space20">
                <!-- 预付款 -->
                <div class="layui-col-md3">
                    <div class="payment-card">
                        <div class="payment-card-title">
                            <i class="layui-icon layui-icon-dollar" style="margin-right: 5px; color: #409EFF;"></i>预付款
                        </div>
                        <div class="payment-card-amount">
                            <div>应收金额：<span id="prepayment-amount">--</span></div>
                            <div>实际回款：<span id="actual-prepayment-amount">--</span></div>
                        </div>
                        <div class="payment-card-status" id="prepayment-status">--</div>
                        <div class="payment-card-info" style="margin: 15px 0; border-top: 1px dashed #ebeef5; padding-top: 15px;">
                            <div style="margin-bottom: 8px;">
                                <i class="layui-icon layui-icon-date" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">付款日期：</span>
                                <span id="prepayment-date">--</span>
                            </div>
                            <div>
                                <i class="layui-icon layui-icon-about" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">备注：</span>
                                <span id="prepayment-remark">--</span>
                            </div>
                        </div>
                        <div class="payment-card-action">
                            <button class="layui-btn layui-btn-sm edit-payment-btn" data-type="prepayment">
                                <i class="layui-icon layui-icon-edit"></i> 修改回款
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 发货款 -->
                <div class="layui-col-md3">
                    <div class="payment-card">
                        <div class="payment-card-title">
                            <i class="layui-icon layui-icon-cart" style="margin-right: 5px; color: #409EFF;"></i>发货款
                        </div>
                        <div class="payment-card-amount">
                            <div>应收金额：<span id="delivery-payment-amount">--</span></div>
                            <div>实际回款：<span id="actual-delivery-payment-amount">--</span></div>
                        </div>
                        <div class="payment-card-status" id="delivery-payment-status">--</div>
                        <div class="payment-card-info" style="margin: 15px 0; border-top: 1px dashed #ebeef5; padding-top: 15px;">
                            <div style="margin-bottom: 8px;">
                                <i class="layui-icon layui-icon-date" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">付款日期：</span>
                                <span id="delivery-payment-date">--</span>
                            </div>
                            <div>
                                <i class="layui-icon layui-icon-about" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">备注：</span>
                                <span id="delivery-payment-remark">--</span>
                            </div>
                        </div>
                        <div class="payment-card-action">
                            <button class="layui-btn layui-btn-sm edit-payment-btn" data-type="delivery_payment">
                                <i class="layui-icon layui-icon-edit"></i> 修改回款
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 验收款 -->
                <div class="layui-col-md3">
                    <div class="payment-card">
                        <div class="payment-card-title">
                            <i class="layui-icon layui-icon-ok-circle" style="margin-right: 5px; color: #409EFF;"></i>验收款
                        </div>
                        <div class="payment-card-amount">
                            <div>应收金额：<span id="acceptance-payment-amount">--</span></div>
                            <div>实际回款：<span id="actual-acceptance-payment-amount">--</span></div>
                        </div>
                        <div class="payment-card-status" id="acceptance-payment-status">--</div>
                        <div class="payment-card-info" style="margin: 15px 0; border-top: 1px dashed #ebeef5; padding-top: 15px;">
                            <div style="margin-bottom: 8px;">
                                <i class="layui-icon layui-icon-date" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">付款日期：</span>
                                <span id="acceptance-payment-date">--</span>
                            </div>
                            <div>
                                <i class="layui-icon layui-icon-about" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">备注：</span>
                                <span id="acceptance-payment-remark">--</span>
                            </div>
                        </div>
                        <div class="payment-card-action">
                            <button class="layui-btn layui-btn-sm edit-payment-btn" data-type="acceptance_payment">
                                <i class="layui-icon layui-icon-edit"></i> 修改回款
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 质保金 -->
                <div class="layui-col-md3">
                    <div class="payment-card">
                        <div class="payment-card-title">
                            <i class="layui-icon layui-icon-auz" style="margin-right: 5px; color: #409EFF;"></i>质保金
                        </div>
                        <div class="payment-card-amount">
                            <div>应收金额：<span id="warranty-payment-amount">--</span></div>
                            <div>实际回款：<span id="actual-warranty-payment-amount">--</span></div>
                        </div>
                        <div class="payment-card-status" id="warranty-payment-status">--</div>
                        <div class="payment-card-info" style="margin: 15px 0; border-top: 1px dashed #ebeef5; padding-top: 15px;">
                            <div style="margin-bottom: 8px;">
                                <i class="layui-icon layui-icon-date" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">付款日期：</span>
                                <span id="warranty-payment-date">--</span>
                            </div>
                            <div>
                                <i class="layui-icon layui-icon-about" style="color: #409EFF; margin-right: 5px;"></i>
                                <span style="color: #606266; font-weight: 600;">备注：</span>
                                <span id="warranty-payment-remark">--</span>
                            </div>
                        </div>
                        <div class="payment-card-action">
                            <button class="layui-btn layui-btn-sm edit-payment-btn" data-type="warranty_payment">
                                <i class="layui-icon layui-icon-edit"></i> 修改回款
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 回款历史记录 -->
            <div class="layui-row layui-col-space20" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header" style="font-size: 16px; font-weight: 600;">
                            <i class="layui-icon layui-icon-log" style="margin-right: 8px; color: #409EFF;"></i>回款历史记录
                        </div>
                        <div class="layui-card-body">
                            <table id="payment-history-table" lay-filter="payment-history-table"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script>
    layui.use(['layer', 'jquery', 'form', 'laydate', 'table'], function() {
        let layer = layui.layer;
        let $ = layui.jquery;
        let form = layui.form;
        let laydate = layui.laydate;
        let table = layui.table;

        // 币种转换为符号的函数
        function getCurrencySymbol(currency) {
            if (!currency) return '¥'; // 默认人民币符号

            switch(currency) {
                case '人民币':
                    return '¥';
                case '美元':
                    return '$';
                case '欧元':
                    return '€';
                case '英镑':
                    return '£';
                case '日元':
                    return '¥';
                case '韩元':
                    return '₩';
                case '港币':
                    return 'HK$';
                case '卢布':
                    return '₽';
                default:
                    return currency; // 如果没有匹配的符号，返回原币种文本
            }
        }

        // 获取项目ID
        let projectId = parseInt("{{ project.id }}");

        // 加载项目回款详情
        function loadPaymentDetails() {
            $.ajax({
                url: '/system/project_payment/api/project/' + projectId,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let data = response.data;

                        // 更新项目基本信息
                        $('#project-name').text(data.project.project_name);

                        // 更新项目编号，如果有项目类型则添加前缀
                        if (data.project.project_type_name) {
                            $('#project-code').html('<span style="font-weight: 600;">' + data.project.project_type_name + '' + data.project.project_code + '</span>');
                        } else {
                            $('#project-code').html('<span style="font-weight: 600;">' + data.project.project_code + '</span>');
                        }
                        $('#project-status').text(data.project.status_name);
                        $('#project-price').text(getCurrencySymbol(data.project.currency) + ' ' + data.project.price);
                        $('#project-start-date').text(data.progress.project_start_date || '--');
                        $('#acceptance-date').text(data.progress.acceptance_date || '--');

                        // 更新回款信息
                        $('#prepayment-amount').text(data.payment.prepayment || 0);
                        $('#delivery-payment-amount').text(data.payment.delivery_payment || 0);
                        $('#acceptance-payment-amount').text(data.payment.acceptance_payment || 0);
                        $('#warranty-payment-amount').text(data.payment.warranty_payment || 0);

                        // 更新实际回款金额
                        $('#actual-prepayment-amount').text(data.payment.actual_prepayment || 0);
                        $('#actual-delivery-payment-amount').text(data.payment.actual_delivery_payment || 0);
                        $('#actual-acceptance-payment-amount').text(data.payment.actual_acceptance_payment || 0);
                        $('#actual-warranty-payment-amount').text(data.payment.actual_warranty_payment || 0);

                        // 更新各类型付款日期
                        $('#prepayment-date').text(data.payment.prepayment_date || '--');
                        $('#delivery-payment-date').text(data.payment.delivery_payment_date || '--');
                        $('#acceptance-payment-date').text(data.payment.acceptance_payment_date || '--');
                        $('#warranty-payment-date').text(data.payment.warranty_payment_date || '--');

                        // 更新各类型付款备注
                        $('#prepayment-remark').text(data.payment.prepayment_remark || '--');
                        $('#delivery-payment-remark').text(data.payment.delivery_payment_remark || '--');
                        $('#acceptance-payment-remark').text(data.payment.acceptance_payment_remark || '--');
                        $('#warranty-payment-remark').text(data.payment.warranty_payment_remark || '--');

                        // 更新回款状态
                        updatePaymentStatus('prepayment', data.payment_status.prepayment_status);
                        updatePaymentStatus('delivery-payment', data.payment_status.delivery_payment_status);
                        updatePaymentStatus('acceptance-payment', data.payment_status.acceptance_payment_status);
                        updatePaymentStatus('warranty-payment', data.payment_status.warranty_payment_status);

                        // 更新审批状态
                        updateApprovalStatus('prepayment', data.pending_approvals.prepayment);
                        updateApprovalStatus('delivery_payment', data.pending_approvals.delivery_payment);
                        updateApprovalStatus('acceptance_payment', data.pending_approvals.acceptance_payment);
                        updateApprovalStatus('warranty_payment', data.pending_approvals.warranty_payment);
                    } else {
                        layer.msg('获取项目回款详情失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('获取项目回款详情失败', {icon: 2});
                }
            });
        }

        // 更新回款状态显示
        function updatePaymentStatus(type, status) {
            let statusText = '';
            let statusClass = '';

            if (status === 0) {
                statusText = '未回款';
                statusClass = 'status-unpaid';
            } else if (status === 1) {
                statusText = '已回款';
                statusClass = 'status-paid';
            } else if (status === 2) {
                statusText = '未到期';
                statusClass = 'status-required';
            } else if (status === 3) {
                statusText = '部分回款';
                statusClass = 'status-partial';
            }

            $('#' + type + '-status').html('<span class="payment-status ' + statusClass + '">' + statusText + '</span>');
        }

        // 更新审批状态显示
        function updateApprovalStatus(type, approvalData) {
            // 将payment_type转换为对应的DOM元素类型
            let elementType = type;
            if (type === 'delivery_payment') {
                elementType = 'delivery-payment';
            } else if (type === 'acceptance_payment') {
                elementType = 'acceptance-payment';
            } else if (type === 'warranty_payment') {
                elementType = 'warranty-payment';
            }

            let buttonSelector = '.edit-payment-btn[data-type="' + type + '"]';
            let statusSelector = '#' + elementType + '-status';

            if (approvalData) {
                // 有待审批的请求
                // 在状态区域添加审批提示
                let approvalHtml = '<div class="approval-pending" style="margin-top: 10px; padding: 8px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px;">' +
                    '<div style="color: #fa8c16; font-weight: 600; margin-bottom: 5px;">' +
                    '<i class="layui-icon layui-icon-time" style="margin-right: 5px;"></i>正在审批中</div>' +
                    '<div style="font-size: 12px; color: #666;">' +
                    '申请金额：' + approvalData.actual_amount + '<br>' +
                    '申请时间：' + approvalData.created_at + '</div>' +
                    '</div>';

                // 在状态元素后添加审批提示
                $(statusSelector).after(approvalHtml);

                // 禁用修改按钮并更改文本
                $(buttonSelector).prop('disabled', true)
                    .removeClass('layui-btn-normal')
                    .addClass('layui-btn-disabled')
                    .html('<i class="layui-icon layui-icon-time"></i> 审批中');
            } else {
                // 没有待审批的请求，移除审批提示
                $(statusSelector).siblings('.approval-pending').remove();

                // 启用修改按钮并恢复文本
                $(buttonSelector).prop('disabled', false)
                    .removeClass('layui-btn-disabled')
                    .addClass('layui-btn-normal')
                    .html('<i class="layui-icon layui-icon-edit"></i> 修改回款');
            }
        }

        // 加载回款历史记录
        function loadPaymentHistory() {
            // 渲染回款历史记录表格
            table.render({
                elem: '#payment-history-table',
                url: '/system/project_payment/api/project/' + projectId + '/payment_history',
                page: true,
                limit: 10,
                limits: [10, 20, 50],
                cols: [[
                    {field: 'payment_type_name', title: '付款类型', align: 'center', templet: function(d) {
                        if (d.payment_type === 'prepayment') {
                            return '<span style="color: #409EFF; font-weight: 600;">预付款</span>';
                        } else if (d.payment_type === 'delivery_payment') {
                            return '<span style="color: #67C23A; font-weight: 600;">发货款</span>';
                        } else if (d.payment_type === 'acceptance_payment') {
                            return '<span style="color: #E6A23C; font-weight: 600;">验收款</span>';
                        } else if (d.payment_type === 'warranty_payment') {
                            return '<span style="color: #F56C6C; font-weight: 600;">质保金</span>';
                        } else {
                            return d.payment_type;
                        }
                    }},
                    {field: 'amount', title: '回款金额', align: 'center'},
                    {field: 'payment_date', title: '付款日期', align: 'center'},
                    {field: 'remark', title: '备注', align: 'center'},
                    {field: 'created_at', title: '记录时间', align: 'center'}
                ]],
                text: {
                    none: '暂无回款历史记录'
                },
                done: function() {
                    // 表格加载完成后的回调
                }
            });
        }

        // 在页面加载时显示加载动画并调用详情函数
        let loadingIndex = layer.load(2, {shade: [0.1, '#fff']});
        loadPaymentDetails();
        loadPaymentHistory();

        // 添加页面加载完成后的回调
        $(document).ajaxStop(function() {
            // 所有ajax请求完成后关闭加载动画
            layer.close(loadingIndex);
        });

        // 返回按钮点击事件
        $('#back-btn').on('click', function() {
            // 添加过渡效果
            layer.msg('正在返回列表...', {icon: 16, time: 500, shade: 0.1});
            setTimeout(function() {
                window.location.href = '/system/project_payment/';
            }, 500);
        });

        // 在父窗口中添加关闭前的处理函数
        if (window.name) {
            try {
                // 获取当前iframe的索引
                var index = parent.layer.getFrameIndex(window.name);

                // 将刷新函数添加到父窗口
                parent.refreshOnClose = function() {
                    refreshParentWindow();
                };

                // 在父窗口中找到当前iframe对应的弹出层
                // 使用parent.layui.jquery代替parent.$
                if (parent.layui && parent.layui.jquery) {
                    var layerElem = parent.layui.jquery('#layui-layer' + index);
                    if (layerElem.length > 0) {
                        // 找到关闭按钮
                        var closeBtn = layerElem.find('.layui-layer-close');

                        // 添加点击事件
                        closeBtn.on('click', function() {
                            // 刷新父窗口数据
                            if (typeof parent.refreshOnClose === 'function') {
                                parent.refreshOnClose();
                            }
                        });
                    }
                }
            } catch (e) {
                console.error('设置关闭按钮事件失败:', e);
            }
        }

        // 刷新父窗口数据的函数
        function refreshParentWindow() {
            try {
                // 刷新父窗口的表格数据
                if (parent.layui && parent.layui.table) {
                    parent.layui.table.reload('payment-table');
                }

                // 刷新父窗口的统计数据
                if (typeof parent.loadStatistics === 'function') {
                    parent.loadStatistics();
                }

                // 如果是从未付款项目列表打开的，也刷新那个表格
                if (typeof parent.getUnpaidProjects === 'function') {
                    var paymentType = getPaymentTypeFromUrl();
                    if (paymentType) {
                        parent.getUnpaidProjects(paymentType, function(projects) {
                            if (parent.layui && parent.layui.table) {
                                parent.layui.table.reload('unpaid-project-table', {
                                    data: projects
                                });
                            }
                        });
                    }
                }
            } catch (e) {
                console.error('刷新父窗口数据失败:', e);
            }
        }

        // 从URL中获取付款类型
        function getPaymentTypeFromUrl() {
            var url = window.location.href;
            var match = url.match(/payment_type=([^&]+)/);
            return match ? match[1] : null;
        }

        // 修改回款状态按钮点击事件
        $('.edit-payment-btn').on('click', function() {
            let paymentType = $(this).data('type');
            let paymentTitle = '';

            // 根据付款类型设置标题
            if (paymentType === 'prepayment') {
                paymentTitle = '预付款';
            } else if (paymentType === 'delivery_payment') {
                paymentTitle = '发货款';
            } else if (paymentType === 'acceptance_payment') {
                paymentTitle = '验收款';
            } else if (paymentType === 'warranty_payment') {
                paymentTitle = '质保金';
            }

            layer.open({
                type: 1,
                title: '<i class="layui-icon layui-icon-edit" style="margin-right: 8px; font-size: 16px; color: #409EFF;"></i>修改' + paymentTitle + '回款',
                area: ['550px', '350px'],
                skin: 'layui-layer-molv',
                shade: 0.3,
                anim: 1,
                content: `
                    <div class="layui-form" style="padding: 25px;">
                        <input type="hidden" name="payment_type" value="${paymentType}">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 120px; font-weight: 600;"><i class="layui-icon layui-icon-rmb" style="margin-right: 5px;"></i>实际回款金额</label>
                            <div class="layui-input-block" style="margin-left: 120px;">
                                <input type="text" name="actual_amount" class="layui-input" placeholder="请输入实际回款金额" style="border-radius: 4px;">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 120px; font-weight: 600;"><i class="layui-icon layui-icon-date" style="margin-right: 5px;"></i>付款日期</label>
                            <div class="layui-input-block" style="margin-left: 120px;">
                                <input type="text" name="payment_date" id="payment-date-picker" class="layui-input" placeholder="请选择付款日期" style="border-radius: 4px;">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 120px; font-weight: 600;"><i class="layui-icon layui-icon-addition" style="margin-right: 5px;"></i>累计回款</label>
                            <div class="layui-input-block" style="margin-left: 120px;">
                                <input type="checkbox" name="is_accumulate" value="true" title="累计回款" lay-skin="primary">
                                <div class="layui-word-aux" style="margin-top: 5px; color: #909399;">
                                    <i class="layui-icon layui-icon-about" style="font-size: 12px;"></i>
                                    选中表示本次回款金额将累加到已有回款金额上，不选中表示一次性回款
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 120px; font-weight: 600;"><i class="layui-icon layui-icon-about" style="margin-right: 5px;"></i>备注</label>
                            <div class="layui-input-block" style="margin-left: 120px;">
                                <textarea name="remark" placeholder="请输入备注" class="layui-textarea" style="min-height: 100px; border-radius: 4px;"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item" style="margin-top: 30px; text-align: center;">
                            <button class="layui-btn" lay-submit lay-filter="savePaymentStatus" style="width: 120px; background-color: #409EFF;">
                                <i class="layui-icon layui-icon-ok"></i> 保存
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary" style="width: 120px; margin-left: 20px;">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                `,
                success: function() {
                    // 渲染表单
                    form.render();

                    // 根据付款类型获取对应的日期和备注
                    let currentDate = '';
                    let currentRemark = '';

                    // 获取当前付款类型的日期和备注
                    $.ajax({
                        url: '/system/project_payment/api/project/' + projectId,
                        type: 'GET',
                        async: false,  // 同步请求，确保在初始化日期选择器前获取到数据
                        success: function(response) {
                            if (response.success) {
                                let data = response.data;

                                // 根据付款类型获取对应的日期和备注
                                if (paymentType === 'prepayment') {
                                    currentDate = data.payment.prepayment_date || '';
                                    currentRemark = data.payment.prepayment_remark || '';
                                } else if (paymentType === 'delivery_payment') {
                                    currentDate = data.payment.delivery_payment_date || '';
                                    currentRemark = data.payment.delivery_payment_remark || '';
                                } else if (paymentType === 'acceptance_payment') {
                                    currentDate = data.payment.acceptance_payment_date || '';
                                    currentRemark = data.payment.acceptance_payment_remark || '';
                                } else if (paymentType === 'warranty_payment') {
                                    currentDate = data.payment.warranty_payment_date || '';
                                    currentRemark = data.payment.warranty_payment_remark || '';
                                }

                                // 如果有备注，填充到表单中
                                if (currentRemark) {
                                    $('textarea[name="remark"]').val(currentRemark);
                                }
                            }
                        }
                    });

                    // 初始化日期选择器
                    laydate.render({
                        elem: '#payment-date-picker',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        value: currentDate || new Date().toLocaleDateString().replace(/\//g, '-'),
                        trigger: 'click',
                        theme: '#409EFF'
                    });

                    // 监听表单提交
                    form.on('submit(savePaymentStatus)', function(data) {
                        // 获取应收金额和实际回款金额
                        let paymentType = data.field.payment_type;
                        let actualAmount = parseFloat(data.field.actual_amount) || 0;
                        let expectedAmount = 0;

                        // 根据付款类型获取对应的应收金额
                        if (paymentType === 'prepayment') {
                            expectedAmount = parseFloat($('#prepayment-amount').text()) || 0;
                        } else if (paymentType === 'delivery_payment') {
                            expectedAmount = parseFloat($('#delivery-payment-amount').text()) || 0;
                        } else if (paymentType === 'acceptance_payment') {
                            expectedAmount = parseFloat($('#acceptance-payment-amount').text()) || 0;
                        } else if (paymentType === 'warranty_payment') {
                            expectedAmount = parseFloat($('#warranty-payment-amount').text()) || 0;
                        }

                        // 如果是累计回款，需要减去已有回款金额
                        let isAccumulate = data.field.is_accumulate === "true";
                        if (isAccumulate) {
                            let currentAmount = 0;
                            if (paymentType === 'prepayment') {
                                currentAmount = parseFloat($('#actual-prepayment-amount').text()) || 0;
                            } else if (paymentType === 'delivery_payment') {
                                currentAmount = parseFloat($('#actual-delivery-payment-amount').text()) || 0;
                            } else if (paymentType === 'acceptance_payment') {
                                currentAmount = parseFloat($('#actual-acceptance-payment-amount').text()) || 0;
                            } else if (paymentType === 'warranty_payment') {
                                currentAmount = parseFloat($('#actual-warranty-payment-amount').text()) || 0;
                            }

                            // 计算累计后的总金额是否超过应收金额
                            if ((currentAmount + actualAmount) > expectedAmount) {
                                layer.msg('累计回款金额（' + (currentAmount + actualAmount) + '）不能大于应收金额（' + expectedAmount + '）！', {icon: 2, time: 3000});
                                return false;
                            }
                        } else {
                            // 不是累计回款，直接比较
                            if (actualAmount > expectedAmount) {
                                layer.msg('实际回款金额（' + actualAmount + '）不能大于应收金额（' + expectedAmount + '）！', {icon: 2, time: 3000});
                                return false;
                            }
                        }

                        // 提交数据
                        $.ajax({
                            url: '/system/project_payment/api/project/' + projectId + '/update_payment_status',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(data.field),
                            success: function(response) {
                                if (response.success) {
                                    layer.msg(response.message, {icon: 1});
                                    layer.closeAll('page');

                                    // 重新加载详情和历史记录
                                    loadPaymentDetails();
                                    loadPaymentHistory();
                                } else {
                                    layer.msg('提交回款请求失败: ' + response.message, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('提交回款请求失败', {icon: 2});
                            }
                        });

                        return false;
                    });
                }
            });
        });
    });
</script>
</html>
