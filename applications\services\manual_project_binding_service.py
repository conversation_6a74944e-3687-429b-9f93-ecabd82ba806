"""
手动项目绑定服务
提供质量异常单的手动项目绑定、解绑、批量操作等功能
"""

import json
import logging
import datetime
from typing import List, Dict, Optional, Tuple
from flask_login import current_user
from applications.extensions import db
from applications.models.quality_exception import QualityException
from applications.models.import_project import Import_project
from applications.models.project_manage_dept import ProjectManageDept
from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
from applications.services.enhanced_project_matcher import EnhancedProjectMatcher
from applications.services.binding_validation_service import BindingValidationService

# 配置日志
logger = logging.getLogger(__name__)


class ManualProjectBindingService:
    """手动项目绑定服务"""

    def __init__(self):
        self.project_matcher = EnhancedProjectMatcher()
        self.validator = BindingValidationService()
    
    def bind_project_to_exception(self, exception_id: int, project_id: int,
                                 notes: str = None) -> Dict:
        """
        手动绑定项目到质量异常单

        Args:
            exception_id: 质量异常单ID
            project_id: 项目ID
            notes: 绑定备注

        Returns:
            dict: 操作结果
        """
        try:
            # 1. 验证绑定请求
            validation_result = self.validator.validate_binding_request(exception_id, project_id)
            if not validation_result['valid']:
                self.validator.log_binding_operation(
                    'manual_bind', exception_id, project_id,
                    success=False, error_message=validation_result['message']
                )
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'error_code': validation_result.get('error_code')
                }

            # 1.5. 检查多项目绑定限制
            multi_binding_check = self._check_multi_binding_restriction(exception_id)
            if not multi_binding_check['allowed']:
                self.validator.log_binding_operation(
                    'manual_bind', exception_id, project_id,
                    success=False, error_message=multi_binding_check['message']
                )
                return {
                    'success': False,
                    'message': multi_binding_check['message'],
                    'error_code': 'MULTI_BINDING_NOT_ALLOWED'
                }

            # 2. 检查绑定冲突
            conflict_result = self.validator.detect_binding_conflicts(exception_id)
            warnings = []
            if conflict_result['has_warnings']:
                warnings = [c['message'] for c in conflict_result['conflicts'] if c['severity'] == 'warning']

            if conflict_result['has_errors']:
                error_messages = [c['message'] for c in conflict_result['conflicts'] if c['severity'] == 'error']
                self.validator.log_binding_operation(
                    'manual_bind', exception_id, project_id,
                    success=False, error_message='; '.join(error_messages)
                )
                return {
                    'success': False,
                    'message': f'绑定冲突: {"; ".join(error_messages)}'
                }
            
            # 3. 从验证结果获取项目和异常信息
            exception_info = validation_result['exception']
            project_info = validation_result['project']

            # 获取项目类型信息
            project_type_name = '未知类型'
            if project_info['type_id']:
                project_type = ProjectManageDept.query.get(project_info['type_id'])
                project_type_name = project_type.dept_name if project_type else '未知类型'

            # 4. 停用现有的自动绑定（如果存在）
            existing_auto_bindings = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception_id,
                binding_type='auto',
                is_active=True,
                status='active'
            ).all()

            deactivated_count = 0
            for auto_binding in existing_auto_bindings:
                auto_binding.is_active = False
                auto_binding.status = 'inactive'
                auto_binding.updated_at = datetime.datetime.now()
                auto_binding.updated_by = current_user.id if current_user.is_authenticated else None
                deactivated_count += 1
                logger.info(f"停用自动绑定: 异常{exception_id} -> 项目{auto_binding.project_id}")

            if deactivated_count > 0:
                warnings.append(f'已停用{deactivated_count}个自动绑定记录')

            # 5. 创建绑定记录
            binding = QualityExceptionProjectBinding.create_binding(
                quality_exception_id=exception_id,
                project_id=project_id,
                binding_type='manual',
                binding_source='manual_bind',
                match_confidence=1.0,  # 手动绑定置信度为1.0
                match_method='manual',
                original_project_code=exception_info['project_code'],
                parsed_project_code=project_info['code'],
                project_type_matched=project_type_name,
                created_by=current_user.id if current_user.is_authenticated else None,
                notes=notes or f'手动绑定项目: {project_info["name"]}',
                match_details=json.dumps({
                    'manual_binding': True,
                    'operator': current_user.username if current_user.is_authenticated else 'system',
                    'project_name': project_info['name'],
                    'project_type': project_type_name,
                    'warnings': warnings,
                    'deactivated_auto_bindings': deactivated_count
                }, ensure_ascii=False)
            )

            db.session.add(binding)

            # 6. 设置为主项目（手动绑定优先级高于自动绑定）
            exception = QualityException.query.get(exception_id)
            exception.project_id = project_id
            logger.info(f"设置主项目: 异常{exception_id} -> 项目{project_id}")

            db.session.commit()

            # 7. 记录操作日志
            self.validator.log_binding_operation(
                'manual_bind', exception_id, project_id,
                details={
                    'project_name': project_info['name'],
                    'binding_type': 'manual',
                    'warnings': warnings,
                    'deactivated_auto_bindings': deactivated_count
                },
                success=True
            )

            logger.info(f"手动绑定成功: 异常{exception_id} -> 项目{project_id}")

            result = {
                'success': True,
                'message': f'成功绑定项目: {project_info["name"]}',
                'binding_id': binding.id,
                'project_info': {
                    'id': project_info['id'],
                    'name': project_info['name'],
                    'code': project_info['code'],
                    'type': project_type_name
                }
            }

            # 7. 添加警告信息（如果有）
            if warnings:
                result['warnings'] = warnings
                result['message'] += f' (注意: {"; ".join(warnings)})'

            return result

        except Exception as e:
            db.session.rollback()
            error_msg = f'手动绑定项目失败: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }

    def _check_multi_binding_restriction(self, exception_id: int) -> Dict:
        """
        检查多项目绑定限制

        Args:
            exception_id: 质量异常单ID

        Returns:
            dict: 检查结果
        """
        try:
            from applications.services.project_code_parser import ProjectCodeParser

            # 获取异常单信息
            exception = QualityException.query.get(exception_id)
            if not exception:
                return {
                    'allowed': False,
                    'message': '质量异常单不存在'
                }

            # 解析项目编号
            parser = ProjectCodeParser()
            parsed_result = parser.parse_project_code(exception.project_code or '')

            # 检查当前绑定数量
            current_binding_count = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception_id,
                is_active=True,
                status='active'
            ).count()

            # 判断是否允许多重绑定
            allow_multiple_binding = len(parsed_result.parsed_codes) > 1

            if not allow_multiple_binding and current_binding_count >= 1:
                return {
                    'allowed': False,
                    'message': f'该异常单只有一个项目编号({exception.project_code})，不支持绑定多个项目'
                }

            return {
                'allowed': True,
                'message': '允许绑定',
                'allow_multiple_binding': allow_multiple_binding,
                'current_binding_count': current_binding_count,
                'parsed_project_count': len(parsed_result.parsed_codes)
            }

        except Exception as e:
            logger.error(f"检查多项目绑定限制失败: {str(e)}")
            return {
                'allowed': False,
                'message': f'检查绑定限制时发生错误: {str(e)}'
            }
    
    def unbind_project_from_exception(self, exception_id: int, project_id: int, 
                                    notes: str = None) -> Dict:
        """
        解绑项目与质量异常单的关联
        
        Args:
            exception_id: 质量异常单ID
            project_id: 项目ID
            notes: 解绑备注
            
        Returns:
            dict: 操作结果
        """
        try:
            # 查找绑定记录
            binding = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception_id,
                project_id=project_id,
                is_active=True
            ).first()
            
            if not binding:
                return {
                    'success': False,
                    'message': '绑定关系不存在或已失效'
                }
            
            # 停用绑定
            success = QualityExceptionProjectBinding.deactivate_binding(
                binding.id,
                updated_by=current_user.id if current_user.is_authenticated else None,
                notes=notes or '手动解绑'
            )
            
            if not success:
                return {
                    'success': False,
                    'message': '解绑操作失败'
                }
            
            # 如果解绑的是主项目，需要重新设置主项目
            exception = QualityException.query.get(exception_id)
            if exception and exception.project_id == project_id:
                # 查找其他有效绑定
                other_bindings = QualityExceptionProjectBinding.query.filter_by(
                    quality_exception_id=exception_id,
                    is_active=True
                ).filter(QualityExceptionProjectBinding.id != binding.id).first()
                
                if other_bindings:
                    exception.project_id = other_bindings.project_id
                    logger.info(f"重新设置主项目: 异常{exception_id} -> 项目{other_bindings.project_id}")
                else:
                    exception.project_id = None
                    logger.info(f"清空主项目: 异常{exception_id}")
            
            db.session.commit()
            
            logger.info(f"手动解绑成功: 异常{exception_id} -> 项目{project_id}")
            
            return {
                'success': True,
                'message': '成功解绑项目'
            }
            
        except Exception as e:
            db.session.rollback()
            error_msg = f'解绑项目失败: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def batch_bind_projects(self, exception_ids: List[int], project_id: int, 
                          notes: str = None) -> Dict:
        """
        批量绑定项目到多个质量异常单
        
        Args:
            exception_ids: 质量异常单ID列表
            project_id: 项目ID
            notes: 绑定备注
            
        Returns:
            dict: 操作结果
        """
        try:
            success_count = 0
            failed_count = 0
            results = []
            
            for exception_id in exception_ids:
                result = self.bind_project_to_exception(exception_id, project_id, notes)
                if result['success']:
                    success_count += 1
                else:
                    failed_count += 1
                
                results.append({
                    'exception_id': exception_id,
                    'success': result['success'],
                    'message': result['message']
                })
            
            return {
                'success': True,
                'message': f'批量绑定完成: 成功{success_count}个，失败{failed_count}个',
                'total': len(exception_ids),
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results
            }
            
        except Exception as e:
            error_msg = f'批量绑定失败: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def get_binding_suggestions(self, exception_id: int) -> Dict:
        """
        获取项目绑定建议
        
        Args:
            exception_id: 质量异常单ID
            
        Returns:
            dict: 绑定建议
        """
        try:
            exception = QualityException.query.get(exception_id)
            if not exception:
                return {
                    'success': False,
                    'message': '质量异常单不存在'
                }
            
            suggestions = []
            
            # 如果有项目编号，使用智能匹配
            if exception.project_code:
                match_result = self.project_matcher.match_projects(
                    project_code=exception.project_code,
                    project_type=exception.project_type
                )
                
                for match in match_result.matches:
                    suggestions.append({
                        'project_id': match.project_id,
                        'project_name': match.project_name,
                        'project_code': match.project_code,
                        'project_type': match.project_type_name,
                        'confidence': match.confidence,
                        'match_type': match.match_type,
                        'reason': match.notes
                    })
            
            # 如果没有智能匹配结果，提供基于项目类型的建议
            if not suggestions and exception.project_type:
                project_type = ProjectManageDept.query.filter_by(
                    dept_name=exception.project_type
                ).first()
                
                if project_type:
                    recent_projects = Import_project.query.filter_by(
                        dept_id=project_type.id
                    ).order_by(Import_project.create_at.desc()).limit(10).all()
                    
                    for project in recent_projects:
                        suggestions.append({
                            'project_id': project.id,
                            'project_name': project.project_name,
                            'project_code': project.project_code,
                            'project_type': exception.project_type,
                            'confidence': 0.5,
                            'match_type': 'type_based',
                            'reason': f'基于项目类型的建议: {exception.project_type}'
                        })
            
            return {
                'success': True,
                'suggestions': suggestions,
                'total': len(suggestions)
            }
            
        except Exception as e:
            error_msg = f'获取绑定建议失败: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
    
    def get_exception_bindings(self, exception_id: int) -> Dict:
        """
        获取质量异常单的所有项目绑定
        
        Args:
            exception_id: 质量异常单ID
            
        Returns:
            dict: 绑定信息
        """
        try:
            bindings = QualityExceptionProjectBinding.get_active_bindings(exception_id)
            
            binding_list = []
            for binding in bindings:
                project = Import_project.query.get(binding.project_id)
                project_type = ProjectManageDept.query.get(project.dept_id) if project else None
                
                binding_info = binding.to_dict()
                binding_info.update({
                    'project_name': project.project_name if project else '未知项目',
                    'project_code': project.project_code if project else '',
                    'project_type_name': project_type.dept_name if project_type else '未知类型'
                })
                
                binding_list.append(binding_info)
            
            return {
                'success': True,
                'bindings': binding_list,
                'total': len(binding_list)
            }
            
        except Exception as e:
            error_msg = f'获取绑定信息失败: {str(e)}'
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
