<!DOCTYPE html>
<html>
<head>
    <title>添加节假日</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="layui-form-item">
                <label class="layui-form-label">节假日日期</label>
                <div class="layui-input-block">
                    <input type="text" name="holiday_date" lay-verify="required" autocomplete="off" 
                           placeholder="请选择节假日日期" class="layui-input" id="holiday-date">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">节假日名称</label>
                <div class="layui-input-block">
                    <input type="text" name="holiday_name" lay-verify="required" autocomplete="off" 
                           placeholder="请输入节假日名称" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">节假日类型</label>
                <div class="layui-input-block">
                    <select name="holiday_type" lay-verify="required" lay-filter="holiday-type">
                        <option value="">请选择节假日类型</option>
                        <option value="legal">法定节假日(3倍工资)</option>
                        <option value="makeup">调休日(2倍工资)</option>
                        <option value="weekend">周末(2倍工资)</option>
                        <option value="company_rest">公司调休休息日(2倍工资)</option>
                        <option value="company_work">公司调休工作日(1.5倍工资)</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">加班倍率</label>
                <div class="layui-input-block">
                    <select name="overtime_rate" lay-verify="required" id="overtime-rate">
                        <option value="">请选择加班倍率</option>
                        <option value="1.5">1.5倍</option>
                        <option value="2.0">2.0倍</option>
                        <option value="3.0">3.0倍</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">年份</label>
                <div class="layui-input-block">
                    <input type="number" name="year" lay-verify="required" autocomplete="off" 
                           placeholder="请输入年份" class="layui-input" min="2020" max="2050" 
                           value="2025">
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注说明</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入备注说明" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>
        </div>
    </div>
    
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="pear-btn pear-btn-primary pear-btn-sm" lay-submit lay-filter="holiday-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="pear-btn pear-btn-primary pear-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
</body>

{% include 'system/common/footer.html' %}

<script>
layui.use(['form', 'laydate', 'layer', 'jquery'], function () {
    let form = layui.form;
    let laydate = layui.laydate;
    let layer = layui.layer;
    let $ = layui.jquery;
    
    // 日期选择器
    laydate.render({
        elem: '#holiday-date',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    
    // 监听节假日类型变化，自动设置加班倍率
    form.on('select(holiday-type)', function(data) {
        let overtimeRateSelect = document.getElementById('overtime-rate');
        let value = data.value;
        
        // 清空当前选择
        overtimeRateSelect.selectedIndex = 0;
        
        if (value === 'legal') {
            // 法定节假日默认3倍
            overtimeRateSelect.value = '3.0';
        } else if (value === 'makeup' || value === 'weekend' || value === 'company_rest') {
            // 调休日、周末、公司调休休息日默认2倍
            overtimeRateSelect.value = '2.0';
        } else if (value === 'company_work') {
            // 公司调休工作日默认1.5倍
            overtimeRateSelect.value = '1.5';
        }
        
        form.render('select');
    });
    
    // 表单提交
    form.on('submit(holiday-save)', function (data) {
        let field = data.field;
        
        // 数据验证
        if (!field.holiday_date) {
            layer.msg('请选择节假日日期', {icon: 2});
            return false;
        }
        
        if (!field.holiday_name.trim()) {
            layer.msg('请输入节假日名称', {icon: 2});
            return false;
        }
        
        if (!field.holiday_type) {
            layer.msg('请选择节假日类型', {icon: 2});
            return false;
        }
        
        if (!field.overtime_rate) {
            layer.msg('请选择加班倍率', {icon: 2});
            return false;
        }
        
        // 从日期中提取年份
        let dateYear = new Date(field.holiday_date).getFullYear();
        field.year = dateYear;
        
        // 提交数据
        layer.load(2);
        $.ajax({
            url: '/system/holiday_config/save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(field),
            success: function (res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg(res.msg, {icon: 1}, function() {
                        // 关闭弹窗
                        let index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function () {
                layer.closeAll('loading');
                layer.msg('提交失败', {icon: 2});
            }
        });
        
        return false;
    });
});
</script>
</html>
