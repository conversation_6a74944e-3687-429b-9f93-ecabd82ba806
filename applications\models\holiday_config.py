from applications.extensions import db
from datetime import datetime


class HolidayConfig(db.Model):
    """节假日配置模型"""
    __tablename__ = 'holiday_config'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    holiday_date = db.Column(db.Date, nullable=False, unique=True, comment='节假日日期')
    holiday_name = db.Column(db.String(100), nullable=False, comment='节假日名称')
    holiday_type = db.Column(
        db.Enum('legal', 'makeup', 'weekend', 'company_rest', 'company_work', name='holiday_type_enum'),
        nullable=False,
        comment='节假日类型：legal=法定节假日(3倍), makeup=调休日(2倍), weekend=周末(2倍), company_rest=公司调休休息日(2倍), company_work=公司调休工作日(1.5倍)'
    )
    overtime_rate = db.Column(db.Numeric(3, 1), nullable=False, comment='加班倍率：1.5, 2.0, 3.0')
    year = db.Column(db.Integer, nullable=False, comment='年份')
    description = db.Column(db.Text, comment='备注说明')
    status = db.Column(db.SmallInteger, default=1, comment='状态：1=启用, 0=禁用')
    created_by = db.Column(db.Integer, comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_by = db.Column(db.Integer, comment='更新人ID')
    updated_at = db.Column(
        db.DateTime, 
        default=datetime.now, 
        onupdate=datetime.now, 
        comment='更新时间'
    )

    def __repr__(self):
        return f'<HolidayConfig {self.holiday_date}: {self.holiday_name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'holiday_date': self.holiday_date.strftime('%Y-%m-%d') if self.holiday_date else None,
            'holiday_name': self.holiday_name,
            'holiday_type': self.holiday_type,
            'holiday_type_text': self.get_holiday_type_text(),
            'overtime_rate': float(self.overtime_rate) if self.overtime_rate else None,
            'year': self.year,
            'description': self.description,
            'status': self.status,
            'status_text': '启用' if self.status == 1 else '禁用',
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    def get_holiday_type_text(self):
        """获取节假日类型文本"""
        type_map = {
            'legal': '法定节假日(3倍)',
            'makeup': '调休日(2倍)',
            'weekend': '周末(2倍)',
            'company_rest': '公司调休休息日(2倍)',
            'company_work': '公司调休工作日(1.5倍)'
        }
        return type_map.get(self.holiday_type, '未知')


class HolidayOperationLog(db.Model):
    """节假日操作日志模型"""
    __tablename__ = 'holiday_operation_log'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    operation_type = db.Column(
        db.Enum('create', 'update', 'delete', 'import', name='operation_type_enum'), 
        nullable=False, 
        comment='操作类型'
    )
    holiday_date = db.Column(db.Date, comment='操作的节假日日期')
    old_data = db.Column(db.JSON, comment='修改前数据')
    new_data = db.Column(db.JSON, comment='修改后数据')
    operator_id = db.Column(db.Integer, nullable=False, comment='操作人ID')
    operator_name = db.Column(db.String(50), comment='操作人姓名')
    operation_time = db.Column(db.DateTime, default=datetime.now, comment='操作时间')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    remark = db.Column(db.Text, comment='操作备注')

    def __repr__(self):
        return f'<HolidayOperationLog {self.operation_type}: {self.holiday_date}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'operation_type': self.operation_type,
            'operation_type_text': self.get_operation_type_text(),
            'holiday_date': self.holiday_date.strftime('%Y-%m-%d') if self.holiday_date else None,
            'old_data': self.old_data,
            'new_data': self.new_data,
            'operator_id': self.operator_id,
            'operator_name': self.operator_name,
            'operation_time': self.operation_time.strftime('%Y-%m-%d %H:%M:%S') if self.operation_time else None,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'remark': self.remark
        }

    def get_operation_type_text(self):
        """获取操作类型文本"""
        type_map = {
            'create': '新增',
            'update': '修改',
            'delete': '删除',
            'import': '导入'
        }
        return type_map.get(self.operation_type, '未知')
