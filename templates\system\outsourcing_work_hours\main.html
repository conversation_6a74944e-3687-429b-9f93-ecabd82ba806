<!DOCTYPE html>
<html>
<head>
    <title>外协工时管理</title>
    {% include 'system/common/header.html' %}
</head>
<style>
    .layui-form-item {
    position: relative;
    margin-bottom: 0px;
    clear: both;
    }
    .layui-form-item .layui-input-inline {
        float: left;
        width: 140px;
        margin-right: 10px;
    }

</style>


<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">项目类型</label>
                    <div class="layui-input-inline">
                        <select name="projectType" lay-search="">
                            <option value="">请选择项目类型</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">项目编号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="projectCode" placeholder="请输入项目编号" class="layui-input">
                    </div>
                </div>
             
                <div class="layui-inline">
                    <label class="layui-form-label">外协名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="outsourcingName" placeholder="请输入外协名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">工作地点</label>
                    <div class="layui-input-inline">
                        <select name="workLocation">
                            <option value="">请选择工作地点</option>
                            <option value="厂内">厂内</option>
                            <option value="厂外">厂外</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">所属部门</label>
                    <div class="layui-input-inline">
                        <select name="deptName" lay-search="">
                            <option value="">请选择部门</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-md" lay-submit lay-filter="work-hours-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table id="work-hours-table" lay-filter="work-hours-table"></table>
    </div>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="work-hours-toolbar">
    <div class="layui-btn-container">
        {% if authorize("system:outsourcing_work_hours:add") %}
        <button class="layui-btn layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            新增
        </button>
        {% endif %}
        {% if authorize("system:outsourcing_work_hours:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>
            批量删除
        </button>
        {% endif %}
    </div>
</script>

<!-- 操作栏模板 -->
<script type="text/html" id="work-hours-bar">
    {% if authorize("system:outsourcing_work_hours:edit") %}
    <a class="layui-btn layui-btn-xs" lay-event="edit">
        <i class="layui-icon layui-icon-edit"></i>
        编辑
    </a>
    {% endif %}
    {% if authorize("system:outsourcing_work_hours:remove") %}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </a>
    {% endif %}
</script>

<!-- 工作地点模板 -->
<script type="text/html" id="work-location-tpl">
    {% raw %}{{# if(d.work_location === '厂内'){ }}{% endraw %}
    <span class="layui-badge layui-badge-normal">{% raw %}{{ d.work_location }}{% endraw %}</span>
    {% raw %}{{# } else { }}{% endraw %}
    <span class="layui-badge layui-badge-warm">{% raw %}{{ d.work_location }}{% endraw %}</span>
    {% raw %}{{# } }}{% endraw %}
</script>

<!-- 工时数模板 -->
<script type="text/html" id="work-hours-tpl">
    <span style="color: #1E9FFF; font-weight: bold;">{% raw %}{{ d.work_hours }}{% endraw %}小时</span>
</script>

<!-- 总费用模板 -->
<script type="text/html" id="total-cost-tpl">
    {% raw %}{{# if(d.total_cost && d.total_cost > 0){ }}{% endraw %}
    <span style="color: #FF5722; font-weight: bold;">¥{% raw %}{{ d.total_cost }}{% endraw %}</span>
    {% raw %}{{# } else { }}{% endraw %}
    <span style="color: #999;">未设置</span>
    {% raw %}{{# } }}{% endraw %}
</script>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['table', 'form', 'layer', 'popup', 'jquery'], function () {
        let table = layui.table
        let form = layui.form
        let layer = layui.layer
        let popup = layui.popup
        let $ = layui.jquery

        const MODULE_PATH = "/system/outsourcing_work_hours/"

        let cols = [
            [
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', width: 80, title: 'ID', sort: true},
                {field: 'project_type', width: 100, title: '项目类型'},
                {field: 'project_code', width: 120, title: '项目编号'},
                {field: 'outsourcing_name', width: 150, title: '外协名称'},
                {field: 'work_location', width: 100, title: '工作地点', templet: '#work-location-tpl'},
                {field: 'work_hours', width: 100, title: '工时数', templet: '#work-hours-tpl'},
                {field: 'work_start_date', width: 110, title: '开始日期'},
                {field: 'work_end_date', width: 110, title: '结束日期'},
                {field: 'hourly_rate', width: 100, title: '时薪(元)'},
                {field: 'total_cost', width: 120, title: '总费用', templet: '#total-cost-tpl'},
                {field: 'dept_name', width: 120, title: '所属部门'},
                {field: 'creator_name', width: 100, title: '创建人'},
                {field: 'create_at', width: 160, title: '创建时间'},
                {title: '操作', toolbar: '#work-hours-bar', align: 'center', width: 200, fixed: 'right'}
            ]
        ]

        // 渲染表格
        table.render({
            elem: '#work-hours-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#work-hours-toolbar',
            text: {none: '暂无外协工时记录'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports']
        })

        // 加载项目类型选项
        $.get(MODULE_PATH + 'project_types', function(res) {
            if(res.success) {
                let options = '<option value="">请选择项目类型</option>'
                res.data.forEach(function(item) {
                    options += '<option value="' + item.value + '">' + item.label + '</option>'
                })
                $('select[name="projectType"]').html(options)
                form.render('select')
            }
        })

        // 加载可管理部门选项（用于筛选）
        $.get(MODULE_PATH + 'manageable_depts', function(res) {
            if(res.success) {
                let options = '<option value="">请选择部门</option>'
                res.data.forEach(function(item) {
                    options += '<option value="' + item.label + '">' + item.label + '</option>'
                })
                $('select[name="deptName"]').html(options)
                form.render('select')
            }
        })

        // 查询
        form.on('submit(work-hours-query)', function (data) {
            table.reload('work-hours-table', {
                where: data.field,
                page: {curr: 1}
            })
            return false
        })

        // 工具栏事件
        table.on('toolbar(work-hours-table)', function (obj) {
            let checkStatus = table.checkStatus(obj.config.id)
            switch (obj.event) {
                case 'add':
                    window.add()
                    break
                case 'batchRemove':
                    if (checkStatus.data.length === 0) {
                        popup.warning('请选择要删除的数据')
                        return
                    }
                    window.batchRemove(checkStatus.data)
                    break
                case 'refresh':
                    table.reload('work-hours-table')
                    break
            }
        })

        // 行工具事件
        table.on('tool(work-hours-table)', function (obj) {
            let data = obj.data
            switch (obj.event) {
                case 'edit':
                    window.edit(data)
                    break
                case 'remove':
                    window.remove(data)
                    break
            }
        })

        // 新增
        window.add = function () {
            layer.open({
                type: 2,
                title: '新增外协工时',
                shade: 0.1,
                area: ['800px', '600px'],
                content: MODULE_PATH + 'add',
                end: function () {
                    table.reload('work-hours-table')
                }
            })
        }

        // 编辑
        window.edit = function (data) {
            layer.open({
                type: 2,
                title: '编辑外协工时',
                shade: 0.1,
                area: ['800px', '600px'],
                content: MODULE_PATH + 'edit?id=' + data.id,
                end: function () {
                    table.reload('work-hours-table')
                }
            })
        }

        // 删除
        window.remove = function (data) {
            layer.confirm('确定要删除这条外协工时记录吗？', {icon: 3, title: '提示'}, function (index) {
                let loading = layer.load()
                $.ajax({
                    url: MODULE_PATH + 'remove/' + data.id,
                    type: 'DELETE',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            popup.success(result.msg)
                            table.reload('work-hours-table')
                        } else {
                            popup.failure(result.msg)
                        }
                    }
                })
                layer.close(index)
            })
        }

        // 批量删除
        window.batchRemove = function (data) {
            let ids = data.map(item => item.id)
            layer.confirm('确定要删除选中的 ' + ids.length + ' 条记录吗？', {icon: 3, title: '提示'}, function (index) {
                let loading = layer.load()
                let promises = ids.map(id => {
                    return $.ajax({
                        url: MODULE_PATH + 'remove/' + id,
                        type: 'DELETE'
                    })
                })
                
                Promise.all(promises).then(function(results) {
                    layer.close(loading)
                    let successCount = results.filter(r => r.success).length
                    if (successCount === ids.length) {
                        popup.success('批量删除成功')
                    } else {
                        popup.warning('部分删除失败，成功删除 ' + successCount + ' 条记录')
                    }
                    table.reload('work-hours-table')
                }).catch(function() {
                    layer.close(loading)
                    popup.failure('批量删除失败')
                })
                layer.close(index)
            })
        }
    })
</script>
</body>
</html>
