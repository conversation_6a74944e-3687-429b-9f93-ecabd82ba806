import json
import datetime
from typing import List, Dict, Optional
from applications.extensions import db
from applications.models.quality_exception import QualityException
from applications.models.import_project import Import_project
from applications.models.admin_dept import Dept
from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
from applications.services.enhanced_project_matcher import EnhancedProjectMatcher


class QualityExceptionProcessor:
    """质量异常单业务处理器"""
    
    def __init__(self):
        from applications.services.dingtalk_service import DingTalkService
        self.dingtalk_service = DingTalkService()
        self.project_matcher = EnhancedProjectMatcher()
    
    def process_quality_exceptions(self, approval_instances: List[Dict]) -> Dict:
        """
        处理质量异常单审批实例数据

        Args:
            approval_instances: 钉钉审批实例列表

        Returns:
            dict: 处理结果
        """
        try:
            # 验证输入参数
            if not isinstance(approval_instances, list):
                return {
                    'status': 'error',
                    'message': f'输入参数类型错误，期望list，实际{type(approval_instances)}',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'results': []
                }

            if not approval_instances:
                return {
                    'status': 'success',
                    'message': '没有需要处理的审批实例',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'results': []
                }

            success_count = 0
            failed_count = 0
            results = []

            print(f"开始处理 {len(approval_instances)} 个质量异常单审批实例")

            for index, instance in enumerate(approval_instances):
                try:
                    print(f"正在处理第 {index + 1}/{len(approval_instances)} 个实例")

                    # 验证实例数据格式
                    if not isinstance(instance, dict):
                        failed_count += 1
                        results.append({
                            'success': False,
                            'process_instance_id': 'unknown',
                            'error': f'实例数据格式错误，期望dict，实际{type(instance)}'
                        })
                        continue

                    # 处理单个异常单
                    result = self._process_single_exception(instance)
                    if result.get('success'):
                        success_count += 1
                    else:
                        failed_count += 1
                    results.append(result)

                except Exception as e:
                    failed_count += 1
                    error_msg = f'处理实例失败: {str(e)}'
                    print(error_msg)
                    results.append({
                        'success': False,
                        'process_instance_id': instance.get('process_instance_id', 'unknown') if isinstance(instance, dict) else 'unknown',
                        'error': error_msg
                    })

            message = f'批量处理完成：成功 {success_count} 条，失败 {failed_count} 条'
            print(message)

            return {
                'status': 'success',
                'total': len(approval_instances),
                'success_count': success_count,
                'failed_count': failed_count,
                'message': message,
                'results': results
            }

        except Exception as e:
            error_msg = f'批量处理失败: {str(e)}'
            print(error_msg)
            return {
                'status': 'error',
                'message': error_msg,
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'results': []
            }
    
    def _process_single_exception(self, instance: Dict) -> Dict:
        """
        处理单个质量异常单

        Args:
            instance: 钉钉审批实例数据

        Returns:
            dict: 处理结果
        """
        try:
            # 标准化实例数据结构
            normalized_instance = self._normalize_instance_data(instance)

            process_instance_id = normalized_instance.get('process_instance_id')
            if not process_instance_id:
                print(f"错误：缺少审批实例ID，原始数据: {instance}")
                return {
                    'success': False,
                    'process_instance_id': 'unknown',
                    'error': '缺少审批实例ID'
                }

            print(f"处理审批实例: {process_instance_id}")
            print(f"标准化后的数据: {normalized_instance}")

            # 检查是否已存在
            existing = QualityException.query.filter_by(
                process_instance_id=process_instance_id
            ).first()

            if existing:
                print(f"更新现有记录: {process_instance_id}")
                # 更新现有记录
                self._update_exception_from_instance(existing, normalized_instance)
                action = 'updated'
            else:
                print(f"创建新记录: {process_instance_id}")
                # 创建新记录
                existing = self._create_exception_from_instance(normalized_instance)
                action = 'created'

            db.session.commit()
            print(f"成功保存记录: {process_instance_id}, 操作: {action}")

            return {
                'success': True,
                'process_instance_id': process_instance_id,
                'action': action,
                'id': existing.id
            }

        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            print(f"处理单个异常单失败: {error_msg}")
            print(f"失败的实例数据: {instance}")
            return {
                'success': False,
                'process_instance_id': instance.get('process_instance_id', 'unknown'),
                'error': error_msg
            }
    
    def _create_exception_from_instance(self, instance: Dict) -> QualityException:
        """
        从钉钉审批实例创建质量异常单记录
        
        Args:
            instance: 钉钉审批实例数据
            
        Returns:
            QualityException: 创建的质量异常单对象
        """
        try:
            # 解析表单数据
            form_data = self._parse_form_component_values(instance.get('form_component_values', []))
            print(f"解析的表单数据: {form_data}")

            # 解析钉钉部门和用户信息
            dingtalk_info = self._resolve_dingtalk_info(instance)
            print(f"解析的钉钉信息: {dingtalk_info}")

            # 解析责任部门ID
            responsible_dept_info = self._resolve_responsible_dept_id(form_data.get('责任部门'))
            print(f"解析的责任部门信息: {responsible_dept_info}")

            # 解析发起人系统部门ID
            originator_local_dept_info = self._resolve_originator_local_dept_id(dingtalk_info.get('originator_dept_name'))
            print(f"解析的发起人系统部门信息: {originator_local_dept_info}")

            # 验证必填字段
            process_instance_id = instance.get('process_instance_id')
            title = instance.get('title', '')
            originator_userid = instance.get('originator_userid')
            approval_status = instance.get('status')

            print(f"必填字段检查:")
            print(f"  process_instance_id: {process_instance_id}")
            print(f"  title: {title}")
            print(f"  originator_userid: {originator_userid}")
            print(f"  approval_status: {approval_status}")

            if not process_instance_id:
                raise ValueError("缺少process_instance_id")
            if not title:
                title = "质量异常单"  # 设置默认标题
                print(f"  设置默认标题: {title}")
            if not originator_userid:
                raise ValueError("缺少originator_userid")
            if not approval_status:
                raise ValueError("缺少approval_status")

            # 处理创建时间
            create_time_raw = instance.get('create_time')
            create_time = self._parse_datetime_smart(create_time_raw)
            if not create_time:
                create_time = datetime.datetime.now()
            print(f"  create_time: {create_time} (原始值: {create_time_raw})")

            # 处理完成时间
            finish_time_raw = instance.get('finish_time')
            finish_time = self._parse_datetime_smart(finish_time_raw)
            print(f"  finish_time: {finish_time} (原始值: {finish_time_raw})")

            # 创建质量异常单对象
            exception = QualityException(
                process_instance_id=process_instance_id,
                business_id=instance.get('business_id'),
                title=title,
                originator_userid=originator_userid,
                originator_name=dingtalk_info.get('originator_user_name'),
                originator_dept_id=instance.get('originator_dept_id'),
                originator_dept_name=dingtalk_info.get('originator_dept_name') or instance.get('originator_dept_name'),
            originator_local_dept_id=originator_local_dept_info.get('dept_id'),
                approval_status=approval_status,
                create_time=create_time,
                finish_time=finish_time,
            
            # 从表单数据中提取业务字段
            exception_type=form_data.get('异常类型'),
            severity_level=form_data.get('严重程度'),
            project_type=form_data.get('项目类型'),
            project_code=form_data.get('项目编号'),
            machine_number=form_data.get('机台编号'),
            part_drawing_number=form_data.get('料件图号'),
            exception_quantity=self._parse_int(form_data.get('异常数量')),
            estimated_hours=self._parse_float(form_data.get('预估工时（H）')),
            responsible_person=self._extract_responsible_person(form_data.get('责任部门')),
            responsible_dept_id=responsible_dept_info.get('dept_id'),
            responsible_dept_name=responsible_dept_info.get('dept_name'),
            exception_description=form_data.get('问题描述'),
            root_cause=form_data.get('根本原因'),
            corrective_action=form_data.get('纠正措施'),
            preventive_action=form_data.get('预防措施'),
            discovery_time=self._parse_datetime(form_data.get('发现时间')),
            expected_completion_time=self._parse_datetime(form_data.get('预期完成时间')),
            actual_completion_time=self._parse_datetime(form_data.get('实际完成时间')),
            quality_impact=form_data.get('质量影响'),
            cost_impact=self._parse_float(form_data.get('成本影响')),
            schedule_impact=form_data.get('进度影响'),
            images=form_data.get('图片'),
            attachments=form_data.get('附件'),
            remark=form_data.get('备注'),

            # 数据来源标识
            import_source='dingtalk',
            import_time=datetime.datetime.now(),

            sync_time=datetime.datetime.now(),
            sync_status=1
        )

            print(f"创建的异常单对象: {exception}")

            db.session.add(exception)
            db.session.flush()  # 刷新会话以获取异常单ID

            # 使用增强的项目匹配器进行智能项目绑定
            self._bind_projects_to_exception(exception, form_data)

            return exception

        except Exception as e:
            print(f"创建异常单对象失败: {str(e)}")
            print(f"实例数据: {instance}")
            raise
    
    def _update_exception_from_instance(self, exception: QualityException, instance: Dict):
        """
        从钉钉审批实例更新质量异常单记录
        
        Args:
            exception: 现有的质量异常单对象
            instance: 钉钉审批实例数据
        """
        # 解析表单数据
        form_data = self._parse_form_component_values(instance.get('form_component_values', []))

        # 解析钉钉部门和用户信息
        dingtalk_info = self._resolve_dingtalk_info(instance)

        # 解析责任部门ID
        responsible_dept_info = self._resolve_responsible_dept_id(form_data.get('责任部门'))

        # 解析发起人系统部门ID
        originator_local_dept_info = self._resolve_originator_local_dept_id(dingtalk_info.get('originator_dept_name'))

        # 更新基础字段
        exception.business_id = instance.get('business_id')
        exception.title = instance.get('title', '')
        exception.approval_status = instance.get('status')
        exception.finish_time = datetime.datetime.fromtimestamp(instance.get('finish_time', 0) / 1000) if instance.get('finish_time') else None

        # 更新部门名称（如果从钉钉API获取到了新的名称）
        if dingtalk_info.get('originator_dept_name'):
            exception.originator_dept_name = dingtalk_info['originator_dept_name']

        # 更新发起人姓名（如果从钉钉API获取到了新的姓名）
        if dingtalk_info.get('originator_user_name'):
            exception.originator_name = dingtalk_info['originator_user_name']

        # 更新发起人系统部门ID
        exception.originator_local_dept_id = originator_local_dept_info.get('dept_id')
        
        # 更新业务字段
        exception.exception_type = form_data.get('异常类型')
        exception.severity_level = form_data.get('严重程度')
        exception.project_code = form_data.get('项目编号')
        exception.responsible_person = self._extract_responsible_person(form_data.get('责任部门'))
        exception.responsible_dept_id = responsible_dept_info.get('dept_id')
        exception.exception_description = form_data.get('异常描述')
        exception.root_cause = form_data.get('根本原因')
        exception.corrective_action = form_data.get('纠正措施')
        exception.preventive_action = form_data.get('预防措施')
        exception.discovery_time = self._parse_datetime(form_data.get('发现时间'))
        exception.expected_completion_time = self._parse_datetime(form_data.get('预期完成时间'))
        exception.actual_completion_time = self._parse_datetime(form_data.get('实际完成时间'))
        exception.quality_impact = form_data.get('质量影响')
        exception.cost_impact = self._parse_float(form_data.get('成本影响'))
        exception.schedule_impact = form_data.get('进度影响')
        exception.remark = form_data.get('备注')
        
        # 更新同步信息
        exception.sync_time = datetime.datetime.now()
        exception.sync_status = 1

        # 使用增强的项目匹配器更新项目绑定
        self._update_projects_binding_for_exception(exception, form_data)
    
    def _parse_form_component_values(self, form_components: List[Dict]) -> Dict:
        """
        解析钉钉表单组件值
        
        Args:
            form_components: 表单组件列表
            
        Returns:
            dict: 解析后的表单数据
        """
        form_data = {}
        
        for component in form_components:
            name = component.get('name', '')
            value = component.get('value', '')
            
            if name and value:
                form_data[name] = value
        
        return form_data

    def _resolve_dingtalk_info(self, instance: Dict) -> Dict:
        """
        解析钉钉实例中的部门和用户信息，转换ID为名称

        Args:
            instance: 钉钉审批实例数据

        Returns:
            dict: 包含解析后的部门和用户信息
        """
        resolved_info = {
            'originator_dept_name': '',
            'originator_user_name': ''
        }

        try:
            # 获取发起人部门ID和用户ID
            originator_dept_id = instance.get('originator_dept_id')
            originator_userid = instance.get('originator_userid')

            # 解析部门名称
            if originator_dept_id:
                try:
                    dept_info = self.dingtalk_service.get_department_info(str(originator_dept_id))
                    if dept_info and dept_info.get('name'):
                        resolved_info['originator_dept_name'] = dept_info['name']
                        print(f"成功获取部门名称: {originator_dept_id} -> {dept_info['name']}")
                    else:
                        print(f"未能获取部门名称: {originator_dept_id}")
                except Exception as e:
                    print(f"获取部门信息失败 (dept_id: {originator_dept_id}): {str(e)}")

            # 解析用户名称
            if originator_userid:
                try:
                    user_info = self.dingtalk_service.get_user_info(str(originator_userid))
                    if user_info and user_info.get('name'):
                        resolved_info['originator_user_name'] = user_info['name']
                        print(f"成功获取用户名称: {originator_userid} -> {user_info['name']}")
                    else:
                        print(f"未能获取用户名称: {originator_userid}")
                except Exception as e:
                    print(f"获取用户信息失败 (userid: {originator_userid}): {str(e)}")

        except Exception as e:
            print(f"解析钉钉信息时发生错误: {str(e)}")

        return resolved_info
    
    def _parse_datetime(self, datetime_str: str) -> Optional[datetime.datetime]:
        """
        解析日期时间字符串
        
        Args:
            datetime_str: 日期时间字符串
            
        Returns:
            datetime: 解析后的日期时间对象
        """
        if not datetime_str:
            return None
        
        try:
            # 尝试多种日期格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    return datetime.datetime.strptime(datetime_str, fmt)
                except ValueError:
                    continue
            
            return None
        except Exception:
            return None

    def _parse_datetime_smart(self, time_value) -> Optional[datetime.datetime]:
        """
        智能解析时间值，支持时间戳和字符串格式

        Args:
            time_value: 时间值，可能是时间戳(int/float)或字符串

        Returns:
            datetime: 解析后的日期时间对象，解析失败返回None
        """
        if not time_value:
            return None

        try:
            # 如果是数字类型，当作时间戳处理
            if isinstance(time_value, (int, float)):
                # 如果是毫秒时间戳，转换为秒
                if time_value > 1e10:  # 大于10位数，认为是毫秒
                    time_value = time_value / 1000
                return datetime.datetime.fromtimestamp(time_value)

            # 如果是字符串类型，尝试多种格式解析
            elif isinstance(time_value, str):
                # 先尝试当作时间戳字符串解析
                try:
                    timestamp = float(time_value)
                    if timestamp > 1e10:  # 毫秒时间戳
                        timestamp = timestamp / 1000
                    return datetime.datetime.fromtimestamp(timestamp)
                except ValueError:
                    pass

                # 尝试解析日期时间字符串格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y/%m/%d %H:%M:%S',
                    '%Y/%m/%d',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%SZ'
                ]

                for fmt in formats:
                    try:
                        return datetime.datetime.strptime(time_value, fmt)
                    except ValueError:
                        continue

            return None
        except Exception as e:
            print(f"时间解析失败: {time_value}, 错误: {e}")
            return None

    def _normalize_instance_data(self, instance: Dict) -> Dict:
        """
        标准化钉钉审批实例数据结构

        Args:
            instance: 原始钉钉审批实例数据

        Returns:
            dict: 标准化后的实例数据
        """
        # 如果数据在process_instance字段中，需要提取出来
        if 'process_instance' in instance and isinstance(instance['process_instance'], dict):
            process_instance = instance['process_instance']

            # 合并顶层的process_instance_id和process_instance中的数据
            normalized = {
                'process_instance_id': instance.get('process_instance_id'),
                'business_id': process_instance.get('business_id'),
                'title': process_instance.get('title'),
                'originator_userid': process_instance.get('originator_userid'),
                'originator_dept_id': process_instance.get('originator_dept_id'),
                'originator_dept_name': process_instance.get('originator_dept_name'),
                'status': process_instance.get('status'),
                'create_time': process_instance.get('create_time'),
                'finish_time': process_instance.get('finish_time'),
                'form_component_values': process_instance.get('form_component_values', [])
            }

            return normalized
        else:
            # 数据已经是标准格式，直接返回
            return instance
    
    def _parse_float(self, value_str: str) -> Optional[float]:
        """
        解析浮点数字符串
        
        Args:
            value_str: 数值字符串
            
        Returns:
            float: 解析后的浮点数
        """
        if not value_str:
            return None
        
        try:
            return float(value_str)
        except (ValueError, TypeError):
            return None
    
    def format_exceptions_for_response(self, exceptions: List[QualityException]) -> Dict:
        """
        格式化质量异常单数据为响应格式
        
        Args:
            exceptions: 质量异常单列表
            
        Returns:
            dict: 格式化后的响应数据
        """
        formatted_exceptions = []
        
        for exception in exceptions:
            exception_data = exception.to_dict()
            
            # 添加关联信息
            if exception.project:
                exception_data['project_name'] = exception.project.project_name
            
            if exception.responsible_dept:
                exception_data['responsible_dept_name'] = exception.responsible_dept.dept_name
            
            formatted_exceptions.append(exception_data)
        
        return {
            'status': 'success',
            'total_count': len(formatted_exceptions),
            'exceptions': formatted_exceptions
        }

    def _parse_int(self, value_str: str) -> Optional[int]:
        """
        解析整数字符串

        Args:
            value_str: 数值字符串

        Returns:
            int: 解析后的整数，解析失败返回None
        """
        if not value_str:
            return None

        try:
            return int(value_str)
        except (ValueError, TypeError):
            return None

    def _extract_responsible_person(self, dept_json_str: str) -> Optional[str]:
        """
        从责任部门JSON字符串中提取责任人信息

        Args:
            dept_json_str: 责任部门JSON字符串，如 '["设计开发一部"]'

        Returns:
            str: 责任部门名称，解析失败返回None
        """
        if not dept_json_str or dept_json_str == 'null':
            return None

        try:
            import json
            dept_list = json.loads(dept_json_str)
            if isinstance(dept_list, list) and len(dept_list) > 0:
                return dept_list[0]  # 取第一个部门作为责任人
            return None
        except (json.JSONDecodeError, TypeError):
            # 如果不是JSON格式，直接返回原字符串
            return dept_json_str

    def _resolve_responsible_dept_id(self, dept_json_str: str) -> Dict:
        """
        解析责任部门信息，将部门名称转换为部门ID

        Args:
            dept_json_str: 责任部门JSON字符串，如 '["设计开发一部"]'

        Returns:
            dict: 包含部门名称和部门ID的字典
        """
        result = {
            'dept_name': None,
            'dept_id': None
        }

        # 首先提取部门名称
        dept_name = self._extract_responsible_person(dept_json_str)
        if not dept_name:
            return result

        result['dept_name'] = dept_name

        # 查找对应的部门ID
        try:
            dept = Dept.query.filter_by(dept_name=dept_name, status=1).first()
            if dept:
                result['dept_id'] = dept.id
                print(f"成功匹配责任部门: '{dept_name}' -> ID:{dept.id}")
            else:
                print(f"未找到匹配的部门: '{dept_name}'")
        except Exception as e:
            print(f"查询部门信息失败: {str(e)}")

        return result

    def _resolve_originator_local_dept_id(self, dept_name: str) -> Dict:
        """
        解析发起人部门信息，将部门名称转换为系统部门ID

        Args:
            dept_name: 发起人部门名称

        Returns:
            dict: 包含部门名称和系统部门ID的字典
        """
        result = {
            'dept_name': dept_name,
            'dept_id': None
        }

        if not dept_name:
            return result

        # 查找对应的系统部门ID
        try:
            dept = Dept.query.filter_by(dept_name=dept_name, status=1).first()
            if dept:
                result['dept_id'] = dept.id
                print(f"成功匹配发起人部门: '{dept_name}' -> 系统部门ID:{dept.id}")
            else:
                print(f"未找到匹配的发起人部门: '{dept_name}'")
        except Exception as e:
            print(f"查询发起人部门信息失败: {str(e)}")

        return result

    def _bind_projects_to_exception(self, exception: QualityException, form_data: Dict):
        """
        为质量异常单绑定项目（创建时）

        Args:
            exception: 质量异常单对象
            form_data: 表单数据
        """
        if not exception.project_code:
            print("项目编号为空，跳过项目绑定")
            return

        try:
            print(f"开始项目绑定: 异常单ID={exception.id}, 项目编号={exception.project_code}, 项目类型={exception.project_type}")

            # 使用增强匹配器进行项目匹配
            match_result = self.project_matcher.match_projects(
                project_code=exception.project_code,
                project_type=exception.project_type
            )

            print(f"项目匹配完成:")
            print(f"  - 总体置信度: {match_result.total_confidence:.2f}")
            print(f"  - 匹配项目数: {len(match_result.matches)}")
            print(f"  - 未匹配编号数: {len(match_result.unmatched_codes)}")
            print(f"  - 详细说明: {match_result.notes}")

            # 如果有匹配的项目，创建绑定记录
            if match_result.matches:
                # 设置主项目ID（第一个匹配的项目）
                primary_match = match_result.matches[0]
                exception.project_id = primary_match.project_id
                print(f"设置主项目: ID={primary_match.project_id}, 名称={primary_match.project_name}, 置信度={primary_match.confidence:.2f}")

                # 为每个匹配的项目创建绑定记录
                for i, match in enumerate(match_result.matches):
                    binding = QualityExceptionProjectBinding.create_binding(
                        quality_exception_id=exception.id,
                        project_id=match.project_id,
                        binding_type='auto',
                        binding_source='sync',
                        match_confidence=match.confidence,
                        match_method=match.match_type,
                        original_project_code=exception.project_code,
                        parsed_project_code=match.project_code,
                        project_type_matched=match.project_type_name,
                        notes=match.notes,
                        match_details=json.dumps({
                            'original_code': match_result.original_code,
                            'parsed_codes': match_result.parsed_result.parsed_codes,
                            'format_type': match_result.parsed_result.format_type,
                            'total_confidence': match_result.total_confidence,
                            'match_index': i + 1,
                            'is_primary': i == 0
                        }, ensure_ascii=False)
                    )

                    db.session.add(binding)
                    print(f"创建项目绑定 #{i+1}: 异常{exception.id} -> 项目{match.project_id} ({match.project_name})")
                    print(f"  - 匹配方式: {match.match_type}")
                    print(f"  - 置信度: {match.confidence:.2f}")
                    print(f"  - 说明: {match.notes}")
            else:
                print(f"❌ 项目绑定失败: 未找到匹配的项目")
                print(f"  - 原始编号: {exception.project_code}")
                print(f"  - 项目类型: {exception.project_type}")
                print(f"  - 失败原因: {match_result.notes}")

                # 记录绑定失败的信息
                failed_binding = QualityExceptionProjectBinding.create_binding(
                    quality_exception_id=exception.id,
                    project_id=None,
                    binding_type='auto',
                    binding_source='sync',
                    match_confidence=0.0,
                    match_method='no_match',
                    original_project_code=exception.project_code,
                    parsed_project_code=None,
                    project_type_matched=exception.project_type,
                    notes=f"自动匹配失败: {match_result.notes}",
                    match_details=json.dumps({
                        'original_code': match_result.original_code,
                        'parsed_codes': match_result.parsed_result.parsed_codes,
                        'format_type': match_result.parsed_result.format_type,
                        'total_confidence': match_result.total_confidence,
                        'failure_reason': 'no_match'
                    }, ensure_ascii=False),
                    is_active=False  # 失败的绑定记录设为非活跃状态
                )
                db.session.add(failed_binding)
                print(f"记录绑定失败信息: 异常{exception.id}")

        except Exception as e:
            print(f"❌ 项目绑定过程中发生错误: {str(e)}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")

    def _update_projects_binding_for_exception(self, exception: QualityException, form_data: Dict):
        """
        更新质量异常单的项目绑定（更新时）

        Args:
            exception: 质量异常单对象
            form_data: 表单数据
        """
        if not exception.project_code:
            # 如果项目编号为空，停用所有现有绑定
            self._deactivate_all_bindings(exception.id)
            exception.project_id = None
            print("项目编号为空，停用所有项目绑定")
            return

        try:
            # 获取当前有效的绑定
            current_bindings = QualityExceptionProjectBinding.get_active_bindings(exception.id)
            current_project_ids = {binding.project_id for binding in current_bindings}

            # 使用增强匹配器进行项目匹配
            match_result = self.project_matcher.match_projects(
                project_code=exception.project_code,
                project_type=exception.project_type
            )

            print(f"更新项目匹配结果: {match_result.notes}")

            if match_result.matches:
                new_project_ids = {match.project_id for match in match_result.matches}

                # 更新主项目ID
                primary_match = match_result.matches[0]
                exception.project_id = primary_match.project_id

                # 停用不再匹配的绑定
                for binding in current_bindings:
                    if binding.project_id not in new_project_ids:
                        QualityExceptionProjectBinding.deactivate_binding(
                            binding.id,
                            notes="项目编号更新后不再匹配"
                        )
                        print(f"停用绑定: 异常{exception.id} -> 项目{binding.project_id}")

                # 创建新的绑定
                for match in match_result.matches:
                    if match.project_id not in current_project_ids:
                        binding = QualityExceptionProjectBinding.create_binding(
                            quality_exception_id=exception.id,
                            project_id=match.project_id,
                            binding_type='auto',
                            binding_source='sync_update',
                            match_confidence=match.confidence,
                            match_method=match.match_type,
                            original_project_code=exception.project_code,
                            parsed_project_code=match.project_code,
                            project_type_matched=match.project_type_name,
                            notes=match.notes,
                            match_details=json.dumps({
                                'original_code': match_result.original_code,
                                'parsed_codes': match_result.parsed_result.parsed_codes,
                                'format_type': match_result.parsed_result.format_type,
                                'total_confidence': match_result.total_confidence
                            }, ensure_ascii=False)
                        )

                        db.session.add(binding)
                        print(f"创建新项目绑定: 异常{exception.id} -> 项目{match.project_id}")
            else:
                # 没有匹配的项目，停用所有现有绑定
                self._deactivate_all_bindings(exception.id)
                exception.project_id = None
                print(f"未找到匹配的项目，停用所有绑定: {exception.project_code}")

        except Exception as e:
            print(f"更新项目绑定过程中发生错误: {str(e)}")

    def _deactivate_all_bindings(self, quality_exception_id: int):
        """
        停用质量异常单的所有项目绑定

        Args:
            quality_exception_id: 质量异常单ID
        """
        try:
            bindings = QualityExceptionProjectBinding.get_active_bindings(quality_exception_id)
            for binding in bindings:
                QualityExceptionProjectBinding.deactivate_binding(
                    binding.id,
                    notes="项目编号清空或更新"
                )
            print(f"停用异常{quality_exception_id}的所有项目绑定")
        except Exception as e:
            print(f"停用项目绑定时发生错误: {str(e)}")
