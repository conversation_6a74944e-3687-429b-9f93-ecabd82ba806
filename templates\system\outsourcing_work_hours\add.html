<!DOCTYPE html>
<html>
<head>
    <title>新增外协工时</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">项目类型</label>
                        <div class="layui-input-block">
                            <select name="projectType" lay-verify="required" lay-search="" lay-filter="projectType">
                                <option value="">请选择项目类型</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">项目编号</label>
                        <div class="layui-input-block">
                            <select name="projectCode" lay-verify="required" lay-search="">
                                <option value="">请先选择项目类型</option>
                            </select>
                        </div>
                    </div>
                </div>
          
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">外协名称</label>
                        <div class="layui-input-block">
                            <select name="outsourcingId" lay-verify="required" lay-search="" lay-filter="outsourcingId">
                                <option value="">请选择外协</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工作地点</label>
                        <div class="layui-input-block">
                            <select name="workLocation" lay-verify="required">
                                <option value="">请选择工作地点</option>
                                <option value="厂内">厂内</option>
                                <option value="厂外">厂外</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工时数</label>
                        <div class="layui-input-block">
                            <input type="number" name="workHours" lay-verify="required|number" autocomplete="off" 
                                   placeholder="请输入工时数" class="layui-input" step="0.5" min="0">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">时薪(元)</label>
                        <div class="layui-input-block">
                            <input type="number" name="hourlyRate" autocomplete="off"
                                   placeholder="请先选择外协" class="layui-input" step="0.01" min="0" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属部门</label>
                        <div class="layui-input-block">
                            <select name="deptId" lay-verify="required" lay-search="">
                                <option value="">请选择所属部门</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <!-- 占位列 -->
                </div>
            </div>

            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">开始日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="workStartDate" lay-verify="required" autocomplete="off" 
                                   placeholder="请选择开始日期" class="layui-input" id="workStartDate">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">结束日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="workEndDate" lay-verify="required" autocomplete="off" 
                                   placeholder="请选择结束日期" class="layui-input" id="workEndDate">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">工作内容</label>
                <div class="layui-input-block">
                    <textarea name="workDescription" placeholder="请输入工作内容描述" 
                              class="layui-textarea" rows="3"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" 
                              class="layui-textarea" rows="2"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="work-hours-save">
                        <i class="layui-icon layui-icon-ok"></i>
                        提交
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'laydate', 'popup', 'jquery'], function () {
        let form = layui.form
        let laydate = layui.laydate
        let popup = layui.popup
        let $ = layui.jquery

        const MODULE_PATH = "/system/outsourcing_work_hours/"

        // 日期选择器
        laydate.render({
            elem: '#workStartDate',
            type: 'date',
            done: function(value) {
                // 设置结束日期的最小值
                laydate.render({
                    elem: '#workEndDate',
                    type: 'date',
                    min: value
                })
            }
        })

        laydate.render({
            elem: '#workEndDate',
            type: 'date'
        })

        // 加载项目类型选项
        $.get(MODULE_PATH + 'project_types', function(res) {
            if(res.success) {
                let options = '<option value="">请选择项目类型</option>'
                res.data.forEach(function(item) {
                    options += '<option value="' + item.value + '">' + item.label + '</option>'
                })
                $('select[name="projectType"]').html(options)
                form.render('select')
            }
        })

        // 监听项目类型选择变化
        form.on('select(projectType)', function(data) {
            const projectTypeId = data.value
            const projectCodeSelect = $('select[name="projectCode"]')

            if (projectTypeId) {
                // 加载对应的项目编号
                $.get(MODULE_PATH + 'project_codes/' + projectTypeId, function(res) {
                    if(res.success) {
                        let options = '<option value="">请选择项目编号</option>'
                        res.data.forEach(function(item) {
                            options += '<option value="' + item.value + '">' + item.label + '</option>'
                        })
                        projectCodeSelect.html(options)
                        form.render('select')
                    }
                })
            } else {
                // 清空项目编号选项
                projectCodeSelect.html('<option value="">请先选择项目类型</option>')
                form.render('select')
            }
        })

        // 存储外协信息数据
        let outsourcingData = {}

        // 加载外协信息选项
        $.get(MODULE_PATH + 'outsourcing_list', function(res) {
            if(res.success) {
                let options = '<option value="">请选择外协</option>'
                res.data.forEach(function(item) {
                    // 存储外协数据，包括时薪
                    outsourcingData[item.id] = item
                    options += '<option value="' + item.id + '">' + item.outsourcing_name +
                              (item.contact_person ? ' (' + item.contact_person + ')' : '') + '</option>'
                })
                $('select[name="outsourcingId"]').html(options)
                form.render('select')
            }
        })

        // 加载可管理部门选项
        $.get(MODULE_PATH + 'manageable_depts', function(res) {
            if(res.success) {
                let options = '<option value="">请选择所属部门</option>'
                res.data.forEach(function(item) {
                    options += '<option value="' + item.value + '">' + item.label + '</option>'
                })
                $('select[name="deptId"]').html(options)
                form.render('select')
            }
        })

        // 监听外协选择变化，自动填充时薪
        form.on('select(outsourcingId)', function(data) {
            const outsourcingId = data.value
            const hourlyRateInput = $('input[name="hourlyRate"]')

            if (outsourcingId && outsourcingData[outsourcingId]) {
                const hourlyRate = outsourcingData[outsourcingId].hourly_rate || 0
                hourlyRateInput.val(hourlyRate)
                // 触发总费用计算
                hourlyRateInput.trigger('input')
            } else {
                hourlyRateInput.val('')
                $('.total-cost-display').remove()
            }
        })

        // 自动计算总费用
        $('input[name="workHours"], input[name="hourlyRate"]').on('input', function() {
            let workHours = parseFloat($('input[name="workHours"]').val()) || 0
            let hourlyRate = parseFloat($('input[name="hourlyRate"]').val()) || 0
            let totalCost = workHours * hourlyRate
            
            if (totalCost > 0) {
                $('.total-cost-display').remove()
                $('.layui-input-block').last().after(
                    '<div class="total-cost-display" style="margin-top: 10px; color: #FF5722; font-weight: bold;">' +
                    '预计总费用：¥' + totalCost.toFixed(2) + '</div>'
                )
            }
        })

        // 表单提交
        form.on('submit(work-hours-save)', function (data) {
            let loading = layer.load()
            $.ajax({
                url: MODULE_PATH + 'save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg, function () {
                            let index = parent.layer.getFrameIndex(window.name)
                            parent.layer.close(index)
                        })
                    } else {
                        popup.failure(result.msg)
                    }
                },
                error: function () {
                    layer.close(loading)
                    popup.failure('网络异常')
                }
            })
            return false
        })
    })
</script>
</body>
</html>
