"""
质量异常单自动同步服务
提供后台自动同步、增量同步、失败重试等功能
"""

import datetime
import time
from typing import Dict, List, Optional
from applications.extensions import db
from applications.services.dingtalk_service import DingTalkService
from applications.services.quality_exception_processor import QualityExceptionProcessor
from applications.models.quality_exception import QualityException


class QualityExceptionSyncService:
    """质量异常单自动同步服务"""
    
    def __init__(self):
        self.dingtalk_service = DingTalkService()
        self.processor = QualityExceptionProcessor()
        self.process_code = "PROC-CDDAA486-7E7E-4AB0-86F5-F7CF22B9E5BE"  # 质量异常单审批模板代码
    
    def auto_sync_quality_exceptions(self, days: int = 7) -> Dict:
        """
        自动同步质量异常单

        Args:
            days: 同步最近几天的数据，默认7天

        Returns:
            dict: 同步结果
        """
        try:
            # 计算时间范围
            end_timestamp = int(time.time() * 1000)
            start_timestamp = end_timestamp - days * 24 * 60 * 60 * 1000

            # 获取钉钉审批实例
            instances = self.dingtalk_service.get_approval_instances(
                self.process_code,
                start_timestamp,
                end_timestamp,
                'COMPLETED'
            )

            if not instances:
                return {
                    'status': 'success',
                    'message': f'未找到最近 {days} 天的质量异常单',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            # 过滤出需要同步的数据（增量同步）
            new_instances = self._filter_new_instances(instances)
            
            if not new_instances:
                return {
                    'status': 'success',
                    'message': f'所有数据已是最新，无需同步',
                    'total': len(instances),
                    'success_count': 0,
                    'failed_count': 0
                }
            
            print(f"发现 {len(new_instances)} 条新数据需要同步")
            
            # 分批处理数据，避免一次性处理过多数据
            batch_size = 20
            total_success = 0
            total_failed = 0
            
            for i in range(0, len(new_instances), batch_size):
                batch = new_instances[i:i + batch_size]
                print(f"处理第 {i//batch_size + 1} 批数据，共 {len(batch)} 条")
                
                # 处理当前批次
                result = self.processor.process_quality_exceptions(batch)
                
                if result.get('status') == 'success':
                    total_success += result.get('success_count', 0)
                    total_failed += result.get('failed_count', 0)
                else:
                    total_failed += len(batch)
                
                # 批次间添加短暂延迟，避免数据库压力过大
                if i + batch_size < len(new_instances):
                    time.sleep(1)
            
            # 更新最后同步时间
            self._update_last_sync_time()
            
            message = f"自动同步完成：处理 {len(new_instances)} 条新数据，成功 {total_success} 条，失败 {total_failed} 条"
            
            return {
                'status': 'success',
                'message': message,
                'total': len(new_instances),
                'success_count': total_success,
                'failed_count': total_failed
            }
            
        except Exception as e:
            error_msg = f"自动同步质量异常单失败: {str(e)}"
            print(error_msg)
            return {
                'status': 'error',
                'message': error_msg,
                'total': 0,
                'success_count': 0,
                'failed_count': 0
            }
    
    def _filter_new_instances(self, instances: List[Dict]) -> List[Dict]:
        """
        过滤出需要同步的新实例（增量同步）
        
        Args:
            instances: 钉钉审批实例列表
            
        Returns:
            list: 需要同步的新实例列表
        """
        try:
            # 获取已存在的process_instance_id列表
            existing_ids = set()
            existing_records = QualityException.query.with_entities(
                QualityException.process_instance_id
            ).all()
            
            for record in existing_records:
                existing_ids.add(record.process_instance_id)
            
            # 过滤出新的实例
            new_instances = []
            for instance in instances:
                process_instance_id = instance.get('process_instance_id')
                if process_instance_id and process_instance_id not in existing_ids:
                    new_instances.append(instance)
            
            return new_instances
            
        except Exception as e:
            print(f"过滤新实例时发生错误: {str(e)}")
            # 如果过滤失败，返回所有实例（安全策略）
            return instances
    
    def _update_last_sync_time(self):
        """更新最后同步时间"""
        try:
            # 这里可以将同步时间记录到配置表或缓存中
            # 暂时使用简单的方式，后续可以扩展
            pass
        except Exception as e:
            print(f"更新最后同步时间失败: {str(e)}")
    
    def manual_sync_quality_exceptions(self, start_date: str, end_date: str) -> Dict:
        """
        手动同步质量异常单（用于紧急情况或补充同步）
        
        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            
        Returns:
            dict: 同步结果
        """
        try:
            # 转换时间格式
            start_timestamp = int(datetime.datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
            end_timestamp = int(datetime.datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp() * 1000)

            # 获取钉钉审批实例
            instances = self.dingtalk_service.get_approval_instances(
                self.process_code,
                start_timestamp,
                end_timestamp,
                'COMPLETED'
            )

            if not instances:
                return {
                    'status': 'success',
                    'message': f'指定时间范围内未找到质量异常单',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            # 处理数据
            result = self.processor.process_quality_exceptions(instances)

            return result
            
        except Exception as e:
            error_msg = f"手动同步质量异常单失败: {str(e)}"
            return {
                'status': 'error',
                'message': error_msg,
                'total': 0,
                'success_count': 0,
                'failed_count': 0
            }
    

    def get_sync_status(self) -> Dict:
        """
        获取同步状态信息
        
        Returns:
            dict: 同步状态信息
        """
        try:
            # 获取数据库中的质量异常单统计
            total_count = QualityException.query.count()
            
            # 获取最近同步的记录
            latest_record = QualityException.query.order_by(
                QualityException.sync_time.desc()
            ).first()
            
            last_sync_time = None
            if latest_record and latest_record.sync_time:
                last_sync_time = latest_record.sync_time.strftime('%Y-%m-%d %H:%M:%S')
            
            return {
                'status': 'success',
                'total_records': total_count,
                'last_sync_time': last_sync_time,
                'auto_sync_enabled': True  # 可以从配置中读取
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f"获取同步状态失败: {str(e)}",
                'total_records': 0,
                'last_sync_time': None,
                'auto_sync_enabled': False
            }

    def auto_sync_quality_exceptions_with_config(self, config: Dict) -> Dict:
        """
        根据配置自动同步质量异常单

        Args:
            config: 同步配置
                - days: 同步天数
                - sync_mode: 同步模式 (incremental/full)
                - sync_status: 状态过滤列表
                - enable_schedule: 是否启用定时同步
                - schedule_frequency: 同步频率
                - schedule_time: 执行时间

        Returns:
            dict: 同步结果
        """
        try:
            print(f"开始配置化智能同步: {config}")

            # 获取配置参数 (支持两种参数名格式)
            days = config.get('sync_days') or config.get('days', 7)
            sync_mode = config.get('sync_mode', 'incremental')
            sync_status = config.get('sync_status', ['NEW', 'RUNNING', 'COMPLETED'])
            enable_schedule = config.get('enable_schedule', False)

            # 确保days是整数
            if isinstance(days, str):
                days = int(days)

            # 确保sync_status是列表
            if isinstance(sync_status, str):
                sync_status = sync_status.split(',')

            # 清理状态列表中的空白字符
            sync_status = [status.strip() for status in sync_status if status.strip()]

            # 计算时间范围
            end_timestamp = int(time.time() * 1000)
            start_timestamp = end_timestamp - days * 24 * 60 * 60 * 1000

            print(f"同步时间范围: {days}天")
            print(f"同步模式: {sync_mode}")
            print(f"状态过滤: {sync_status}")

            # 获取钉钉审批实例
            instances = self.dingtalk_service.get_approval_instances(
                process_code=self.process_code,
                start_time=start_timestamp,
                end_time=end_timestamp
            )

            if not instances:
                return {
                    'status': 'success',
                    'message': '没有找到需要同步的质量异常单',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            print(f"获取到 {len(instances)} 个审批实例")

            # 根据状态过滤实例
            filtered_instances = []
            for instance in instances:
                # 钉钉API返回的状态在process_instance内部
                if 'process_instance' in instance and isinstance(instance['process_instance'], dict):
                    instance_status = instance['process_instance'].get('status', '')
                else:
                    # 兼容其他可能的数据结构
                    instance_status = instance.get('status', '')

                print(f"[DEBUG] 实例状态: {instance_status}, 目标状态: {sync_status}")

                if instance_status in sync_status:
                    filtered_instances.append(instance)
                    print(f"[DEBUG] 实例匹配，已添加到过滤列表")

            print(f"状态过滤后剩余 {len(filtered_instances)} 个实例")

            # 如果是增量同步，进一步过滤已存在的记录
            if sync_mode == 'incremental':
                filtered_instances = self._filter_incremental_instances(filtered_instances)
                print(f"增量同步过滤后剩余 {len(filtered_instances)} 个实例")

            # 处理审批实例
            result = self.processor.process_quality_exceptions(filtered_instances)

            # 如果启用定时同步，保存配置
            if enable_schedule:
                self._save_schedule_config(config)
                print(f"已保存定时同步配置: {self._get_frequency_description(config)} at {config.get('schedule_time')}")

            return {
                'status': 'success',
                'message': f'配置化智能同步完成，处理了 {len(filtered_instances)} 个实例',
                'total': len(filtered_instances),
                'success_count': result.get('success_count', 0),
                'failed_count': result.get('failed_count', 0),
                'config': config
            }

        except Exception as e:
            print(f"配置化智能同步失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'配置化智能同步失败: {str(e)}',
                'total': 0,
                'success_count': 0,
                'failed_count': 0
            }

    def _filter_incremental_instances(self, instances: List[Dict]) -> List[Dict]:
        """
        过滤增量同步实例（排除已存在的记录）

        Args:
            instances: 审批实例列表

        Returns:
            List[Dict]: 过滤后的实例列表
        """
        from applications.models.quality_exception import QualityException

        filtered_instances = []
        for instance in instances:
            process_instance_id = instance.get('process_instance_id')
            if process_instance_id:
                # 检查是否已存在
                existing = QualityException.query.filter_by(
                    process_instance_id=process_instance_id
                ).first()

                if not existing:
                    filtered_instances.append(instance)
                else:
                    print(f"跳过已存在的实例: {process_instance_id}")

        return filtered_instances

    def _save_schedule_config(self, config: Dict):
        """
        保存定时同步配置并启动定时任务

        Args:
            config: 同步配置
        """
        try:
            print(f"保存定时同步配置: {config}")

            # 保存配置到字典表
            from applications.services.dingtalk_sync_config_service import DingTalkSyncConfigService
            save_success = DingTalkSyncConfigService.save_config(config)

            if save_success:
                print("配置已保存到字典表")

                # 调用调度器添加定时任务
                from applications.extensions.init_scheduler import add_quality_exception_sync_job_with_config
                add_quality_exception_sync_job_with_config(config)

                print(f"已启动定时同步任务: {self._get_frequency_description(config)}")
            else:
                print("配置保存到字典表失败")

        except Exception as e:
            print(f"保存定时同步配置失败: {str(e)}")

    def _get_frequency_description(self, config: Dict) -> str:
        """获取频率配置的中文描述"""
        frequency_type = config.get('frequency_type', 'daily')

        if frequency_type == 'hourly':
            return '每小时'
        elif frequency_type == 'daily':
            return '每天'
        elif frequency_type == 'weekly':
            week_day = config.get('week_day', 6)
            week_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            return f'每周{week_names[int(week_day)]}'
        elif frequency_type == 'monthly':
            month_day = config.get('month_day', 1)
            if month_day == 'last':
                return '每月月末'
            else:
                return f'每月{month_day}号'
        else:
            return frequency_type
