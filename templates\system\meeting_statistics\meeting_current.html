<!DOCTYPE html>
<html>
<head>
    <title>当前月统计数据</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <style>
        .layui-card {
            margin-bottom: 15px;
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }
        .layui-card-body {
            padding: 15px;
        }
        .layui-table {
            margin-top: 0;
        }
        .echarts-chart {
            height: 300px;
        }
        .layui-form-label {
            position: relative;
            float: left;
            display: block;
            padding: 9px 5px;
            width: 60px;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }
        .layui-input-block {
            margin-left: 70px;
            min-height: 36px;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 筛选表单 -->
            <form class="layui-form" action="" method="get" id="searchForm">
                <div class="layui-row">
                    <!-- 项目类型 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目类型</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_prefix" placeholder="请输入项目类型" class="layui-input" value="{{ project_prefix or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- 项目编号 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_number" placeholder="请输入项目编号" class="layui-input" value="{{ project_number or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- 工作地点 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工作地点</label>
                            <div class="layui-input-block">
                                <select name="location" class="layui-input">
                                    <option value="">全部</option>
                                    <option value="厂内" {% if location == '厂内' %}selected{% endif %}>厂内</option>
                                    <option value="厂外" {% if location == '厂外' %}selected{% endif %}>厂外</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 部门 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <label class="layui-form-label">部门</label>
                            <div class="layui-input-block">
                                <select name="dept_id" class="layui-input">
                                    <option value="">全部</option>
                                    {% if depts %}
                                        {% for dept in depts %}
                                        <option value="{{ dept.id }}" {% if dept_id and dept_id|string == dept.id|string %}selected{% endif %}>{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 员工姓名 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <label class="layui-form-label">员工姓名</label>
                            <div class="layui-input-block">
                                <input type="text" name="employee_name" placeholder="请输入员工姓名" class="layui-input" value="{{ employee_name or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- 查询和重置按钮 -->
                    <div class="layui-col-md2">
                        <div class="layui-form-item">
                            <div class="layui-input-block" style="margin-left: 20px;">
                                <button class="layui-btn layui-btn-md" lay-submit>
                                    <i class="layui-icon layui-icon-search"></i>
                                    查询
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                                    <i class="layui-icon layui-icon-refresh"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 第一行：员工数据 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            当前月员工工资预估
                            <small style="color: #999; margin-left: 10px;">(包含所有类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>部门</th>
                                        <th>员工姓名</th>
                                        <th>工作地点</th>
                                        <th>项目类型</th>
                                        <th>项目编号</th>
                                        <th>正工时时长</th>
                                        <th>加班工时时长</th>
                                        <th>正工时工资</th>
                                        <th>加班工资</th>
                                        <th>总工资</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if employee_data %}
                                        {% for emp in employee_data %}
                                        <tr>
                                            <td>{{ emp.dept_name }}</td>
                                            <td>{{ emp.name }}</td>
                                            <td>{{ emp.location }}</td>
                                            <td>{{ emp.projectPrefix }}</td>
                                            <td>{{ emp.projectNumber }}</td>
                                            <td>{{ emp.regularWorkingHours }}</td>
                                            <td>{{ emp.overtimeWorkingHours }}</td>
                                            <td>{{ emp.regular_pay }}</td>
                                            <td>{{ emp.overtime_pay }}</td>
                                            <td>{{ emp.total_pay }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="8" style="text-align: center;">
                                                <div style="display: flex; flex-direction: column; align-items: center; padding: 20px;">
                                                    <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; color: #999;"></i>
                                                    <p style="margin-top: 10px; color: #666;">暂无数据</p>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div id="pagination" class="layui-box layui-laypage"></div>
                </div>
            </div>

            <!-- 第二行：部门数据 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            当前月部门预估工资
                            <small style="color: #999; margin-left: 10px;">(包含所有类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>部门</th>
                                        <th>工作地点</th>
                                        <th>项目类型</th>
                                        <th>项目编号</th>
                                        <th>正工时时长</th>
                                        <th>加班工时时长</th>
                                        <th>正工时工资</th>
                                        <th>加班工资</th>
                                        <th>总工资</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if dept_pay %}
                                        {% for dept in dept_pay %}
                                        <tr>
                                            <td>{{ dept.dept_name }}</td>
                                            <td>{{ dept.location }}</td>
                                            <td>{{ dept.projectPrefix }}</td>
                                            <td>{{ dept.projectNumber }}</td>
                                            <td>{{ dept.total_regular_hours }}</td>
                                            <td>{{ dept.total_overtime_hours }}</td>
                                            <td>{{ dept.total_regular_pay }}</td>
                                            <td>{{ dept.total_overtime_pay }}</td>
                                            <td>{{ dept.total_pay }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="6" style="text-align: center;">
                                                <div style="display: flex; flex-direction: column; align-items: center; padding: 20px;">
                                                    <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; color: #999;"></i>
                                                    <p style="margin-top: 10px; color: #666;">暂无数据</p>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门工资分页控件 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div id="deptPagination" class="layui-box layui-laypage"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据可视化面板 -->
    {% if employee_data %}
    <!-- 关键指标卡片 -->
    <div class="layui-card">
        <div class="layui-card-header">
            关键指标概览
            <small style="color: #999; margin-left: 10px;">(包含所有类型工时)</small>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md3">
                    <div class="layui-card" style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div class="layui-card-body">
                            <div style="font-size: 24px; font-weight: bold;" id="totalCost">0</div>
                            <div style="margin-top: 5px;">总工资 (元)</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card" style="text-align: center; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <div class="layui-card-body">
                            <div style="font-size: 24px; font-weight: bold;" id="totalHours">0</div>
                            <div style="margin-top: 5px;">总工时 (小时)</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card" style="text-align: center; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        <div class="layui-card-body">
                            <div style="font-size: 24px; font-weight: bold;" id="avgCost">0</div>
                            <div style="margin-top: 5px;">平均工资 (元/小时)</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card" style="text-align: center; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        <div class="layui-card-body">
                            <div style="font-size: 24px; font-weight: bold;" id="deptCount">0</div>
                            <div style="margin-top: 5px;">涉及部门数</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表分析区域 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <!-- 部门工资对比柱状图 -->
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            部门工资对比分析
                            <small style="color: #999; margin-left: 10px;">(按部门统计)</small>
                        </div>
                        <div class="layui-card-body">
                            <div id="deptBarChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工时分析和排行榜 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <!-- 工时类型分析 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            工时类型分析
                            <small style="color: #999; margin-left: 10px;">(正工时/加班工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <div id="hoursTypeChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 部门项目工资工时分析 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            各部门项目工资工时分析
                            <small style="color: #999; margin-left: 10px;">(工资+工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <div id="deptProjectChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="layui-card">
        <div class="layui-card-body">
            <div style="text-align: center; padding: 30px;">
                <i class="layui-icon layui-icon-chart" style="font-size: 50px; color: #999;"></i>
                <p style="margin-top: 10px; color: #666;">暂无数据可供图表展示</p>
            </div>
        </div>
    </div>
    {% endif %}

    {% include 'system/common/footer.html' %}

    <script>
        // 全局加载动画函数
        function showLoading(text) {
            const loadingHtml = `
                <div id="custom-loading" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 9999;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: white;
                    font-size: 16px;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #009688;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 10px;
                        "></div>
                        <div>${text}</div>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHtml);
        }

        layui.use(['element', 'table', 'laypage', 'form'], function(){
            var element = layui.element;
            var table = layui.table;
            var laypage = layui.laypage;
            var form = layui.form;

            // 初始化折叠面板
            element.render('collapse');

            // 监听查询表单提交
            form.on('submit', function(data){
                showLoading('正在查询数据...');
                return true; // 继续提交表单
            });

            // 初始化分页
            laypage.render({
                elem: 'pagination',
                count: {{ total }},  // 总数据量
                limit: {{ per_page }},  // 每页显示条数
                curr: {{ page }},  // 当前页
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],  // 分页布局
                jump: function(obj, first){
                    if (!first) {
                        // 显示加载动画并保持所有筛选条件的分页跳转
                        showLoading('正在加载数据...');
                        const urlParams = new URLSearchParams(window.location.search);
                        urlParams.set('page', obj.curr);
                        urlParams.set('per_page', obj.limit);
                        window.location.search = urlParams.toString();
                    }
                }
            });
        });
    </script>

    <!-- 数据可视化脚本 -->
    <script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
    <script>
        // 数据处理和计算
        var employeeData = [
            {% for emp in employee_data %}
            {
                name: '{{ emp.name }}',
                dept_name: '{{ emp.dept_name }}',
                location: '{{ emp.location }}',
                regular_hours: {{ emp.regularWorkingHours }},
                overtime_hours: {{ emp.overtimeWorkingHours }},
                regular_pay: {{ emp.regular_pay }},
                overtime_pay: {{ emp.overtime_pay }},
                total_pay: {{ emp.total_pay }}
            },
            {% endfor %}
        ];

        // 计算关键指标
        function calculateMetrics() {
            var totalCost = employeeData.reduce((sum, emp) => sum + emp.total_pay, 0);
            var totalHours = employeeData.reduce((sum, emp) => sum + emp.regular_hours + emp.overtime_hours, 0);
            var avgCost = totalHours > 0 ? (totalCost / totalHours) : 0;
            var deptSet = new Set(employeeData.map(emp => emp.dept_name));
            var deptCount = deptSet.size;

            document.getElementById('totalCost').textContent = totalCost.toLocaleString();
            document.getElementById('totalHours').textContent = totalHours.toFixed(1);
            document.getElementById('avgCost').textContent = avgCost.toFixed(2);
            document.getElementById('deptCount').textContent = deptCount;
        }

        // 聚合部门数据
        function aggregateDeptData() {
            var deptMap = {};
            employeeData.forEach(emp => {
                if (!deptMap[emp.dept_name]) {
                    deptMap[emp.dept_name] = {
                        dept_name: emp.dept_name,
                        regular_hours: 0,
                        overtime_hours: 0,
                        regular_pay: 0,
                        overtime_pay: 0,
                        total_pay: 0
                    };
                }
                deptMap[emp.dept_name].regular_hours += emp.regular_hours;
                deptMap[emp.dept_name].overtime_hours += emp.overtime_hours;
                deptMap[emp.dept_name].regular_pay += emp.regular_pay;
                deptMap[emp.dept_name].overtime_pay += emp.overtime_pay;
                deptMap[emp.dept_name].total_pay += emp.total_pay;
            });
            return Object.values(deptMap);
        }

        // 部门工资对比柱状图
        function initDeptBarChart(deptData) {
            var chart = echarts.init(document.getElementById('deptBarChart'));
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['正工时工资', '加班工资', '总工资']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: deptData.map(d => d.dept_name),
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '工资(元)'
                },
                series: [
                    {
                        name: '正工时工资',
                        type: 'bar',
                        data: deptData.map(d => d.regular_pay),
                        itemStyle: {
                            color: '#5470c6'
                        }
                    },
                    {
                        name: '加班工资',
                        type: 'bar',
                        data: deptData.map(d => d.overtime_pay),
                        itemStyle: {
                            color: '#91cc75'
                        }
                    },
                    {
                        name: '总工资',
                        type: 'line',
                        data: deptData.map(d => d.total_pay),
                        itemStyle: {
                            color: '#fac858'
                        }
                    }
                ]
            };
            chart.setOption(option);
            window.addEventListener('resize', () => chart.resize());
        }

        // 工时类型分析图
        function initHoursTypeChart() {
            var totalRegularHours = employeeData.reduce((sum, emp) => sum + emp.regular_hours, 0);
            var totalOvertimeHours = employeeData.reduce((sum, emp) => sum + emp.overtime_hours, 0);

            var chart = echarts.init(document.getElementById('hoursTypeChart'));
            var option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} 小时 ({d}%)'
                },
                series: [
                    {
                        name: '工时类型',
                        type: 'pie',
                        radius: ['30%', '60%'],
                        data: [
                            {
                                value: totalRegularHours,
                                name: '正工时',
                                itemStyle: {
                                    color: '#5470c6'
                                }
                            },
                            {
                                value: totalOvertimeHours,
                                name: '加班工时',
                                itemStyle: {
                                    color: '#ee6666'
                                }
                            }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            chart.setOption(option);
            window.addEventListener('resize', () => chart.resize());
        }

        // 聚合部门项目数据
        function aggregateDeptProjectData() {
            var deptProjectMap = {};
            employeeData.forEach(emp => {
                // 从工作地点提取项目信息（假设格式为：厂内-项目名 或 厂外-项目名）
                var projectName = emp.location.includes('-') ? emp.location.split('-')[1] : emp.location;
                var key = emp.dept_name + '_' + projectName;

                if (!deptProjectMap[key]) {
                    deptProjectMap[key] = {
                        dept_name: emp.dept_name,
                        project_name: projectName,
                        total_hours: 0,
                        total_pay: 0
                    };
                }
                deptProjectMap[key].total_hours += emp.regular_hours + emp.overtime_hours;
                deptProjectMap[key].total_pay += emp.total_pay;
            });
            return Object.values(deptProjectMap);
        }

        // 部门项目工资工时分析图
        function initDeptProjectChart(deptProjectData) {
            // 按部门分组数据
            var deptGroups = {};
            deptProjectData.forEach(item => {
                if (!deptGroups[item.dept_name]) {
                    deptGroups[item.dept_name] = [];
                }
                deptGroups[item.dept_name].push(item);
            });

            // 获取所有项目名称
            var allProjects = [...new Set(deptProjectData.map(item => item.project_name))];
            var deptNames = Object.keys(deptGroups);

            var chart = echarts.init(document.getElementById('deptProjectChart'));
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                legend: {
                    data: ['总工资', '总工时'],
                    top: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: deptNames,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 45
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '工资(元)',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: '工时(小时)',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: '总工资',
                        type: 'bar',
                        data: deptNames.map(deptName => {
                            return deptGroups[deptName].reduce((sum, item) => sum + item.total_pay, 0);
                        }),
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#83bff6'},
                                {offset: 0.5, color: '#188df0'},
                                {offset: 1, color: '#188df0'}
                            ])
                        }
                    },
                    {
                        name: '总工时',
                        type: 'line',
                        yAxisIndex: 1,
                        data: deptNames.map(deptName => {
                            return deptGroups[deptName].reduce((sum, item) => sum + item.total_hours, 0);
                        }),
                        itemStyle: {
                            color: '#ee6666'
                        },
                        lineStyle: {
                            width: 3
                        },
                        symbol: 'circle',
                        symbolSize: 8
                    }
                ]
            };
            chart.setOption(option);
            window.addEventListener('resize', () => chart.resize());
        }

        // 初始化所有图表
        function initCharts() {
            calculateMetrics();

            var aggregatedDeptData = aggregateDeptData();
            var deptProjectData = aggregateDeptProjectData();

            // 部门工资对比柱状图
            initDeptBarChart(aggregatedDeptData);

            // 工时类型分析图
            initHoursTypeChart();

            // 部门项目工资工时分析图
            initDeptProjectChart(deptProjectData);
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            if (employeeData.length > 0) {
                initCharts();
            }
        });
    </script>

    <!-- 在页面底部添加部门工资分页脚本 -->
    <script>
        layui.use(['element', 'table', 'laypage'], function(){
            var element = layui.element;
            var table = layui.table;
            var laypage = layui.laypage;

            // 初始化部门工资分页
            laypage.render({
                elem: 'deptPagination',
                count: {{ dept_total }},  // 部门工资总数据量
                limit: {{ dept_per_page }},  // 每页显示条数
                curr: {{ dept_page }},  // 当前页
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],  // 分页布局
                jump: function(obj, first){
                    if (!first) {
                        // 显示加载动画并保持所有筛选条件的分页跳转
                        showLoading('正在加载部门数据...');
                        const urlParams = new URLSearchParams(window.location.search);
                        urlParams.set('dept_page', obj.curr);
                        urlParams.set('dept_per_page', obj.limit);
                        window.location.search = urlParams.toString();
                    }
                }
            });
        });
    </script>
</body>
</html>

