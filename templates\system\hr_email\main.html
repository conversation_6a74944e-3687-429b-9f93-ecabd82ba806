<!DOCTYPE html>
<html>
<head>
    <title>HR邮箱配置管理</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">

{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="hr-email-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">邮箱地址</label>
                <div class="layui-input-inline">
                    <input type="text" name="email" placeholder="请输入邮箱地址" class="layui-input">
                </div>
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="is_active">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="hr-email-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

{# HR邮箱配置表格 #}
<div class="layui-card">
    <div class="layui-card-body">
        <table id="hr-email-table" lay-filter="hr-email-table"></table>
    </div>
</div>

</body>

{% include 'system/common/footer.html' %}

{# 表格工具栏 #}
<script type="text/html" id="hr-email-toolbar">
    {% if authorize("system:hr_email:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            新增邮箱
        </button>
    {% endif %}
    <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh">
        <i class="layui-icon layui-icon-refresh"></i>
        刷新
    </button>
</script>

{# 操作按钮 #}
<script type="text/html" id="hr-email-bar">
    {% if authorize("system:hr_email:edit") %}
        <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit" title="编辑">
            <i class="layui-icon layui-icon-edit"></i>
        </button>
        <button class="layui-btn layui-btn-warm layui-btn-xs" lay-event="toggle_status" title="切换状态">
            <i class="layui-icon layui-icon-release"></i>
        </button>
    {% endif %}
    {% if authorize("system:hr_email:remove") %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove" title="删除">
            <i class="layui-icon layui-icon-delete"></i>
        </button>
    {% endif %}
</script>

{# 状态模板 #}
{% raw %}
<script type="text/html" id="status-template">
    {{# if(d.is_active) { }}
        <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">禁用</span>
    {{# } }}
</script>
{% endraw %}

{# 创建时间模板 #}
{% raw %}
<script type="text/html" id="created-time-template">
    {{# if(d.created_at) { }}
        {{ d.created_at }}
    {{# } else { }}
        <span style="color: #999;">-</span>
    {{# } }}
</script>
{% endraw %}

<script>
    layui.use(['table', 'form', 'jquery', 'popup'], function () {
        let table = layui.table
        let form = layui.form
        let $ = layui.jquery
        let popup = layui.popup

        const MODULE_PATH = '/system/hr_email_config/'

        // 表格列配置
        let cols = [
            [
                {title: 'ID', field: 'id', align: 'center', width: 80},
                {title: '邮箱地址', field: 'email', align: 'center', width: 300},
                {title: '描述/备注', field: 'description', align: 'center', width: 300, templet: function(d) {
                    return d.description || '<span style="color: #999;">-</span>';
                }},
                {title: '优先级', field: 'priority', align: 'center', width: 180},
                {title: '状态', field: 'is_active', align: 'center', width: 180, templet: '#status-template'},
                {title: '创建时间', field: 'created_at', align: 'center', width: 180, templet: '#created-time-template'},
                {title: '操作', toolbar: '#hr-email-bar', align: 'center', width: 300, fixed: 'right'}
            ]
        ]

        // 渲染表格
        table.render({
            elem: '#hr-email-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#hr-email-toolbar',
            text: {none: '暂无HR邮箱配置'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'exports']
        })

        // 查询表单提交
        form.on('submit(hr-email-query)', function (data) {
            table.reload('hr-email-table', {where: data.field})
            return false
        })

        // 重置按钮事件
        $('button[type="reset"]').on('click', function() {
            $('form[lay-filter="hr-email-query-form"]')[0].reset();
            form.render();
            table.reload('hr-email-table', {where: {}});
        });

        // 工具栏事件
        table.on('toolbar(hr-email-table)', function (obj) {
            if (obj.event === 'add') {
                // 新增邮箱
                showEmailForm('新增HR邮箱', {}, function(formData) {
                    $.ajax({
                        url: MODULE_PATH + 'save',
                        type: 'POST',
                        dataType: 'json',
                        contentType: 'application/json',
                        data: JSON.stringify(formData),
                        success: function (result) {
                            if (result.success) {
                                popup.success(result.msg, function() {
                                    table.reload('hr-email-table');
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            popup.failure('添加失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'refresh') {
                table.reload('hr-email-table')
            }
        })

        // 行工具事件
        table.on('tool(hr-email-table)', function (obj) {
            if (obj.event === 'edit') {
                // 编辑邮箱
                showEmailForm('编辑HR邮箱', obj.data, function(formData) {
                    formData.id = obj.data.id;
                    $.ajax({
                        url: MODULE_PATH + 'update',
                        type: 'PUT',
                        dataType: 'json',
                        contentType: 'application/json',
                        data: JSON.stringify(formData),
                        success: function (result) {
                            if (result.success) {
                                popup.success(result.msg, function() {
                                    table.reload('hr-email-table');
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            popup.failure('更新失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'toggle_status') {
                // 切换状态
                let statusText = obj.data.is_active ? '禁用' : '启用';
                layer.confirm(`确定要${statusText}该邮箱配置吗？`, {
                    icon: 3,
                    title: '状态切换确认'
                }, function (index) {
                    layer.close(index);
                    let loading = layer.load();
                    $.ajax({
                        url: MODULE_PATH + 'toggle_status/' + obj.data.id,
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(loading);
                            if (result.success) {
                                popup.success(result.msg, function() {
                                    table.reload('hr-email-table');
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            layer.close(loading);
                            popup.failure('操作失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'remove') {
                // 删除邮箱
                layer.confirm(`确定要删除邮箱 "${obj.data.email}" 吗？<br><span style="color: red;">此操作不可恢复！</span>`, {
                    icon: 0,
                    title: '删除确认',
                    btn: ['确认删除', '取消']
                }, function (index) {
                    layer.close(index);
                    let loading = layer.load();
                    $.ajax({
                        url: MODULE_PATH + 'remove/' + obj.data.id,
                        type: 'DELETE',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(loading);
                            if (result.success) {
                                popup.success(result.msg, function() {
                                    table.reload('hr-email-table');
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            layer.close(loading);
                            popup.failure('删除失败，请稍后重试');
                        }
                    });
                });
            }
        })

        // 显示邮箱表单
        function showEmailForm(title, data, callback) {
            layer.open({
                type: 1,
                title: title,
                area: ['500px', '450px'],
                content: `
                    <div style="padding: 20px;">
                        <form class="layui-form" lay-filter="email-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮箱地址 <span style="color: red;">*</span></label>
                                <div class="layui-input-block">
                                    <input type="email" name="email" placeholder="请输入邮箱地址" class="layui-input" required lay-verify="required|email" value="${data.email || ''}">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">描述/备注</label>
                                <div class="layui-input-block">
                                    <textarea name="description" placeholder="请输入描述或备注信息" class="layui-textarea">${data.description || ''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">优先级</label>
                                <div class="layui-input-block">
                                    <input type="number" name="priority" placeholder="数字越小优先级越高" class="layui-input" min="1" max="999" value="${data.priority || 1}">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="is_active" value="true" title="启用" ${(data.is_active !== false) ? 'checked' : ''}>
                                    <input type="radio" name="is_active" value="false" title="禁用" ${(data.is_active === false) ? 'checked' : ''}>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="email-submit">确认</button>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                                </div>
                            </div>
                        </form>
                    </div>
                `,
                success: function() {
                    form.render();
                    
                    // 监听表单提交
                    form.on('submit(email-submit)', function(formData) {
                        let submitData = {
                            email: formData.field.email.trim(),
                            description: formData.field.description ? formData.field.description.trim() : '',
                            priority: parseInt(formData.field.priority) || 1,
                            is_active: formData.field.is_active === 'true'
                        };
                        
                        layer.closeAll();
                        callback(submitData);
                        return false;
                    });
                }
            });
        }
    })
</script>
</body>
</html>
