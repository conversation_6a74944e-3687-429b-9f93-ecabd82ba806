"""
质量异常单导入日志模型
记录Excel导入的详细信息和结果
"""

import datetime
from applications.extensions import db
from flask_login import UserMixin


class QualityExceptionImportLog(db.Model, UserMixin):
    """质量异常单导入日志"""
    __tablename__ = 'quality_exception_import_log'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='导入日志ID')
    
    # 导入用户信息
    import_user_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False, comment='导入用户ID')
    import_user_name = db.Column(db.String(50), nullable=True, comment='导入用户姓名')
    
    # 文件信息
    file_name = db.Column(db.String(255), nullable=True, comment='导入文件名')
    file_size = db.Column(db.Integer, nullable=True, comment='文件大小(字节)')
    file_hash = db.Column(db.String(64), nullable=True, comment='文件MD5哈希值')
    
    # 导入统计
    total_count = db.Column(db.Integer, default=0, comment='总记录数')
    success_count = db.Column(db.Integer, default=0, comment='成功数')
    failed_count = db.Column(db.Integer, default=0, comment='失败数')
    duplicate_count = db.Column(db.Integer, default=0, comment='重复数')
    
    # 导入配置
    import_mode = db.Column(db.String(20), nullable=True, comment='导入模式：insert/update/upsert')
    
    # 验证和错误信息
    validation_errors = db.Column(db.Text, nullable=True, comment='验证错误详情(JSON格式)')
    import_status = db.Column(db.String(20), default='processing', comment='导入状态：processing/completed/failed')
    error_message = db.Column(db.Text, nullable=True, comment='错误信息')
    
    # 时间信息
    start_time = db.Column(db.DateTime, default=datetime.datetime.now, comment='开始时间')
    end_time = db.Column(db.DateTime, nullable=True, comment='结束时间')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联关系
    import_user = db.relationship('User', foreign_keys=[import_user_id], backref='quality_exception_import_logs', lazy=True)
    
    def __repr__(self):
        return f'<QualityExceptionImportLog {self.id}: {self.file_name}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'import_user_id': self.import_user_id,
            'import_user_name': self.import_user_name,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'file_hash': self.file_hash,
            'total_count': self.total_count,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'duplicate_count': self.duplicate_count,
            'import_mode': self.import_mode,
            'validation_errors': self.validation_errors,
            'import_status': self.import_status,
            'error_message': self.error_message,
            'start_time': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'duration': self._calculate_duration()
        }
    
    def _calculate_duration(self):
        """计算导入耗时"""
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            return str(duration)
        return None
    
    @classmethod
    def create_log(cls, user_id, user_name, file_name, file_size, file_hash, import_mode):
        """创建导入日志"""
        log = cls(
            import_user_id=user_id,
            import_user_name=user_name,
            file_name=file_name,
            file_size=file_size,
            file_hash=file_hash,
            import_mode=import_mode,
            import_status='processing'
        )
        db.session.add(log)
        db.session.flush()
        return log
    
    def update_status(self, status, **kwargs):
        """更新导入状态"""
        self.import_status = status
        
        if status in ['completed', 'failed']:
            self.end_time = datetime.datetime.now()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        db.session.commit()
    
    def add_validation_error(self, error_message):
        """添加验证错误"""
        import json
        
        try:
            errors = json.loads(self.validation_errors) if self.validation_errors else []
        except:
            errors = []
        
        errors.append(error_message)
        self.validation_errors = json.dumps(errors, ensure_ascii=False)
        db.session.commit()
