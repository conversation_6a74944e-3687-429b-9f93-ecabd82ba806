import datetime
from applications.extensions import db


class AutoCalcLog(db.Model):
    """自动计算配置和执行日志表"""
    __tablename__ = 'auto_calc_log'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    log_type = db.Column(db.String(20), nullable=False, comment='日志类型：config_change-配置变更, execution-执行日志')
    operator_id = db.Column(db.Integer, comment='操作人ID')
    operator_name = db.Column(db.String(50), comment='操作人姓名')
    
    # 配置变更相关字段
    config_key = db.Column(db.String(50), comment='配置项键名')
    old_value = db.Column(db.String(255), comment='原值')
    new_value = db.Column(db.String(255), comment='新值')
    
    # 执行日志相关字段
    execution_status = db.Column(db.String(20), comment='执行状态：success-成功, failed-失败, running-执行中')
    total_projects = db.Column(db.Integer, comment='总项目数')
    success_count = db.Column(db.Integer, comment='成功项目数')
    failed_count = db.Column(db.Integer, comment='失败项目数')
    execution_duration = db.Column(db.String(50), comment='执行耗时')
    error_message = db.Column(db.Text, comment='错误信息')
    
    # 通用字段
    description = db.Column(db.Text, comment='描述信息')
    ip_address = db.Column(db.String(50), comment='操作IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')

    def __init__(self, **kwargs):
        super(AutoCalcLog, self).__init__(**kwargs)

    def __repr__(self):
        return f"<AutoCalcLog {self.log_type}:{self.id}>"

    @classmethod
    def log_config_change(cls, operator_id, operator_name, config_key, old_value, new_value,
                         description=None, ip_address=None, user_agent=None):
        """记录配置变更日志（简化版）"""
        log = cls(
            log_type='config_change',
            operator_id=operator_id,
            operator_name=operator_name,
            config_key=config_key,
            old_value=str(old_value) if old_value is not None else None,
            new_value=str(new_value) if new_value is not None else None,
            description=description
            # 移除ip_address和user_agent，减少冗余信息
        )
        db.session.add(log)
        db.session.commit()
        return log

    @classmethod
    def log_execution_start(cls, operator_id=None, operator_name=None, description=None, trigger_type='auto'):
        """记录执行开始日志"""
        # 如果没有提供操作人信息，根据触发类型设置默认值
        if not operator_name:
            if trigger_type == 'manual':
                operator_name = '手动触发'
            else:
                operator_name = '系统定时任务'

        log = cls(
            log_type='execution',
            operator_id=operator_id,
            operator_name=operator_name,
            execution_status='running',
            description=description or f'开始执行自动计算任务（{trigger_type == "manual" and "手动触发" or "定时执行"}）'
        )
        db.session.add(log)
        db.session.commit()
        return log

    @classmethod
    def log_execution_result(cls, log_id, total_projects, success_count, failed_count, 
                           execution_duration, error_message=None):
        """更新执行结果日志"""
        log = cls.query.get(log_id)
        if log:
            log.execution_status = 'success' if failed_count == 0 else 'failed'
            log.total_projects = total_projects
            log.success_count = success_count
            log.failed_count = failed_count
            log.execution_duration = execution_duration
            log.error_message = error_message
            log.description = f'执行完成：成功 {success_count} 个，失败 {failed_count} 个'
            db.session.commit()
        return log
