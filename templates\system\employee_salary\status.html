<!DOCTYPE html>
<html>
<head>
    <title>工资条确认</title>
    {% include 'system/common/header.html' %}
    <style>
        body {
            background-color: #f5f5f5;
        }
        .salary-card {
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .salary-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            margin-left: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #FFB800;
            color: #fff;
        }
        .status-confirmed {
            background-color: #5FB878;
            color: #fff;
        }
        .status-disputed {
            background-color: #FF5722;
            color: #fff;
        }
        .employee-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .employee-info .info-item {
            display: inline-block;
            margin-right: 30px;
            font-size: 16px;
        }
        .employee-info .info-label {
            opacity: 0.9;
            margin-right: 8px;
        }
        .salary-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .salary-table th {
            background-color: #f8f9fa;
            color: #333;
            font-weight: bold;
            padding: 15px 12px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
        }
        .salary-table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        .salary-table tr:hover {
            background-color: #f8f9fa;
        }
        .salary-table .label-col {
            width: 35%;
            font-weight: 500;
            color: #555;
        }
        .salary-table .value-col {
            width: 65%;
            text-align: right;
            font-weight: bold;
            color: #333;
        }
        .salary-table .amount {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .salary-table .amount::before {
            content: '¥ ';
            color: #666;
            font-weight: normal;
        }
        .summary-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .summary-item:last-child {
            border-bottom: none;
            background-color: #f8f9fa;
            margin: 15px -20px -20px -20px;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .summary-label {
            font-size: 16px;
            font-weight: 500;
            color: #555;
        }
        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            font-family: 'Courier New', monospace;
        }
        .summary-value.highlight {
            font-size: 24px;
            color: #5FB878;
        }
        .summary-value::before {
            content: '¥ ';
            color: #666;
            font-weight: normal;
            font-size: 14px;
        }
        .action-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .dispute-reason {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #5FB878;
            display: inline-block;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <!-- <div class="salary-title">
                {{ employee.name }} - {{ formatted_month }} 工资条确认
                {% if salary.view_status == 'pending' %}
                <span class="status-badge status-pending">未确认</span>
                {% elif salary.view_status == 'confirmed' %}
                <span class="status-badge status-confirmed">已确认</span>
                {% elif salary.view_status == 'disputed' %}
                <span class="status-badge status-disputed">有异议</span>
                {% endif %}
            </div> -->
        </div>
        <div class="layui-card-body">
            <!-- 员工信息区域 -->
            <div class="employee-info">
                <div class="info-item">
                    <span class="info-label">员工姓名：</span>
                    <span class="info-value">{{ employee.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">工资月份：</span>
                    <span class="info-value">{{ formatted_month }}</span>
                </div>
            </div>

            <!-- 收入项目表格 -->
            <div class="layui-card salary-card">
                <div class="layui-card-header">
                    <span class="section-title">收入项目</span>
                </div>
                <div class="layui-card-body" style="padding: 0;">
                    <table class="salary-table">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th style="text-align: right;">金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="label-col">基本工资</td>
                                <td class="value-col"><span class="amount">{{ salary.base_salary }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">绩效工资</td>
                                <td class="value-col"><span class="amount">{{ salary.performance_salary }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">主管考核项</td>
                                <td class="value-col"><span class="amount">{{ salary.supervisor_assessment }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">考核系数</td>
                                <td class="value-col">{{ salary.assessment_coefficient }}</td>
                            </tr>
                            <tr>
                                <td class="label-col">职务津贴</td>
                                <td class="value-col"><span class="amount">{{ salary.position_allowance }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">全勤奖</td>
                                <td class="value-col"><span class="amount">{{ salary.full_attendance }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">加班费</td>
                                <td class="value-col"><span class="amount">{{ salary.overtime_pay }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">房补</td>
                                <td class="value-col"><span class="amount">{{ salary.housing_subsidy }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">项目考核</td>
                                <td class="value-col"><span class="amount">{{ salary.project_assessment }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">高温费</td>
                                <td class="value-col"><span class="amount">{{ salary.high_temp_allowance }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">其他津贴</td>
                                <td class="value-col"><span class="amount">{{ salary.other_allowance }}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 扣款项目表格 -->
            <div class="layui-card salary-card">
                <div class="layui-card-header">
                    <span class="section-title">扣款项目</span>
                </div>
                <div class="layui-card-body" style="padding: 0;">
                    <table class="salary-table">
                        <thead>
                            <tr>
                                <th>扣款项目</th>
                                <th style="text-align: right;">金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="label-col">请假扣款</td>
                                <td class="value-col"><span class="amount">{{ salary.leave_deduction }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">其他扣款</td>
                                <td class="value-col"><span class="amount">{{ salary.other_deduction }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">社保扣款</td>
                                <td class="value-col"><span class="amount">{{ salary.insurance_deduction }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">个人所得税</td>
                                <td class="value-col"><span class="amount">{{ salary.tax_deduction }}</span></td>
                            </tr>
                            <tr>
                                <td class="label-col">年终奖个税扣款</td>
                                <td class="value-col"><span class="amount">{{ salary.year_end_tax }}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 工资合计区域 -->
            <div class="summary-section">
                <div class="summary-item">
                    <span class="summary-label">应发工资</span>
                    <span class="summary-value">{{ salary.should_pay }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">总扣款</span>
                    <span class="summary-value">{{ salary.total_deduction }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">实发工资</span>
                    <span class="summary-value highlight">{{ salary.actual_salary }}</span>
                </div>
            </div>

            {% if salary.view_status == 'pending' %}
            <div class="action-section">
                <div class="layui-form">
                    <!-- <div class="layui-form-item">
                        <button type="button" class="layui-btn layui-btn-lg" id="confirm-btn">
                            <i class="layui-icon layui-icon-ok"></i> 确认工资条
                        </button>
                        <button type="button" class="layui-btn layui-btn-danger layui-btn-lg" id="dispute-btn" style="margin-left: 15px;">
                            <i class="layui-icon layui-icon-close"></i> 有异议
                        </button>
                    </div> -->

                    <div class="dispute-reason" id="dispute-reason-div">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 80px;">异议原因：</label>
                            <div class="layui-input-block" style="margin-left: 100px;">
                                <textarea name="dispute_reason" placeholder="请详细说明您的异议原因..." class="layui-textarea" id="dispute-reason-input" rows="4"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block" style="margin-left: 0; text-align: center;">
                                <button type="button" class="layui-btn layui-btn-danger" id="submit-dispute-btn">
                                    <i class="layui-icon layui-icon-ok"></i> 提交异议
                                </button>
                                <button type="button" class="layui-btn layui-btn-primary" id="cancel-dispute-btn" style="margin-left: 10px;">
                                    <i class="layui-icon layui-icon-return"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<script>
layui.use(['form', 'jquery', 'popup'], function() {
    let form = layui.form;
    let $ = layui.jquery;
    let popup = layui.popup;

    // 确认工资条
    $('#confirm-btn').click(function() {
        updateStatus('confirmed');
    });

    // 显示异议输入框
    $('#dispute-btn').click(function() {
        $('#dispute-reason-div').show();
    });

    // 取消异议
    $('#cancel-dispute-btn').click(function() {
        $('#dispute-reason-div').hide();
        $('#dispute-reason-input').val('');
    });

    // 提交异议
    $('#submit-dispute-btn').click(function() {
        let reason = $('#dispute-reason-input').val();
        if (!reason) {
            popup.failure('请输入异议原因');
            return;
        }
        updateStatus('disputed', reason);
    });

    // 更新状态函数
    function updateStatus(status, reason) {
        let data = {
            id: {{ salary.id }},  // 模板变量会在服务器端渲染为具体的数值
            status: status
        };

        if (reason) {
            data.dispute_reason = reason;
        }

        $.ajax({
            url: '/system/employee_salary/update_status',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(res) {
                if (res.success) {
                    popup.success(res.msg);
                    // 刷新页面显示最新状态
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    popup.failure(res.msg);
                }
            },
            error: function() {
                popup.failure('操作失败，请稍后重试');
            }
        });
    }
});
</script>
</html>