<!DOCTYPE html>
<html>
<head>
    <title>项目成本详情</title>
    {% include 'system/common/header.html' %}
    <style>
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }

        .layui-card {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden; /* 保持默认的hidden */
            margin-bottom: 20px;
        }

        /* 专门为包含筛选器的卡片设置overflow:visible */
        .layui-card:has(.layui-form) {
            overflow: visible;
        }

        /* 如果浏览器不支持:has选择器，使用类名方式 */
        .layui-card.filter-card {
            overflow: visible;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .project-info {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .project-info .project-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .project-info p {
            margin: 8px 0;
            color: #666;
            line-height: 1.6;
        }

        .project-info .code {
            color: #009688;
            font-weight: 600;
        }

        .project-info .status {
            color: #fff;
            background: linear-gradient(135deg, #01AAED 0%, #0288d1 100%);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .cost-card {
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #009688;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .cost-card .title {
            color: #333;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .cost-card .amount {
            font-size: 28px;
            font-weight: 600;
            color: #009688;
            margin-bottom: 8px;
        }
        .cost-card .change {
            font-size: 14px;
            font-weight: 600;
        }
        .change.increase {
            color: #5FB878;
        }
        .change.decrease {
            color: #FF5722;
        }
        /* 差异占比颜色 */
        .change.red {
            color: #FF5722; /* 红色，超出预估 */
        }
        .change.yellow {
            color: #FFB800; /* 黄色，达到预估的80%以上但未超出 */
        }
        .change.orange {
            color: #FF9966; /* 橙色，达到预估的60%以上但未达到80% */
        }
        .change.green {
            color: #5FB878; /* 绿色，低于预估的60% */
        }

        /* 控制准确率颜色样式 */
        #controlAccuracy.red {
            color: #FF5722; /* 红色，准确率较差 */
            font-weight: 600;
        }
        #controlAccuracy.yellow {
            color: #FFB800; /* 黄色，准确率良好 */
            font-weight: 600;
        }
        #controlAccuracy.orange {
            color: #FF9966; /* 橙色，准确率一般 */
            font-weight: 600;
        }
        #controlAccuracy.green {
            color: #5FB878; /* 绿色，准确率优秀 */
            font-weight: 600;
        }
        .chart-container {
            height: 350px;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .cost-detail-table .layui-table-cell {
            height: auto;
            line-height: 24px;
        }

        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .layui-btn-normal {
            background: linear-gradient(135deg, #1E9FFF 0%, #1890ff 100%);
            border: none;
        }

        .layui-btn-normal:hover {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .layui-btn-primary {
            border: 1px solid #d9d9d9;
            color: #666;
        }

        .layui-btn-primary:hover {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }

        .layui-form-label {
            font-weight: 500;
            color: #333;
        }

        .layui-input, .layui-select, .layui-textarea {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-input:focus, .layui-select:focus, .layui-textarea:focus {
            border-color: #1E9FFF;
            box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
        }

        .layui-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .layui-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
        }

        /* 解决下拉框被遮挡问题 - 真正的原因是overflow:hidden */
        /* 通过设置包含筛选器的卡片为overflow:visible来解决 */
        /* 项目状态颜色样式 */
        .status-draft {
            background-color: #BDBDBD !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-reviewing {
            background-color: #1E9FFF !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-pending {
            background-color: #FFB800 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-production {
            background-color: #5FB878 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-warehouse {
            background-color: #8BC34A !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-shipping {
            background-color: #2196F3 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-installation {
            background-color: #00BCD4 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-acceptance {
            background-color: #E91E63 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-completed {
            background-color: #2F4056 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-cancelled {
            background-color: #FF5722 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-acceptance {
            background-color: #01AAED !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-debugging {
            background-color: #9966CC !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-modification {
            background-color: #FF9966 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
    </style>
</head>
<body class="pear-container">
    <!-- 项目基本信息 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="project-info" style="background: white;">
                <div class="project-title">
                    项目编号：<span class="code"><span id="projectType">--</span>-{{ project.project_code }}</span> - {{ project.project_name }}
                    <span class="status" id="projectStatus">--</span>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md2" style="width: 20%;">
                        <p><i class="layui-icon layui-icon-rmb"></i> 项目价格：¥<span id="projectPrice">--</span></p>
                    </div>
                    <div class="layui-col-md2" style="width: 20%;">
                        <p><i class="layui-icon layui-icon-component"></i> 机台数量：<span id="machineNumber">--</span></p>
                    </div>
                    <div class="layui-col-md2" style="width: 20%;">
                        <p><i class="layui-icon layui-icon-chart"></i> 预估成本：¥<span id="estimateCost">--</span></p>
                    </div>
                    <div class="layui-col-md3" style="width: 20%;">
                        <p><i class="layui-icon layui-icon-chart-screen"></i> 成本占比：<span id="costPercent">--</span>%</p>
                    </div>
                    <div class="layui-col-md3" style="width: 20%;">
                        <p><i class="layui-icon layui-icon-ok-circle"></i> 控制准确率：<span id="controlAccuracy">--</span>%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实际成本卡片 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际总成本</div>
                <div class="amount" id="totalCost">¥--</div>
                <div class="change" id="totalCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际BOM成本</div>
                <div class="amount" id="bomCost">¥--</div>
                <div class="change" id="bomCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际人工成本(含外协费)</div>
                <div class="amount" id="laborCost">¥--</div>
                <div class="change" id="laborCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际其他总成本</div>
                <div class="amount" id="otherCost">¥--</div>
                <div class="change" id="otherCostChange">--</div>
            </div>
        </div>
    </div>

    <!-- 预估成本卡片 -->
    <div class="layui-row layui-col-space10" style="margin-top: 10px;">
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估总成本</div>
                <div class="amount" id="estimateTotalCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateTotalCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估BOM成本</div>
                <div class="amount" id="estimateBomCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateBomCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估人工成本</div>
                <div class="amount" id="estimateLaborCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateLaborCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估其他成本</div>
                <div class="amount" id="estimateOtherCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateOtherCostChange">--</div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">成本构成分析</div>
                <div class="layui-card-body chart-container">
                    <div id="costStructureChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">月度成本趋势</div>
                <div class="layui-card-body chart-container">
                    <div id="costTrendChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成本明细表格 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card filter-card">
                <div class="layui-card-header">成本明细</div>
                <div class="layui-card-body">
                    <!-- 筛选控件区域 -->
                    <div class="layui-form" style="margin-bottom: 15px; padding: 15px; background-color: #f8f8f8; border-radius: 4px;">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md3">
                                <label class="layui-form-label" style="width: 60px;">成本类型</label>
                                <div class="layui-input-block" style="margin-left: 90px;">
                                    <select name="cost_type" id="cost-type-filter" lay-filter="cost-type-filter">
                                        <option value="">全部类型</option>
                                        <option value="BOM成本">BOM成本</option>
                                        <option value="人工成本">人工成本</option>
                                        <option value="差旅费">差旅费</option>
                                        <option value="外协费">外协费</option>
                                        <option value="项目激励">项目激励</option>
                                        <option value="培训费">培训费</option>
                                        <option value="维修费">维修费</option>
                                        <option value="管理费">管理费</option>
                                        <option value="运杂费">运杂费</option>
                                        <option value="其他费用">其他费用</option>

                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <label class="layui-form-label" style="width: 60px;">开始时间</label>
                                <div class="layui-input-block" style="margin-left: 90px;">
                                    <input type="text" name="start_date" id="start-date-filter" placeholder="YYYY-MM" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <label class="layui-form-label" style="width: 60px;">结束时间</label>
                                <div class="layui-input-block" style="margin-left: 90px;">
                                    <input type="text" name="end_date" id="end-date-filter" placeholder="YYYY-MM" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="filter-btn">筛选</button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="reset-btn">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <table id="cost-detail-table" lay-filter="cost-detail-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 人工成本明细部分 - 已整合到成本明细表格中 -->
    <!-- <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span>月度人工成本明细(不含外协费)</span>
                </div>
                <div class="layui-card-body">
                    <table id="labor-cost-table" lay-filter="labor-cost-table"></table>
                </div>
            </div>
        </div>
    </div> -->

    <!-- 部门预估成本明细 -->
    <!-- <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span>部门预估成本明细</span>
                </div>
                <div class="layui-card-body">
                    <table id="dept-estimate-table" lay-filter="dept-estimate-table"></table>
                </div>
            </div>
        </div>
    </div> -->
</body>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['echarts', 'table', 'jquery', 'element', 'form', 'laydate'], function() {
        let echarts = layui.echarts;
        let table = layui.table;
        let $ = layui.jquery;
        let element = layui.element;
        let form = layui.form;
        let laydate = layui.laydate;

        // 将项目ID作为数值传递给JavaScript
        let projectId = parseInt('{{ project.id }}');

        // 加载项目详情数据
        loadProjectDetails();

        // 加载成本趋势图表
        loadCostTrendChart();

        // 加载成本明细表格
        loadCostDetailsTable();

        // 加载月度人工成本数据 - 已整合到成本明细表格中
        // loadLaborCostTable();

        // 加载部门预估成本明细
        loadDeptEstimateTable();

        // 初始化筛选功能
        initFilterControls();

        function loadProjectDetails() {
            $.ajax({
                url: '/system/project_cost/api/project/' + projectId,
                success: function(data) {
                    // 更新项目基本信息
                    $('#projectStatus').text(data.status_name);

                    // 根据状态设置不同的样式类
                    const $statusElement = $('#projectStatus');
                    $statusElement.removeClass('status'); // 移除默认的status类

                    // 尝试从多个可能的属性中获取状态值
                    let statusValue = null;
                    if (data.status_value !== undefined) {
                        statusValue = data.status_value;
                    } else if (data.project_status !== undefined) {
                        statusValue = data.project_status;
                    } else if (data.status !== undefined) {
                        statusValue = data.status;
                    } else if (data.data_value !== undefined) {
                        statusValue = data.data_value;
                    }

                    // 状态映射 - 根据状态值
                    const statusValueMapping = {
                        '0': 'status-pending',    // 未开始
                        '1': 'status-production', // 生产中
                        '3': 'status-warehouse',  // 入库完成
                        '5': 'status-shipping',   // 发货完成
                        '6': 'status-installation', // 安装调试中
                        '7': 'status-installation', // 安装调试完成
                        '10': 'status-acceptance', // 验收中
                        '12': 'status-completed'  // 项目已完成
                    };

                    // 状态映射 - 根据状态名称
                    const statusNameMapping = {
                        '未开始': 'status-pending',
                        '生产中': 'status-production',
                        '入库完成': 'status-warehouse',
                        '发货完成': 'status-shipping',
                        '安装调试中': 'status-installation',
                        '安装调试完成': 'status-installation',
                        '验收中': 'status-acceptance',
                        '调试中': 'status-debugging',
                        '整改中': 'status-modification',
                        '项目已完成': 'status-completed'
                    };

                    // 确定使用哪个样式类
                    let statusClass = '';

                    // 优先使用状态值匹配
                    if (statusValue !== null) {
                        statusClass = statusValueMapping[statusValue] || '';
                    }

                    // 如果没有匹配到，使用状态名称匹配
                    if (!statusClass) {
                        statusClass = statusNameMapping[data.status_name] || '';
                    }

                    // 如果还没有匹配到，尝试使用包含关键词的方式
                    if (!statusClass) {
                        if (data.status_name.includes('草稿')) {
                            statusClass = 'status-draft';
                        } else if (data.status_name.includes('审核中')) {
                            statusClass = 'status-reviewing';
                        } else if (data.status_name.includes('待生产')) {
                            statusClass = 'status-pending';
                        } else if (data.status_name.includes('已取消')) {
                            statusClass = 'status-cancelled';
                        }
                    }

                    // 如果找到了匹配的样式类，则应用它，否则使用默认的status类
                    if (statusClass) {
                        $statusElement.addClass(statusClass);
                    } else {
                        $statusElement.addClass('status');
                    }

                    $('#projectType').text(data.project_type_name || '');
                    $('#projectPrice').text(data.price ? data.price.toLocaleString() : 0);
                    $('#machineNumber').text(data.machine_number || 0);
                    $('#estimateCost').text(data.estimate_cost ? data.estimate_cost.toLocaleString() : 0);
                    $('#costPercent').text(data.cost_percent);

                    // 更新控制准确率显示
                    updateControlAccuracyDisplay('#controlAccuracy', data.control_accuracy);

                    // 更新实际成本卡片
                    $('#totalCost').text('¥' + data.costs.total.value.toLocaleString());
                    updateEstimateDiffDisplay('#totalCostChange', data.costs.total.value, data.estimate_costs.total.value);

                    $('#bomCost').text('¥' + data.costs.bom.value.toLocaleString());
                    updateEstimateDiffDisplay('#bomCostChange', data.costs.bom.value, data.estimate_costs.bom.value);

                    $('#laborCost').text('¥' + data.costs.labor.value.toLocaleString());
                    updateEstimateDiffDisplay('#laborCostChange', data.costs.labor.value, data.estimate_costs.labor.value);

                    $('#otherCost').text('¥' + data.costs.other.value.toLocaleString());
                    updateEstimateDiffDisplay('#otherCostChange', data.costs.other.value, data.estimate_costs.other.value);

                    // 更新预估成本卡片
                    $('#estimateTotalCost').text('¥' + data.estimate_costs.total.value.toLocaleString());
                    $('#estimateTotalCostChange').text(''); // 移除同比增长信息

                    $('#estimateBomCost').text('¥' + data.estimate_costs.bom.value.toLocaleString());
                    $('#estimateBomCostChange').text(''); // 移除同比增长信息

                    $('#estimateLaborCost').text('¥' + data.estimate_costs.labor.value.toLocaleString());
                    $('#estimateLaborCostChange').text(''); // 移除同比增长信息

                    $('#estimateOtherCost').text('¥' + data.estimate_costs.other.value.toLocaleString());
                    $('#estimateOtherCostChange').text(''); // 移除同比增长信息

                    // 渲染成本构成图表
                    renderCostStructureChart(data.costs);
                },
                error: function() {
                    layer.msg('加载项目详情数据失败', {icon: 2});
                }
            });
        }

        function updateChangeDisplay(selector, change, changeType) {
            let $el = $(selector);
            $el.removeClass('increase decrease');

            if (changeType === 'increase') {
                $el.addClass('increase');
                $el.html('<i class="layui-icon layui-icon-up"></i> ' + Math.abs(change).toFixed(1) + '% 同比增长');
            } else {
                $el.addClass('decrease');
                $el.html('<i class="layui-icon layui-icon-down"></i> ' + Math.abs(change).toFixed(1) + '% 同比下降');
            }
        }

        // 计算实际成本与预估成本的差异占比
        function updateEstimateDiffDisplay(selector, actualValue, estimateValue) {
            let $el = $(selector);
            $el.removeClass('increase decrease red yellow orange green');

            if (estimateValue === 0) {
                // 预估成本为0时，无法计算差异占比
                $el.html('无法计算');
                return;
            }

            // 计算差异占比
            let diff = actualValue - estimateValue;
            let diffPercent = (diff / estimateValue) * 100;
            let ratio = actualValue / estimateValue;

            if (diff >= 0) {
                // 实际成本大于等于预估成本，显示红色
                $el.addClass('red');
                $el.html('<i class="layui-icon layui-icon-up"></i> ' + Math.abs(diffPercent).toFixed(1) + '% 超出预估');
            } else {
                // 实际成本小于预估成本，根据比例设置不同颜色
                if (ratio >= 0.8) {
                    // 达到预估的80%以上但未超出，显示黄色
                    $el.addClass('yellow');
                } else if (ratio >= 0.6) {
                    // 达到预估的60%以上但未达到80%，显示橙色
                    $el.addClass('orange');
                } else {
                    // 低于预估的60%，显示绿色
                    $el.addClass('green');
                }
                $el.html('<i class="layui-icon layui-icon-down"></i> ' + Math.abs(diffPercent).toFixed(1) + '% 低于预估');
            }
        }

        // 更新控制准确率显示
        function updateControlAccuracyDisplay(selector, accuracy) {
            let $el = $(selector);
            $el.removeClass('red yellow orange green');

            if (accuracy === undefined || accuracy === null) {
                $el.html('--');
                return;
            }

            // 根据准确率设置不同颜色
            if (accuracy >= 90) {
                // 90%以上，显示绿色（优秀）
                $el.addClass('green');
            } else if (accuracy >= 80) {
                // 80%-90%，显示黄色（良好）
                $el.addClass('yellow');
            } else if (accuracy >= 70) {
                // 70%-80%，显示橙色（一般）
                $el.addClass('orange');
            } else {
                // 70%以下，显示红色（较差）
                $el.addClass('red');
            }

            $el.html(accuracy.toFixed(1));
        }

        function renderCostStructureChart(costs) {
            let chartDom = document.getElementById('costStructureChart');
            let myChart = echarts.init(chartDom);

            let option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    data: ['BOM成本', '人工成本', '其他成本']
                },
                series: [
                    {
                        name: '成本构成',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: costs.bom.value, name: 'BOM成本'},
                            {value: costs.labor.value, name: '人工成本'},
                            {value: costs.other.value, name: '其他成本'}
                        ]
                    }
                ]
            };

            myChart.setOption(option);

            // 窗口大小变化时自动调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        function loadCostTrendChart() {
            $.ajax({
                url: '/system/project_cost/api/project/' + projectId + '/trend',
                success: function(data) {
                    let chartDom = document.getElementById('costTrendChart');
                    let myChart = echarts.init(chartDom);

                    let months = data.months;
                    let bomCosts = data.bom_costs;
                    let laborCosts = data.labor_costs;
                    let otherCosts = data.other_costs;
                    let totalCosts = data.total_costs;

                    let option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['BOM成本', '人工成本', '其他成本', '总成本']
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: months
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '¥{value}'
                            }
                        },
                        series: [
                            {
                                name: 'BOM成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: bomCosts
                            },
                            {
                                name: '人工成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: laborCosts
                            },
                            {
                                name: '其他成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: otherCosts
                            },
                            {
                                name: '总成本',
                                type: 'line',
                                data: totalCosts
                            }
                        ]
                    };

                    myChart.setOption(option);

                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载成本趋势数据失败', {icon: 2});
                }
            });
        }

        function loadCostDetailsTable(filterParams = {}) {
            // 构建请求URL
            let url = '/system/project_cost/api/project/' + projectId + '/cost_details';
            let params = [];

            // 添加筛选参数
            if (filterParams.cost_types && filterParams.cost_types.length > 0) {
                filterParams.cost_types.forEach(function(type) {
                    params.push('cost_type=' + encodeURIComponent(type));
                });
            }
            if (filterParams.start_date) {
                params.push('start_date=' + encodeURIComponent(filterParams.start_date));
            }
            if (filterParams.end_date) {
                params.push('end_date=' + encodeURIComponent(filterParams.end_date));
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            console.log('请求URL:', url);  // 调试用

            table.render({
                elem: '#cost-detail-table',
                url: url,
                page: true,
                limit: 10,
                limits: [10, 20, 50],
                cols: [[
                    {field: 'date', title: '导入日期', align: 'center'},
                    {field: 'year_month', title: '发生年月', align: 'center'},
                    {field: 'type', title: '类型', align: 'center'},
                    {field: 'amount', title: '金额', align: 'center', templet: function(d) {
                        return '¥' + d.amount.toLocaleString();
                    }},
                    {field: 'remark', title: '备注', align: 'center'}
                ]],
                done: function(res) {
                    // 移除之前的合计行
                    $('.cost-summary-row').remove();

                    // 更新合计显示
                    if (res.total_amount !== undefined && res.total_amount > 0) {
                        // 创建合计行HTML
                        let summaryHtml = `
                            <div class="cost-summary-row" style="margin: 15px 0; padding: 0;">
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2); border-left: 5px solid #5a67d8;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div style="color: white; font-size: 16px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                            <i class="layui-icon layui-icon-calculator" style="font-size: 18px; margin-right: 8px; opacity: 0.9;"></i>
                                          合计
                                        </div>
                                        <div style="color: #fff; font-size: 24px; font-weight: 900; text-shadow: 0 2px 4px rgba(0,0,0,0.2); letter-spacing: 1px;">
                                            <i class="layui-icon layui-icon-rmb" style="font-size: 20px; margin-right: 5px; opacity: 0.9;"></i>
                                            <span style="background: rgba(255,255,255,0.15); padding: 8px 15px; border-radius: 25px; backdrop-filter: blur(10px);">
                                                ${res.total_amount.toLocaleString()}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // 将合计行插入到分页控件前面
                        $('#cost-detail-table').next('.layui-table-view').find('.layui-table-page').before(summaryHtml);
                    }
                }
            });
        }

        function loadLaborCostTable() {
            $.ajax({
                url: '/system/project_cost/api/labor_cost/result?project_id=' + projectId,
                success: function(data) {
                    if (data && data.monthly_costs) {
                        // 渲染表格
                        table.render({
                            elem: '#labor-cost-table',
                            data: data.monthly_costs,
                            page: false,
                            cols: [[
                                {field: 'year_month', title: '年月', align: 'center'},
                                {field: 'cost', title: '人工成本', align: 'center', templet: function(d) {
                                    return '¥' + d.cost.toLocaleString();
                                }}
                            ]],
                            done: function() {
                                // 移除之前的合计行
                                $('.labor-cost-summary-row').remove();

                                // 添加表格底部合计行
                                let totalCost = data.total_cost || 0;

                                if (totalCost > 0) {
                                    // 创建现代化合计行HTML
                                    let summaryHtml = `
                                        <div class="labor-cost-summary-row" style="margin: 15px 0; padding: 0;">
                                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2); border-left: 5px solid #5a67d8;">
                                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                                    <div style="color: white; font-size: 16px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                                        <i class="layui-icon layui-icon-user" style="font-size: 18px; margin-right: 8px; opacity: 0.9;"></i>
                                                        人工成本合计
                                                    </div>
                                                    <div style="color: #fff; font-size: 24px; font-weight: 900; text-shadow: 0 2px 4px rgba(0,0,0,0.2); letter-spacing: 1px;">
                                                        <i class="layui-icon layui-icon-rmb" style="font-size: 20px; margin-right: 5px; opacity: 0.9;"></i>
                                                        <span style="background: rgba(255,255,255,0.15); padding: 8px 15px; border-radius: 25px; backdrop-filter: blur(10px);">
                                                            ${totalCost.toLocaleString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `;

                                    // 在表格下方添加合计信息
                                    $('#labor-cost-table').next().append(summaryHtml);
                                }
                            }
                        });
                    } else {
                        // 如果没有数据，显示空表格
                        table.render({
                            elem: '#labor-cost-table',
                            data: [],
                            page: false,
                            cols: [[
                                {field: 'work_month', title: '年月', align: 'center'},
                                {field: 'cost', title: '人工成本', align: 'center'}
                            ]]
                        });
                    }
                },
                error: function() {
                    layer.msg('加载人工成本数据失败', {icon: 2});
                }
            });
        }

        function loadDeptEstimateTable() {
            $.ajax({
                url: '/system/project_cost/api/project/' + projectId + '/dept_estimates',
                success: function(data) {
                    if (data.code === 0 && data.data) {
                        // 渲染部门预估成本明细表格
                        table.render({
                            elem: '#dept-estimate-table',
                            data: data.data,
                            page: false,
                            cols: [[
                                {field: 'dept_name', title: '部门', align: 'center'},
                                {field: 'location', title: '工作地点', align: 'center'},
                                {field: 'estimate_hours', title: '预估工时', align: 'center'},
                                {field: 'labor_cost', title: '预估人工成本', align: 'center', templet: function(d) {
                                    return '¥' + d.labor_cost.toLocaleString();
                                }},
                                {field: 'mechanical_bom_cost', title: '预估机械BOM成本', align: 'center', templet: function(d) {
                                    return '¥' + d.mechanical_bom_cost.toLocaleString();
                                }},
                                {field: 'electrical_bom_cost', title: '预估电气BOM成本', align: 'center', templet: function(d) {
                                    return '¥' + d.electrical_bom_cost.toLocaleString();
                                }},
                                {field: 'bom_cost', title: '预估BOM成本合计', align: 'center', templet: function(d) {
                                    return '¥' + d.bom_cost.toLocaleString();
                                }},
                                {field: 'other_cost', title: '预估其他成本', align: 'center', templet: function(d) {
                                    return '¥' + d.other_cost.toLocaleString();
                                }},
                                {field: 'total_cost', title: '预估总成本', align: 'center', templet: function(d) {
                                    return '¥' + d.total_cost.toLocaleString();
                                }}
                            ]],
                            text: {none: '暂无部门预估数据'},
                            done: function() {
                                // 移除之前的合计行
                                $('.dept-estimate-summary-row').remove();

                                // 计算合计
                                let totalLaborCost = data.data.reduce((sum, item) => sum + item.labor_cost, 0);
                                let totalMechanicalBom = data.data.reduce((sum, item) => sum + item.mechanical_bom_cost, 0);
                                let totalElectricalBom = data.data.reduce((sum, item) => sum + item.electrical_bom_cost, 0);
                                let totalBomCost = data.data.reduce((sum, item) => sum + item.bom_cost, 0);
                                let totalOtherCost = data.data.reduce((sum, item) => sum + item.other_cost, 0);
                                let totalEstimateCost = data.data.reduce((sum, item) => sum + item.total_cost, 0);

                                if (data.data.length > 0) {
                                    // 创建合计行HTML
                                    let summaryHtml = `
                                        <div class="dept-estimate-summary-row" style="margin: 15px 0; padding: 0;">
                                            <div style="background: linear-gradient(135deg, #FFB800 0%, #FF9966 100%); padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(255, 184, 0, 0.2); border-left: 5px solid #FFB800;">
                                                <div style="color: white; font-size: 16px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.1); margin-bottom: 10px;">
                                                    <i class="layui-icon layui-icon-chart" style="font-size: 18px; margin-right: 8px; opacity: 0.9;"></i>
                                                    部门预估成本合计
                                                </div>
                                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                                    <div style="background: rgba(255,255,255,0.15); padding: 8px 12px; border-radius: 6px;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">人工成本</div>
                                                        <div style="color: white; font-size: 18px; font-weight: 700;">¥${totalLaborCost.toLocaleString()}</div>
                                                    </div>
                                                    <div style="background: rgba(255,255,255,0.15); padding: 8px 12px; border-radius: 6px;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">BOM成本</div>
                                                        <div style="color: white; font-size: 18px; font-weight: 700;">¥${totalBomCost.toLocaleString()}</div>
                                                    </div>
                                                    <div style="background: rgba(255,255,255,0.15); padding: 8px 12px; border-radius: 6px;">
                                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">其他成本</div>
                                                        <div style="color: white; font-size: 18px; font-weight: 700;">¥${totalOtherCost.toLocaleString()}</div>
                                                    </div>
                                                    <div style="background: rgba(255,255,255,0.25); padding: 8px 12px; border-radius: 6px; border: 2px solid rgba(255,255,255,0.3);">
                                                        <div style="color: white; font-size: 12px; font-weight: 600;">总预估成本</div>
                                                        <div style="color: white; font-size: 20px; font-weight: 900;">¥${totalEstimateCost.toLocaleString()}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `;

                                    // 在表格下方添加合计信息
                                    $('#dept-estimate-table').next().append(summaryHtml);
                                }
                            }
                        });
                    } else {
                        // 如果没有数据，显示空表格
                        table.render({
                            elem: '#dept-estimate-table',
                            data: [],
                            page: false,
                            cols: [[
                                {field: 'dept_name', title: '部门', align: 'center'},
                                {field: 'location', title: '工作地点', align: 'center'},
                                {field: 'estimate_hours', title: '预估工时', align: 'center'},
                                {field: 'labor_cost', title: '预估人工成本', align: 'center'},
                                {field: 'bom_cost', title: '预估BOM成本', align: 'center'},
                                {field: 'other_cost', title: '预估其他成本', align: 'center'},
                                {field: 'total_cost', title: '预估总成本', align: 'center'}
                            ]],
                            text: {none: '暂无部门预估数据'}
                        });
                    }
                },
                error: function() {
                    layer.msg('加载部门预估成本数据失败', {icon: 2});
                }
            });
        }

        function initFilterControls() {
            // 渲染表单
            form.render();

            // 初始化时间选择器
            // 开始时间选择器
            laydate.render({
                elem: '#start-date-filter',
                type: 'month',
                format: 'yyyy-MM'
            });

            // 结束时间选择器
            laydate.render({
                elem: '#end-date-filter',
                type: 'month',
                format: 'yyyy-MM'
            });

            // 筛选按钮事件
            $('#filter-btn').on('click', function() {
                applyFilter();
            });

            // 重置按钮事件
            $('#reset-btn').on('click', function() {
                resetFilter();
            });

            // 监听表单选择事件
            form.on('select(cost-type-filter)', function(data) {
                console.log('成本类型选择:', data.value);
            });
        }

        function applyFilter() {
            // 获取筛选条件
            let costType = $('#cost-type-filter').val();
            let startDate = $('#start-date-filter').val();
            let endDate = $('#end-date-filter').val();

            // 构建筛选参数
            let filterParams = {};
            if (costType && costType !== '') {
                filterParams.cost_types = [costType];  // 单选改为数组形式
            }
            if (startDate) {
                filterParams.start_date = startDate;
            }
            if (endDate) {
                filterParams.end_date = endDate;
            }


            // 重新加载表格
            loadCostDetailsTable(filterParams);
        }

        function resetFilter() {
            // 清空筛选条件
            $('#cost-type-filter').val('');
            $('#start-date-filter').val('');
            $('#end-date-filter').val('');

            // 重新渲染表单
            form.render('select');

            // 重新加载表格
            loadCostDetailsTable();
        }
    });
</script>
</html>