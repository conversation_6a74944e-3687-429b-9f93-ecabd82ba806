"""
试用期提醒服务
负责检查试用期到期员工并发送邮件提醒
"""
import datetime
from datetime import date, timedelta
from flask import current_app, render_template
from applications.models import ygong, ProbationReminderLog, Dept, HREmailConfig
from applications.extensions import db
from applications.common.utils.mail import send_mail
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProbationReminderService:
    """试用期提醒服务类"""
    
    @staticmethod
    def check_and_send_reminders():
        """检查并发送试用期到期提醒"""
        try:
            logger.info("开始执行试用期提醒检查...")

            today = date.today()

            # 查询所有试用期和实习期员工
            probation_employees = ygong.query.filter(
                ygong.is_formal.in_(['试用期', '实习期', '试用', '实习']),
                ygong.enable == 1
            ).all()

            reminder_count = 0

            for employee in probation_employees:
                try:
                    # 计算试用期结束日期
                    end_date = employee.calculate_probation_end_date()
                    if not end_date:
                        continue

                    days_remaining = (end_date - today).days

                    # 判断是否需要发送提醒
                    reminder_type = ProbationReminderService._get_reminder_type(days_remaining)

                    if reminder_type:
                        should_send = ProbationReminderService._should_send_reminder(employee, reminder_type)

                        if should_send:
                            # 发送提醒邮件
                            success = ProbationReminderService._send_probation_reminder(
                                employee, days_remaining, reminder_type, end_date
                            )

                            if success:
                                reminder_count += 1
                                # 更新员工的最后提醒日期
                                employee.last_reminder_date = today
                                db.session.commit()
                                logger.info(f"成功为员工 {employee.name} 发送 {reminder_type} 提醒")
                            else:
                                logger.error(f"为员工 {employee.name} 发送提醒失败")

                except Exception as e:
                    logger.error(f"处理员工 {employee.name} 时发生错误: {str(e)}")
                    continue

            logger.info(f"试用期提醒检查完成，共发送 {reminder_count} 条提醒")
            return reminder_count

        except Exception as e:
            logger.error(f"试用期提醒检查过程中发生错误: {str(e)}")
            return 0

    @staticmethod
    def check_and_send_reminders_with_details():
        """检查并发送试用期到期提醒（带详细信息）"""
        try:
            logger.info("开始执行试用期提醒检查（详细模式）...")

            today = date.today()
            details = []

            # 查询所有试用期和实习期员工
            probation_employees = ygong.query.filter(
                ygong.is_formal.in_(['试用期', '实习期', '试用', '实习']),
                ygong.enable == 1
            ).all()

            details.append(f"找到 {len(probation_employees)} 个试用期/实习期员工")

            reminder_count = 0

            for employee in probation_employees:
                try:
                    employee_detail = f"员工: {employee.name} (ID: {employee.id})"

                    # 计算试用期结束日期
                    end_date = employee.calculate_probation_end_date()
                    if not end_date:
                        details.append(f"{employee_detail} - 无法计算试用期结束日期")
                        continue

                    days_remaining = (end_date - today).days
                    employee_detail += f" - 剩余天数: {days_remaining}"

                    # 判断是否需要发送提醒
                    reminder_type = ProbationReminderService._get_reminder_type(days_remaining)
                    employee_detail += f" - 提醒类型: {reminder_type or '无需提醒'}"

                    if reminder_type:
                        should_send = ProbationReminderService._should_send_reminder(employee, reminder_type)
                        employee_detail += f" - 是否发送: {should_send}"

                        if should_send:
                            # 获取收件人
                            recipients = ProbationReminderService._get_recipients(employee)
                            employee_detail += f" - 收件人: {', '.join(recipients)}"

                            # 发送提醒邮件
                            success = ProbationReminderService._send_probation_reminder(
                                employee, days_remaining, reminder_type, end_date
                            )

                            if success:
                                reminder_count += 1
                                # 更新员工的最后提醒日期
                                employee.last_reminder_date = today
                                db.session.commit()
                                employee_detail += " - 发送成功"
                            else:
                                employee_detail += " - 发送失败"
                        else:
                            employee_detail += " - 今天已发送过，跳过"

                    details.append(employee_detail)

                except Exception as e:
                    error_detail = f"员工 {employee.name} 处理失败: {str(e)}"
                    details.append(error_detail)
                    logger.error(error_detail)
                    continue

            result = {
                'reminder_count': reminder_count,
                'total_employees': len(probation_employees),
                'details': details
            }

            logger.info(f"试用期提醒检查完成，共发送 {reminder_count} 条提醒")
            return result

        except Exception as e:
            logger.error(f"试用期提醒检查过程中发生错误: {str(e)}")
            return {
                'reminder_count': 0,
                'total_employees': 0,
                'details': [f"检查过程发生错误: {str(e)}"]
            }
    
    @staticmethod
    def _get_reminder_type(days_remaining):
        """根据剩余天数确定提醒类型"""
        if days_remaining <= 0:
            return 'overdue'  # 已逾期
        elif days_remaining == 1:
            return 'today'    # 明天到期
        elif days_remaining == 3:
            return '3days'    # 3天后到期
        elif days_remaining == 7:
            return '7days'    # 7天后到期
        else:
            return None       # 不需要提醒
    
    @staticmethod
    def _should_send_reminder(employee, reminder_type):
        """判断是否应该发送提醒（避免重复发送）"""
        today = date.today()
        
        # 检查今天是否已经发送过相同类型的提醒
        existing_reminder = ProbationReminderLog.query.filter(
            ProbationReminderLog.employee_id == employee.id,
            ProbationReminderLog.reminder_type == reminder_type,
            db.func.date(ProbationReminderLog.sent_date) == today
        ).first()
        
        return existing_reminder is None
    
    @staticmethod
    def _send_probation_reminder(employee, days_remaining, reminder_type, end_date):
        """发送试用期提醒邮件"""
        try:
            # 获取收件人列表
            recipients = ProbationReminderService._get_recipients(employee)
            
            if not recipients:
                logger.warning(f"员工 {employee.name} 没有找到合适的收件人")
                return False
            
            # 生成邮件内容
            subject = ProbationReminderService._generate_email_subject(employee, days_remaining, reminder_type)
            html_content = ProbationReminderService._generate_email_content(
                employee, days_remaining, reminder_type, end_date
            )
            
            # 发送邮件
            try:
                send_mail(
                    recipients=recipients,
                    subject=subject,
                    content=html_content
                )

                # 记录发送成功日志
                log_entry = ProbationReminderLog(
                    employee_id=employee.id,
                    employee_name=employee.name,
                    reminder_type=reminder_type,
                    recipients=','.join(recipients),
                    email_subject=subject,
                    status='sent'
                )
                db.session.add(log_entry)
                db.session.commit()

                return True

            except Exception as mail_error:
                logger.error(f"邮件发送失败：{str(mail_error)}")

                # 记录发送失败日志
                log_entry = ProbationReminderLog(
                    employee_id=employee.id,
                    employee_name=employee.name,
                    reminder_type=reminder_type,
                    recipients=','.join(recipients),
                    email_subject=subject,
                    status='failed'
                )
                db.session.add(log_entry)
                db.session.commit()

                return False
            
        except Exception as e:
            logger.error(f"发送提醒邮件时发生错误: {str(e)}")
            return False
    
    @staticmethod
    def _get_recipients(employee):
        """获取邮件收件人列表"""
        recipients = []

        # 从数据库获取启用的HR邮箱列表
        try:
            hr_emails = HREmailConfig.get_active_email_addresses()
            if hr_emails:
                recipients.extend(hr_emails)
            else:
                # 如果数据库中没有配置HR邮箱，则使用配置文件中的默认邮箱
                hr_email = current_app.config.get('HR_EMAIL', '<EMAIL>')
                recipients.append(hr_email)
                logger.warning("数据库中未找到启用的HR邮箱配置，使用默认配置邮箱")
        except Exception as e:
            # 如果数据库查询失败，使用配置文件中的默认邮箱
            hr_email = current_app.config.get('HR_EMAIL', '<EMAIL>')
            recipients.append(hr_email)
            logger.error(f"获取HR邮箱配置失败，使用默认配置邮箱: {str(e)}")

        # 添加部门负责人邮箱（如果有的话）
        if employee.dept and hasattr(employee.dept, 'leader_email'):
            if employee.dept.leader_email:
                recipients.append(employee.dept.leader_email)

        return list(set(recipients))  # 去重
    
    @staticmethod
    def _generate_email_subject(employee, days_remaining, reminder_type):
        """生成邮件主题"""
        if reminder_type == 'overdue':
            return f"【紧急】员工试用期已到期 - {employee.name}"
        elif reminder_type == 'today':
            return f"【提醒】员工试用期明天到期 - {employee.name}"
        elif reminder_type == '3days':
            return f"【提醒】员工试用期3天后到期 - {employee.name}"
        elif reminder_type == '7days':
            return f"【提醒】员工试用期7天后到期 - {employee.name}"
        else:
            return f"【提醒】员工试用期即将到期 - {employee.name}"
    
    @staticmethod
    def _generate_email_content(employee, days_remaining, reminder_type, end_date):
        """生成邮件HTML内容"""
        urgency_class = 'urgent' if days_remaining <= 1 else ('warning' if days_remaining <= 3 else 'normal')

        # 确定优先级文本
        if reminder_type == 'overdue':
            priority_text = '紧急处理'
        elif reminder_type == 'today':
            priority_text = '明天到期'
        elif reminder_type == '3days':
            priority_text = '3天到期'
        elif reminder_type == '7days':
            priority_text = '7天到期'
        else:
            priority_text = '即将到期'

        # 使用模板渲染
        try:
            html_content = render_template(
                'email/probation_reminder.html',
                employee=employee,
                days_remaining=days_remaining,
                reminder_type=reminder_type,
                end_date=end_date,
                urgency_class=urgency_class,
                priority_text=priority_text,
                system_url=current_app.config.get('SYSTEM_URL', '#'),
                sent_time=datetime.datetime.now()
            )
            return html_content
        except Exception as e:
            logger.error(f"渲染邮件模板失败: {str(e)}")
            # 如果模板渲染失败，返回简单的HTML内容
            return f"""
            <html>
            <body>
                <h2>员工试用期到期提醒</h2>
                <p>员工 {employee.name} 的{employee.is_formal}将在 {days_remaining} 天后到期，请及时处理。</p>
                <p>到期日期：{end_date}</p>
            </body>
            </html>
            """


# 定时任务函数
def check_probation_reminders():
    """定时任务：检查试用期提醒"""
    return ProbationReminderService.check_and_send_reminders()
