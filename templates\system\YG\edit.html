<!DOCTYPE html>
<head>
    <title>用户编辑</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.id }}" name="id" lay-verify="title"
                               autocomplete="off" placeholder="用户编号" class="layui-input">
                    </div>
                </div>
                <!-- <div class="layui-form-item">
                    <label class="layui-form-label">账号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.username }}" name="username" lay-verify="title"
                               autocomplete="off" placeholder="登录账号" class="layui-input">
                    </div> 
                </div> -->
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.name }}" name="name" lay-verify="title"
                               autocomplete="off" placeholder="用户姓名"
                               class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">手机号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.phone }}" name="phone" lay-verify="title"
                               autocomplete="off" placeholder="用户手机号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">职务</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.position }}" name="position" lay-verify="title"
                               autocomplete="off" placeholder="用户职务" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="deptId" class="dtree" data-id="0"></ul>
                    </div>
                </div>

          
                <div class="layui-form-item">
                    <label class="layui-form-label">工号</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.employee_id }}" name="employee_id" lay-verify="title"
                               autocomplete="off" placeholder="员工工号" class="layui-input">
                    </div>
                </div>
             
                <div class="layui-form-item">
                    <label class="layui-form-label">入职日期</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.hire_date }}" name="hire_date" lay-verify="date" autocomplete="off"
                               placeholder="入职日期" class="layui-input" id="hire_date">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性别</label>
                    <div class="layui-input-block">
                        <select name="gender" lay-verify="required">
                            <option value="男" {% if user.gender == '男' %}selected{% endif %}>男</option>
                            <option value="女" {% if user.gender == '女' %}selected{% endif %}>女</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">出生日期</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.birth_date }}" name="birth_date" lay-verify="date" autocomplete="off"
                               placeholder="出生日期" class="layui-input" id="birth_date">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">年龄</label>
                    <div class="layui-input-block">
                        <input type="number" value="{{ user.age }}" name="age" lay-verify="number" autocomplete="off"
                               placeholder="年龄" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">身份证号码</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.id_card }}" name="id_card" lay-verify="identity" autocomplete="off"
                               placeholder="身份证号码" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否正式</label>
                    <div class="layui-input-block">
                        <select name="is_formal" lay-verify="required" lay-filter="is_formal_change">
                            <option value="正式" {% if user.is_formal in ['正式员工', '正式'] %}selected{% endif %}>正式员工</option>
                            <option value="试用" {% if user.is_formal in ['试用期', '试用'] %}selected{% endif %}>试用期</option>
                            <option value="实习" {% if user.is_formal == '实习期' %}selected{% endif %}>实习期</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" id="probation_duration_item" style="{% if user.is_formal in ['正式员工', '正式'] %}display: none;{% endif %}">
                    <label class="layui-form-label">试用期时长</label>
                    <div class="layui-input-block">
                        <select name="probation_duration" lay-verify="" lay-filter="probation_duration">
                            <option value="30" {% if user.probation_duration == 30 %}selected{% endif %}>30天 (1个月)</option>
                            <option value="60" {% if user.probation_duration == 60 %}selected{% endif %}>60天 (2个月)</option>
                            <option value="90" {% if user.probation_duration == 90 or not user.probation_duration %}selected{% endif %}>90天 (3个月)</option>
                            <option value="120" {% if user.probation_duration == 120 %}selected{% endif %}>120天 (4个月)</option>
                            <option value="180" {% if user.probation_duration == 180 %}selected{% endif %}>180天 (6个月)</option>
                            <option value="365" {% if user.probation_duration == 365 %}selected{% endif %}>365天 (12个月)</option>
                            <option value="custom" {% if user.probation_duration and user.probation_duration not in [30, 60, 90, 120, 180, 365] %}selected{% endif %}>自定义</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item" id="custom_duration_item" style="{% if not user.probation_duration or user.probation_duration in [30, 60, 90, 120, 180, 365] %}display: none;{% endif %}">
                    <label class="layui-form-label">自定义天数</label>
                    <div class="layui-input-block">
                        <input type="number" value="{% if user.probation_duration and user.probation_duration not in [30, 60, 90, 120, 180, 365] %}{{ user.probation_duration }}{% endif %}"
                               name="custom_probation_duration" min="1" max="1095"
                               placeholder="请输入天数 (1-1095天)" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">基本工资</label>
                    <div class="layui-input-block">
                        <input type="number" value="{{ user.base_salary }}" name="base_salary" lay-verify="number" autocomplete="off"
                               placeholder="基本工资" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">绩效工资</label>
                    <div class="layui-input-block">
                        <input type="number" value="{{ user.performance_salary }}" name="performance_salary" lay-verify="number"
                               autocomplete="off" placeholder="绩效工资" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">主管考核项</label>
                    <div class="layui-input-block">
                        <input type="text" value="{{ user.supervisor_assessment }}" name="supervisor_assessment" lay-verify="title"
                               autocomplete="off" placeholder="主管考核项" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">角色</label>
                    <div class="layui-input-block">
                        {% for role in roles %}
                            <input{% if role.id in checked_roles %}
                                checked
                            {% endif %}
                                value="{{ role.id }}" title="{{ role.name }}" type="checkbox"
                                name="roleIds" lay-skin="primary">
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit=""
                    lay-filter="user-update">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'jquery', 'dtree'], function () {
        let form = layui.form
        let $ = layui.jquery
        let dtree = layui.dtree

        dtree.renderSelect({
            elem: '#selectParent',
            url: '/system/dept/tree',
            method: 'get',
            selectInputName: {nodeId: 'deptId', context: 'deptName'},
            skin: 'layui',
            dataFormat: 'list',
            response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'},
            selectInitVal: "{{ user.dept_id }}"
        })

        // 监听员工类型变化
        form.on('select(is_formal_change)', function(data){
            let value = data.value;
            let durationItem = $('#probation_duration_item');
            let customItem = $('#custom_duration_item');

            if (value === '正式') {
                durationItem.hide();
                customItem.hide();
            } else {
                durationItem.show();
                // 根据类型设置默认值
                if (value === '试用') {
                    $('select[name="probation_duration"]').val('90');
                } else if (value === '实习') {
                    $('select[name="probation_duration"]').val('180');
                }
                form.render('select');
            }
        });

        // 监听试用期时长变化
        form.on('select(probation_duration)', function(data){
            let customItem = $('#custom_duration_item');
            if (data.value === 'custom') {
                customItem.show();
            } else {
                customItem.hide();
            }
        });

        form.on('submit(user-update)', function (data) {
            let roleIds = ''
            $('input[type=checkbox]:checked').each(function () {
                roleIds += $(this).val() + ','
            })
            roleIds = roleIds.substr(0, roleIds.length - 1)
            data.field.roleIds = roleIds

            // 处理试用期时长
            if (data.field.probation_duration === 'custom') {
                data.field.probation_duration = data.field.custom_probation_duration;
            }
            // 如果是正式员工，清空试用期时长
            if (data.field.is_formal === '正式') {
                data.field.probation_duration = null;
            }
            $.ajax({
                url: '/system/yg_info/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
                            parent.layui.table.reload('yg-table')
                        })
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
            return false
        })
    })
</script>
</body>
</html>