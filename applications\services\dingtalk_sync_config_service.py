"""
钉钉同步配置服务
提供钉钉同步配置的持久化存储和管理功能
"""

import datetime
import json
from typing import Dict, Any, Optional
from applications.models.admin_dict import DictType, DictData
from applications.extensions import db


class DingTalkSyncConfigService:
    """钉钉同步配置服务类"""
    
    TYPE_CODE = 'dingtalk_sync_config'
    
    # 默认配置
    DEFAULT_CONFIG = {
        'enabled': '1',
        'frequency_type': 'daily',
        'schedule_time': '02:00',
        'week_day': '6',
        'month_day': '1',
        'sync_days': '7',
        'sync_mode': 'incremental',
        'sync_status': 'NEW,RUNNING,COMPLETED'
    }
    
    @classmethod
    def init_config_data(cls):
        """初始化配置数据到字典表"""
        try:
            # 检查是否在应用上下文中
            from flask import has_app_context
            if not has_app_context():
                return True
            
            # 检查字典类型是否存在
            dict_type = DictType.query.filter_by(type_code=cls.TYPE_CODE).first()
            if not dict_type:
                # 创建字典类型
                dict_type = DictType(
                    type_name='钉钉同步配置',
                    type_code=cls.TYPE_CODE,
                    description='钉钉质量异常单自动同步相关配置',
                    enable=1
                )
                db.session.add(dict_type)
                db.session.flush()
            
            # 检查并创建配置项
            config_items = [
                {'key': 'enabled', 'label': '启用状态', 'value': '1', 'remark': '1-启用, 0-禁用'},
                {'key': 'frequency_type', 'label': '频率类型', 'value': 'daily', 'remark': 'hourly/daily/weekly/monthly'},
                {'key': 'schedule_time', 'label': '执行时间', 'value': '02:00', 'remark': 'HH:MM格式'},
                {'key': 'week_day', 'label': '每周执行日期', 'value': '6', 'remark': '0-6 (周一到周日)'},
                {'key': 'month_day', 'label': '每月执行日期', 'value': '1', 'remark': '1-31或last'},
                {'key': 'sync_days', 'label': '同步天数', 'value': '7', 'remark': '1-90天'},
                {'key': 'sync_mode', 'label': '同步模式', 'value': 'incremental', 'remark': 'incremental/full'},
                {'key': 'sync_status', 'label': '状态过滤', 'value': 'NEW,RUNNING,COMPLETED', 'remark': '逗号分隔的状态列表'}
            ]
            
            for item in config_items:
                existing = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=item['key']
                ).first()
                
                if not existing:
                    dict_data = DictData(
                        data_label=item['label'],
                        data_value=item['key'],
                        type_code=cls.TYPE_CODE,
                        is_default=0,
                        enable=1,
                        remark=f"{item['value']}|{item['remark']}"
                    )
                    db.session.add(dict_data)
            
            db.session.commit()
            return True
            
        except Exception as e:
            print(f"初始化钉钉同步配置失败: {str(e)}")
            db.session.rollback()
            return False
    
    @classmethod
    def get_config(cls, key=None):
        """获取配置值"""
        try:
            if key:
                # 获取单个配置
                dict_data = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=key,
                    enable=1
                ).first()
                
                if dict_data and dict_data.remark:
                    # 从remark中提取当前值（格式：当前值|说明）
                    current_value = dict_data.remark.split('|')[0]
                    return current_value
                else:
                    return cls.DEFAULT_CONFIG.get(key)
            else:
                # 获取所有配置
                dict_data_list = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    enable=1
                ).all()
                
                config = {}
                for data in dict_data_list:
                    if data.remark:
                        current_value = data.remark.split('|')[0]
                        config[data.data_value] = current_value
                
                # 补充默认值
                for key, default_value in cls.DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = default_value
                
                return config
                
        except Exception as e:
            print(f"获取钉钉同步配置失败: {str(e)}")
            return cls.DEFAULT_CONFIG.get(key) if key else cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def save_config(cls, config_data: Dict[str, Any]) -> bool:
        """保存配置到字典表"""
        try:
            # 确保配置数据已初始化
            cls.init_config_data()
            
            # 验证配置数据
            validated_config = cls._validate_config(config_data)
            
            # 更新配置到字典表
            for key, value in validated_config.items():
                dict_data = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=key
                ).first()
                
                if dict_data:
                    # 更新现有记录
                    remark_parts = dict_data.remark.split('|') if dict_data.remark else ['', '']
                    remark_parts[0] = str(value)  # 更新当前值
                    dict_data.remark = '|'.join(remark_parts)
                    dict_data.update_time = datetime.datetime.now()
                else:
                    # 创建新记录
                    dict_data = DictData(
                        data_label=cls._get_config_label(key),
                        data_value=key,
                        type_code=cls.TYPE_CODE,
                        is_default=0,
                        enable=1,
                        remark=f"{value}|{cls._get_config_remark(key)}"
                    )
                    db.session.add(dict_data)
            
            db.session.commit()
            print(f"钉钉同步配置保存成功: {validated_config}")
            return True
            
        except Exception as e:
            print(f"保存钉钉同步配置失败: {str(e)}")
            db.session.rollback()
            return False
    
    @classmethod
    def _validate_config(cls, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置数据"""
        validated = {}
        
        # 启用状态 (支持两种参数名)
        enabled = config_data.get('enabled') or config_data.get('enable_schedule', cls.DEFAULT_CONFIG['enabled'])
        validated['enabled'] = '1' if str(enabled).lower() in ['1', 'true', 'on'] else '0'
        
        # 频率类型
        frequency_type = config_data.get('frequency_type', cls.DEFAULT_CONFIG['frequency_type'])
        if frequency_type not in ['hourly', 'daily', 'weekly', 'monthly']:
            frequency_type = cls.DEFAULT_CONFIG['frequency_type']
        validated['frequency_type'] = frequency_type
        
        # 执行时间
        schedule_time = config_data.get('schedule_time', cls.DEFAULT_CONFIG['schedule_time'])
        try:
            hour, minute = map(int, schedule_time.split(':'))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                schedule_time = cls.DEFAULT_CONFIG['schedule_time']
        except (ValueError, AttributeError):
            schedule_time = cls.DEFAULT_CONFIG['schedule_time']
        validated['schedule_time'] = schedule_time
        
        # 每周执行日期
        week_day = config_data.get('week_day', cls.DEFAULT_CONFIG['week_day'])
        try:
            week_day_int = int(week_day)
            if not (0 <= week_day_int <= 6):
                week_day = cls.DEFAULT_CONFIG['week_day']
        except (ValueError, TypeError):
            week_day = cls.DEFAULT_CONFIG['week_day']
        validated['week_day'] = str(week_day)
        
        # 每月执行日期
        month_day = config_data.get('month_day', cls.DEFAULT_CONFIG['month_day'])
        if month_day != 'last':
            try:
                month_day_int = int(month_day)
                if not (1 <= month_day_int <= 31):
                    month_day = cls.DEFAULT_CONFIG['month_day']
            except (ValueError, TypeError):
                month_day = cls.DEFAULT_CONFIG['month_day']
        validated['month_day'] = str(month_day)
        
        # 同步天数 (支持两种参数名)
        sync_days = config_data.get('sync_days') or config_data.get('days', cls.DEFAULT_CONFIG['sync_days'])
        try:
            sync_days_int = int(sync_days)
            if not (1 <= sync_days_int <= 90):
                sync_days = cls.DEFAULT_CONFIG['sync_days']
        except (ValueError, TypeError):
            sync_days = cls.DEFAULT_CONFIG['sync_days']
        validated['sync_days'] = str(sync_days)
        
        # 同步模式
        sync_mode = config_data.get('sync_mode', cls.DEFAULT_CONFIG['sync_mode'])
        if sync_mode not in ['incremental', 'full']:
            sync_mode = cls.DEFAULT_CONFIG['sync_mode']
        validated['sync_mode'] = sync_mode

        # 状态过滤
        sync_status = config_data.get('sync_status', cls.DEFAULT_CONFIG['sync_status'])
        if isinstance(sync_status, list):
            # 如果是列表，转换为逗号分隔的字符串
            sync_status = ','.join(sync_status)
        elif not isinstance(sync_status, str):
            sync_status = cls.DEFAULT_CONFIG['sync_status']
        validated['sync_status'] = sync_status

        return validated
    
    @classmethod
    def _get_config_label(cls, key: str) -> str:
        """获取配置项的标签"""
        labels = {
            'enabled': '启用状态',
            'frequency_type': '频率类型',
            'schedule_time': '执行时间',
            'week_day': '每周执行日期',
            'month_day': '每月执行日期',
            'sync_days': '同步天数',
            'sync_mode': '同步模式',
            'sync_status': '状态过滤'
        }
        return labels.get(key, key)
    
    @classmethod
    def _get_config_remark(cls, key: str) -> str:
        """获取配置项的备注"""
        remarks = {
            'enabled': '1-启用, 0-禁用',
            'frequency_type': 'hourly/daily/weekly/monthly',
            'schedule_time': 'HH:MM格式',
            'week_day': '0-6 (周一到周日)',
            'month_day': '1-31或last',
            'sync_days': '1-90天',
            'sync_mode': 'incremental/full',
            'sync_status': '逗号分隔的状态列表'
        }
        return remarks.get(key, '')
    
    @classmethod
    def is_enabled(cls) -> bool:
        """检查同步功能是否启用"""
        enabled = cls.get_config('enabled')
        return str(enabled) == '1'
    
    @classmethod
    def get_sync_config_dict(cls) -> Dict[str, Any]:
        """获取完整的同步配置字典"""
        config = cls.get_config()

        # 处理状态过滤
        sync_status_str = config.get('sync_status', 'NEW,RUNNING,COMPLETED')
        sync_status_list = sync_status_str.split(',') if sync_status_str else ['NEW', 'RUNNING', 'COMPLETED']

        return {
            'enable_schedule': cls.is_enabled(),
            'frequency_type': config.get('frequency_type', 'daily'),
            'schedule_time': config.get('schedule_time', '02:00'),
            'week_day': int(config.get('week_day', 6)),
            'month_day': config.get('month_day', '1'),
            'sync_days': int(config.get('sync_days', 7)),
            'sync_mode': config.get('sync_mode', 'incremental'),
            'sync_status': sync_status_list
        }
