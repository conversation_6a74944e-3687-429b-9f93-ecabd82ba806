import datetime
from flask_login import UserMixin
from applications.extensions import db


class ygong(db.Model, UserMixin):
    __tablename__ = 'admin_yg'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='用户ID')
    openid = db.Column(db.String(255), comment='openid')
    name = db.Column(db.String(255), comment='姓名')
    employee_id = db.Column(db.String(255), comment='员工工号')
    phone = db.Column(db.String(11), comment='电话号码')
    dept_id = db.Column(db.Integer, db.<PERSON>Key('admin_dept.id'), comment='部门id')
    position = db.Column(db.String(255), comment='职位')
    hire_date = db.Column(db.Date, comment='入职日期')
    gender = db.Column(db.String(10), comment='性别')
    birth_date = db.Column(db.Date, comment='出生日期')
    age = db.Column(db.Integer, comment='年龄')
    id_card = db.Column(db.String(18), comment='身份证号码')
    is_formal = db.Column(db.String(20), comment='是否正式')
    base_salary = db.Column(db.Float, default=0, comment='基本工资')
    performance_salary = db.Column(db.Float, default=0, comment='绩效工资')
    supervisor_assessment = db.Column(db.Float, default=0, comment='主管考核项')
    # 试用期相关字段
    probation_duration = db.Column(db.Integer, comment='试用期时长(天)')
    probation_end_date = db.Column(db.Date, comment='试用期结束日期')
    last_reminder_date = db.Column(db.Date, comment='最后提醒日期')
    reminder_status = db.Column(db.String(20), default='normal', comment='提醒状态')
    # 转正相关字段
    formal_date = db.Column(db.Date, comment='转正日期')
    formal_note = db.Column(db.Text, comment='转正备注')
    enable = db.Column(db.Integer, default=0, comment='启用')
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='创建时间')
    role = db.relationship('Role', secondary="admin_yg_role", backref=db.backref('yg'), lazy='dynamic')

    # 添加与部门的关联
    dept = db.relationship('Dept', backref='employees', foreign_keys=[dept_id])

    # 添加唯一性约束，防止重复数据
    __table_args__ = (
        db.UniqueConstraint('employee_id', name='uix_employee_id'),  # 员工工号唯一
        db.UniqueConstraint('phone', name='uix_phone'),  # 手机号唯一
        db.UniqueConstraint('id_card', name='uix_id_card'),  # 身份证号唯一
    )

    def calculate_probation_end_date(self):
        """计算试用期结束日期"""
        if not self.hire_date:
            return None

        # 优先使用自定义的试用期时长
        if self.probation_duration and self.probation_duration > 0:
            return self.hire_date + datetime.timedelta(days=self.probation_duration)

        # 如果没有自定义时长，使用默认规则
        if self.is_formal in ['试用期', '试用']:
            # 试用期默认3个月（90天）
            return self.hire_date + datetime.timedelta(days=90)
        elif self.is_formal in ['实习期', '实习']:
            # 实习期默认6个月（180天）
            return self.hire_date + datetime.timedelta(days=180)
        else:
            return None

    def get_probation_status(self):
        """获取试用期状态"""
        # 正式员工的各种表示方式
        if self.is_formal in ['正式员工', '正式']:
            return 'formal'

        end_date = self.calculate_probation_end_date()
        if not end_date:
            # 如果没有试用期结束日期，但是试用或试用期员工，可能是数据不完整
            if self.is_formal in ['试用', '试用期', '实习期', '实习']:
                return 'unknown'  # 数据不完整
            else:
                # 其他状态也当作正式员工处理
                return 'formal'

        today = datetime.date.today()
        days_remaining = (end_date - today).days

        if days_remaining <= 0:
            return 'urgent'  # 已到期或逾期
        elif days_remaining <= 7:
            return 'warning'  # 7天内到期
        else:
            return 'normal'  # 正常状态


class ProbationReminderLog(db.Model):
    """试用期提醒日志表"""
    __tablename__ = 'probation_reminder_log'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='日志ID')
    employee_id = db.Column(db.Integer, db.ForeignKey('admin_yg.id'), nullable=False, comment='员工ID')
    employee_name = db.Column(db.String(255), comment='员工姓名')
    reminder_type = db.Column(db.String(20), nullable=False, comment='提醒类型')  # '7days', '3days', '1day', 'today', 'overdue'
    sent_date = db.Column(db.DateTime, nullable=False, default=datetime.datetime.now, comment='发送时间')
    recipients = db.Column(db.Text, comment='收件人列表')
    email_subject = db.Column(db.String(255), comment='邮件主题')
    status = db.Column(db.String(20), default='sent', comment='发送状态')  # 'sent', 'failed'
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')

    # 关联员工表
    employee = db.relationship('ygong', backref='reminder_logs', foreign_keys=[employee_id])






