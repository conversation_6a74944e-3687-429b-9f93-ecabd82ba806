<!DOCTYPE html>
<html>
<head>
    <title>导入项目激励</title>
    {% include 'system/common/header.html' %}
    <style>
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }

        .layui-card {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .upload-tips {
            background: #f8f9fa;
            border-left: 4px solid #009688;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .upload-tips h3 {
            margin: 0 0 10px 0;
            color: #009688;
            font-size: 16px;
        }

        .upload-tips p {
            margin: 5px 0;
            color: #666;
            line-height: 1.6;
        }

        .upload-form {
            padding: 20px 0;
        }

        .template-link {
            margin-left: 110px;
            /* margin: 20px 0; */
        }

        .batch-import-tips {
            margin-top: 10px;
            color: #999;
            font-size: 12px;
        }

        .file-name {
            color: #009688;
            font-weight: bold;
            margin-top: 10px;
        }

        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .layui-btn-normal {
            background: linear-gradient(135deg, #1E9FFF 0%, #1890ff 100%);
            border: none;
        }

        .layui-btn-normal:hover {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .layui-btn-primary {
            border: 1px solid #d9d9d9;
            color: #666;
        }

        .layui-btn-primary:hover {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }

        .layui-form-label {
            font-weight: 500;
            color: #333;
        }

        .layui-input, .layui-select, .layui-textarea {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-input:focus, .layui-select:focus, .layui-textarea:focus {
            border-color: #1E9FFF;
            box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
        }

        .layui-tab-title li {
            font-weight: 500;
        }

        .layui-tab-title .layui-this {
            color: #1E9FFF;
        }

        .layui-upload-drag {
            border-radius: 8px;
            border: 2px dashed #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-upload-drag:hover {
            border-color: #1E9FFF;
            background-color: #f0f8ff;
        }

        .layui-upload-drag .layui-icon {
            color: #1E9FFF;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">导入项目激励</div>
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="importTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">手动导入</li>
                    <li>批量导入</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 手动导入选项卡 -->
                    <div class="layui-tab-item layui-show">
                        <div class="upload-tips">
                            <h3>导入说明：</h3>
                            <p>1. 支持单个员工项目激励的导入</p>
                            <p>2. 导入后会自动记录激励详情</p>
                            <p>3. 同一项目同一员工在同一月份多次导入会分别记录</p>
                            <p>4. 项目激励用于记录员工在特定项目中的奖励和激励</p>
                        </div>

                        <div class="upload-form">
                            <form class="layui-form" lay-filter="import-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">选择项目</label>
                                    <div class="layui-input-block">
                                        <select name="projectId" lay-verify="required" lay-filter="projectSelect">
                                            <option value="">请选择项目</option>
                                            {% for project in projects %}
                                            <option value="{{ project.id }}">{{ project.project_code }} - {{ project.project_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属年月</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="yearMonth" id="yearMonth" lay-verify="required" placeholder="请选择年月" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">员工姓名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="employeeName" lay-verify="required" placeholder="请输入员工姓名" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">激励金额</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="amount" lay-verify="required|number" placeholder="请输入激励金额" autocomplete="off" class="layui-input" step="0.01" min="0">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">激励类型</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="incentiveType" value="项目激励" placeholder="激励类型" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">上传文件</label>
                                    <div class="layui-input-block">
                                        <div class="layui-upload-drag" id="fileUpload">
                                            <i class="layui-icon layui-icon-upload"></i>
                                            <p>点击上传，或将文件拖拽到此处（可选）</p>
                                            <div class="layui-hide" id="uploadPreview">
                                                <hr>
                                                <img src="" alt="上传成功后渲染" style="max-width: 100%">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注说明</label>
                                    <div class="layui-input-block">
                                        <textarea name="remark" placeholder="请输入备注说明" class="layui-textarea"></textarea>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn" lay-submit lay-filter="submit-import">立即导入</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 批量导入选项卡 -->
                    <div class="layui-tab-item">
                        <div class="upload-tips">
                            <h3>批量导入说明：</h3>
                            <p>1. 支持从Excel文件批量导入多个员工的项目激励</p>
                            <p>2. 请确保Excel文件包含以下列：月份、项目类型、项目编号、姓名、金额</p>
                            <p>3. 导入后会自动记录激励详情</p>
                            <p>4. 同一项目同一员工在同一月份多次导入会分别记录</p>
                            <p>5. Excel模板格式：月份(YYYY-MM)、项目类型、项目编号、姓名、金额</p>
                        </div>

                        <div class="template-link">
                            <a href="/system/project_cost/api/import/template/download?type=incentive" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-template"></i> 下载导入模板
                            </a>
                        </div>

                        <div class="upload-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择文件</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload-drag" id="batchFileUpload">
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传Excel文件，或将文件拖拽到此处</p>
                                        <div class="layui-hide" id="batchUploadPreview">
                                            <hr>
                                            <img src="" alt="上传成功后渲染" style="max-width: 100%">
                                        </div>
                                    </div>
                                    <div class="batch-import-tips">
                                        <p>支持的文件格式：.xlsx, .xls</p>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="batchImportBtn">开始批量导入</button>
                                </div>
                            </div>
                        </div>

                        <!-- 导入结果区域 -->
                        <div id="importResult" style="display: none; margin-top: 20px;">
                            <div class="layui-card">
                                <div class="layui-card-header">导入结果</div>
                                <div class="layui-card-body">
                                    <div id="resultSummary"></div>
                                    <div id="resultDetails" style="margin-top: 15px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% include 'system/common/footer.html' %}

    <script>
        layui.use(['form', 'upload', 'laydate', 'element', 'jquery'], function() {
            let form = layui.form;
            let upload = layui.upload;
            let laydate = layui.laydate;
            let element = layui.element;
            let $ = layui.jquery;
            let uploadFile = null;
            let batchUploadFile = null;

            // 初始化年月选择器
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                format: 'yyyy-MM'
            });

            // 单个文件上传
            upload.render({
                elem: '#fileUpload',
                url: '/system/project_cost/api/upload',
                accept: 'file',
                exts: 'xlsx|xls|csv|pdf|doc|docx',
                auto: false, // 不自动上传
                choose: function(obj) {
                    // 将每次选择的文件追加到文件队列
                    obj.preview(function(index, file, result) {
                        uploadFile = file;

                        // 显示文件名
                        $('#uploadPreview').removeClass('layui-hide').find('img').attr('src', '/static/system/admin/images/file.png');
                        // 移除旧的文件名显示
                        $('#uploadPreview').siblings('.file-name').remove();
                        // 添加新的文件名
                        $('#uploadPreview').after('<p class="file-name">' + file.name + '</p>');

                        layer.msg('文件已选择，点击"立即导入"按钮完成导入', {icon: 1});
                    });
                }
            });

            // 批量文件上传
            upload.render({
                elem: '#batchFileUpload',
                accept: 'file',
                exts: 'xlsx|xls',
                auto: false, // 不自动上传
                choose: function(obj) {
                    // 将每次选择的文件追加到文件队列
                    obj.preview(function(index, file, result) {
                        batchUploadFile = file;

                        // 显示文件名
                        $('#batchUploadPreview').removeClass('layui-hide').find('img').attr('src', '/static/system/admin/images/file.png');
                        // 移除旧的文件名显示
                        $('#batchUploadPreview').siblings('.file-name').remove();
                        // 添加新的文件名
                        $('#batchUploadPreview').after('<p class="file-name">' + file.name + '</p>');

                        layer.msg('Excel文件已选择，点击"开始批量导入"按钮完成导入', {icon: 1});
                    });
                }
            });

            // 单个表单提交
            form.on('submit(submit-import)', function(data) {
                // 构建FormData
                let formData = new FormData();
                formData.append('project_id', data.field.projectId);
                formData.append('year_month', data.field.yearMonth);
                formData.append('employee_name', data.field.employeeName);
                formData.append('amount', data.field.amount);
                formData.append('incentive_type', data.field.incentiveType);
                formData.append('remark', data.field.remark);

                // 如果选择了文件，则添加到表单数据
                if(uploadFile) {
                    formData.append('file', uploadFile);
                }

                // 显示加载中
                let loadIndex = layer.load(2);

                // 发起请求
                $.ajax({
                    url: '/system/project_cost/api/import/incentive',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(res) {
                        layer.close(loadIndex);

                        if (res.success) {
                            layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                                // 关闭当前窗口并刷新父窗口表格
                                const index = parent.layer.getFrameIndex(window.name);
                                parent.layui.table.reload('project-table');
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('导入失败，请重试', {icon: 2});
                    }
                });

                return false;
            });

            // 批量导入按钮点击事件
            $('#batchImportBtn').on('click', function() {
                if (!batchUploadFile) {
                    layer.msg('请先选择Excel文件', {icon: 2});
                    return;
                }

                // 构建FormData
                let formData = new FormData();
                formData.append('file', batchUploadFile);

                // 显示加载中
                let loadIndex = layer.load(2);

                // 发起请求
                $.ajax({
                    url: '/system/project_cost/api/import/incentive/excel',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(res) {
                        layer.close(loadIndex);

                        // 显示导入结果区域
                        $('#importResult').show();

                        if (res.code === 200) {
                            // 显示导入结果摘要
                            let summaryHtml = `
                                <div class="layui-row">
                                    <div class="layui-col-md4">
                                        <div style="text-align: center; padding: 10px; background: #f0f9ff; border-radius: 4px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #1890ff;">${res.data.total}</div>
                                            <div style="color: #666;">总记录数</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <div style="text-align: center; padding: 10px; background: #f6ffed; border-radius: 4px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">${res.data.success}</div>
                                            <div style="color: #666;">成功导入</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <div style="text-align: center; padding: 10px; background: #fff2f0; border-radius: 4px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #ff4d4f;">${res.data.error}</div>
                                            <div style="color: #666;">导入失败</div>
                                        </div>
                                    </div>
                                </div>
                            `;
                            $('#resultSummary').html(summaryHtml);

                            // 如果有错误详情，显示错误列表
                            if (res.data.error > 0 && res.data.error_details) {
                                let detailsHtml = '<h4>错误详情：</h4><ul>';
                                res.data.error_details.forEach(function(error) {
                                    detailsHtml += '<li style="color: #ff4d4f; margin: 5px 0;">' + error + '</li>';
                                });
                                detailsHtml += '</ul>';
                                $('#resultDetails').html(detailsHtml);
                            } else {
                                $('#resultDetails').html('<div style="color: #52c41a; font-weight: bold;">所有记录导入成功！</div>');
                            }

                            // 如果有成功导入的记录，提示刷新页面
                            if (res.data.success > 0) {
                                layer.msg('导入完成，请刷新页面查看结果', {icon: 1, time: 3000});
                            }
                        } else {
                            $('#resultSummary').html('<div style="color: #ff4d4f;">导入失败：' + res.msg + '</div>');
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('导入失败，请重试', {icon: 2});
                    }
                });
            });
        });
    </script>
</body>
</html>
