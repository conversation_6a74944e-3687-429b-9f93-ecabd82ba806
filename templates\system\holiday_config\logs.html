<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节假日配置操作日志</title>
    {% include 'system/common/header.html' %}
    <style>
        .data-changes {
            max-height: 300px;
            overflow-y: auto;
        }
        .data-changes table {
            margin: 0;
        }
        .data-changes h5 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .operation-type-create { color: #5FB878; }
        .operation-type-update { color: #FFB800; }
        .operation-type-delete { color: #FF5722; }
        .operation-type-import { color: #1E9FFF; }

        /* 优化表单布局 */
        .layui-form-item .layui-inline {
            margin-right: 10px;
        }
        .layui-form-item .layui-input-inline {
            float: left;
            width: 180px;
            margin-right: 10px;
        }
        .layui-form-label {
            width: 60px;
            padding: 9px 10px;
        }
        .layui-input-inline {
            width: 120px;
        }
        /* 操作人输入框稍宽一些 */
        .layui-inline:nth-child(2) .layui-input-inline {
            width: 140px;
        }
        /* 时间输入框稍宽一些 */
        .layui-inline:nth-child(3) .layui-input-inline,
        .layui-inline:nth-child(4) .layui-input-inline {
            width: 150px;
        }
    </style>
</head>
<body class="pear-container">

<!-- 搜索表单 -->
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="log-query">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">操作类型</label>
                    <div class="layui-input-inline">
                        <select name="operation_type">
                            <option value="">全部</option>
                            <option value="create">创建</option>
                            <option value="update">更新</option>
                            <option value="delete">删除</option>
                            <option value="import">导入</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">操作人</label>
                    <div class="layui-input-inline">
                        <input type="text" name="operator_name" placeholder="请输入操作人姓名" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">操作时间</label>
                    <div class="layui-input-inline" >
                        <input type="text" name="start_date" placeholder="开始日期" class="layui-input" id="start-date">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="text-align: center;">至</label>
                    <div class="layui-input-inline">
                        <input type="text" name="end_date" placeholder="结束日期" class="layui-input" id="end-date">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-md" lay-submit lay-filter="log-query">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                        <i class="layui-icon layui-icon-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 数据表格 -->
<div class="layui-card">
    <div class="layui-card-body">
        <table id="log-table" lay-filter="log-table"></table>
    </div>
</div>

<!-- 操作类型模板 -->
<script type="text/html" id="operation-type-tpl">
    {%raw%}{{# if(d.operation_type == 'create'){ }}{%endraw%}
    <span class="layui-badge layui-bg-green operation-type-create">{%raw%}{{d.operation_type_text}}{%endraw%}</span>
    {%raw%}{{# } else if(d.operation_type == 'update'){ }}{%endraw%}
    <span class="layui-badge layui-bg-orange operation-type-update">{%raw%}{{d.operation_type_text}}{%endraw%}</span>
    {%raw%}{{# } else if(d.operation_type == 'delete'){ }}{%endraw%}
    <span class="layui-badge layui-bg-red operation-type-delete">{%raw%}{{d.operation_type_text}}{%endraw%}</span>
    {%raw%}{{# } else if(d.operation_type == 'import'){ }}{%endraw%}
    <span class="layui-badge layui-bg-blue operation-type-import">{%raw%}{{d.operation_type_text}}{%endraw%}</span>
    {%raw%}{{# } else { }}{%endraw%}
    <span class="layui-badge layui-bg-gray">{%raw%}{{d.operation_type_text}}{%endraw%}</span>
    {%raw%}{{# } }}{%endraw%}
</script>

<!-- 操作栏模板 -->
<script type="text/html" id="log-bar">
    <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="detail">
        <i class="layui-icon layui-icon-about"></i> 详情
    </button>
</script>

</body>

{% include 'system/common/footer.html' %}

<script>
layui.use(['table', 'form', 'layer', 'laydate', 'jquery'], function () {
    let table = layui.table;
    let form = layui.form;
    let layer = layui.layer;
    let laydate = layui.laydate;
    let $ = layui.jquery;
    
    const MODULE_PATH = "/system/holiday_config/";
    
    // 日期选择器
    laydate.render({
        elem: '#start-date',
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss'
    });
    
    laydate.render({
        elem: '#end-date',
        type: 'datetime',
        format: 'yyyy-MM-dd HH:mm:ss'
    });
    
    // 表格配置
    let cols = [
        [
            {field: 'id', width: 80, title: 'ID', sort: true},
            {field: 'operation_type', width: 100, title: '操作类型', templet: '#operation-type-tpl'},
            {field: 'holiday_date', width: 120, title: '节假日日期', sort: true},
            {field: 'operator_name', width: 120, title: '操作人'},
            {field: 'operation_time', width: 160, title: '操作时间', sort: true},
            {field: 'ip_address', width: 120, title: 'IP地址'},
            {field: 'remark', title: '备注'},
            {title: '操作', toolbar: '#log-bar', align: 'center', width: 100}
        ]
    ];
    
    // 渲染表格
    table.render({
        elem: '#log-table',
        url: MODULE_PATH + 'logs_data',
        page: true,
        cols: cols,
        skin: 'line',
        defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'exports']
    });
    
    // 行工具事件
    table.on('tool(log-table)', function (obj) {
        let data = obj.data;
        if (obj.event === 'detail') {
            // 显示详细信息
            layer.open({
                type: 1,
                title: '操作详情',
                area: ['80%', '90%'],
                content: `
                    <div style="padding: 20px;">
                        <div class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">操作类型：</label>
                                <div class="layui-input-block">
                                    <span class="operation-type-${data.operation_type}">${data.operation_type_text}</span>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">节假日日期：</label>
                                <div class="layui-input-block">${data.holiday_date || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">操作人：</label>
                                <div class="layui-input-block">${data.operator_name}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">操作时间：</label>
                                <div class="layui-input-block">${data.operation_time}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">IP地址：</label>
                                <div class="layui-input-block">${data.ip_address || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">用户代理：</label>
                                <div class="layui-input-block" style="word-break: break-all;">${data.user_agent || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">备注：</label>
                                <div class="layui-input-block">${data.remark || '-'}</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;">数据变更：</label>
                                <div class="layui-input-block">${data.changes_html || '无数据变更'}</div>
                            </div>
                        </div>
                    </div>
                `
            });
        }
    });
    
    // 搜索表单提交
    form.on('submit(log-query)', function (data) {
        table.reload('log-table', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
});
</script>
</html>
