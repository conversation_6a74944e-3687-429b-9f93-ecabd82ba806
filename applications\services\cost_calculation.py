import datetime
import json
import time
import logging
from sqlalchemy import extract
from applications.extensions.init_sqlalchemy import db
from applications.models.project_cost import ProjectActualCost, BOMCostImport, OtherCostImport, ProjectLaborCost, ProjectIncentive
from applications.models.import_project import Import_project
from applications.models.save_log import LogInfo
from applications.models.employee_salary import EmployeeSalary
from applications.models.admin_dict import DictType, DictData
from applications.models.admin_yg import ygong
from flask import current_app
from sqlalchemy import func, distinct, and_, or_
import pandas as pd
import calendar
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from applications.models.project_manage_dept import ProjectManageDept
import holidays
from sqlalchemy import text


def get_next_month(year, month):
    """获取下一个月的年月"""
    if month == 12:
        return year + 1, 1
    else:
        return year, month + 1


def get_previous_month(year, month):
    """获取上一个月的年月"""
    if month == 1:
        return year - 1, 12
    else:
        return year, month - 1


def format_year_month(year, month):
    """格式化年月为YYYY-MM格式"""
    return f"{year}-{month:02d}"


def get_next_month_string(year, month):
    """获取下一个月的年月字符串"""
    next_year, next_month = get_next_month(year, month)
    return format_year_month(next_year, next_month)


def get_previous_month_string(year, month):
    """获取上一个月的年月字符串"""
    prev_year, prev_month = get_previous_month(year, month)
    return format_year_month(prev_year, prev_month)


def format_year_month_string(year_month_str):
    """
    格式化年月字符串为统一的YYYY-MM格式

    Args:
        year_month_str: 年月字符串，可能的格式：YYYY-M, YYYY-MM, YYYY/M, YYYY/MM等

    Returns:
        str: 统一格式的YYYY-MM字符串
    """
    if not year_month_str:
        return year_month_str

    try:
        # 处理不同的分隔符
        if '-' in year_month_str:
            parts = year_month_str.split('-')
        elif '/' in year_month_str:
            parts = year_month_str.split('/')
        else:
            # 如果没有分隔符，假设是YYYYMM格式
            if len(year_month_str) == 6 and year_month_str.isdigit():
                year = year_month_str[:4]
                month = year_month_str[4:6]
                return f"{year}-{month.zfill(2)}"
            else:
                return year_month_str

        if len(parts) >= 2:
            year = parts[0]
            month = parts[1]
            # 确保月份是两位数
            return f"{year}-{month.zfill(2)}"
        else:
            return year_month_str

    except Exception:
        # 如果格式化失败，返回原字符串
        return year_month_str


def calculate_labor_cost_by_timesheet(year, month, hourly_rate=None):
    """
    根据工时日志计算项目人工成本

    Args:
        year: 年份
        month: 月份
        hourly_rate: 每小时工时成本，如果为None则使用默认值

    Returns:
        成功计算的项目数量
    """
    if hourly_rate is None:
        # 从配置或者其他地方获取默认工时成本
        hourly_rate = 100  # 示例值，实际应该从配置获取

    # 构建月份字符串和日期范围
    work_month = format_year_month(year, month)
    start_date = datetime.date(year, month, 1)
    next_year, next_month = get_next_month(year, month)
    end_date = datetime.date(next_year, next_month, 1)

    # 计算工资发放月份（下个月）
    salary_month = get_next_month_string(year, month)

    # 获取指定月份的所有日志
    logs = LogInfo.query.filter(
        LogInfo.work_date >= start_date,
        LogInfo.work_date < end_date
    ).all()

    # 按项目分组汇总工时
    project_data = {}
    for log in logs:
        project_key = f"{log.projectPrefix}-{log.projectNumber}"
        if project_key not in project_data:
            project_data[project_key] = {
                'total_hours': 0,
                'employee_hours': {},
                'cost': 0
            }

        # 累加工时
        project_data[project_key]['total_hours'] += log.totalHours

        # 按员工累计工时
        if log.employee_id not in project_data[project_key]['employee_hours']:
            project_data[project_key]['employee_hours'][log.employee_id] = 0
        project_data[project_key]['employee_hours'][log.employee_id] += log.totalHours

    # 计算每个项目的人工成本
    success_count = 0
    for project_key, data in project_data.items():
        # 查找对应的项目ID
        project_code = project_key.split('-')[1]  # 假设projectNumber对应项目编码
        project = Import_project.query.filter_by(project_code=project_code).first()
        if not project:
            continue

        # 计算成本
        cost = data['total_hours'] * hourly_rate
        data['cost'] = cost

        # 存储或更新成本数据
        labor_cost = ProjectLaborCost.query.filter_by(
            project_id=project.id,
            work_month=work_month,
            calculation_method='timesheet'
        ).first()

        if labor_cost:
            labor_cost.total_hours = data['total_hours']
            labor_cost.total_cost = cost
            labor_cost.salary_month = salary_month
            labor_cost.cost_details = json.dumps(data['employee_hours'])
            labor_cost.calculated_at = datetime.datetime.now()
        else:
            labor_cost = ProjectLaborCost(
                project_id=project.id,
                work_month=work_month,
                salary_month=salary_month,
                total_hours=data['total_hours'],
                total_cost=cost,
                cost_details=json.dumps(data['employee_hours']),
                calculation_method='timesheet'
            )
            db.session.add(labor_cost)

        # 更新项目月度总成本
        update_project_monthly_cost(project.id, work_month, labor_cost=cost)

        success_count += 1

    db.session.commit()
    return success_count


def calculate_labor_cost_by_salary(year, month):
    """
    根据实际薪资分摊计算项目人工成本

    Args:
        year: 年份
        month: 月份

    Returns:
        成功计算的项目数量
    """
    # 构建月份字符串
    salary_month = format_year_month(year, month)  # 当前发放工资的月份

    # 计算对应的工作月份（上个月）
    prev_year, prev_month = get_previous_month(year, month)
    work_month = format_year_month(prev_year, prev_month)

    # 确定工作月份的日期范围
    start_date = datetime.date(prev_year, prev_month, 1)
    end_date = datetime.date(year, month, 1)  # 当前月的第一天

    # 获取当月发放工资记录
    salaries = EmployeeSalary.query.filter_by(month=salary_month).all()

    # 获取上月工时记录
    logs = LogInfo.query.filter(
        LogInfo.work_date >= start_date,
        LogInfo.work_date < end_date
    ).all()

    # 按员工整理工时分布
    employee_timesheet = {}
    for log in logs:
        if log.employee_id not in employee_timesheet:
            employee_timesheet[log.employee_id] = {
                'total_hours': 0,
                'projects': {}
            }

        project_key = f"{log.projectPrefix}-{log.projectNumber}"
        if project_key not in employee_timesheet[log.employee_id]['projects']:
            employee_timesheet[log.employee_id]['projects'][project_key] = 0

        employee_timesheet[log.employee_id]['total_hours'] += log.totalHours
        employee_timesheet[log.employee_id]['projects'][project_key] += log.totalHours

    # 按项目整理成本
    project_costs = {}

    # 为每个员工的工资按项目分摊
    for salary in salaries:
        employee_id = str(salary.employee_id)  # 确保ID是字符串格式以匹配日志中的employee_id

        # 检查员工是否有工时记录
        if employee_id not in employee_timesheet or employee_timesheet[employee_id]['total_hours'] == 0:
            continue

        # 获取员工总工资（可以根据实际需求调整工资构成）
        total_salary = salary.should_pay  # 或使用actual_salary，取决于业务需求

        # 按工时比例分摊工资到各项目
        for project_key, project_hours in employee_timesheet[employee_id]['projects'].items():
            # 计算工时比例
            hours_ratio = project_hours / employee_timesheet[employee_id]['total_hours']

            # 分摊工资
            project_salary = total_salary * hours_ratio

            # 累加到项目成本
            if project_key not in project_costs:
                project_costs[project_key] = {
                    'total_hours': 0,
                    'total_cost': 0,
                    'employee_details': {}
                }

            project_costs[project_key]['total_hours'] += project_hours
            project_costs[project_key]['total_cost'] += project_salary

            # 记录员工详情
            if employee_id not in project_costs[project_key]['employee_details']:
                project_costs[project_key]['employee_details'][employee_id] = {
                    'hours': 0,
                    'cost': 0
                }

            project_costs[project_key]['employee_details'][employee_id]['hours'] += project_hours
            project_costs[project_key]['employee_details'][employee_id]['cost'] += project_salary

    # 保存项目成本数据
    success_count = 0
    for project_key, data in project_costs.items():
        # 查找对应的项目ID
        project_code = project_key.split('-')[1]
        project = Import_project.query.filter_by(project_code=project_code).first()
        if not project:
            continue

        # 存储或更新成本数据
        labor_cost = ProjectLaborCost.query.filter_by(
            project_id=project.id,
            work_month=work_month,
            calculation_method='salary'
        ).first()

        if labor_cost:
            labor_cost.total_hours = data['total_hours']
            labor_cost.total_cost = data['total_cost']
            labor_cost.salary_month = salary_month
            labor_cost.cost_details = json.dumps(data['employee_details'])
            labor_cost.calculated_at = datetime.datetime.now()
        else:
            labor_cost = ProjectLaborCost(
                project_id=project.id,
                work_month=work_month,
                salary_month=salary_month,
                total_hours=data['total_hours'],
                total_cost=data['total_cost'],
                cost_details=json.dumps(data['employee_details']),
                calculation_method='salary'
            )
            db.session.add(labor_cost)

        # 更新项目月度总成本
        update_project_monthly_cost(project.id, work_month, labor_cost=data['total_cost'])

        success_count += 1

    db.session.commit()
    return success_count


def import_bom_cost(project_id, year_month, amount, import_file=None, import_by=None):
    """
    导入BOM成本

    Args:
        project_id: 项目ID
        year_month: 年月字符串，格式：YYYY-MM
        amount: 成本金额
        import_file: 导入文件名
        import_by: 导入人ID

    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 记录当前的BOM成本
        cost = ProjectActualCost.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).with_for_update().first()  # 使用with_for_update()自动添加行锁

        current_bom_cost = 0
        if cost:
            current_bom_cost = cost.bom_cost

        # 保存导入记录
        bom_import = BOMCostImport(
            project_id=project_id,
            year_month=year_month,
            amount=amount,
            import_file=import_file,
            import_by=import_by,
            created_at=datetime.datetime.now()
        )
        db.session.add(bom_import)
        db.session.flush()  # 确保数据写入但不提交事务

        # 更新项目月度成本
        try:
            # 如果没有找到记录，创建一个新记录
            if not cost:
                cost = ProjectActualCost(
                    project_id=project_id,
                    year_month=year_month,
                    total_cost=amount,  # 初始总成本等于BOM成本
                    bom_cost=amount,    # 设置初始BOM成本
                    labor_cost=0,
                    other_cost=0,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now()
                )
                db.session.add(cost)
            else:
                # 已有记录，更新BOM成本和总成本
                cost.bom_cost += amount
                cost.total_cost = cost.bom_cost + cost.labor_cost + cost.other_cost
                cost.updated_at = datetime.datetime.now()

            # 记录变更，用于调试
            print(f"Project {project_id}, {year_month} cost update:")
            print(f"  BOM cost: {current_bom_cost} + {amount} = {cost.bom_cost}")
            print(f"  Total cost: {cost.total_cost}")
        except Exception as e:
            print(f"更新项目月度成本失败: {str(e)}")
            db.session.rollback()
            raise

        # 查询所有的历史导入记录，检查是否存在累计问题
        all_bom_imports = BOMCostImport.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).all()

        expected_bom_cost = sum(imp.amount for imp in all_bom_imports)

        # 验证累加是否正确
        if cost and abs(expected_bom_cost - cost.bom_cost) > 0.01:  # 考虑浮点数精度
            # 如果累加不正确，进行修正
            original_bom_cost = cost.bom_cost
            cost.bom_cost = expected_bom_cost
            cost.total_cost = expected_bom_cost + cost.labor_cost + cost.other_cost
            cost.updated_at = datetime.datetime.now()
            print(f"已修正BOM成本累计错误: {original_bom_cost} -> {expected_bom_cost}")

        # 提交所有操作
        db.session.commit()

        # 返回成功消息
        return True, f"成功导入BOM成本: ¥{amount:.2f}，当前BOM成本总额: ¥{cost.bom_cost:.2f}"
    except Exception as e:
        db.session.rollback()
        return False, f"导入失败: {str(e)}"


def import_other_cost(project_id, dept_id, year_month, cost_type, amount, import_file=None, import_by=None):
    """
    导入其他成本

    Args:
        project_id: 项目ID
        dept_id: 部门ID
        year_month: 年月字符串，格式：YYYY-MM
        cost_type: 成本类型
        amount: 成本金额
        import_file: 导入文件名
        import_by: 导入人ID

    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 记录当前的其他成本
        cost = ProjectActualCost.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).with_for_update().first()  # 使用with_for_update()自动添加行锁

        current_other_cost = 0
        if cost:
            current_other_cost = cost.other_cost

        # 保存导入记录
        other_import = OtherCostImport(
            project_id=project_id,
            dept_id=dept_id,
            year_month=year_month,
            cost_type=cost_type,
            amount=amount,
            import_file=import_file,
            import_by=import_by,
            created_at=datetime.datetime.now()
        )
        db.session.add(other_import)
        db.session.flush()  # 确保数据写入但不提交事务

        # 更新项目月度成本
        try:
            # 如果没有找到记录，创建一个新记录
            if not cost:
                # 根据成本类型决定初始值
                if cost_type == 'outsource':
                    cost = ProjectActualCost(
                        project_id=project_id,
                        year_month=year_month,
                        total_cost=amount,
                        bom_cost=0,
                        labor_cost=0,
                        other_cost=0,
                        outsource_cost=amount,  # 设置初始外协费
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now()
                    )
                else:
                    cost = ProjectActualCost(
                        project_id=project_id,
                        year_month=year_month,
                        total_cost=amount,
                        bom_cost=0,
                        labor_cost=0,
                        other_cost=amount,   # 设置初始其他成本
                        outsource_cost=0,
                        created_at=datetime.datetime.now(),
                        updated_at=datetime.datetime.now()
                    )
                db.session.add(cost)
            else:
                # 已有记录，根据成本类型更新对应字段
                if cost_type == 'outsource':
                    cost.outsource_cost += amount
                else:
                    cost.other_cost += amount
                cost.total_cost = cost.bom_cost + cost.labor_cost + cost.other_cost + cost.outsource_cost
                cost.updated_at = datetime.datetime.now()

            # 记录变更，用于调试
            print(f"Project {project_id}, {year_month} cost update:")
            if cost_type == 'outsource':
                print(f"  Outsource cost: {current_other_cost} + {amount} = {cost.outsource_cost}")
            else:
                print(f"  Other cost: {current_other_cost} + {amount} = {cost.other_cost}")
            print(f"  Total cost: {cost.total_cost}")
        except Exception as e:
            print(f"更新项目月度成本失败: {str(e)}")
            db.session.rollback()
            raise

        # 查询所有的历史导入记录，检查是否存在累计问题
        all_other_imports = OtherCostImport.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).all()

        # 分别计算外协费和其他成本的期望值
        expected_outsource_cost = sum(imp.amount for imp in all_other_imports if imp.cost_type == 'outsource')
        expected_other_cost = sum(imp.amount for imp in all_other_imports if imp.cost_type != 'outsource')

        # 验证累加是否正确
        if cost:
            # 检查外协费累计
            if abs(expected_outsource_cost - cost.outsource_cost) > 0.01:
                original_outsource_cost = cost.outsource_cost
                cost.outsource_cost = expected_outsource_cost
                print(f"已修正外协费累计错误: {original_outsource_cost} -> {expected_outsource_cost}")

            # 检查其他成本累计
            if abs(expected_other_cost - cost.other_cost) > 0.01:
                original_other_cost = cost.other_cost
                cost.other_cost = expected_other_cost
                print(f"已修正其他成本累计错误: {original_other_cost} -> {expected_other_cost}")

            # 重新计算总成本
            cost.total_cost = cost.bom_cost + cost.labor_cost + cost.other_cost + cost.outsource_cost
            cost.updated_at = datetime.datetime.now()

        # 提交所有操作
        db.session.commit()

        # 返回成功消息
        if cost_type == 'outsource':
            return True, f"成功导入外协费: ¥{amount:.2f}，当前外协费总额: ¥{cost.outsource_cost:.2f}"
        else:
            return True, f"成功导入{cost_type}成本: ¥{amount:.2f}，当前其他成本总额: ¥{cost.other_cost:.2f}"
    except Exception as e:
        db.session.rollback()
        return False, f"导入失败: {str(e)}"


def update_project_monthly_cost(project_id, year_month, bom_cost=None, labor_cost=None, other_cost=None, outsource_cost=None):
    """
    更新项目月度总成本

    Args:
        project_id: 项目ID
        year_month: 年月字符串，格式：YYYY-MM
        bom_cost: BOM成本，为None表示不更新此项
        labor_cost: 人工成本，为None表示不更新此项
        other_cost: 其他成本，为None表示不更新此项
        outsource_cost: 外协费，为None表示不更新此项

    Returns:
        更新后的总成本
    """
    try:
        # 直接使用SQLAlchemy的会话锁定方式
        # 查找或创建月度成本记录
        cost = ProjectActualCost.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).with_for_update().first()  # 使用with_for_update()自动添加行锁

        if not cost:
            cost = ProjectActualCost(
                project_id=project_id,
                year_month=year_month,
                total_cost=0,
                bom_cost=0,
                labor_cost=0,
                other_cost=0,
                outsource_cost=0,
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            db.session.add(cost)
            db.session.flush()  # 确保ID分配

        # 在修改前记录当前值，用于日志
        old_bom_cost = cost.bom_cost
        old_labor_cost = cost.labor_cost
        old_other_cost = cost.other_cost
        old_outsource_cost = cost.outsource_cost

        # 更新各项成本
        if bom_cost is not None:
            # BOM成本也需要累加，符合"同一项目在同一月份多次导入会累加成本金额"的需求
            cost.bom_cost += bom_cost

        if labor_cost is not None:
            # 人工成本每次导入是一个完整的新金额，所以直接覆盖
            cost.labor_cost = labor_cost

        if other_cost is not None:
            # 其他成本需要累加，符合"同一项目在同一月份多次导入会累加成本金额"的需求
            cost.other_cost += other_cost

        if outsource_cost is not None:
            # 外协费需要累加，符合"同一项目在同一月份多次导入会累加成本金额"的需求
            cost.outsource_cost += outsource_cost

        # 计算总成本
        cost.total_cost = cost.bom_cost + cost.labor_cost + cost.other_cost + cost.outsource_cost
        cost.updated_at = datetime.datetime.now()

        # 记录变更，用于调试
        print(f"Project {project_id}, {year_month} cost update:")
        if bom_cost is not None:
            print(f"  BOM cost: {old_bom_cost} + {bom_cost} = {cost.bom_cost}")
        if labor_cost is not None:
            print(f"  Labor cost: {old_labor_cost} -> {cost.labor_cost}")
        if other_cost is not None:
            print(f"  Other cost: {old_other_cost} + {other_cost} = {cost.other_cost}")
        if outsource_cost is not None:
            print(f"  Outsource cost: {old_outsource_cost} + {outsource_cost} = {cost.outsource_cost}")
        print(f"  Total cost: {cost.total_cost}")

        # 确保更改被提交到数据库
        db.session.commit()

        return cost.total_cost
    except Exception as e:
        # 如果有任何异常，回滚事务
        db.session.rollback()
        print(f"更新项目月度成本失败: {str(e)}")
        raise


def get_project_other_cost_details(project_id):
    """
    获取项目其他费用明细

    Args:
        project_id: 项目ID

    Returns:
        费用明细字典，包含各种费用类型的金额
    """
    # 查询项目的所有其他费用记录
    other_costs = OtherCostImport.query.filter_by(project_id=project_id).all()

    # 查询项目激励费用
    incentive_cost = db.session.query(db.func.sum(ProjectIncentive.amount)).filter_by(
        project_id=project_id
    ).scalar() or 0

    # 初始化费用明细字典
    cost_details = {
        'travel': 0,        # 差旅费
        'outsource': 0,     # 外协费
        'training': 0,      # 培训费
        'repair': 0,        # 维修费
        'management': 0,    # 管理费
        'transportation': 0, # 运杂费
        'other': 0,         # 其他费用
        'incentive': incentive_cost  # 项目激励费用
    }

    # 按费用类型汇总金额
    for cost in other_costs:
        if cost.cost_type in cost_details:
            cost_details[cost.cost_type] += cost.amount or 0

    return cost_details


def get_project_actual_cost(project_id, year_month=None):
    """
    获取项目实际成本（项目激励归入人工成本）

    Args:
        project_id: 项目ID
        year_month: 年月字符串，格式：YYYY-MM，为None表示获取所有月份的总和

    Returns:
        成本数据字典
    """
    if year_month:
        # 获取指定月份的成本
        cost = ProjectActualCost.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).first()

        # 获取指定月份的项目激励成本
        incentive_cost = db.session.query(db.func.sum(ProjectIncentive.amount)).filter_by(
            project_id=project_id,
            year_month=year_month
        ).scalar() or 0

        if cost:
            # 将项目激励成本加入人工成本
            total_labor_cost = cost.labor_cost + incentive_cost
            total_cost = cost.bom_cost + total_labor_cost + cost.other_cost + cost.outsource_cost

            return {
                'total_cost': total_cost,
                'bom_cost': cost.bom_cost,
                'labor_cost': total_labor_cost,
                'other_cost': cost.other_cost,
                'outsource_cost': cost.outsource_cost,
                'year_month': cost.year_month
            }
        else:
            return {
                'total_cost': incentive_cost,
                'bom_cost': 0,
                'labor_cost': incentive_cost,
                'other_cost': 0,
                'outsource_cost': 0,
                'year_month': year_month
            }
    else:
        # 获取所有月份的成本总和
        costs = ProjectActualCost.query.filter_by(project_id=project_id).all()

        # 获取所有月份的项目激励成本总和
        total_incentive_cost = db.session.query(db.func.sum(ProjectIncentive.amount)).filter_by(
            project_id=project_id
        ).scalar() or 0

        total_cost = sum(c.total_cost for c in costs)
        bom_cost = sum(c.bom_cost for c in costs)
        labor_cost = sum(c.labor_cost for c in costs)
        other_cost = sum(c.other_cost for c in costs)
        outsource_cost = sum(c.outsource_cost for c in costs)

        # 将项目激励成本加入人工成本
        total_labor_cost = labor_cost + total_incentive_cost
        total_cost = bom_cost + total_labor_cost + other_cost + outsource_cost

        return {
            'total_cost': total_cost,
            'bom_cost': bom_cost,
            'labor_cost': total_labor_cost,
            'other_cost': other_cost,
            'outsource_cost': outsource_cost
        }


def get_project_monthly_costs(project_id, months_limit=12):
    """
    获取项目月度成本趋势（项目激励归入人工成本）

    Args:
        project_id: 项目ID
        months_limit: 最近几个月的数据，默认12个月

    Returns:
        月度成本趋势数据列表
    """
    # 获取最近months_limit个月的数据
    costs = ProjectActualCost.query.filter_by(
        project_id=project_id
    ).order_by(ProjectActualCost.year_month.desc()).limit(months_limit).all()

    # 按格式化后的月份进行分组和合并，解决重复月份问题
    monthly_data = {}

    for cost in costs:
        # 格式化月份为统一的 YYYY-MM 格式
        formatted_month = format_year_month_string(cost.year_month)

        if formatted_month not in monthly_data:
            monthly_data[formatted_month] = {
                'bom_cost': 0,
                'labor_cost': 0,
                'other_cost': 0,
                'outsource_cost': 0,
                'original_months': []  # 记录原始月份，用于查询激励成本
            }

        # 累加相同月份的成本数据
        monthly_data[formatted_month]['bom_cost'] += cost.bom_cost
        monthly_data[formatted_month]['labor_cost'] += cost.labor_cost
        monthly_data[formatted_month]['other_cost'] += cost.other_cost
        monthly_data[formatted_month]['outsource_cost'] += cost.outsource_cost
        monthly_data[formatted_month]['original_months'].append(cost.year_month)

    # 获取项目激励成本
    incentive_costs_by_month = {}
    if monthly_data:
        # 收集所有原始月份用于查询激励成本
        all_original_months = []
        for data in monthly_data.values():
            all_original_months.extend(data['original_months'])

        if all_original_months:
            # 批量查询所有月份的项目激励成本
            incentive_query = db.session.query(
                ProjectIncentive.year_month,
                db.func.sum(ProjectIncentive.amount).label('total_incentive')
            ).filter(
                ProjectIncentive.project_id == project_id,
                ProjectIncentive.year_month.in_(all_original_months)
            ).group_by(ProjectIncentive.year_month).all()

            # 按格式化后的月份重新分组激励成本
            for item in incentive_query:
                formatted_month = format_year_month_string(item.year_month)
                if formatted_month not in incentive_costs_by_month:
                    incentive_costs_by_month[formatted_month] = 0
                incentive_costs_by_month[formatted_month] += item.total_incentive

    # 按日期排序（正确的时间顺序）
    sorted_months = sorted(monthly_data.keys(), key=lambda x: tuple(map(int, x.split('-'))))

    # 转换为前端需要的格式
    formatted_months = []
    total_costs = []
    bom_costs = []
    labor_costs = []
    other_costs = []

    for month in sorted_months:
        data = monthly_data[month]
        incentive_cost = incentive_costs_by_month.get(month, 0)
        total_labor_cost = data['labor_cost'] + incentive_cost
        total_cost = data['bom_cost'] + total_labor_cost + data['other_cost'] + data['outsource_cost']

        formatted_months.append(month)
        total_costs.append(total_cost)
        bom_costs.append(data['bom_cost'])
        labor_costs.append(total_labor_cost)
        other_costs.append(data['other_cost'])

    result = {
        'months': formatted_months,
        'total_costs': total_costs,
        'bom_costs': bom_costs,
        'labor_costs': labor_costs,
        'other_costs': other_costs
    }

    return result


def get_dashboard_metrics(current_year=None, current_month=None, compare_with_last_year=True):
    """
    计算仪表盘关键指标

    Args:
        current_year: 当前年份，为None表示使用系统当前年份
        current_month: 当前月份，为None表示使用系统当前月份
        compare_with_last_year: 是否与去年同期比较，否则与上月比较

    Returns:
        包含各项指标及其同比/环比变化的字典
    """
    if current_year is None or current_month is None:
        now = datetime.datetime.now()
        current_year = now.year
        current_month = now.month

    # 确定比较的年月
    if compare_with_last_year:
        compare_year = current_year - 1
        compare_month = current_month
    else:
        compare_year, compare_month = get_previous_month(current_year, current_month)

    # 计算当前指标
    current_projects = Import_project.query.all()
    current_project_count = len(current_projects)

    # 计算BOM成本异常项目数（超额或低于预估BOM成本20%的为报警）
    current_bom_abnormal_count = 0
    for p in current_projects:
        # 获取项目的预估BOM成本
        estimated_bom_cost = p.bom_cost or 0
        if estimated_bom_cost <= 0:
            continue  # 跳过没有预估BOM成本的项目

        # 获取项目的实际BOM成本
        actual_cost_data = get_project_actual_cost(p.id)
        actual_bom_cost = actual_cost_data['bom_cost']

        # 计算偏差率：(实际BOM成本 - 预估BOM成本) / 预估BOM成本 * 100
        if estimated_bom_cost > 0:
            deviation_rate = abs((actual_bom_cost - estimated_bom_cost) / estimated_bom_cost * 100)
            # 如果偏差率超过20%，则视为异常项目
            if deviation_rate > 20:
                current_bom_abnormal_count += 1

    # 移除current_avg_cost计算，因为我们不再需要总预估成本

    # 计算人工成本异常项目数（超额或低于预估人工成本20%的为报警）
    current_labor_abnormal_count = 0
    for p in current_projects:
        # 获取项目的预估人工成本
        estimated_labor_cost = p.labor_cost or 0
        if estimated_labor_cost <= 0:
            continue  # 跳过没有预估人工成本的项目

        # 获取项目的实际人工成本
        actual_cost_data = get_project_actual_cost(p.id)
        actual_labor_cost = actual_cost_data['labor_cost']

        # 计算偏差率：(实际人工成本 - 预估人工成本) / 预估人工成本 * 100
        if estimated_labor_cost > 0:
            deviation_rate = abs((actual_labor_cost - estimated_labor_cost) / estimated_labor_cost * 100)
            # 如果偏差率超过20%，则视为异常项目
            if deviation_rate > 20:
                current_labor_abnormal_count += 1

    # 计算异常项目数（实际成本超过预估成本的10%）
    current_abnormal_count = 0
    for project in current_projects:
        actual_cost_data = get_project_actual_cost(project.id)
        actual_cost = actual_cost_data['total_cost']
        # 使用更新后的预估成本进行比较
        project_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)
        if project_estimate_cost > 0 and actual_cost > project_estimate_cost * 1.1:
            current_abnormal_count += 1

    # 获取比较期间的数据
    # 由于项目可能没有历史版本，我们简单根据创建时间筛选
    # 实际应用中，可能需要更复杂的逻辑或历史记录表
    compare_projects = Import_project.query.filter(
        extract('year', Import_project.create_at) <= compare_year,
        extract('month', Import_project.create_at) <= compare_month
    ).all()

    compare_project_count = len(compare_projects)

    # 计算比较期间的BOM成本异常项目数
    compare_bom_abnormal_count = 0
    for p in compare_projects:
        # 获取项目的预估BOM成本
        estimated_bom_cost = p.bom_cost or 0
        if estimated_bom_cost <= 0:
            continue  # 跳过没有预估BOM成本的项目

        # 获取项目的实际BOM成本
        actual_cost_data = get_project_actual_cost(p.id)
        actual_bom_cost = actual_cost_data['bom_cost']

        # 计算偏差率：(实际BOM成本 - 预估BOM成本) / 预估BOM成本 * 100
        if estimated_bom_cost > 0:
            deviation_rate = abs((actual_bom_cost - estimated_bom_cost) / estimated_bom_cost * 100)
            # 如果偏差率超过20%，则视为异常项目
            if deviation_rate > 20:
                compare_bom_abnormal_count += 1

    # 计算比较期间的人工成本异常项目数
    compare_labor_abnormal_count = 0
    for p in compare_projects:
        # 获取项目的预估人工成本
        estimated_labor_cost = p.labor_cost or 0
        if estimated_labor_cost <= 0:
            continue  # 跳过没有预估人工成本的项目

        # 获取项目的实际人工成本
        actual_cost_data = get_project_actual_cost(p.id)
        actual_labor_cost = actual_cost_data['labor_cost']

        # 计算偏差率：(实际人工成本 - 预估人工成本) / 预估人工成本 * 100
        if estimated_labor_cost > 0:
            deviation_rate = abs((actual_labor_cost - estimated_labor_cost) / estimated_labor_cost * 100)
            # 如果偏差率超过20%，则视为异常项目
            if deviation_rate > 20:
                compare_labor_abnormal_count += 1

    # 计算比较期间的异常项目数
    compare_abnormal_count = 0
    for project in compare_projects:
        actual_cost_data = get_project_actual_cost(project.id)
        actual_cost = actual_cost_data['total_cost']
        # 使用更新后的预估成本进行比较
        project_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)
        if project_estimate_cost > 0 and actual_cost > project_estimate_cost * 1.1:
            compare_abnormal_count += 1

    # 计算变化百分比
    def calculate_percentage_change(current, previous):
        if previous == 0:
            return 100 if current > 0 else 0
        return ((current - previous) / previous) * 100

    project_count_change = calculate_percentage_change(current_project_count, compare_project_count)
    bom_abnormal_count_change = calculate_percentage_change(current_bom_abnormal_count, compare_bom_abnormal_count)
    labor_abnormal_count_change = calculate_percentage_change(current_labor_abnormal_count, compare_labor_abnormal_count)
    abnormal_count_change = calculate_percentage_change(current_abnormal_count, compare_abnormal_count)

    # 构建结果字典
    metrics = {
        'project_count': {
            'value': current_project_count,
            'change': project_count_change,
            'change_type': 'increase' if project_count_change >= 0 else 'decrease'
        },
        'bom_abnormal_count': {
            'value': current_bom_abnormal_count,
            'change': bom_abnormal_count_change,
            'change_type': 'increase' if bom_abnormal_count_change >= 0 else 'decrease'
        },
        'labor_abnormal_count': {
            'value': current_labor_abnormal_count,
            'change': labor_abnormal_count_change,
            'change_type': 'increase' if labor_abnormal_count_change >= 0 else 'decrease'
        },
        'abnormal_count': {
            'value': current_abnormal_count,
            'change': abnormal_count_change,
            'change_type': 'increase' if abnormal_count_change >= 0 else 'decrease'
        }
    }

    return metrics


def get_cost_structure_data():
    """
    获取成本构成分析数据

    Returns:
        成本构成数据字典
    """
    # 获取所有项目的实际成本
    costs = ProjectActualCost.query.all()

    # 计算各类成本总和
    total_bom_cost = sum(c.bom_cost for c in costs)
    total_labor_cost = sum(c.labor_cost for c in costs)
    total_other_cost = sum(c.other_cost for c in costs)
    total_cost = total_bom_cost + total_labor_cost + total_other_cost

    # 构建结果
    result = {
        'labels': ['BOM成本', '人工成本', '其他成本'],
        'values': [total_bom_cost, total_labor_cost, total_other_cost],
        'total': total_cost
    }

    return result


def get_status_distribution_data():
    """
    获取项目状态分布数据，动态适应数据库中的项目状态配置

    Returns:
        项目状态分布数据字典
    """
    # 获取所有启用的项目状态字典数据
    enabled_statuses = DictData.query.filter_by(type_code='project_status', enable=1).all()
    status_dict = {status.data_value: status.data_label for status in enabled_statuses}

    # 查询实际存在项目的状态值（去重）
    existing_status_query = db.session.query(Import_project.project_status).distinct().all()
    existing_status_values = {str(status[0]) for status in existing_status_query if status[0] is not None}

    # 取启用状态和实际存在状态的交集，并按状态值排序
    valid_status_values = sorted(
        existing_status_values.intersection(status_dict.keys()),
        key=lambda x: int(x)
    )

    # 初始化结果
    result = {
        'labels': [],
        'counts': [],
        'costs': []
    }

    # 按照排序后的状态值处理每个状态
    for status_value in valid_status_values:
        # 获取状态标签
        status_label = status_dict.get(status_value, f'状态{status_value}')

        # 查询该状态的项目数量
        projects = Import_project.query.filter_by(project_status=status_value).all()
        project_count = len(projects)

        # 计算该状态项目的总成本（使用实际成本）
        total_cost = 0
        for p in projects:
            actual_cost_data = get_project_actual_cost(p.id)
            total_cost += actual_cost_data['total_cost']

        # 添加到结果中
        result['labels'].append(status_label)
        result['counts'].append(project_count)
        result['costs'].append(total_cost)

    return result


def calculate_overtime_pay(base_salary, overtime_hours, work_date, precise=False):
    """计算加班工资（使用节假日配置系统）
    参数:
        base_salary: 基本工资
        overtime_hours: 加班工时
        work_date: 工作日期
        precise: 是否返回精确值（不四舍五入），默认False保持兼容性
    返回:
        加班工资
    """
    if not overtime_hours or overtime_hours == 0:
        return 0

    from applications.services.holiday_service import HolidayService

    # 获取加班倍率（使用新的节假日配置系统）
    overtime_rate = HolidayService.get_overtime_rate(work_date)

    # 计算时薪和精确费率
    hourly_rate = base_salary / 21.75 / 8
    precise_rate = hourly_rate * overtime_rate

    # 计算精确加班费
    precise_pay = overtime_hours * precise_rate

    # 根据参数决定是否四舍五入
    if precise:
        return precise_pay
    else:
        # 保持原有行为，确保兼容性
        return round(precise_pay)


def calculate_labor_cost_by_actual_salary(project_id=None, date_range=None):
    """基于实际薪资计算人工成本

    Args:
        project_id: 项目ID，如果不提供则计算所有项目
        date_range: 日期范围元组 (start_date, end_date)，如果不提供则使用当前月

    Returns:
        int: 处理的项目数量
    """
    # 获取需要计算的项目列表
    if project_id:
        projects = [Import_project.query.get(project_id)]
        if not projects[0]:
            return 0
    else:
        # 获取所有有工时记录的项目
        projects = Import_project.query.all()

    # 定义时间范围
    if date_range and len(date_range) == 2:
        start_date, end_date = date_range
    else:
        # 获取最早的工时记录日期，使用work_date而不是created_at
        earliest_log = LogInfo.query.order_by(LogInfo.work_date.asc()).first()
        start_date = earliest_log.work_date if earliest_log else datetime.datetime.now() - timedelta(days=365)
        end_date = datetime.datetime.now()

    # 记录计算成功的项目数量
    success_count = 0

    # 循环处理每个项目
    for project in projects:
        # 获取项目所属部门
        if not project.dept_id:
            continue

        # 查询部门信息
        dept = ProjectManageDept.query.get(project.dept_id)
        if not dept or not dept.dept_name:
            continue

        # 使用部门名称作为项目类型
        project_prefix = dept.dept_name

        # 获取项目工时数据，按月份分组
        project_timesheet_data = get_project_timesheet_data(project_prefix, project.project_code, (start_date, end_date))

        if not project_timesheet_data:
            continue

        # 初始化项目总人工成本
        total_labor_cost = 0

        # 遍历每个月份的工时数据
        for year_month, month_data in project_timesheet_data.items():
            month_labor_cost = 0
            cost_details = []  # 用于记录详细的成本计算过程

            # 遍历每个员工的工时数据
            for employee_id, employee_data in month_data['employees'].items():
                # 获取员工在当月的薪资数据
                employee_salary = get_employee_salary(employee_id, year_month)

                if not employee_salary:
                    # 如果没有找到当月薪资，尝试获取最近一个月的薪资数据
                    employee_salary = get_latest_employee_salary(employee_id)

                if employee_salary:
                    # 使用应发工资进行计算
                    should_pay = employee_salary.should_pay if employee_salary.should_pay else 0

                    # 计算正常工时费率
                    # 修改这里的计算逻辑：使用包含所有类型的总正工时
                    total_regular_hours_all = employee_data.get('total_regular_hours_all', 0)

                    # 正常工时费率 = (应发工资 - 加班费) / 月度总正工时（包含项目和会议和其他的正工时）
                    regular_rate = (should_pay - employee_salary.overtime_pay) / total_regular_hours_all if total_regular_hours_all > 0 else 0

                    # 计算正常工时成本
                    regular_cost = regular_rate * employee_data['project_regular_hours']

                    # 计算加班工时成本
                    overtime_cost = 0
                    if employee_data['project_overtime_hours'] > 0:
                        # 获取员工基本工资
                        base_salary = employee_salary.base_salary if employee_salary.base_salary else should_pay
                        print(f"Using base_salary: {base_salary} for employee {employee_id} overtime calculation")

                        # 提取年月
                        year = int(year_month.split('-')[0])
                        month = int(year_month.split('-')[1])

                        # 获取加班日期 - 修复：添加项目和月份过滤条件
                        overtime_logs = LogInfo.query.filter(
                            LogInfo.employee_id == str(employee_id),
                            LogInfo.work_date.between(start_date, end_date),
                            LogInfo.overtimeWorkingHours > 0,
                            LogInfo.projectPrefix == project_prefix,  # 添加项目类型过滤
                            LogInfo.projectNumber == str(project.project_code),  # 修复：使用正确的项目编号变量
                            extract('year', LogInfo.work_date) == year,  # 添加年份过滤
                            extract('month', LogInfo.work_date) == month  # 添加月份过滤
                        ).all()

                        print(f"Found {len(overtime_logs)} overtime logs for employee {employee_id} in project {project_prefix}-{project.project_code}, month {year_month}")

                        # 计算每天的加班费并累加
                        for log in overtime_logs:
                            overtime_pay = calculate_overtime_pay(
                                base_salary=base_salary,
                                overtime_hours=log.overtimeWorkingHours,
                                work_date=log.work_date
                            )
                            overtime_cost += overtime_pay
                            print(f"Overtime log ID {log.id}: date={log.work_date}, hours={log.overtimeWorkingHours}, pay={overtime_pay}")

                        print(f"Total overtime cost for employee {employee_id} in project {project_prefix}-{project.project_code}, month {year_month}: {overtime_cost}")

                    # 计算员工贡献到项目的总薪资成本
                    employee_project_cost = regular_cost + overtime_cost

                    # 记录详细的成本计算过程
                    cost_details.append({
                        'employee_id': employee_id,
                        'regular_hours': employee_data['project_regular_hours'],
                        'overtime_hours': employee_data['project_overtime_hours'],
                        'regular_cost': regular_cost,
                        'overtime_cost': overtime_cost,
                        'total_cost': employee_project_cost
                    })

                    # 累加到月度人工成本
                    month_labor_cost += employee_project_cost

            # 保存或更新月度人工成本
            year, month = map(int, year_month.split('-'))
            update_project_labor_cost(
                project.id,
                year,
                month,
                month_labor_cost,
                total_regular_hours=month_data['total_regular_hours'],
                total_overtime_hours=month_data['total_overtime_hours'],
                cost_details=cost_details
            )

            # 累加到项目总人工成本
            total_labor_cost += month_labor_cost

        # 更新项目总人工成本
        update_project_total_labor_cost(project.id, total_labor_cost)

        success_count += 1

    return success_count


def get_employee_salary(employee_id, month):
    """获取员工某月的薪资记录"""
    try:
        # 尝试将employee_id转换为整数（如果是字符串的话）
        if isinstance(employee_id, str) and employee_id.isdigit():
            employee_id = int(employee_id)

        # 从ygong表中查找员工
        employee = ygong.query.filter_by(employee_id=employee_id).first()
        if not employee:
            print(f"Employee not found with ID: {employee_id}")
            return None

        # 处理月份格式：从YYYY-MM-DD提取YYYY-M
        if '-' in month:
            parts = month.split('-')
            if len(parts) >= 2:
                month = f"{parts[0]}-{int(parts[1])}"  # 转换为YYYY-M格式

        # 使用员工ID查询薪资记录
        salary = EmployeeSalary.query.filter_by(
            employee_id=employee.id,
            month=month
        ).first()

        if not salary:
            print(f"No salary record found for employee {employee_id} in month {month}")
        return salary

    except Exception as e:
        print(f"Error getting salary for employee {employee_id}: {str(e)}")
        return None


def get_latest_employee_salary(employee_id):
    """
    获取员工最近一个月的薪资数据

    Args:
        employee_id: 员工ID

    Returns:
        EmployeeSalary: 薪资数据对象，如果没有找到则返回None
    """
    return EmployeeSalary.query.filter_by(
        employee_id=employee_id
    ).order_by(EmployeeSalary.month.desc()).first()


def calculate_total_salary(salary):
    """
    计算员工月度总薪资

    Args:
        salary: EmployeeSalary对象

    Returns:
        float: 月度总薪资
    """
    # 记录日志
    current_app.logger.info(f"Calculating total salary for employee {salary.employee_id}")

    # 如果有记录应发工资且不为零，则直接使用
    if salary.should_pay and salary.should_pay > 0:
        current_app.logger.info(f"Using should_pay: {salary.should_pay}")
        return salary.should_pay

    total = 0

    # 基本工资
    if salary.base_salary:
        total += salary.base_salary
        current_app.logger.info(f"Added base_salary: {salary.base_salary}")

    # 绩效工资
    if salary.performance_salary:
        total += salary.performance_salary
        current_app.logger.info(f"Added performance_salary: {salary.performance_salary}")

    # 主管考核项
    if salary.supervisor_assessment:
        total += salary.supervisor_assessment
        current_app.logger.info(f"Added supervisor_assessment: {salary.supervisor_assessment}")

    # 职务津贴
    if salary.position_allowance:
        total += salary.position_allowance
        current_app.logger.info(f"Added position_allowance: {salary.position_allowance}")

    # 全勤奖
    if salary.full_attendance:
        total += salary.full_attendance
        current_app.logger.info(f"Added full_attendance: {salary.full_attendance}")

    # 加班费
    if salary.overtime_pay:
        total += salary.overtime_pay
        current_app.logger.info(f"Added overtime_pay: {salary.overtime_pay}")

    # 如果计算出的总薪资为零，尝试使用基本工资
    if total == 0:
        if salary.base_salary:
            current_app.logger.info(f"Using base_salary as fallback: {salary.base_salary}")
            return salary.base_salary
        else:
            current_app.logger.warning(f"Total salary is 0 and no base_salary available for employee {salary.employee_id}")

    current_app.logger.info(f"Final total salary: {total}")
    return total


def get_project_timesheet_data(project_prefix, project_number, date_range):
    """
    获取项目的工时数据，按月份分组

    Args:
        project_prefix: 项目类型（部门名称）
        project_number: 项目编号
        date_range: 日期范围元组(start_date, end_date)

    Returns:
        dict: 按月份分组的工时数据
    """
    start_date, end_date = date_range

    # 查询指定日期范围内的工时记录
    logs = LogInfo.query.filter(
        LogInfo.work_date.between(start_date, end_date),
        LogInfo.projectPrefix == project_prefix,
        LogInfo.projectNumber == str(project_number)  # 确保project_number是字符串类型
    ).all()

    # 记录日志
    print(f"Found {len(logs)} logs for project {project_prefix}-{project_number}")
    print(f"Query parameters: prefix={project_prefix}, number={project_number}, date_range={start_date} to {end_date}")

    # 按月份分组
    monthly_data = {}

    for log in logs:
        # 确保work_date不为空
        if not log.work_date:
            print(f"Log {log.id} has no work_date, skipping")
            continue

        # 获取年月
        year_month = log.work_date.strftime('%Y-%m')

        if year_month not in monthly_data:
            monthly_data[year_month] = {
                'total_regular_hours': 0,
                'total_overtime_hours': 0,
                'total_hours': 0,
                'employees': {}
            }

        # 确保工时数据不为空，并设置默认值
        regular_hours = log.regularWorkingHours if log.regularWorkingHours is not None else (log.totalHours - (log.overtimeWorkingHours or 0))
        overtime_hours = log.overtimeWorkingHours if log.overtimeWorkingHours is not None else 0
        total_hours = log.totalHours if log.totalHours is not None else (regular_hours + overtime_hours)

        print(f"Processing log {log.id}: regular={regular_hours}, overtime={overtime_hours}, total={total_hours}")

        # 更新总工时
        monthly_data[year_month]['total_regular_hours'] += regular_hours
        monthly_data[year_month]['total_overtime_hours'] += overtime_hours
        monthly_data[year_month]['total_hours'] += total_hours

        # 更新员工工时
        if log.employee_id not in monthly_data[year_month]['employees']:
            monthly_data[year_month]['employees'][log.employee_id] = {
                'total_regular_hours': 0,
                'total_overtime_hours': 0,
                'total_hours': 0,
                'project_regular_hours': 0,
                'project_overtime_hours': 0,
                'project_hours': 0,
                'total_regular_hours_all': 0  # 新增：包含所有类型的总正工时
            }

        # 更新员工总工时和项目工时
        monthly_data[year_month]['employees'][log.employee_id]['total_regular_hours'] += regular_hours
        monthly_data[year_month]['employees'][log.employee_id]['total_overtime_hours'] += overtime_hours
        monthly_data[year_month]['employees'][log.employee_id]['total_hours'] += total_hours
        monthly_data[year_month]['employees'][log.employee_id]['project_regular_hours'] += regular_hours
        monthly_data[year_month]['employees'][log.employee_id]['project_overtime_hours'] += overtime_hours
        monthly_data[year_month]['employees'][log.employee_id]['project_hours'] += total_hours

    # 获取每个员工在每个月的所有工时记录（包括会议和其他）
    for year_month, data in monthly_data.items():
        for employee_id in data['employees'].keys():
            # 获取员工在该月份的所有工时记录，包括所有类型的项目
            employee_logs = LogInfo.query.filter(
                LogInfo.employee_id == str(employee_id),
                LogInfo.work_date.between(start_date, end_date),
                extract('year', LogInfo.work_date) == int(year_month.split('-')[0]),
                extract('month', LogInfo.work_date) == int(year_month.split('-')[1])
            ).all()

            # 计算包含所有类型的总正工时
            total_regular_hours_all = sum(
                log.regularWorkingHours if log.regularWorkingHours is not None else
                (log.totalHours - (log.overtimeWorkingHours or 0))
                for log in employee_logs
            )

            # 更新到员工数据中
            data['employees'][employee_id]['total_regular_hours_all'] = total_regular_hours_all

            print(f"Employee {employee_id} in month {year_month}: total_regular_hours={data['employees'][employee_id]['total_regular_hours']}, "
                  f"total_regular_hours_all={total_regular_hours_all}")

    # 记录结果
    print(f"Processed {len(monthly_data)} months of data")
    for year_month, data in monthly_data.items():
        print(f"Month {year_month}: {data['total_hours']} total hours, {len(data['employees'])} employees")

    return monthly_data


def update_project_labor_cost(project_id, year, month, labor_cost, total_regular_hours, total_overtime_hours, cost_details):
    """
    更新项目月度人工成本

    Args:
        project_id: 项目ID
        year: 年份
        month: 月份
        labor_cost: 人工成本
        total_regular_hours: 总正常工时
        total_overtime_hours: 总加班工时
        cost_details: 成本明细
    """
    work_month = f"{year}-{month:02d}"  # 确保月份是两位数

    # 查找是否已存在该项目该月的人工成本记录
    labor_cost_record = ProjectLaborCost.query.filter_by(
        project_id=project_id,
        work_month=work_month,
        calculation_method='actual_salary'
    ).first()

    if labor_cost_record:
        # 更新已有记录
        labor_cost_record.total_cost = labor_cost
        labor_cost_record.total_regular_hours = total_regular_hours
        labor_cost_record.total_overtime_hours = total_overtime_hours
        labor_cost_record.cost_details = json.dumps(cost_details)
        labor_cost_record.calculated_at = datetime.datetime.now()
    else:
        # 创建新记录
        labor_cost_record = ProjectLaborCost(
            project_id=project_id,
            work_month=work_month,
            salary_month=work_month,  # 使用相同的月份作为工资发放月份
            total_cost=labor_cost,
            total_regular_hours=total_regular_hours,
            total_overtime_hours=total_overtime_hours,
            cost_details=json.dumps(cost_details),
            calculation_method='actual_salary'
        )
        db.session.add(labor_cost_record)

    # 同时更新项目月度总成本记录（先更新ProjectActualCost表）
    update_project_monthly_cost(project_id, work_month, labor_cost=labor_cost)

    # 最后提交所有更改
    db.session.commit()


def update_project_total_labor_cost(project_id, total_labor_cost):
    """
    更新项目总人工成本（所有月份汇总）

    Args:
        project_id: 项目ID
        total_labor_cost: 总人工成本
    """
    # 这里可以额外保存一个汇总记录，或者仅用于展示
    pass


def import_project_incentive(project_id, year_month, employee_name, amount, incentive_type='项目激励', import_file=None, import_by=None, remark=None):
    """
    导入项目激励记录

    Args:
        project_id: 项目ID
        year_month: 年月字符串，格式：YYYY-MM
        employee_name: 员工姓名
        amount: 激励金额
        incentive_type: 激励类型，默认为'项目激励'
        import_file: 导入文件名
        import_by: 导入人ID
        remark: 备注

    Returns:
        (success, message): 成功状态和消息
    """
    try:
        # 检查项目是否存在
        project = Import_project.query.get(project_id)
        if not project:
            return False, f"项目ID {project_id} 不存在"

        # 保存激励记录
        incentive = ProjectIncentive(
            project_id=project_id,
            year_month=year_month,
            employee_name=employee_name,
            amount=amount,
            incentive_type=incentive_type,
            import_file=import_file,
            import_by=import_by,
            remark=remark,
            created_at=datetime.datetime.now()
        )
        db.session.add(incentive)
        db.session.flush()  # 确保数据写入但不提交事务

        # 提交所有操作
        db.session.commit()

        # 返回成功消息
        return True, f"成功导入项目激励: {employee_name} ¥{amount:.2f}"
    except Exception as e:
        db.session.rollback()
        return False, f"导入失败: {str(e)}"


def get_project_incentive_summary(project_id, year_month=None):
    """
    获取项目激励汇总

    Args:
        project_id: 项目ID
        year_month: 年月字符串，格式：YYYY-MM，为None表示获取所有月份的总和

    Returns:
        激励汇总数据字典
    """
    if year_month:
        # 获取指定月份的激励
        incentives = ProjectIncentive.query.filter_by(
            project_id=project_id,
            year_month=year_month
        ).all()
    else:
        # 获取所有月份的激励
        incentives = ProjectIncentive.query.filter_by(project_id=project_id).all()

    # 计算汇总数据
    total_amount = sum(inc.amount for inc in incentives)
    employee_count = len(set(inc.employee_name for inc in incentives))
    record_count = len(incentives)

    # 按员工分组
    employee_summary = {}
    for inc in incentives:
        if inc.employee_name not in employee_summary:
            employee_summary[inc.employee_name] = {
                'total_amount': 0,
                'record_count': 0,
                'records': []
            }
        employee_summary[inc.employee_name]['total_amount'] += inc.amount
        employee_summary[inc.employee_name]['record_count'] += 1
        employee_summary[inc.employee_name]['records'].append({
            'year_month': inc.year_month,
            'amount': inc.amount,
            'incentive_type': inc.incentive_type,
            'created_at': inc.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    return {
        'total_amount': total_amount,
        'employee_count': employee_count,
        'record_count': record_count,
        'employee_summary': employee_summary
    }


def auto_calculate_all_projects_labor_cost(delay_seconds=5, date_range=None):
    """
    自动计算所有项目的人工成本（定时任务专用）

    Args:
        delay_seconds: 项目间延迟时间（秒），默认5秒
        date_range: 日期范围元组 (start_date, end_date)，如果不提供则使用默认范围

    Returns:
        dict: 包含执行结果的字典
    """
    logger = current_app.logger
    start_time = datetime.datetime.now()

    logger.info(f"开始自动计算项目人工成本 - {start_time}")

    # 获取所有项目
    try:
        projects = Import_project.query.all()
        total_projects = len(projects)
        logger.info(f"找到 {total_projects} 个项目需要计算")

        if total_projects == 0:
            logger.warning("没有找到需要计算的项目")
            return {
                'success': True,
                'total_projects': 0,
                'success_count': 0,
                'failed_count': 0,
                'start_time': start_time,
                'end_time': datetime.datetime.now(),
                'message': '没有找到需要计算的项目'
            }

    except Exception as e:
        logger.error(f"获取项目列表失败: {str(e)}")
        return {
            'success': False,
            'error': f'获取项目列表失败: {str(e)}',
            'start_time': start_time,
            'end_time': datetime.datetime.now()
        }

    # 初始化计数器
    success_count = 0
    failed_count = 0
    failed_projects = []

    # 逐个项目计算
    for i, project in enumerate(projects, 1):
        try:
            logger.info(f"正在计算项目 {i}/{total_projects}: {project.project_name} (ID: {project.id})")

            # 调用现有的计算函数
            result_count = calculate_labor_cost_by_actual_salary(
                project_id=project.id,
                date_range=date_range
            )

            if result_count > 0:
                success_count += 1
                logger.info(f"项目 {project.project_name} 计算成功")
            else:
                logger.warning(f"项目 {project.project_name} 计算结果为0，可能没有相关数据")
                success_count += 1  # 仍然算作成功，只是没有数据

        except Exception as e:
            failed_count += 1
            error_msg = f"项目 {project.project_name} (ID: {project.id}) 计算失败: {str(e)}"
            logger.error(error_msg)
            failed_projects.append({
                'project_id': project.id,
                'project_name': project.project_name,
                'error': str(e)
            })

        # 添加延迟，避免服务器压力（最后一个项目不需要延迟）
        if i < total_projects and delay_seconds > 0:
            logger.debug(f"等待 {delay_seconds} 秒后处理下一个项目...")
            time.sleep(delay_seconds)

    end_time = datetime.datetime.now()
    duration = end_time - start_time

    # 记录最终结果
    logger.info(f"自动计算完成 - 耗时: {duration}")
    logger.info(f"总项目数: {total_projects}, 成功: {success_count}, 失败: {failed_count}")

    if failed_projects:
        logger.error(f"失败的项目列表: {failed_projects}")

    return {
        'success': failed_count == 0,
        'total_projects': total_projects,
        'success_count': success_count,
        'failed_count': failed_count,
        'failed_projects': failed_projects,
        'start_time': start_time,
        'end_time': end_time,
        'duration': str(duration),
        'message': f'计算完成：成功 {success_count} 个，失败 {failed_count} 个'
    }