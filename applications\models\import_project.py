import datetime
from flask_login import UserMixin
from applications.extensions import db


class Import_project(db.Model, UserMixin):
    __tablename__ = 'import_project'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='项目ID')
    project_name = db.Column(db.String(255), comment='项目名称')
    project_code = db.Column(db.String(255), comment='项目编码')
    project_status = db.Column(db.Integer, comment='项目状态 (0:未开始, 1:生产中, 2:生产完成, 3:发货中, 4:发货完成, 5:安装中, 6:安装完成, 7:调试中, 8:调试完成, 9:验收中, 10:验收完成, 11:项目已完成)')
    price = db.Column(db.Float, comment='项目价格')
    currency = db.Column(db.String(50), comment='币种')
    machine_number = db.Column(db.Integer, comment='机台数量')
    estimate_cost = db.Column(db.Float, comment='预估成本')
    bom_cost = db.Column(db.Float, comment='BOM成本')
    mechanical_bom_cost = db.Column(db.Float, comment='机械BOM成本')
    electrical_bom_cost = db.Column(db.Float, comment='电气BOM成本')
    labor_cost = db.Column(db.Float, comment='人工成本')
    other_cost = db.Column(db.Float, comment='其他成本')
    dept_estimate_cost = db.Column(db.String(255), comment='部门预估成本')
    dept_id = db.Column(db.Integer, db.ForeignKey('project_manage_dept.id'), comment='项目类型id')
    dept_ids = db.Column(db.String(255), comment='参与部门ID列表')
    project_manager = db.Column(db.String(50), comment='项目负责人')
    delivery_date = db.Column(db.Date, comment='计划交货时间')
    contract_start_date = db.Column(db.Date, comment='合同开始日期')
    prepayment_days = db.Column(db.Integer, default=0, comment='预付款天数')
    delivery_payment_days = db.Column(db.Integer, default=0, comment='发货款天数')
    acceptance_payment_days = db.Column(db.Integer, default=0, comment='验收款天数')
    warranty_payment_days = db.Column(db.Integer, default=0, comment='质保金天数')
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='创建时间')
    user_estimates = db.relationship('UserEstimate', backref='project', lazy=True)
    departments = db.relationship('Dept', secondary='project_dept', backref='projects', lazy='joined')


class UserEstimate(db.Model):
    __tablename__ = 'user_estimate'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), nullable=False)
    estimate_cost = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)


# 添加关联表
project_dept = db.Table('project_dept',
    db.Column('project_id', db.Integer, db.ForeignKey('import_project.id'), primary_key=True),
    db.Column('dept_id', db.Integer, db.ForeignKey('admin_dept.id'), primary_key=True)
)


class ProjectEstimate(db.Model):
    __tablename__ = 'project_estimate'
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), nullable=False)
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False)
    project_type_id = db.Column(db.Integer, db.ForeignKey('project_manage_dept.id'), nullable=False)
    location = db.Column(db.String(255), nullable=False)
    estimate_hours = db.Column(db.Float, nullable=False)

    # 新增成本字段（对应Excel的F、G、H、I列）
    labor_cost = db.Column(db.Float, default=0, comment='预估人工成本')
    mechanical_bom_cost = db.Column(db.Float, default=0, comment='预估机械BOM成本')
    electrical_bom_cost = db.Column(db.Float, default=0, comment='预估电气BOM成本')
    other_cost = db.Column(db.Float, default=0, comment='预估其他成本')

    created_at = db.Column(db.DateTime, default=datetime.datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    project = db.relationship('Import_project', backref='estimates')
    dept = db.relationship('Dept', backref='project_estimates')
    project_type = db.relationship('ProjectManageDept', backref='project_estimates')

    # 添加唯一约束，确保同一项目、部门、类型、地点只有一条记录
    __table_args__ = (
        db.UniqueConstraint('project_id', 'dept_id', 'project_type_id', 'location',
                          name='uix_project_dept_type_location'),
    )





