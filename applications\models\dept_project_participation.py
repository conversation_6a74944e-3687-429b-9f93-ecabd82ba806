import datetime
from applications.extensions import db


class DeptProjectParticipation(db.Model):
    """部门项目参与比例模型"""
    __tablename__ = 'dept_project_participation'
    
    id = db.Column(db.Integer, primary_key=True, comment='主键ID')
    project_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('import_project.id'), nullable=False, comment='项目ID')
    dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=False, comment='部门ID')
    participation_rate = db.Column(db.Numeric(5,2), nullable=False, comment='参与比例(0.00-100.00)')
    remark = db.Column(db.String(255), comment='备注')
    created_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 建立关联关系
    project = db.relationship('Import_project', backref='dept_participations', lazy=True)
    dept = db.relationship('Dept', backref='project_participations', lazy=True)
    creator = db.relationship('User', foreign_keys=[created_by], lazy=True)
    
    # 建立唯一约束：每个项目每个部门只能有一条参与比例记录
    __table_args__ = (
        db.UniqueConstraint('project_id', 'dept_id', name='uk_project_dept_participation'),
    )
    
    def __repr__(self):
        return f'<DeptProjectParticipation {self.project_id}-{self.dept_id}: {self.participation_rate}%>'
    
    def to_dict(self):
        """转换为字典格式，便于JSON序列化"""
        try:
            return {
                'id': self.id,
                'project_id': self.project_id,
                'dept_id': self.dept_id,
                'participation_rate': float(self.participation_rate) if self.participation_rate else 0,
                'remark': self.remark,
                'created_by': self.created_by,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
                'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
                # 关联对象信息 - 使用安全的访问方式
                'project_name': getattr(self.project, 'project_name', None) if hasattr(self, 'project') and self.project else None,
                'project_code': getattr(self.project, 'project_code', None) if hasattr(self, 'project') and self.project else None,
                'dept_name': getattr(self.dept, 'dept_name', None) if hasattr(self, 'dept') and self.dept else None,
                'creator_name': getattr(self.creator, 'realname', None) if hasattr(self, 'creator') and self.creator else None
            }
        except Exception as e:
            print(f"❌ DeptProjectParticipation.to_dict() 错误: {str(e)}")
            # 返回基本信息，避免关联对象访问错误
            return {
                'id': self.id,
                'project_id': self.project_id,
                'dept_id': self.dept_id,
                'participation_rate': float(self.participation_rate) if self.participation_rate else 0,
                'remark': self.remark,
                'created_by': self.created_by,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
                'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
                'project_name': None,
                'project_code': None,
                'dept_name': None,
                'creator_name': None
            }
