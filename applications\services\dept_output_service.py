"""
部门单位产出分析服务
处理部门产出计算的核心业务逻辑
"""
import datetime
import hashlib
import json
from functools import wraps
from sqlalchemy import func, or_, and_
from applications.extensions.init_sqlalchemy import db
from applications.models.import_project import Import_project
from applications.models.project_progress import ProjectProgress
from applications.models.project_manage_dept import ProjectManageDept
from applications.models.project_cost import OtherCostImport, ProjectIncentive
from applications.models.save_log import LogInfo
from applications.models.admin_yg import ygong
from applications.models.admin_dept import Dept
from applications.models.dept_output_target import DeptOutputTarget
from applications.models.dept_project_participation import DeptProjectParticipation

# 简单的内存缓存（生产环境建议使用Redis）
_cache = {}
_cache_timestamps = {}


def clear_dept_output_cache():
    """
    清理部门产出分析相关的缓存
    用于解决参与比例更新后缓存不一致的问题
    """
    global _cache, _cache_timestamps

    # 记录清理前的缓存数量
    cache_count = len(_cache)

    # 清理所有缓存
    _cache.clear()
    _cache_timestamps.clear()

    # 同时清理排除部门名称缓存（如果函数已定义）
    try:
        clear_excluded_dept_cache()
    except NameError:
        # 函数还未定义时忽略（模块加载阶段）
        pass

    print(f"✅ 部门产出分析缓存已清理，共清理了 {cache_count} 个缓存项")
    print("🏢 排除部门名称缓存已清理，将重新从数据库加载")

    return {
        'success': True,
        'cleared_count': cache_count,
        'message': f'成功清理了 {cache_count} 个缓存项，包括排除部门配置缓存'
    }


# 注释掉立即清理缓存，避免模块加载时的函数未定义错误
# clear_dept_output_cache()

def simple_cache(expire_seconds=300):
    """
    简单的内存缓存装饰器
    Args:
        expire_seconds: 缓存过期时间（秒），默认5分钟
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hashlib.md5(str(args).encode() + str(kwargs).encode()).hexdigest()}"

            # 检查缓存是否存在且未过期
            current_time = datetime.datetime.now().timestamp()
            if (cache_key in _cache and
                cache_key in _cache_timestamps and
                current_time - _cache_timestamps[cache_key] < expire_seconds):
                return _cache[cache_key]

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            _cache[cache_key] = result
            _cache_timestamps[cache_key] = current_time

            # 清理过期缓存（简单策略）
            if len(_cache) > 100:  # 限制缓存大小
                expired_keys = [
                    key for key, timestamp in _cache_timestamps.items()
                    if current_time - timestamp > expire_seconds
                ]
                for key in expired_keys:
                    _cache.pop(key, None)
                    _cache_timestamps.pop(key, None)

            return result
        return wrapper
    return decorator

# 缓存排除部门名称列表，避免频繁查询数据库
_excluded_dept_names_cache = None
_cache_timestamp = None
CACHE_EXPIRE_SECONDS = 300  # 缓存5分钟


def get_excluded_dept_names():
    """
    从数据库动态获取不参与部门产出计算的部门名称列表

    Returns:
        list: 排除的部门名称列表
    """
    global _excluded_dept_names_cache, _cache_timestamp

    import time
    from applications.models.admin_dict import DictData

    # 检查缓存是否有效
    current_time = time.time()
    if (_excluded_dept_names_cache is not None and
        _cache_timestamp is not None and
        current_time - _cache_timestamp < CACHE_EXPIRE_SECONDS):
        return _excluded_dept_names_cache

    try:
        # 从数据库查询排除的部门名称
        excluded_depts = DictData.query.filter_by(
            type_code='dept_output_excluded',
            enable=1
        ).all()

        # 提取部门名称列表
        dept_names = [dept.data_value for dept in excluded_depts]

        # 更新缓存
        _excluded_dept_names_cache = dept_names
        _cache_timestamp = current_time

        return dept_names

    except Exception as e:
        # 如果数据库查询失败，返回默认的硬编码列表作为备用
        print(f"获取排除部门列表失败，使用默认配置: {str(e)}")
        default_excluded = ['品质部', '财务部', '人事行政部', '总经办', '销售部','售后部','制造部']

        # 更新缓存为默认值
        _excluded_dept_names_cache = default_excluded
        _cache_timestamp = current_time

        return default_excluded


def clear_excluded_dept_cache():
    """
    清除排除部门名称缓存
    """
    global _excluded_dept_names_cache, _cache_timestamp
    _excluded_dept_names_cache = None
    _cache_timestamp = None


def filter_excluded_depts(dept_ids):
    """
    过滤掉不参与产出计算的部门

    Args:
        dept_ids: 部门ID列表

    Returns:
        list: 过滤后的部门ID列表
    """
    if not dept_ids:
        return []

    # 动态获取需要排除的部门名称列表
    excluded_dept_names = get_excluded_dept_names()

    # 查询需要排除的部门ID
    excluded_depts = Dept.query.filter(
        Dept.dept_name.in_(excluded_dept_names),
        Dept.status == 1
    ).all()
    excluded_dept_ids = [dept.id for dept in excluded_depts]

    # 过滤掉排除的部门
    filtered_dept_ids = [dept_id for dept_id in dept_ids if dept_id not in excluded_dept_ids]

    return filtered_dept_ids


def get_filtered_depts_query():
    """
    获取过滤掉排除部门的查询对象

    Returns:
        Query: 过滤后的部门查询对象
    """
    # 动态获取需要排除的部门名称列表
    excluded_dept_names = get_excluded_dept_names()

    return Dept.query.filter(
        ~Dept.dept_name.in_(excluded_dept_names),
        Dept.status == 1
    )



@simple_cache(expire_seconds=300)  # 5分钟缓存
def get_dept_shipped_projects(dept_ids, start_date=None, end_date=None):
    """
    获取部门相关的入库完成项目（优化版本）

    计算范围包括：
    1. 部门员工参与的已完成入库项目
    2. 部门有差旅费支出的项目
    3. 部门有项目激励支出的项目
    4. 部门有外协费支出的项目

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        list: 入库完成项目列表
    """
    if not dept_ids:
        return []

    shipped_projects = []
    processed_project_ids = set()

    # 生成时间范围内的月份列表（用于成本数据查询）
    months = []
    if start_date and end_date:
        current = start_date.replace(day=1)
        while current <= end_date:
            month_str_no_zero = f"{current.year}-{current.month}"
            month_str_with_zero = current.strftime('%Y-%m')
            months.extend([month_str_no_zero, month_str_with_zero])

            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

    # 1. 获取部门员工参与的项目（优化查询）
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if dept_employees:
        employee_ids = [emp.employee_id for emp in dept_employees]
        employee_names = [emp.name for emp in dept_employees if emp.name]

        # 使用联表查询一次性获取员工参与的已完成项目
        employee_projects_query = db.session.query(Import_project).join(
            ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
        ).outerjoin(
            ProjectProgress, Import_project.id == ProjectProgress.project_id
        ).join(
            LogInfo, and_(
                LogInfo.projectPrefix == ProjectManageDept.dept_name,
                LogInfo.projectNumber == Import_project.project_code
            )
        ).filter(
            LogInfo.employee_id.in_(employee_ids),
            ~LogInfo.projectPrefix.in_(['会议', '其它', '方案支持', '培训']),
            or_(
                and_(
                    ProjectProgress.warehouse_completion_date.isnot(None),
                    ProjectProgress.warehouse_completion_date <= datetime.date.today()
                ),
                Import_project.project_status >= 3
            )
        ).distinct()

        if start_date and end_date:
            employee_projects_query = employee_projects_query.filter(
                LogInfo.work_date.between(start_date, end_date)
            )

        employee_projects = employee_projects_query.all()
        for project in employee_projects:
            if project.id not in processed_project_ids:
                processed_project_ids.add(project.id)
                shipped_projects.append(project)

    # 2. 获取部门有差旅费/外协费支出的项目（优化查询）
    if months:
        cost_projects = db.session.query(Import_project).join(
            OtherCostImport, Import_project.id == OtherCostImport.project_id
        ).outerjoin(
            ProjectProgress, Import_project.id == ProjectProgress.project_id
        ).filter(
            OtherCostImport.dept_id.in_(dept_ids),
            OtherCostImport.cost_type.in_(['travel', 'outsource']),
            OtherCostImport.year_month.in_(months),
            or_(
                and_(
                    ProjectProgress.warehouse_completion_date.isnot(None),
                    ProjectProgress.warehouse_completion_date <= datetime.date.today()
                ),
                Import_project.project_status >= 3
            )
        ).distinct().all()

        for project in cost_projects:
            if project.id not in processed_project_ids:
                processed_project_ids.add(project.id)
                shipped_projects.append(project)

    # 3. 获取部门有项目激励支出的项目（优化查询）
    if months and dept_employees:
        employee_names = [emp.name for emp in dept_employees if emp.name]
        if employee_names:
            incentive_projects = db.session.query(Import_project).join(
                ProjectIncentive, Import_project.id == ProjectIncentive.project_id
            ).outerjoin(
                ProjectProgress, Import_project.id == ProjectProgress.project_id
            ).filter(
                ProjectIncentive.employee_name.in_(employee_names),
                ProjectIncentive.year_month.in_(months),
                or_(
                    and_(
                        ProjectProgress.warehouse_completion_date.isnot(None),
                        ProjectProgress.warehouse_completion_date <= datetime.date.today()
                    ),
                    Import_project.project_status >= 3
                )
            ).distinct().all()

            for project in incentive_projects:
                if project.id not in processed_project_ids:
                    processed_project_ids.add(project.id)
                    shipped_projects.append(project)

    return shipped_projects


@simple_cache(expire_seconds=300)  # 5分钟缓存
def get_dept_project_details(dept_ids, start_date=None, end_date=None):
    """
    获取部门相关项目的详细信息，包含关联方式（优化版本）

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        list: 项目详细信息列表，包含关联方式
    """
    if not dept_ids:
        return []

    project_details = []
    processed_projects = set()  # 避免重复

    # 生成时间范围内的月份列表
    months = []
    if start_date and end_date:
        current = start_date.replace(day=1)
        while current <= end_date:
            month_str_no_zero = f"{current.year}-{current.month}"
            month_str_with_zero = current.strftime('%Y-%m')
            months.extend([month_str_no_zero, month_str_with_zero])

            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

    # 1. 获取部门员工参与的项目（优化查询）
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if dept_employees:
        employee_ids = [emp.employee_id for emp in dept_employees]
        employee_names = [emp.name for emp in dept_employees if emp.name]

        # 优化：使用联表查询一次性获取项目信息
        employee_projects_query = db.session.query(
            Import_project,
            ProjectManageDept.dept_name.label('project_type_name'),
            ProjectProgress.warehouse_completion_date
        ).join(
            ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
        ).outerjoin(
            ProjectProgress, Import_project.id == ProjectProgress.project_id
        ).join(
            LogInfo, and_(
                LogInfo.projectPrefix == ProjectManageDept.dept_name,
                LogInfo.projectNumber == Import_project.project_code
            )
        ).filter(
            LogInfo.employee_id.in_(employee_ids),
            ~LogInfo.projectPrefix.in_(['会议', '其它', '方案支持', '培训']),
            or_(
                and_(
                    ProjectProgress.warehouse_completion_date.isnot(None),
                    ProjectProgress.warehouse_completion_date <= datetime.date.today()
                ),
                Import_project.project_status >= 3
            )
        ).distinct()

        if start_date and end_date:
            employee_projects_query = employee_projects_query.filter(
                LogInfo.work_date.between(start_date, end_date)
            )

        employee_projects = employee_projects_query.all()

        # 处理员工参与的项目
        for project, project_type_name, _ in employee_projects:
            if project.id not in processed_projects:
                processed_projects.add(project.id)

                project_details.append({
                    'project_id': project.id,
                    'project_code': project.project_code,
                    'project_name': project.project_name,
                    'project_type': project_type_name,
                    'project_price': project.price or 0,
                    'project_status': project.project_status,
                    'relation_type': '员工参与',
                    'relation_detail': '部门员工有工时记录'
                })

    # 2. 获取部门有差旅费/外协费支出的项目（优化查询）
    if months:
        cost_projects_query = db.session.query(
            Import_project,
            ProjectManageDept.dept_name.label('project_type_name'),
            OtherCostImport.cost_type,
            func.sum(OtherCostImport.amount).label('total_amount'),
            ProjectProgress.warehouse_completion_date
        ).join(
            OtherCostImport, Import_project.id == OtherCostImport.project_id
        ).join(
            ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
        ).outerjoin(
            ProjectProgress, Import_project.id == ProjectProgress.project_id
        ).filter(
            OtherCostImport.dept_id.in_(dept_ids),
            OtherCostImport.cost_type.in_(['travel', 'outsource']),
            OtherCostImport.year_month.in_(months),
            or_(
                and_(
                    ProjectProgress.warehouse_completion_date.isnot(None),
                    ProjectProgress.warehouse_completion_date <= datetime.date.today()
                ),
                Import_project.project_status >= 3
            )
        ).group_by(
            Import_project.id,
            ProjectManageDept.dept_name,
            OtherCostImport.cost_type,
            ProjectProgress.warehouse_completion_date
        ).all()

        for project, project_type_name, cost_type, total_amount, _ in cost_projects_query:
            if project.id not in processed_projects:
                processed_projects.add(project.id)

                cost_type_name = '差旅费' if cost_type == 'travel' else '外协费'

                project_details.append({
                    'project_id': project.id,
                    'project_code': project.project_code,
                    'project_name': project.project_name,
                    'project_type': project_type_name,
                    'project_price': project.price or 0,
                    'project_status': project.project_status,
                    'relation_type': f'{cost_type_name}支出',
                    'relation_detail': f'部门{cost_type_name}支出: {total_amount:.2f}元'
                })

    # 3. 获取部门有项目激励支出的项目（优化查询）
    if months and dept_employees:
        employee_names = [emp.name for emp in dept_employees if emp.name]
        if employee_names:
            incentive_projects_query = db.session.query(
                Import_project,
                ProjectManageDept.dept_name.label('project_type_name'),
                func.sum(ProjectIncentive.amount).label('total_amount'),
                ProjectProgress.warehouse_completion_date
            ).join(
                ProjectIncentive, Import_project.id == ProjectIncentive.project_id
            ).join(
                ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
            ).outerjoin(
                ProjectProgress, Import_project.id == ProjectProgress.project_id
            ).filter(
                ProjectIncentive.employee_name.in_(employee_names),
                ProjectIncentive.year_month.in_(months),
                or_(
                    and_(
                        ProjectProgress.warehouse_completion_date.isnot(None),
                        ProjectProgress.warehouse_completion_date <= datetime.date.today()
                    ),
                    Import_project.project_status >= 3
                )
            ).group_by(
                Import_project.id,
                ProjectManageDept.dept_name,
                ProjectProgress.warehouse_completion_date
            ).all()

            for project, project_type_name, total_amount, _ in incentive_projects_query:
                if project.id not in processed_projects:
                    processed_projects.add(project.id)

                    project_details.append({
                        'project_id': project.id,
                        'project_code': project.project_code,
                        'project_name': project.project_name,
                        'project_type': project_type_name,
                        'project_price': project.price or 0,
                        'project_status': project.project_status,
                        'relation_type': '项目激励',
                        'relation_detail': f'部门员工项目激励: {total_amount:.2f}元'
                    })

    return project_details

# 注意：_find_completed_project 和 _is_project_completed 函数已被优化的联表查询替代


def calculate_dept_project_labor_cost(dept_ids, start_date, end_date):
    """
    计算部门员工的项目人工成本（基于员工应发工资）

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        dict: 包含工时和成本信息
    """
    # 1. 获取部门员工
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if not dept_employees:
        return {'work_hours': 0, 'labor_cost': 0, 'project_details': []}

    employee_ids = [emp.employee_id for emp in dept_employees]

    # 2. 统计员工工时（排除非项目工时）
    work_logs = LogInfo.query.filter(
        LogInfo.employee_id.in_(employee_ids),
        LogInfo.work_date.between(start_date, end_date),
        ~LogInfo.projectPrefix.in_(['会议', '其它', '方案支持', '培训'])
    ).all()

    # 3. 按项目分组统计工时
    project_hours = {}
    total_project_hours = 0

    for log in work_logs:
        project_key = f"{log.projectPrefix}-{log.projectNumber}"
        if project_key not in project_hours:
            project_hours[project_key] = {
                'hours': 0,
                'prefix': log.projectPrefix,
                'number': log.projectNumber
            }
        hours = log.regularWorkingHours + log.overtimeWorkingHours
        project_hours[project_key]['hours'] += hours
        total_project_hours += hours

    # 4. 获取部门员工的总薪资成本
    total_salary_cost = calculate_dept_salary_cost(dept_ids, start_date, end_date)

    # 5. 按工时比例分摊薪资成本到各项目
    project_details = []
    total_allocated_cost = 0

    for project_key, project_info in project_hours.items():
        prefix = project_info['prefix']
        number = project_info['number']
        hours = project_info['hours']

        # 查找项目信息
        project = db.session.query(Import_project)\
            .join(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id)\
            .filter(
                ProjectManageDept.dept_name == prefix,
                Import_project.project_code == number
            ).first()

        # 按工时比例分摊薪资成本
        if total_project_hours > 0:
            project_cost = (hours / total_project_hours) * total_salary_cost
        else:
            project_cost = 0

        total_allocated_cost += project_cost

        project_details.append({
            'project_name': project.project_name if project else f"{prefix}-{number}",
            'project_code': number,
            'project_type': prefix,
            'work_hours': hours,
            'labor_cost': project_cost
        })

    return {
        'work_hours': total_project_hours,
        'labor_cost': total_salary_cost,  # 使用实际薪资总额
        'project_details': project_details
    }


def calculate_dept_other_costs(dept_ids, start_date, end_date):
    """
    计算部门其他成本（差旅费、外协费）

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        dict: 其他成本信息
    """
    # 生成时间范围内的月份列表
    months = []
    current = start_date.replace(day=1)
    while current <= end_date:
        # 生成两种格式：YYYY-M 和 YYYY-MM
        month_str_no_zero = f"{current.year}-{current.month}"  # 2025-5
        month_str_with_zero = current.strftime('%Y-%m')  # 2025-05
        months.extend([month_str_no_zero, month_str_with_zero])

        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)

    # 直接查询该部门的差旅费和外协费（按部门ID和时间范围）
    other_costs = db.session.query(
        OtherCostImport.cost_type,
        func.sum(OtherCostImport.amount)
    ).filter(
        OtherCostImport.dept_id.in_(dept_ids),
        OtherCostImport.cost_type.in_(['travel', 'outsource']),
        OtherCostImport.year_month.in_(months)  # 使用year_month字段而不是created_at
    ).group_by(OtherCostImport.cost_type).all()

    cost_dict = dict(other_costs)
    travel_cost = cost_dict.get('travel', 0)
    outsource_cost = cost_dict.get('outsource', 0)

    return {
        'travel_cost': travel_cost,
        'outsource_cost': outsource_cost,
        'total_other_cost': travel_cost + outsource_cost
    }


def get_dept_project_price_with_participation(dept_ids, shipped_projects):
    """
    根据部门参与比例计算部门应分摊的项目价格

    Args:
        dept_ids: 部门ID列表
        shipped_projects: 入库完成项目列表

    Returns:
        float: 按参与比例分摊后的项目总价格
    """
    total_allocated_price = 0

    for project in shipped_projects:
        project_price = project.price or 0
        if project_price <= 0:
            continue

        # 查询该项目的部门参与比例
        participations = DeptProjectParticipation.query.filter(
            DeptProjectParticipation.project_id == project.id,
            DeptProjectParticipation.dept_id.in_(dept_ids)
        ).all()

        if participations:
            # 如果有设置参与比例，按比例分摊
            for participation in participations:
                allocated_price = project_price * (float(participation.participation_rate) / 100)
                total_allocated_price += allocated_price
        else:
            # 如果没有设置参与比例，默认100%（向后兼容）
            # 由于项目已经在shipped_projects中，说明该部门参与了该项目
            total_allocated_price += project_price

    return total_allocated_price


@simple_cache(expire_seconds=600)  # 10分钟缓存（主计算函数缓存时间更长）
def calculate_dept_unit_output(dept_ids, start_date, end_date):
    """
    计算部门单位产出

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        dict: 部门单位产出分析结果
    """
    # 1. 获取入库完成项目总价格（分子）
    shipped_projects = get_dept_shipped_projects(dept_ids, start_date, end_date)

    # 使用参与比例计算分摊后的项目价格
    total_project_price = get_dept_project_price_with_participation(dept_ids, shipped_projects)

    # 2. 计算部门总开支（分母）
    # 2.1 人工成本
    try:
        labor_cost_info = calculate_dept_project_labor_cost(dept_ids, start_date, end_date)
        labor_cost = labor_cost_info['labor_cost']
    except Exception:
        labor_cost = 0
        labor_cost_info = {
            'work_hours': 0,
            'labor_cost': labor_cost,
            'project_details': []
        }

    # 2.2 其他成本（差旅费、外协费）
    try:
        other_cost_info = calculate_dept_other_costs(dept_ids, start_date, end_date)
        travel_cost = other_cost_info['travel_cost']
        outsource_cost = other_cost_info['outsource_cost']
    except Exception:
        travel_cost = 0
        outsource_cost = 0

    # 2.3 项目激励费用
    try:
        project_incentive_cost = calculate_dept_project_incentive_cost(dept_ids, start_date, end_date)
    except Exception:
        project_incentive_cost = 0

    # 2.4 社保和公积金单位扣款
    try:
        insurance_cost_info = calculate_dept_social_insurance_cost(dept_ids, start_date, end_date)
        social_insurance_cost = insurance_cost_info['social_insurance_cost']
        housing_fund_cost = insurance_cost_info['housing_fund_cost']
    except Exception:
        social_insurance_cost = 0
        housing_fund_cost = 0

    # 3. 计算总开支和单位产出
    total_expense = labor_cost + travel_cost + outsource_cost + project_incentive_cost + social_insurance_cost + housing_fund_cost
    unit_output = total_project_price / total_expense if total_expense > 0 else 0
    
    return {
        'shipped_projects': shipped_projects,  # 注：此处保持原变量名以兼容现有代码
        'total_project_price': total_project_price,
        'labor_cost': labor_cost,
        'travel_cost': travel_cost,
        'outsource_cost': outsource_cost,
        'project_incentive_cost': project_incentive_cost,
        'social_insurance_cost': social_insurance_cost,
        'housing_fund_cost': housing_fund_cost,
        'total_expense': total_expense,
        'unit_output': unit_output,
        'project_count': len(shipped_projects),
        'work_hours': labor_cost_info['work_hours'],
        'project_details': get_dept_project_details(dept_ids, start_date, end_date)
    }


def get_dept_output_summary(dept_ids, start_date, end_date):
    """
    获取部门产出汇总信息（用于图表展示）

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        dict: 汇总信息
    """
    result = calculate_dept_unit_output(dept_ids, start_date, end_date)

    # 按月份统计（如果时间跨度超过1个月）
    monthly_data = []
    try:
        if (end_date - start_date).days > 31:
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                # 计算当月最后一天
                if current_date.month == 12:
                    next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    next_month = current_date.replace(month=current_date.month + 1, day=1)

                month_end = next_month - datetime.timedelta(days=1)
                if month_end > end_date:
                    month_end = end_date

                monthly_result = calculate_dept_unit_output(dept_ids, current_date, month_end)
                monthly_data.append({
                    'month': current_date.strftime('%Y-%m'),
                    'unit_output': round(monthly_result['unit_output'], 4),
                    'total_expense': round(monthly_result['total_expense'], 2),
                    'project_price': round(monthly_result['total_project_price'], 2)
                })

                current_date = next_month
    except Exception:
        # 如果月度计算失败，返回空的月度数据
        monthly_data = []

    result['monthly_data'] = monthly_data
    return result


def get_dept_output_by_dept(accessible_dept_ids, project_type=None, project_code=None, project_status=None):
    """
    按部门分别获取产出数据
    """
    # 过滤掉不参与产出计算的部门
    filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)

    dept_results = []

    for dept_id in filtered_dept_ids:
        # 获取部门信息
        dept = Dept.query.get(dept_id)
        if not dept:
            continue

        # 1. 获取该部门的员工
        dept_employees = ygong.query.filter(ygong.dept_id == dept_id).all()

        if not dept_employees:
            continue

        employee_ids = [emp.employee_id for emp in dept_employees]

        # 2. 通过员工工时记录获取项目信息
        project_infos = db.session.query(
            LogInfo.projectPrefix,
            LogInfo.projectNumber
        ).filter(
            LogInfo.employee_id.in_(employee_ids),
            ~LogInfo.projectPrefix.in_(['会议', '其它', '方案支持', '培训'])  # 排除非项目工时
        ).distinct().all()

        # 3. 根据projectPrefix和projectNumber查找Import_project中的项目
        projects = []
        for prefix, number in project_infos:
            if not prefix or not number:
                continue

            # 查找项目
            project = db.session.query(Import_project)\
                .join(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id)\
                .filter(
                    ProjectManageDept.dept_name == prefix,
                    Import_project.project_code == str(number)
                ).first()

            if project and project not in projects:
                projects.append(project)

        # 4. 应用筛选条件
        filtered_projects = []
        for project in projects:
            # 项目类型筛选
            if project_type:
                # 获取项目类型名称
                project_type_obj = ProjectManageDept.query.get(project.dept_id)
                project_type_name = project_type_obj.dept_name if project_type_obj else ''
                if project_type_name != project_type:
                    continue

            # 项目编号筛选
            if project_code:
                if project_code not in project.project_code:
                    continue

            # 项目状态筛选
            if project_status:
                if project.project_status != int(project_status):
                    continue
            else:
                # 默认只显示入库完成及以上状态的项目
                # 检查是否有入库完成记录（与发货项目判断逻辑保持一致）
                progress = ProjectProgress.query.filter_by(project_id=project.id).first()
                is_warehouse_completed = False

                if progress:
                    # 有入库完成日期且日期已到达
                    if (progress.warehouse_completion_date and
                        progress.warehouse_completion_date <= datetime.date.today()):
                        is_warehouse_completed = True

                # 或者项目状态>=3（入库完成）
                if project.project_status >= 3:
                    is_warehouse_completed = True

                if not is_warehouse_completed:
                    continue

            filtered_projects.append(project)

        if not filtered_projects:
            continue

        projects = filtered_projects

        # 计算部门产出数据
        try:
            # 使用动态时间范围：从最早的数据开始到当前日期
            from applications.models.employee_salary import EmployeeSalary
            from applications.models.project_cost import OtherCostImport
            from applications.models.project_cost import ProjectIncentive

            # 获取各种数据的最早时间
            earliest_dates = []

            # 1. 最早的薪资数据月份
            earliest_salary = EmployeeSalary.query.order_by(EmployeeSalary.month.asc()).first()
            if earliest_salary:
                month_parts = earliest_salary.month.split('-')
                start_year = int(month_parts[0])
                start_month = int(month_parts[1])
                earliest_dates.append(datetime.date(start_year, start_month, 1))

            # 2. 最早的差旅外协费数据月份
            earliest_other_cost = OtherCostImport.query.order_by(OtherCostImport.year_month.asc()).first()
            if earliest_other_cost:
                month_parts = earliest_other_cost.year_month.split('-')
                start_year = int(month_parts[0])
                start_month = int(month_parts[1])
                earliest_dates.append(datetime.date(start_year, start_month, 1))

            # 3. 最早的项目激励数据月份
            earliest_incentive = ProjectIncentive.query.order_by(ProjectIncentive.year_month.asc()).first()
            if earliest_incentive:
                month_parts = earliest_incentive.year_month.split('-')
                start_year = int(month_parts[0])
                start_month = int(month_parts[1])
                earliest_dates.append(datetime.date(start_year, start_month, 1))

            # 使用最早的日期作为开始时间
            if earliest_dates:
                start_date = min(earliest_dates)
            else:
                # 如果没有任何数据，使用当前年份
                current_year = datetime.datetime.now().year
                start_date = datetime.date(current_year, 1, 1)

            # 结束日期使用当前日期
            end_date = datetime.date.today()

            dept_result = calculate_dept_unit_output([dept_id], start_date, end_date)
        except Exception:
            # 使用默认值
            dept_result = {
                'unit_output': 0,
                'total_project_price': 0,
                'total_expense': 0,
                'labor_cost': 0,
                'travel_cost': 0,
                'outsource_cost': 0,
                'project_incentive_cost': 0,
                'social_insurance_cost': 0,
                'housing_fund_cost': 0,
                'work_hours': 0,
                'project_details': []
            }

        # 获取详细的项目数据（包含关联方式）
        project_list = get_dept_project_details([dept_id], start_date, end_date)

        # 按项目编号排序
        project_list = sorted(project_list, key=lambda x: x.get('project_code', ''))

        dept_results.append({
            'dept_id': dept_id,
            'dept_name': dept.dept_name,
            'unit_output': dept_result['unit_output'],
            'total_project_price': dept_result['total_project_price'],
            'total_expense': dept_result['total_expense'],
            'labor_cost': dept_result['labor_cost'],
            'travel_cost': dept_result['travel_cost'],
            'outsource_cost': dept_result['outsource_cost'],
            'project_incentive_cost': dept_result['project_incentive_cost'],
            'social_insurance_cost': dept_result['social_insurance_cost'],
            'housing_fund_cost': dept_result['housing_fund_cost'],
            'project_count': len(projects),
            'projects': project_list  # 只保留项目明细表格需要的数据
        })

    return dept_results


def calculate_dept_salary_cost(dept_ids, start_date, end_date):
    """
    计算部门员工的应发工资总额
    """
    from applications.models.employee_salary import EmployeeSalary
    from applications.models.admin_yg import ygong

    # 获取部门员工
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if not dept_employees:
        return 0

    employee_ids = [emp.id for emp in dept_employees]  # 注意这里是emp.id，不是emp.employee_id

    # 生成时间范围内的所有月份
    months = []
    current = start_date.replace(day=1)
    while current <= end_date:
        # 生成两种格式：YYYY-M 和 YYYY-MM
        month_str_no_zero = f"{current.year}-{current.month}"  # 2025-5
        month_str_with_zero = current.strftime('%Y-%m')  # 2025-05
        months.extend([month_str_no_zero, month_str_with_zero])

        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)

    # 查询时间范围内的工资记录
    salary_records = EmployeeSalary.query.filter(
        EmployeeSalary.employee_id.in_(employee_ids),
        EmployeeSalary.month.in_(months)
    ).all()

    total_salary = sum(record.should_pay or 0 for record in salary_records)

    return total_salary


def calculate_dept_travel_cost(dept_ids, start_date, end_date):
    """
    计算部门的差旅费（直接按部门查询）
    """
    from applications.models.project_cost import OtherCostImport

    # 直接按部门ID查询差旅费
    travel_costs = db.session.query(
        func.sum(OtherCostImport.amount)
    ).filter(
        OtherCostImport.dept_id.in_(dept_ids),
        OtherCostImport.cost_type == 'travel',
        OtherCostImport.created_at.between(start_date, end_date)
    ).scalar()

    return travel_costs or 0


def calculate_dept_outsource_cost(dept_ids, start_date, end_date):
    """
    计算部门的外协费（直接按部门查询）
    """
    from applications.models.project_cost import OtherCostImport

    # 查询外协费（从OtherCostImport表，按部门）
    outsource_costs = db.session.query(
        func.sum(OtherCostImport.amount)
    ).filter(
        OtherCostImport.dept_id.in_(dept_ids),
        OtherCostImport.cost_type == 'outsource',
        OtherCostImport.created_at.between(start_date, end_date)
    ).scalar() or 0

    return outsource_costs


def calculate_dept_project_incentive_cost(dept_ids, start_date, end_date):
    """
    计算部门的项目激励费用

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        float: 项目激励总费用
    """
    from applications.models.project_cost import ProjectIncentive
    from applications.models.admin_yg import ygong

    # 1. 获取部门员工姓名列表
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if not dept_employees:
        return 0

    employee_names = [emp.name for emp in dept_employees if emp.name]

    if not employee_names:
        return 0

    # 2. 生成时间范围内的月份列表
    months = []
    current = start_date.replace(day=1)
    while current <= end_date:
        # 生成两种格式：YYYY-M 和 YYYY-MM
        month_str_no_zero = f"{current.year}-{current.month}"  # 2025-5
        month_str_with_zero = current.strftime('%Y-%m')  # 2025-05
        months.extend([month_str_no_zero, month_str_with_zero])

        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)

    # 3. 直接通过员工姓名查询项目激励费用
    incentive_costs = db.session.query(
        func.sum(ProjectIncentive.amount)
    ).filter(
        ProjectIncentive.employee_name.in_(employee_names),
        ProjectIncentive.year_month.in_(months)
    ).scalar() or 0

    return incentive_costs


def calculate_dept_social_insurance_cost(dept_ids, start_date, end_date):
    """
    计算部门的社保和公积金单位扣款费用

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        dict: 包含社保和公积金费用的字典
    """
    from applications.models.social_insurance import SocialInsurance
    from applications.models.admin_yg import ygong

    # 1. 获取部门员工
    dept_employees = ygong.query.filter(ygong.dept_id.in_(dept_ids)).all()
    if not dept_employees:
        return {'social_insurance_cost': 0, 'housing_fund_cost': 0, 'total_insurance_cost': 0}

    employee_ids = [emp.employee_id for emp in dept_employees]

    # 2. 生成时间范围内的月份列表
    months = []
    current = start_date.replace(day=1)
    while current <= end_date:
        # 生成两种格式：YYYY-M 和 YYYY-MM
        month_str_no_zero = f"{current.year}-{current.month}"  # 2025-5
        month_str_with_zero = current.strftime('%Y-%m')  # 2025-05
        months.extend([month_str_no_zero, month_str_with_zero])

        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)

    # 3. 查询社保和公积金单位扣款
    insurance_records = SocialInsurance.query.filter(
        SocialInsurance.employee_id.in_(employee_ids),
        SocialInsurance.month.in_(months)
    ).all()

    social_insurance_cost = sum(record.social_insurance_deduction or 0 for record in insurance_records)
    housing_fund_cost = sum(record.housing_fund_deduction or 0 for record in insurance_records)
    total_insurance_cost = social_insurance_cost + housing_fund_cost

    return {
        'social_insurance_cost': social_insurance_cost,
        'housing_fund_cost': housing_fund_cost,
        'total_insurance_cost': total_insurance_cost
    }


def get_project_status_name(status):
    """从数据字典中获取项目状态名称"""
    from applications.models.admin_dict import DictData

    try:
        # 查询项目状态字典数据
        status_dict = DictData.query.filter_by(
            type_code='project_status',
            data_value=str(status),
            enable=1
        ).first()

        if status_dict:
            return status_dict.data_label
        else:
            return f'状态{status}'
    except Exception:
        # 如果查询失败，返回默认值
        return f'状态{status}'


# ==================== 目标值相关服务函数 ====================

def get_dept_output_with_target(dept_ids, start_date, end_date, year=None):
    """
    获取部门产出数据并包含目标值对比

    Args:
        dept_ids: 部门ID列表
        start_date: 开始日期
        end_date: 结束日期
        year: 目标值年度（可选，默认使用当前年度）

    Returns:
        dict: 包含实际产出和目标值对比的数据
    """
    # 1. 获取实际产出数据
    actual_data = calculate_dept_unit_output(dept_ids, start_date, end_date)

    # 2. 获取目标值数据
    if year is None:
        year = datetime.datetime.now().year

    target_data = {}
    for dept_id in dept_ids:
        target = DeptOutputTarget.get_by_dept_year(dept_id, year)
        if target:
            target_data[dept_id] = {
                'target_value': float(target.target_value),
                'remark': target.remark
            }

    # 3. 计算对比数据
    comparison_data = {
        'actual_output': actual_data['unit_output'],
        'target_output': 0,
        'achievement_rate': 0,
        'target_details': target_data
    }

    # 计算加权平均目标值（如果有多个部门）
    if target_data:
        total_target = sum(data['target_value'] for data in target_data.values())
        comparison_data['target_output'] = total_target / len(target_data)

        # 计算完成率
        if comparison_data['target_output'] > 0:
            comparison_data['achievement_rate'] = (
                comparison_data['actual_output'] / comparison_data['target_output'] * 100
            )

    # 4. 合并数据
    result = actual_data.copy()
    result['target_comparison'] = comparison_data

    return result


def get_dept_targets_summary(dept_ids, year=None):
    """
    获取部门目标值汇总信息

    Args:
        dept_ids: 部门ID列表
        year: 年度（可选，默认当前年度）

    Returns:
        list: 部门目标值列表
    """
    if year is None:
        year = datetime.datetime.now().year

    targets = DeptOutputTarget.get_targets_by_year(year, dept_ids)

    result = []
    for target in targets:
        result.append({
            'dept_id': target.dept_id,
            'dept_name': target.dept.dept_name,
            'year': target.year,
            'target_value': float(target.target_value),
            'remark': target.remark,
            'created_at': target.created_at.strftime('%Y-%m-%d') if target.created_at else None
        })

    return result


def calculate_dept_output_vs_target(dept_ids, year=None):
    """
    计算部门产出与目标值的对比分析

    Args:
        dept_ids: 部门ID列表
        year: 年度（可选，默认当前年度）

    Returns:
        dict: 对比分析结果
    """
    if year is None:
        year = datetime.datetime.now().year

    # 计算年度实际产出（使用整年数据）
    start_date = datetime.date(year, 1, 1)
    end_date = datetime.date(year, 12, 31)

    results = []

    for dept_id in dept_ids:
        # 获取部门信息
        dept = Dept.query.get(dept_id)
        if not dept:
            continue

        # 计算实际产出
        actual_data = calculate_dept_unit_output([dept_id], start_date, end_date)

        # 获取目标值
        target = DeptOutputTarget.get_by_dept_year(dept_id, year)
        target_value = float(target.target_value) if target else 0

        # 计算完成率和差异
        achievement_rate = 0
        difference = 0
        conversion_score = 0  # 转化得分
        if target_value > 0:
            achievement_rate = actual_data['unit_output'] / target_value * 100
            difference = actual_data['unit_output'] - target_value
            # 计算转化得分：25 * 实际值 / 目标值，最大25分
            conversion_score = min(25, 25 * actual_data['unit_output'] / target_value)

        results.append({
            'dept_id': dept_id,
            'dept_name': dept.dept_name,
            'actual_output': round(actual_data['unit_output'], 4),
            'target_output': target_value,
            'achievement_rate': round(achievement_rate, 2),
            'difference': round(difference, 4),
            'conversion_score': round(conversion_score, 1),  # 转化得分
            'total_project_price': round(actual_data['total_project_price'], 2),
            'total_expense': round(actual_data['total_expense'], 2),
            'project_count': actual_data['project_count'],
            'target_remark': target.remark if target else '',
            'status': '达标' if achievement_rate >= 100 else '未达标' if target_value > 0 else '无目标'
        })

    return {
        'year': year,
        'dept_results': results,
        'summary': {
            'total_depts': len(results),
            'achieved_depts': len([r for r in results if r['achievement_rate'] >= 100]),
            'avg_achievement_rate': round(
                sum(r['achievement_rate'] for r in results if r['target_output'] > 0) /
                max(len([r for r in results if r['target_output'] > 0]), 1), 2
            )
        }
    }
