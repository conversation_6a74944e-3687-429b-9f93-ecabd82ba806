import datetime
from applications.extensions import db


class HREmailConfig(db.Model):
    """HR邮箱配置模型"""
    __tablename__ = 'hr_email_config'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='主键ID')
    email = db.Column(db.String(255), nullable=False, unique=True, comment='HR邮箱地址')
    description = db.Column(db.String(500), comment='邮箱描述/备注')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False, comment='是否启用：True=启用, False=禁用')
    priority = db.Column(db.Integer, default=1, comment='优先级：数字越小优先级越高')
    created_by = db.Column(db.Integer, comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_by = db.Column(db.Integer, comment='更新人ID')
    updated_at = db.Column(
        db.DateTime, 
        default=datetime.datetime.now, 
        onupdate=datetime.datetime.now, 
        comment='更新时间'
    )

    def __repr__(self):
        return f'<HREmailConfig {self.email}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'email': self.email,
            'description': self.description,
            'is_active': self.is_active,
            'priority': self.priority,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }

    @classmethod
    def get_active_emails(cls):
        """获取所有启用的HR邮箱列表，按优先级排序"""
        return cls.query.filter_by(is_active=True).order_by(cls.priority.asc(), cls.id.asc()).all()

    @classmethod
    def get_active_email_addresses(cls):
        """获取所有启用的HR邮箱地址列表"""
        active_configs = cls.get_active_emails()
        return [config.email for config in active_configs]
