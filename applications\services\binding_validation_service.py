"""
项目绑定验证服务
提供绑定操作的数据验证、冲突检测和审计日志功能
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from flask_login import current_user
from applications.extensions import db
from applications.models.quality_exception import QualityException
from applications.models.import_project import Import_project
from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
from applications.models.admin_log import AdminLog

# 配置日志
logger = logging.getLogger(__name__)


class BindingValidationService:
    """项目绑定验证服务"""
    
    def __init__(self):
        pass
    
    def validate_binding_request(self, exception_id: int, project_id: int) -> Dict:
        """
        验证绑定请求的有效性
        
        Args:
            exception_id: 质量异常单ID
            project_id: 项目ID
            
        Returns:
            dict: 验证结果
        """
        try:
            # 1. 验证异常单是否存在
            exception = QualityException.query.get(exception_id)
            if not exception:
                return {
                    'valid': False,
                    'error_code': 'EXCEPTION_NOT_FOUND',
                    'message': f'质量异常单不存在: ID={exception_id}'
                }
            
            # 2. 验证项目是否存在
            project = Import_project.query.get(project_id)
            if not project:
                return {
                    'valid': False,
                    'error_code': 'PROJECT_NOT_FOUND',
                    'message': f'项目不存在: ID={project_id}'
                }
            
            # 3. 检查是否已存在绑定
            existing_binding = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception_id,
                project_id=project_id,
                is_active=True
            ).first()
            
            if existing_binding:
                return {
                    'valid': False,
                    'error_code': 'BINDING_ALREADY_EXISTS',
                    'message': f'项目已绑定: {project.project_name}',
                    'existing_binding_id': existing_binding.id
                }
            
            # 4. 检查项目状态（如果项目有状态字段）
            # 这里可以根据实际业务需求添加项目状态检查
            
            # 5. 检查绑定数量限制（可选）
            current_bindings_count = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception_id,
                is_active=True,
                status='active'
            ).count()
            
            max_bindings = 10  # 最大绑定数量限制
            if current_bindings_count >= max_bindings:
                return {
                    'valid': False,
                    'error_code': 'MAX_BINDINGS_EXCEEDED',
                    'message': f'绑定数量已达上限({max_bindings}个)',
                    'current_count': current_bindings_count
                }
            
            # 6. 检查项目类型匹配度（警告级别）
            warnings = []
            if exception.project_type and project.dept_id:
                from applications.models.project_manage_dept import ProjectManageDept
                project_type = ProjectManageDept.query.get(project.dept_id)
                if project_type and project_type.dept_name != exception.project_type:
                    warnings.append({
                        'code': 'PROJECT_TYPE_MISMATCH',
                        'message': f'项目类型不匹配: 异常单类型={exception.project_type}, 项目类型={project_type.dept_name}'
                    })
            
            return {
                'valid': True,
                'exception': {
                    'id': exception.id,
                    'title': exception.title,
                    'project_code': exception.project_code,
                    'project_type': exception.project_type
                },
                'project': {
                    'id': project.id,
                    'name': project.project_name,
                    'code': project.project_code,
                    'type_id': project.dept_id
                },
                'warnings': warnings,
                'current_bindings_count': current_bindings_count
            }
            
        except Exception as e:
            logger.error(f"绑定验证失败: {str(e)}")
            return {
                'valid': False,
                'error_code': 'VALIDATION_ERROR',
                'message': f'验证过程中发生错误: {str(e)}'
            }
    
    def validate_batch_binding_request(self, exception_ids: List[int], project_id: int) -> Dict:
        """
        验证批量绑定请求
        
        Args:
            exception_ids: 质量异常单ID列表
            project_id: 项目ID
            
        Returns:
            dict: 验证结果
        """
        try:
            if not exception_ids:
                return {
                    'valid': False,
                    'error_code': 'EMPTY_EXCEPTION_LIST',
                    'message': '异常单列表为空'
                }
            
            if len(exception_ids) > 100:  # 批量操作数量限制
                return {
                    'valid': False,
                    'error_code': 'BATCH_SIZE_EXCEEDED',
                    'message': f'批量操作数量超限，最多支持100个，当前{len(exception_ids)}个'
                }
            
            # 验证项目是否存在
            project = Import_project.query.get(project_id)
            if not project:
                return {
                    'valid': False,
                    'error_code': 'PROJECT_NOT_FOUND',
                    'message': f'项目不存在: ID={project_id}'
                }
            
            # 验证每个异常单
            valid_exceptions = []
            invalid_exceptions = []
            already_bound = []
            
            for exception_id in exception_ids:
                validation_result = self.validate_binding_request(exception_id, project_id)
                
                if validation_result['valid']:
                    valid_exceptions.append(validation_result['exception'])
                elif validation_result['error_code'] == 'BINDING_ALREADY_EXISTS':
                    already_bound.append(exception_id)
                else:
                    invalid_exceptions.append({
                        'exception_id': exception_id,
                        'error': validation_result['message']
                    })
            
            return {
                'valid': len(valid_exceptions) > 0,
                'project': {
                    'id': project.id,
                    'name': project.project_name,
                    'code': project.project_code
                },
                'valid_exceptions': valid_exceptions,
                'invalid_exceptions': invalid_exceptions,
                'already_bound': already_bound,
                'summary': {
                    'total': len(exception_ids),
                    'valid': len(valid_exceptions),
                    'invalid': len(invalid_exceptions),
                    'already_bound': len(already_bound)
                }
            }
            
        except Exception as e:
            logger.error(f"批量绑定验证失败: {str(e)}")
            return {
                'valid': False,
                'error_code': 'BATCH_VALIDATION_ERROR',
                'message': f'批量验证过程中发生错误: {str(e)}'
            }
    
    def detect_binding_conflicts(self, exception_id: int) -> Dict:
        """
        检测绑定冲突
        
        Args:
            exception_id: 质量异常单ID
            
        Returns:
            dict: 冲突检测结果
        """
        try:
            conflicts = []
            
            # 获取异常单信息
            exception = QualityException.query.get(exception_id)
            if not exception:
                return {'conflicts': [], 'total': 0}
            
            # 获取当前绑定
            current_bindings = QualityExceptionProjectBinding.get_active_bindings(exception_id)
            
            # 1. 检查项目编号冲突
            if exception.project_code:
                # 查找其他异常单是否绑定了相同项目编号的项目
                conflicting_bindings = db.session.query(
                    QualityExceptionProjectBinding, Import_project, QualityException
                ).join(
                    Import_project, QualityExceptionProjectBinding.project_id == Import_project.id
                ).join(
                    QualityException, QualityExceptionProjectBinding.quality_exception_id == QualityException.id
                ).filter(
                    Import_project.project_code == exception.project_code,
                    QualityExceptionProjectBinding.quality_exception_id != exception_id,
                    QualityExceptionProjectBinding.is_active == True,
                    QualityExceptionProjectBinding.status == 'active'
                ).all()

                # 优化：按项目分组，避免重复警告
                project_conflicts = {}
                for binding, project, other_exception in conflicting_bindings:
                    if project.id not in project_conflicts:
                        project_conflicts[project.id] = {
                            'project': project,
                            'conflicting_exceptions': []
                        }
                    project_conflicts[project.id]['conflicting_exceptions'].append(other_exception)

                # 为每个冲突项目生成一个汇总警告
                for project_id, conflict_info in project_conflicts.items():
                    project = conflict_info['project']
                    conflicting_exceptions = conflict_info['conflicting_exceptions']
                    exception_count = len(conflicting_exceptions)

                    if exception_count == 1:
                        message = f'项目编号{project.project_code}已被异常单"{conflicting_exceptions[0].title}"绑定'
                    else:
                        message = f'项目编号{project.project_code}已被{exception_count}个其他异常单绑定'

                    conflicts.append({
                        'type': 'PROJECT_CODE_CONFLICT',
                        'message': message,
                        'conflicting_exceptions': [
                            {'id': exc.id, 'title': exc.title} for exc in conflicting_exceptions
                        ],
                        'conflicting_project': {
                            'id': project.id,
                            'name': project.project_name,
                            'code': project.project_code
                        },
                        'conflict_count': exception_count,
                        'severity': 'warning'
                    })
            
            # 2. 检查重复绑定
            project_ids = [binding.project_id for binding in current_bindings]
            duplicate_projects = []
            seen = set()
            for pid in project_ids:
                if pid in seen:
                    duplicate_projects.append(pid)
                seen.add(pid)
            
            for project_id in duplicate_projects:
                project = Import_project.query.get(project_id)
                if project:
                    conflicts.append({
                        'type': 'DUPLICATE_BINDING',
                        'message': f'项目{project.project_name}存在重复绑定',
                        'project': {
                            'id': project.id,
                            'name': project.project_name,
                            'code': project.project_code
                        },
                        'severity': 'error'
                    })
            
            # 3. 检查项目状态冲突（如果项目有状态字段）
            # 这里可以根据实际业务需求添加更多冲突检测逻辑
            
            return {
                'conflicts': conflicts,
                'total': len(conflicts),
                'has_errors': any(c['severity'] == 'error' for c in conflicts),
                'has_warnings': any(c['severity'] == 'warning' for c in conflicts)
            }
            
        except Exception as e:
            logger.error(f"冲突检测失败: {str(e)}")
            return {
                'conflicts': [],
                'total': 0,
                'error': str(e)
            }
    
    def log_binding_operation(self, operation_type: str, exception_id: int, 
                            project_id: Optional[int] = None, 
                            details: Optional[Dict] = None,
                            success: bool = True, 
                            error_message: Optional[str] = None):
        """
        记录绑定操作日志
        
        Args:
            operation_type: 操作类型 (bind, unbind, batch_bind等)
            exception_id: 质量异常单ID
            project_id: 项目ID（可选）
            details: 操作详情
            success: 是否成功
            error_message: 错误信息（如果失败）
        """
        try:
            # 构建日志描述
            desc_parts = [f'质量异常项目绑定操作: {operation_type}']
            desc_parts.append(f'异常ID={exception_id}')
            
            if project_id:
                desc_parts.append(f'项目ID={project_id}')
            
            if details:
                if 'project_name' in details:
                    desc_parts.append(f'项目名称={details["project_name"]}')
                if 'binding_type' in details:
                    desc_parts.append(f'绑定类型={details["binding_type"]}')
                if 'batch_count' in details:
                    desc_parts.append(f'批量数量={details["batch_count"]}')
            
            if not success and error_message:
                desc_parts.append(f'错误: {error_message}')
            
            desc_info = '; '.join(desc_parts)
            
            # 创建日志记录
            log_entry = AdminLog(
                method='POST' if operation_type.startswith('bind') else 'DELETE',
                url=f'/system/dindin/api/quality-exceptions/{exception_id}/bind-project',
                desc_info=desc_info,
                ip=self._get_client_ip(),
                success=1 if success else 0,
                user_id=current_user.id if current_user.is_authenticated else None,
                create_time=datetime.now()
            )
            
            db.session.add(log_entry)
            db.session.commit()
            
            logger.info(f"绑定操作日志已记录: {desc_info}")
            
        except Exception as e:
            logger.error(f"记录绑定操作日志失败: {str(e)}")
    
    def _get_client_ip(self) -> str:
        """获取客户端IP地址"""
        try:
            from flask import request
            if request.environ.get('HTTP_X_FORWARDED_FOR'):
                return request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
            elif request.environ.get('HTTP_X_REAL_IP'):
                return request.environ['HTTP_X_REAL_IP']
            else:
                return request.environ.get('REMOTE_ADDR', '127.0.0.1')
        except:
            return '127.0.0.1'
    
    def get_binding_statistics(self) -> Dict:
        """
        获取绑定统计信息
        
        Returns:
            dict: 统计信息
        """
        try:
            from sqlalchemy import func
            
            # 基础统计
            total_exceptions = QualityException.query.count()
            total_bindings = QualityExceptionProjectBinding.query.filter_by(
                is_active=True, status='active'
            ).count()
            
            # 绑定状态统计
            bound_exceptions = db.session.query(
                func.count(func.distinct(QualityExceptionProjectBinding.quality_exception_id))
            ).filter(
                QualityExceptionProjectBinding.is_active == True,
                QualityExceptionProjectBinding.status == 'active'
            ).scalar()
            
            unbound_exceptions = total_exceptions - bound_exceptions
            
            # 绑定类型统计
            binding_type_stats = db.session.query(
                QualityExceptionProjectBinding.binding_type,
                func.count(QualityExceptionProjectBinding.id).label('count')
            ).filter(
                QualityExceptionProjectBinding.is_active == True,
                QualityExceptionProjectBinding.status == 'active'
            ).group_by(QualityExceptionProjectBinding.binding_type).all()
            
            # 匹配方法统计
            match_method_stats = db.session.query(
                QualityExceptionProjectBinding.match_method,
                func.count(QualityExceptionProjectBinding.id).label('count')
            ).filter(
                QualityExceptionProjectBinding.is_active == True,
                QualityExceptionProjectBinding.status == 'active'
            ).group_by(QualityExceptionProjectBinding.match_method).all()
            
            # 平均置信度
            avg_confidence = db.session.query(
                func.avg(QualityExceptionProjectBinding.match_confidence)
            ).filter(
                QualityExceptionProjectBinding.is_active == True,
                QualityExceptionProjectBinding.status == 'active',
                QualityExceptionProjectBinding.match_confidence.isnot(None)
            ).scalar()
            
            return {
                'total_exceptions': total_exceptions,
                'total_bindings': total_bindings,
                'bound_exceptions': bound_exceptions,
                'unbound_exceptions': unbound_exceptions,
                'binding_rate': round(bound_exceptions / total_exceptions * 100, 2) if total_exceptions > 0 else 0,
                'avg_bindings_per_exception': round(total_bindings / bound_exceptions, 2) if bound_exceptions > 0 else 0,
                'binding_type_stats': {stat.binding_type: stat.count for stat in binding_type_stats},
                'match_method_stats': {stat.match_method or 'unknown': stat.count for stat in match_method_stats},
                'average_confidence': round(float(avg_confidence or 0), 3)
            }
            
        except Exception as e:
            logger.error(f"获取绑定统计失败: {str(e)}")
            return {
                'error': str(e)
            }
