"""
部门单位产出目标值序列化类
用于API数据的序列化和反序列化
"""
from flask_marshmallow.sqla import SQLAlchemyAutoSchema
from marshmallow import fields, validates, ValidationError
from applications.models.dept_output_target import DeptOutputTarget


class DeptOutputTargetSchema(SQLAlchemyAutoSchema):
    """部门产出目标值序列化类"""
    
    class Meta:
        model = DeptOutputTarget
        include_fk = True
        load_instance = True
    
    # 自定义字段
    dept_name = fields.Str(dump_only=True)
    import_user_name = fields.Str(dump_only=True)
    
    @validates('target_value')
    def validate_target_value(self, value):
        """验证目标值"""
        if value <= 0:
            raise ValidationError('目标值必须大于0')
        return value
    
    @validates('year')
    def validate_year(self, value):
        """验证年度"""
        import datetime
        current_year = datetime.datetime.now().year
        if value < 2020 or value > current_year + 5:
            raise ValidationError(f'年度必须在2020到{current_year + 5}之间')
        return value


class DeptOutputTargetListSchema(SQLAlchemyAutoSchema):
    """部门产出目标值列表序列化类（用于表格显示）"""
    
    class Meta:
        model = DeptOutputTarget
        include_fk = True
    
    dept_name = fields.Str()
    import_user_name = fields.Str()
    created_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    updated_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')


class DeptOutputTargetImportSchema(SQLAlchemyAutoSchema):
    """部门产出目标值导入序列化类"""
    
    class Meta:
        model = DeptOutputTarget
        exclude = ['id', 'created_at', 'updated_at', 'import_by', 'import_file']
        load_instance = True
    
    @validates('dept_id')
    def validate_dept_id(self, value):
        """验证部门ID是否存在"""
        from applications.models.admin_dept import Dept
        dept = Dept.query.filter_by(id=value, status=1).first()
        if not dept:
            raise ValidationError('部门不存在或已禁用')
        return value


# 实例化序列化器
dept_output_target_schema = DeptOutputTargetSchema()
dept_output_targets_schema = DeptOutputTargetSchema(many=True)
dept_output_target_list_schema = DeptOutputTargetListSchema(many=True)
dept_output_target_import_schema = DeptOutputTargetImportSchema()
