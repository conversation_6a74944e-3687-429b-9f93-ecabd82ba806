<!DOCTYPE html>
<html>
<head>
    <title>部门成本分析</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/console.css') }}"/>
    <style>
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }

        .layui-card {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .layui-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .layui-card-header.dashboard-header {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }

        .dashboard-card {
            height: 120px;
            position: relative;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .dashboard-card h2 {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .dashboard-card p {
            font-size: 14px;
            margin-bottom: 0;
            color: #666;
        }

        .dashboard-card .icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 56px;
            color: rgba(0, 0, 0, 0.1);
        }

        .dashboard-card .change-rate {
            font-weight: 600;
        }

        .text-success {
            color: #5FB878 !important;
        }

        .text-danger {
            color: #FF5722 !important;
        }

        .chart-container {
            min-height: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .project-table .layui-table-cell {
            height: auto;
            line-height: 24px;
        }

        .layui-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .layui-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
        }
        .cost-status-warning {
            background-color: #FFB800 !important;
            color: #fff;
        }
        .cost-status-critical {
            background-color: #FF5722 !important;
            color: #fff;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form" action="" lay-filter="dept-cost-form">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">部门名称</label>
                                <div class="layui-input-inline">
                                    <select name="dept_id" id="deptSelect">
                                        <option value="">全部</option>
                                        {% for dept in project_departments %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">项目状态</label>
                                <div class="layui-input-inline">
                                    <select name="status" id="projectStatus">
                                        <option value="">全部</option>
                                        {% for status in statuses %}
                                        <option value="{{ status.data_value }}">{{ status.data_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间范围</label>
                                <div class="layui-input-inline">
                                    <select name="time_range" id="timeRange">
                                        <option value="month">本月</option>
                                        <option value="quarter">本季度</option>
                                        <option value="year" selected>本年度</option>
                                        <option value="all">全部</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-md" lay-submit lay-filter="dept-cost-query">
                                    <i class="layui-icon layui-icon-search"></i>
                                    查询
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                                    <i class="layui-icon layui-icon-refresh"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 指标卡片 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body dashboard-card">
                    <div class="icon layui-icon layui-icon-group"></div>
                    <div class="title">部门总数</div>
                    <h2 id="deptCount">--</h2>
                    <p>已有成本数据的部门数</p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body dashboard-card">
                    <div class="icon layui-icon layui-icon-cart"></div>
                    <div class="title">BOM总成本</div>
                    <h2 id="totalBOMCost">--</h2>
                    <p>所有部门BOM总成本</p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body dashboard-card">
                    <div class="icon layui-icon layui-icon-user"></div>
                    <div class="title">人工总成本</div>
                    <h2 id="totalLaborCost">--</h2>
                    <p>所有部门人工总成本</p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body dashboard-card">
                    <div class="icon layui-icon layui-icon-rmb"></div>
                    <div class="title">总成本</div>
                    <h2 id="totalCost">--</h2>
                    <p>所有部门总成本</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">部门成本占比</div>
                <div class="layui-card-body chart-container">
                    <div id="deptCostPieChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">成本趋势分析</div>
                <div class="layui-card-body chart-container">
                    <div id="deptCostTrendChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 部门成本表格 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">
                    部门成本明细
                    <div class="layui-inline" style="float: right;">
                        <button class="layui-btn layui-btn-sm layui-btn-normal" id="exportDeptCost">
                            <i class="layui-icon layui-icon-export"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <table id="dept-cost-table" lay-filter="dept-cost-table"></table>
                </div>
            </div>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<!-- 表格操作 -->
<script type="text/html" id="dept-cost-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>
        </button>
    </div>
</script>

<script>
    layui.use(['table', 'form', 'echarts', 'element', 'jquery', 'popup', 'common'], function() {
        let table = layui.table;
        let form = layui.form;
        let echarts = layui.echarts;
        let element = layui.element;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        
        // 渲染表格
        table.render({
            elem: '#dept-cost-table',
            url: '/system/project_cost/api/dept_costs',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                {type: 'numbers', title: '序号', width: 60, align: 'center'},
                {field: 'dept_name', title: '部门名称', align: 'center'},
                {field: 'project_count', title: '项目数量', align: 'center'},
                {field: 'total_bom_cost', title: 'BOM成本', align: 'center', templet: function(d) {
                    return '¥' + d.total_bom_cost.toLocaleString();
                }},
                {field: 'total_labor_cost', title: '人工成本', align: 'center', templet: function(d) {
                    return '¥' + d.total_labor_cost.toLocaleString();
                }},
                {field: 'total_other_cost', title: '其他成本', align: 'center', templet: function(d) {
                    return '¥' + d.total_other_cost.toLocaleString();
                }},
                {field: 'total_cost', title: '总成本', align: 'center', templet: function(d) {
                    return '¥' + d.total_cost.toLocaleString();
                }},
                {field: 'cost_percent', title: '成本占比', align: 'center', templet: function(d) {
                    return d.cost_percent + '%';
                }}
            ]],
            toolbar: '#dept-cost-toolbar',
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'],
            done: function(res, curr, count) {
                // 自定义样式
                $('th').css({'background-color': '#009688', 'color': '#fff', 'font-weight': '600'});
                
                // 加载指标卡片数据
                loadDeptCostMetrics(res.data);
            },
            skin: 'line'
        });
        
        // 表单提交
        form.on('submit(dept-cost-query)', function(data) {
            table.reload('dept-cost-table', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            
            // 重新加载图表数据
            loadDeptCostPieChart(data.field);
            loadDeptCostTrendChart(data.field);
            
            return false;
        });
        
        // 表格工具栏事件
        table.on('toolbar(dept-cost-table)', function(obj) {
            if (obj.event === 'refresh') {
                // 刷新表格
                table.reload('dept-cost-table');
                
                // 刷新图表
                loadDeptCostPieChart();
                loadDeptCostTrendChart();
            }
        });
        
        // 加载指标卡片数据
        function loadDeptCostMetrics(data) {
            if (!data || data.length === 0) {
                $('#deptCount').text('0');
                $('#totalBOMCost').text('¥0');
                $('#totalLaborCost').text('¥0');
                $('#totalCost').text('¥0');
                return;
            }
            
            // 部门总数
            $('#deptCount').text(data.length);
            
            // 计算总成本
            let totalBOMCost = 0;
            let totalLaborCost = 0;
            let totalOtherCost = 0;
            let totalCost = 0;
            
            data.forEach(function(dept) {
                totalBOMCost += dept.total_bom_cost;
                totalLaborCost += dept.total_labor_cost;
                totalOtherCost += dept.total_other_cost;
                totalCost += dept.total_cost;
            });
            
            // 更新指标卡片
            $('#totalBOMCost').text('¥' + totalBOMCost.toLocaleString());
            $('#totalLaborCost').text('¥' + totalLaborCost.toLocaleString());
            $('#totalCost').text('¥' + totalCost.toLocaleString());
        }
        
        // 加载部门成本占比饼图
        function loadDeptCostPieChart(params) {
            $.ajax({
                url: '/system/project_cost/api/dept_cost_structure',
                data: params,
                success: function(data) {
                    const chartDom = document.getElementById('deptCostPieChart');
                    const myChart = echarts.init(chartDom);
                    
                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            right: 10,
                            top: 'center',
                            type: 'scroll',
                            data: data.map(item => item.name)
                        },
                        series: [
                            {
                                name: '部门成本',
                                type: 'pie',
                                radius: ['50%', '70%'],
                                avoidLabelOverlap: false,
                                label: {
                                    show: false,
                                    position: 'center'
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontSize: '18',
                                        fontWeight: 'bold'
                                    }
                                },
                                labelLine: {
                                    show: false
                                },
                                data: data
                            }
                        ]
                    };
                    
                    myChart.setOption(option);
                    
                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载部门成本占比数据失败', {icon: 2});
                }
            });
        }
        
        // 加载成本趋势折线图
        function loadDeptCostTrendChart(params) {
            $.ajax({
                url: '/system/project_cost/api/dept_cost_trend',
                data: params,
                success: function(data) {
                    const chartDom = document.getElementById('deptCostTrendChart');
                    const myChart = echarts.init(chartDom);
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross',
                                label: {
                                    backgroundColor: '#6a7985'
                                }
                            }
                        },
                        legend: {
                            data: ['BOM成本', '人工成本', '其他成本', '总成本']
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: [
                            {
                                type: 'category',
                                boundaryGap: false,
                                data: data.months
                            }
                        ],
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '¥{value}'
                                }
                            }
                        ],
                        series: [
                            {
                                name: 'BOM成本',
                                type: 'line',
                                stack: '总量',
                                areaStyle: {},
                                emphasis: {
                                    focus: 'series'
                                },
                                data: data.bom_costs
                            },
                            {
                                name: '人工成本',
                                type: 'line',
                                stack: '总量',
                                areaStyle: {},
                                emphasis: {
                                    focus: 'series'
                                },
                                data: data.labor_costs
                            },
                            {
                                name: '其他成本',
                                type: 'line',
                                stack: '总量',
                                areaStyle: {},
                                emphasis: {
                                    focus: 'series'
                                },
                                data: data.other_costs
                            },
                            {
                                name: '总成本',
                                type: 'line',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: data.total_costs
                            }
                        ]
                    };
                    
                    myChart.setOption(option);
                    
                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载成本趋势数据失败', {icon: 2});
                }
            });
        }
        
        // 导出Excel按钮
        $('#exportDeptCost').click(function() {
            // 获取表单数据
            const formData = form.val('dept-cost-form');
            
            // 构建URL参数
            let params = [];
            for (let key in formData) {
                if (formData[key]) {
                    params.push(key + '=' + encodeURIComponent(formData[key]));
                }
            }
            
            // 拼接URL并导出
            window.location.href = '/project_cost/api/export_dept_costs?' + params.join('&');
        });
        
        // 初始加载图表
        loadDeptCostPieChart();
        loadDeptCostTrendChart();
    });
</script>
</html>
