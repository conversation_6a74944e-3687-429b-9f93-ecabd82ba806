"""
部门单位产出分析模块
提供部门产出计算和分析功能
"""
from flask import Blueprint, render_template, request, jsonify, send_file
from flask_login import login_required, current_user
from applications.common.utils.rights import authorize
from applications.common.utils.http import success_api, fail_api, table_api
from applications.extensions.init_sqlalchemy import db
from applications.models.admin_dept import Dept
from applications.models.project_manage_dept import ProjectManageDept
from applications.models.dept_output_target import DeptOutputTarget
from applications.models.admin_dict import DictData
from applications.schemas.dept_output_target import (
    dept_output_target_schema,
    dept_output_targets_schema,
    dept_output_target_list_schema
)
from applications.services.dept_output_service import (
    calculate_dept_unit_output,
    get_dept_output_summary,
    get_dept_output_by_dept,
    filter_excluded_depts,
    get_filtered_depts_query,
    clear_dept_output_cache
)
from applications.services.dept_participation_service import (
    get_participation_list,
    save_participation_rate,
    update_participation_rate,
    delete_participation_rate,
    validate_participation_rate,
    get_project_participation_summary
)
from datetime import datetime, timedelta
import pandas as pd
from io import BytesIO
import uuid
from sqlalchemy import text, cast


bp = Blueprint('dept_output', __name__, url_prefix='/dept_output')


def get_subordinate_depts(dept_id):
    """获取指定部门及其所有子部门（复用Statistical_data.py的函数）"""
    depts = Dept.query.order_by(Dept.sort).all()
    result = [dept_id]

    def find_children(parent_id):
        for dept in depts:
            if dept.parent_id == parent_id:
                result.append(dept.id)
                find_children(dept.id)

    find_children(dept_id)
    return result




@bp.get('/')
@authorize("system:dept_output:main", log=True)
def main():
    """部门产出分析主页面"""
    # 权限控制：获取用户可查看的部门列表
    user_dept_id = current_user.dept_id

    if current_user.username == 'admin':
        # 管理员可查看所有部门（排除指定部门），按拼音排序
        depts = get_filtered_depts_query().order_by(text("CONVERT(dept_name USING gbk)")).all()
    else:
        if not user_dept_id:
            # 用户未分配部门，返回空的部门列表
            depts = []
        else:
            # 普通用户只能查看本部门及下级部门（排除指定部门）
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)
            depts = Dept.query.filter(
                Dept.id.in_(filtered_dept_ids),
                Dept.status == 1
            ).order_by(text("CONVERT(dept_name USING gbk)")).all()

    # 获取所有启用的项目类别（排除会议、其他、方案支持、培训）
    project_categories = ProjectManageDept.query.filter_by(status=1).filter(
        ~ProjectManageDept.dept_name.in_(['会议', '其他', '方案支持', '培训'])
    ).order_by(
        ProjectManageDept.sort, ProjectManageDept.id
    ).all()

    # 获取项目状态字典数据（排除未开始和生产中状态）
    project_status_options = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).filter(
        ~DictData.data_value.in_(['0', '1'])  # 排除未开始(0)和生产中(1)
    ).order_by(cast(DictData.data_value, db.Integer)).all()

    return render_template('system/dept_output/main.html',
                         depts=depts,
                         project_categories=project_categories,
                         project_status_options=project_status_options)




@bp.post('/dept_data')
@authorize("system:dept_output:data", log=True)
def dept_data():
    """按部门获取产出数据"""
    try:
        request_data = request.get_json()
        if not request_data:
            return fail_api(msg="请求数据格式错误")

        dept_id = request_data.get('dept_id')
        if dept_id:
            dept_id = int(dept_id)
        project_type = request_data.get('project_type')
        project_code = request_data.get('project_code')
        project_status = request_data.get('project_status')

        # 权限控制：获取用户可查看的部门列表
        user_dept_id = current_user.dept_id

        if current_user.username == 'admin':
            # 管理员可查看所有部门（排除指定部门）
            if dept_id:
                accessible_dept_ids = [dept_id]
            else:
                accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]
        else:
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            # 普通用户只能查看本部门及下级部门（排除指定部门）
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)

            # 如果指定了部门ID，检查权限
            if dept_id:
                if dept_id not in accessible_dept_ids:
                    return fail_api(msg="无权限查看该部门数据")
                accessible_dept_ids = [dept_id]

        # 获取按部门分组的数据
        dept_results = get_dept_output_by_dept(
            accessible_dept_ids,
            project_type,
            project_code,
            project_status
        )



        # 添加目标值对比数据
        current_year = datetime.now().year
        for dept_result in dept_results:
            dept_id = dept_result['dept_id']

            # 获取目标值
            target = DeptOutputTarget.get_by_dept_year(dept_id, current_year)
            if target:
                dept_result['target_output'] = float(target.target_value)
                dept_result['target_remark'] = target.remark

                # 计算完成率和差异
                if target.target_value > 0:
                    dept_result['achievement_rate'] = (dept_result['unit_output'] / float(target.target_value)) * 100
                    dept_result['difference'] = dept_result['unit_output'] - float(target.target_value)
                    # 计算转化得分：25 * 实际值 / 目标值，最大25分
                    dept_result['conversion_score'] = min(25, 25 * dept_result['unit_output'] / float(target.target_value))
                else:
                    dept_result['achievement_rate'] = 0
                    dept_result['difference'] = 0
                    dept_result['conversion_score'] = 0
            else:
                dept_result['target_output'] = None
                dept_result['target_remark'] = ''
                dept_result['achievement_rate'] = None
                dept_result['difference'] = None
                dept_result['conversion_score'] = None

        return success_api(data=dept_results, msg="数据获取成功")

    except Exception as e:
        return fail_api(msg=f"数据查询失败: {str(e)}")


@bp.post('/data')
@authorize("system:dept_output:data", log=True)
def data():
    """获取部门产出数据"""
    try:
        # 获取请求参数
        request_data = request.get_json()
        if not request_data:
            return fail_api(msg="请求数据格式错误")

        dept_id = request_data.get('dept_id')
        if dept_id:
            dept_id = int(dept_id)
        start_date_str = request_data.get('start_date')
        end_date_str = request_data.get('end_date')

        # 参数验证
        if not start_date_str or not end_date_str:
            return fail_api(msg="请选择时间范围")

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

            if start_date > end_date:
                return fail_api(msg="开始日期不能大于结束日期")

        except ValueError:
            return fail_api(msg="日期格式错误")

        # 权限控制：获取用户可查看的部门列表
        user_dept_id = current_user.dept_id


        if current_user.username == 'admin':
            # 管理员可查看所有部门（排除指定部门）
            if dept_id:
                accessible_dept_ids = [dept_id]
            else:
                accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]

        else:
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            # 普通用户只能查看本部门及下级部门（排除指定部门）
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)

            # 如果指定了部门ID，检查权限
            if dept_id:
                if dept_id not in accessible_dept_ids:
                    return fail_api(msg="无权限查看该部门数据")
                accessible_dept_ids = [dept_id]
        
        # 计算部门产出数据
        result = calculate_dept_unit_output(accessible_dept_ids, start_date, end_date)
        
        # 格式化返回数据
        response_data = {
            'unit_output': round(result['unit_output'], 4),
            'total_project_price': round(result['total_project_price'], 2),
            'total_expense': round(result['total_expense'], 2),
            'labor_cost': round(result['labor_cost'], 2),
            'travel_cost': round(result['travel_cost'], 2),
            'outsource_cost': round(result['outsource_cost'], 2),
            'project_incentive_cost': round(result['project_incentive_cost'], 2),
            'social_insurance_cost': round(result['social_insurance_cost'], 2),
            'housing_fund_cost': round(result['housing_fund_cost'], 2),
            'project_count': result['project_count'],
            'work_hours': round(result['work_hours'], 1),
            'shipped_projects': [
                {
                    'id': project.id,
                    'project_name': project.project_name,
                    'project_code': project.project_code,
                    'price': project.price or 0,
                    'currency': project.currency or 'CNY'
                }
                for project in result['shipped_projects']
            ],
            'project_details': result['project_details']
        }
        
        return success_api(data=response_data, msg="数据获取成功")
        
    except Exception as e:
        return fail_api(msg=f"数据获取失败: {str(e)}")


@bp.post('/chart_data')
@authorize("system:dept_output:data", log=True)
def chart_data():
    """获取图表数据"""
    try:
        # 获取请求参数
        dept_id = request.json.get('dept_id', type=int)
        start_date_str = request.json.get('start_date')
        end_date_str = request.json.get('end_date')
        
        if not start_date_str or not end_date_str:
            return fail_api(msg="请选择时间范围")
        
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        
        # 权限控制
        user_dept_id = current_user.dept_id
        if not user_dept_id:
            return fail_api(msg="用户未分配部门")
        
        if current_user.username == 'admin':
            if dept_id:
                accessible_dept_ids = [dept_id]
            else:
                accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]
        else:
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)
            if dept_id and dept_id not in accessible_dept_ids:
                return fail_api(msg="无权限查看该部门数据")
            if dept_id:
                accessible_dept_ids = [dept_id]
        
        # 获取汇总数据
        summary_data = get_dept_output_summary(accessible_dept_ids, start_date, end_date)
        
        # 构建图表数据
        chart_data = {
            'cost_structure': {
                'labels': ['人工成本', '差旅费', '外协费', '项目激励', '社保单位扣款', '公积金单位扣款'],
                'data': [
                    summary_data['labor_cost'],
                    summary_data['travel_cost'],
                    summary_data['outsource_cost'],
                    summary_data['project_incentive_cost'],
                    summary_data['social_insurance_cost'],
                    summary_data['housing_fund_cost']
                ]
            },
            'monthly_trend': {
                'months': [item['month'] for item in summary_data['monthly_data']],
                'unit_output': [item['unit_output'] for item in summary_data['monthly_data']],
                'total_expense': [item['total_expense'] for item in summary_data['monthly_data']],
                'project_price': [item['project_price'] for item in summary_data['monthly_data']]
            }
        }
        
        return success_api(data=chart_data, msg="图表数据获取成功")
        
    except Exception as e:
        return fail_api(msg=f"图表数据获取失败: {str(e)}")


@bp.post('/export')
@authorize("system:dept_output:export", log=True)
def export_excel():
    """导出部门产出数据为Excel"""
    try:
        # 获取请求参数
        dept_id = request.json.get('dept_id', type=int)
        start_date_str = request.json.get('start_date')
        end_date_str = request.json.get('end_date')
        
        if not start_date_str or not end_date_str:
            return fail_api(msg="请选择时间范围")
        
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        
        # 权限控制
        user_dept_id = current_user.dept_id
        if not user_dept_id:
            return fail_api(msg="用户未分配部门")
        
        if current_user.username == 'admin':
            if dept_id:
                accessible_dept_ids = [dept_id]
            else:
                accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]
        else:
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)
            if dept_id and dept_id not in accessible_dept_ids:
                return fail_api(msg="无权限导出该部门数据")
            if dept_id:
                accessible_dept_ids = [dept_id]
        
        # 获取数据
        result = calculate_dept_unit_output(accessible_dept_ids, start_date, end_date)
        
        # 创建Excel文件
        output = BytesIO()
        
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 汇总数据
            summary_df = pd.DataFrame([{
                '部门单位产出': result['unit_output'],
                '发货项目总价格': result['total_project_price'],
                '部门总开支': result['total_expense'],
                '人工成本': result['labor_cost'],
                '差旅费': result['travel_cost'],
                '外协费': result['outsource_cost'],
                '项目激励': result['project_incentive_cost'],
                '社保单位扣款': result['social_insurance_cost'],
                '公积金单位扣款': result['housing_fund_cost'],
                '项目数量': result['project_count'],
                '总工时': result['work_hours']
            }])
            summary_df.to_excel(writer, sheet_name='产出汇总', index=False)
            
            # 发货项目明细
            if result['shipped_projects']:
                projects_data = []
                for project in result['shipped_projects']:
                    projects_data.append({
                        '项目编码': project.project_code,
                        '项目名称': project.project_name,
                        '项目价格': project.price or 0,
                        '币种': project.currency or 'CNY'
                    })
                projects_df = pd.DataFrame(projects_data)
                projects_df.to_excel(writer, sheet_name='发货项目明细', index=False)
            
            # 项目工时明细
            if result['project_details']:
                details_df = pd.DataFrame(result['project_details'])
                details_df.to_excel(writer, sheet_name='项目工时明细', index=False)
        
        output.seek(0)
        
        # 生成文件名
        dept_name = "全部部门" if not dept_id else Dept.query.get(dept_id).dept_name
        filename = f"部门产出分析_{dept_name}_{start_date_str}至{end_date_str}_{uuid.uuid4().hex[:8]}.xlsx"
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return fail_api(msg=f"导出失败: {str(e)}")


@bp.get('/dept_tree')
@authorize("system:dept_output:main", log=True)
def dept_tree():
    """获取部门树形结构（用于部门选择下拉框）"""
    try:
        # 权限控制：只返回用户可访问的部门
        user_dept_id = current_user.dept_id
        if not user_dept_id:
            return fail_api(msg="用户未分配部门")
        
        if current_user.username == 'admin':
            # 管理员可查看所有部门（排除指定部门），按拼音排序
            depts = get_filtered_depts_query().order_by(text("CONVERT(dept_name USING gbk)")).all()
        else:
            # 普通用户只能查看本部门及下级部门（排除指定部门），按拼音排序
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)
            depts = Dept.query.filter(
                Dept.id.in_(filtered_dept_ids),
                Dept.status == 1
            ).order_by(text("CONVERT(dept_name USING gbk)")).all()
        
        # 构建树形结构
        dept_tree = []
        dept_dict = {dept.id: {
            'id': dept.id,
            'name': dept.dept_name,
            'parent_id': dept.parent_id,
            'children': []
        } for dept in depts}
        
        for dept in depts:
            if dept.parent_id == 0 or dept.parent_id not in dept_dict:
                dept_tree.append(dept_dict[dept.id])
            else:
                dept_dict[dept.parent_id]['children'].append(dept_dict[dept.id])

        return success_api(data=dept_tree, msg="部门树获取成功")

    except Exception as e:
        return fail_api(msg=f"部门树获取失败: {str(e)}")


# ==================== 目标值管理相关路由 ====================

@bp.get('/target')
@authorize("system:dept_output:target:main", log=True)
def target_main():
    """部门产出目标值管理主页面"""
    # 获取所有启用的部门（排除指定部门），按拼音排序
    depts = get_filtered_depts_query().order_by(text("CONVERT(dept_name USING gbk)")).all()

    return render_template('system/dept_output/target_import.html', depts=depts)


@bp.post('/target/data')
@authorize("system:dept_output:target:data", log=True)
def target_data():
    """获取目标值数据列表"""
    try:
        # 获取请求参数
        page = request.json.get('page', 1)
        limit = request.json.get('limit', 10)
        year = request.json.get('year')
        dept_id = request.json.get('dept_id')

        # 权限控制：获取用户可查看的部门列表
        user_dept_id = current_user.dept_id

        if current_user.username == 'admin':
            # 管理员可查看所有部门（排除指定部门）
            accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]
        else:
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            # 普通用户只能查看本部门及下级部门（排除指定部门）
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)

        # 构建查询
        query = DeptOutputTarget.query.filter(
            DeptOutputTarget.dept_id.in_(accessible_dept_ids),
            DeptOutputTarget.status == 1
        )

        # 添加筛选条件
        if year:
            query = query.filter(DeptOutputTarget.year == year)
        if dept_id:
            if int(dept_id) not in accessible_dept_ids:
                return fail_api(msg="无权限查看该部门数据")
            query = query.filter(DeptOutputTarget.dept_id == dept_id)

        # 分页查询
        query = query.join(Dept).order_by(DeptOutputTarget.year.desc(), Dept.sort)
        total = query.count()
        targets = query.offset((page - 1) * limit).limit(limit).all()

        # 序列化数据
        data = []
        for target in targets:
            data.append(target.to_dict())

        return table_api(data=data, count=total, msg="数据获取成功")

    except Exception as e:
        return fail_api(msg=f"数据获取失败: {str(e)}")


@bp.post('/target/save')
@authorize("system:dept_output:target:import", log=True)
def target_save():
    """保存单个目标值"""
    try:
        req_data = request.get_json()

        # 数据验证
        dept_id = req_data.get('dept_id')
        year = req_data.get('year')
        target_value = req_data.get('target_value')
        remark = req_data.get('remark', '')

        if not all([dept_id, year, target_value]):
            return fail_api(msg="部门、年度和目标值不能为空")

        # 权限验证
        user_dept_id = current_user.dept_id
        if current_user.username != 'admin':
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)
            if int(dept_id) not in accessible_dept_ids:
                return fail_api(msg="无权限操作该部门数据")

        # 检查是否已存在
        existing = DeptOutputTarget.query.filter_by(
            dept_id=dept_id,
            year=year
        ).first()

        if existing:
            # 更新现有记录
            existing.target_value = target_value
            existing.remark = remark
            existing.import_by = current_user.id
            existing.updated_at = datetime.now()
        else:
            # 创建新记录
            new_target = DeptOutputTarget(
                dept_id=dept_id,
                year=year,
                target_value=target_value,
                remark=remark,
                import_by=current_user.id,
                status=1
            )
            db.session.add(new_target)

        db.session.commit()
        return success_api(msg="目标值保存成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"目标值保存失败: {str(e)}")


@bp.post('/target/delete')
@authorize("system:dept_output:target:delete", log=True)
def target_delete():
    """删除目标值"""
    try:
        req_data = request.get_json()
        target_id = req_data.get('id')

        if not target_id:
            return fail_api(msg="目标值ID不能为空")

        target = DeptOutputTarget.query.get(target_id)
        if not target:
            return fail_api(msg="目标值不存在")

        # 权限验证
        user_dept_id = current_user.dept_id
        if current_user.username != 'admin':
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)
            if target.dept_id not in accessible_dept_ids:
                return fail_api(msg="无权限删除该部门数据")

        # 软删除
        target.status = 0
        target.updated_at = datetime.now()
        db.session.commit()

        return success_api(msg="目标值删除成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"目标值删除失败: {str(e)}")


@bp.post('/target/import/excel')
@authorize("system:dept_output:target:import", log=True)
def target_import_excel():
    """Excel批量导入目标值"""
    try:
        if 'file' not in request.files:
            return fail_api(msg='未选择文件')

        file = request.files['file']
        if file.filename == '':
            return fail_api(msg='未选择文件')

        if not file.filename.endswith(('.xlsx', '.xls')):
            return fail_api(msg='仅支持Excel文件')

        # 权限验证
        user_dept_id = current_user.dept_id
        if current_user.username != 'admin':
            if not user_dept_id:
                return fail_api(msg="用户未分配部门")
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            accessible_dept_ids = filter_excluded_depts(accessible_dept_ids)
        else:
            accessible_dept_ids = [dept.id for dept in get_filtered_depts_query().all()]

        # 读取Excel文件
        df = pd.read_excel(file)

        # 验证必需列
        required_columns = ['部门名称', '年度', '目标值']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return fail_api(msg=f'Excel文件缺少必需列: {", ".join(missing_columns)}')

        # 获取部门映射（排除指定部门）
        depts = get_filtered_depts_query().all()
        dept_name_to_id = {dept.dept_name: dept.id for dept in depts}

        # 处理数据
        targets_data = []
        error_details = []
        success_count = 0
        error_count = 0
        update_count = 0  # 更新记录数
        insert_count = 0  # 新增记录数

        for index, row in df.iterrows():
            try:
                # 验证部门名称
                dept_name = str(row['部门名称']).strip()
                if dept_name not in dept_name_to_id:
                    error_details.append(f"第{index+2}行: 部门名称 '{dept_name}' 不存在")
                    error_count += 1
                    continue

                dept_id = dept_name_to_id[dept_name]

                # 权限验证
                if dept_id not in accessible_dept_ids:
                    error_details.append(f"第{index+2}行: 无权限操作部门 '{dept_name}'")
                    error_count += 1
                    continue

                # 验证年度
                try:
                    year = int(row['年度'])
                    current_year = datetime.now().year
                    if year < 2020 or year > current_year + 5:
                        error_details.append(f"第{index+2}行: 年度 '{year}' 超出有效范围(2020-{current_year+5})")
                        error_count += 1
                        continue
                except (ValueError, TypeError):
                    error_details.append(f"第{index+2}行: 年度 '{row['年度']}' 不是有效数字")
                    error_count += 1
                    continue

                # 验证目标值
                try:
                    target_value = float(row['目标值'])
                    if target_value <= 0:
                        error_details.append(f"第{index+2}行: 目标值必须大于0")
                        error_count += 1
                        continue
                except (ValueError, TypeError):
                    error_details.append(f"第{index+2}行: 目标值 '{row['目标值']}' 不是有效数字")
                    error_count += 1
                    continue

                # 获取备注
                remark = str(row.get('备注', '')).strip() if pd.notna(row.get('备注')) else ''

                # 检查是否已存在记录
                existing = DeptOutputTarget.query.filter_by(dept_id=dept_id, year=year).first()

                targets_data.append({
                    'dept_id': dept_id,
                    'year': year,
                    'target_value': target_value,
                    'remark': remark,
                    'is_update': existing is not None
                })

                if existing:
                    update_count += 1
                else:
                    insert_count += 1

                success_count += 1

            except Exception as e:
                error_details.append(f"第{index+2}行: 处理失败 - {str(e)}")
                error_count += 1

        # 批量保存
        if targets_data:
            import_file = f"target_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success, message = DeptOutputTarget.batch_upsert(
                targets_data,
                import_by=current_user.id,
                import_file=import_file
            )

            if not success:
                return fail_api(msg=f"数据保存失败: {message}")
        else:
            # 没有有效数据的情况
            if error_count > 0:
                return fail_api(msg=f"导入失败: 所有{error_count}条记录都存在错误\n\n错误详情:\n" + "\n".join(error_details[:10]))
            else:
                return fail_api(msg="导入失败: 未找到有效的数据记录")

        # 返回详细结果
        if success_count > 0:
            result_msg = f"🎉 导入成功完成!\n\n"
            result_msg += f"📊 处理统计:\n"
            result_msg += f"✅ 成功处理: {success_count}条记录\n"
            result_msg += f"🆕 新增记录: {insert_count}条\n"
            result_msg += f"🔄 更新记录: {update_count}条\n"

            if error_count > 0:
                result_msg += f"❌ 失败记录: {error_count}条\n"
                result_msg += f"\n📋 错误详情:\n"
                if len(error_details) <= 5:
                    result_msg += "\n".join(error_details)
                else:
                    result_msg += "\n".join(error_details[:5])
                    result_msg += f"\n... 还有{len(error_details) - 5}条错误未显示"

            if update_count > 0:
                result_msg += f"\n⚠️ 覆盖提醒: 已成功覆盖{update_count}条现有目标值"

            return success_api(msg=result_msg)
        else:
            return fail_api(msg=f"导入失败: 所有记录都存在错误\n\n错误详情:\n" + "\n".join(error_details[:10]))

    except Exception as e:
        return fail_api(msg=f"导入失败: {str(e)}")


@bp.get('/target/template/download')
@authorize("system:dept_output:target:main", log=True)
def target_template_download():
    """下载目标值导入模板"""
    try:
        # 创建示例数据
        current_year = datetime.now().year
        template_data = {
            '部门名称': ['设计开发一部', '设计开发二部', '设计开发三部'],
            '年度': [current_year, current_year, current_year],
            '目标值': [45.0, 38.0, 38.0],
            '备注': ['示例数据1', '示例数据2', '示例数据3']
        }

        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='目标值导入模板', index=False)

            # 获取工作表
            worksheet = writer.sheets['目标值导入模板']

            # 设置列宽
            worksheet.column_dimensions['A'].width = 20  # 部门名称
            worksheet.column_dimensions['B'].width = 10  # 年度
            worksheet.column_dimensions['C'].width = 15  # 目标值
            worksheet.column_dimensions['D'].width = 30  # 备注

        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name=f'部门产出目标值导入模板_{datetime.now().strftime("%Y%m%d")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return fail_api(msg=f"模板下载失败: {str(e)}")


@bp.get('/api/project_status_dict')
@authorize("system:dept_output:main", log=True)
def get_project_status_dict():
    """获取项目状态字典数据"""
    from applications.models.admin_dict import DictData

    try:
        # 查询项目状态字典数据
        status_list = DictData.query.filter_by(
            type_code='project_status',
            enable=1
        ).order_by(DictData.data_value.asc()).all()

        data = []
        for status in status_list:
            data.append({
                'data_value': status.data_value,
                'data_label': status.data_label
            })

        return success_api(data=data, msg="获取成功")
    except Exception as e:
        return fail_api(msg=f"获取项目状态字典失败: {str(e)}")


@bp.post('/clear_cache')
def clear_cache():
    """清理部门产出分析缓存"""
    try:
        print("🔄 开始清理部门产出分析缓存...")
        result = clear_dept_output_cache()
        print(f"✅ 缓存清理完成: {result}")

        if result and result.get('success'):
            return success_api(
                msg=f"缓存清理成功！{result.get('message', '')}，请重新查询数据",
                data=result
            )
        else:
            return fail_api(msg="缓存清理失败")
    except Exception as e:
        print(f"❌ 缓存清理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return fail_api(msg=f"缓存清理失败: {str(e)}")


# ==================== 部门产出比例统计相关路由 ====================

@bp.get('/participation')
@authorize("system:dept_output:participation:main", log=True)
def participation_manage():
    """部门项目参与比例管理页面"""
    # 权限控制：获取用户可查看的部门列表
    user_dept_id = current_user.dept_id

    if current_user.username == 'admin':
        # 管理员可查看所有部门（排除指定部门），按拼音排序
        depts = get_filtered_depts_query().order_by(text("CONVERT(dept_name USING gbk)")).all()
    else:
        if not user_dept_id:
            # 用户未分配部门，返回空的部门列表
            depts = []
        else:
            # 普通用户只能查看本部门及下级部门（排除指定部门）
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)
            depts = Dept.query.filter(
                Dept.id.in_(filtered_dept_ids),
                Dept.status == 1
            ).order_by(text("CONVERT(dept_name USING gbk)")).all()

    # 获取项目类型列表
    project_types = ProjectManageDept.query.filter(
        ProjectManageDept.status == 1,
        ProjectManageDept.dept_name != "所有项目类型"
    ).order_by(ProjectManageDept.sort).all()

    return render_template('system/dept_output/participation_manage.html',
                         depts=depts,
                         project_types=project_types)


@bp.get('/participation/data')
@authorize("system:dept_output:participation:main", log=True)
def get_participation_data():
    """获取参与比例数据（支持分页和筛选）"""
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('limit', 20))
        project_type_id = request.args.get('project_type_id')
        project_code = request.args.get('project_code')
        dept_id = request.args.get('dept_id')

        # 权限控制：限制用户只能查看有权限的部门数据
        user_dept_id = current_user.dept_id
        if current_user.username != 'admin' and user_dept_id:
            # 普通用户只能查看本部门及下级部门的数据
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)

            if dept_id:
                # 如果指定了部门，检查是否有权限
                if int(dept_id) not in filtered_dept_ids:
                    return fail_api(msg="无权限查看该部门数据")
            else:
                # 如果没有指定部门，限制查询范围
                dept_id = filtered_dept_ids

        # 处理dept_id参数
        processed_dept_id = None
        if dept_id:
            if isinstance(dept_id, str):
                # 如果是字符串，转换为整数
                processed_dept_id = int(dept_id)
            elif isinstance(dept_id, list):
                # 如果是列表，保持列表格式
                processed_dept_id = dept_id
            else:
                # 其他情况直接使用
                processed_dept_id = dept_id

        # 调用服务层获取数据
        result = get_participation_list(
            page=page,
            per_page=per_page,
            project_type_id=int(project_type_id) if project_type_id else None,
            project_code=project_code,
            dept_id=processed_dept_id
        )

        if 'error' in result:
            return fail_api(msg=f"查询失败: {result['error']}")

        # 构造符合Layui表格要求的返回格式
        response_data = {
            'items': result['items'],
            'total': result['total'],
            'page': result['page'],
            'per_page': result['per_page'],
            'pages': result.get('pages', 0)
        }

        return success_api(data=response_data, msg="查询成功")

    except Exception as e:
        return fail_api(msg=f"查询失败: {str(e)}")


@bp.post('/participation/save')
@authorize("system:dept_output:participation:add", log=True)
def save_participation():
    """保存部门项目参与比例"""
    try:
        req_json = request.get_json(force=True)

        # 获取参数
        project_id = req_json.get('project_id')
        dept_id = req_json.get('dept_id')
        participation_rate = req_json.get('participation_rate')
        remark = req_json.get('remark', '')

        # 参数验证
        if not project_id or not dept_id or participation_rate is None:
            return fail_api(msg="项目ID、部门ID和参与比例不能为空")

        try:
            participation_rate = float(participation_rate)
        except (ValueError, TypeError):
            return fail_api(msg="参与比例必须为数字")

        # 权限控制：检查用户是否有权限操作该部门
        user_dept_id = current_user.dept_id
        if current_user.username != 'admin' and user_dept_id:
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)

            if int(dept_id) not in filtered_dept_ids:
                return fail_api(msg="无权限操作该部门数据")

        # 调用服务层保存数据
        result = save_participation_rate(
            project_id=int(project_id),
            dept_id=int(dept_id),
            participation_rate=participation_rate,
            remark=remark,
            created_by=current_user.id
        )

        if result['success']:
            return success_api(data=result['data'], msg=result['message'])
        else:
            return fail_api(msg=result['message'])

    except Exception as e:
        return fail_api(msg=f"保存失败: {str(e)}")


@bp.post('/participation/edit')
@authorize("system:dept_output:participation:edit", log=True)
def edit_participation():
    """编辑部门项目参与比例"""
    try:
        req_json = request.get_json(force=True)

        # 获取参数
        participation_id = req_json.get('id')
        participation_rate = req_json.get('participation_rate')
        remark = req_json.get('remark', '')

        # 参数验证
        if not participation_id or participation_rate is None:
            return fail_api(msg="记录ID和参与比例不能为空")

        try:
            participation_rate = float(participation_rate)
        except (ValueError, TypeError):
            return fail_api(msg="参与比例必须为数字")

        # 权限控制：检查记录是否存在且用户有权限操作
        from applications.models.dept_project_participation import DeptProjectParticipation
        participation = DeptProjectParticipation.query.get(participation_id)
        if not participation:
            return fail_api(msg="记录不存在")

        user_dept_id = current_user.dept_id
        if current_user.username != 'admin' and user_dept_id:
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)

            if participation.dept_id not in filtered_dept_ids:
                return fail_api(msg="无权限操作该部门数据")

        # 调用服务层更新数据
        result = update_participation_rate(
            participation_id=int(participation_id),
            participation_rate=participation_rate,
            remark=remark
        )

        if result['success']:
            return success_api(data=result['data'], msg=result['message'])
        else:
            return fail_api(msg=result['message'])

    except Exception as e:
        return fail_api(msg=f"编辑失败: {str(e)}")


@bp.post('/participation/delete')
@authorize("system:dept_output:participation:remove", log=True)
def delete_participation():
    """删除部门项目参与比例"""
    try:
        req_json = request.get_json(force=True)
        participation_id = req_json.get('id')

        if not participation_id:
            return fail_api(msg="记录ID不能为空")

        # 权限控制：检查记录是否存在且用户有权限操作
        from applications.models.dept_project_participation import DeptProjectParticipation
        participation = DeptProjectParticipation.query.get(participation_id)
        if not participation:
            return fail_api(msg="记录不存在")

        user_dept_id = current_user.dept_id
        if current_user.username != 'admin' and user_dept_id:
            accessible_dept_ids = get_subordinate_depts(user_dept_id)
            filtered_dept_ids = filter_excluded_depts(accessible_dept_ids)

            if participation.dept_id not in filtered_dept_ids:
                return fail_api(msg="无权限操作该部门数据")

        # 调用服务层删除数据
        result = delete_participation_rate(int(participation_id))

        if result['success']:
            return success_api(msg=result['message'])
        else:
            return fail_api(msg=result['message'])

    except Exception as e:
        return fail_api(msg=f"删除失败: {str(e)}")


@bp.post('/participation/validate')
@authorize("system:dept_output:participation:main", log=True)
def validate_participation():
    """验证部门项目参与比例"""
    try:
        req_json = request.get_json(force=True)

        project_id = req_json.get('project_id')
        dept_id = req_json.get('dept_id')
        participation_rate = req_json.get('participation_rate')
        exclude_id = req_json.get('exclude_id')  # 编辑时排除当前记录

        # 参数验证
        if not project_id or not dept_id or participation_rate is None:
            return fail_api(msg="项目ID、部门ID和参与比例不能为空")

        try:
            participation_rate = float(participation_rate)
        except (ValueError, TypeError):
            return fail_api(msg="参与比例必须为数字")

        # 调用服务层验证
        result = validate_participation_rate(
            project_id=int(project_id),
            dept_id=int(dept_id),
            new_rate=participation_rate,
            exclude_id=int(exclude_id) if exclude_id else None
        )

        return success_api(data=result, msg="验证完成")

    except Exception as e:
        return fail_api(msg=f"验证失败: {str(e)}")


@bp.get('/participation/project_summary/<int:project_id>')
@authorize("system:dept_output:participation:main", log=True)
def get_project_participation_summary_api(project_id):
    """获取项目的部门参与比例汇总"""
    try:
        result = get_project_participation_summary(project_id)

        if 'error' in result:
            return fail_api(msg=f"查询失败: {result['error']}")

        return success_api(data=result, msg="查询成功")

    except Exception as e:
        return fail_api(msg=f"查询失败: {str(e)}")


@bp.get('/api/projects_by_type')
@authorize("system:dept_output:participation:main", log=True)
def get_projects_by_type():
    """根据项目类型获取项目列表"""
    try:
        project_type_id = request.args.get('project_type_id')

        if not project_type_id:
            return fail_api(msg="项目类型ID不能为空")

        # 查询该类型下的所有项目
        from applications.models.import_project import Import_project
        projects = Import_project.query.filter_by(dept_id=int(project_type_id)).all()

        # 转换为前端需要的格式
        project_list = []
        for project in projects:
            project_list.append({
                'id': project.id,
                'project_code': project.project_code,
                'project_name': project.project_name
            })

        return success_api(data=project_list, msg="查询成功")

    except Exception as e:
        return fail_api(msg=f"查询失败: {str(e)}")
