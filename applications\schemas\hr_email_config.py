from marshmallow import Schema, fields, validate, validates, ValidationError, pre_load
from applications.extensions import ma
import re


class HREmailConfigSchema(ma.Schema):
    """HR邮箱配置序列化模型"""
    
    id = fields.Integer(dump_only=True)
    email = fields.Email(required=True, validate=validate.Length(min=1, max=255))
    description = fields.String(allow_none=True, validate=validate.Length(max=500))
    is_active = fields.Boolean(missing=True)
    priority = fields.Integer(missing=1)
    created_by = fields.Integer(dump_only=True)
    created_at = fields.DateTime(dump_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_by = fields.Integer(dump_only=True)
    updated_at = fields.DateTime(dump_only=True, format='%Y-%m-%d %H:%M:%S')
    
    # 额外的显示字段
    is_active_text = fields.Method("get_is_active_text", dump_only=True)
    
    def get_is_active_text(self, obj):
        """获取启用状态的文本显示"""
        return "启用" if obj.is_active else "禁用"
    
    @validates('email')
    def validate_email(self, value):
        """验证邮箱格式"""
        if not value:
            raise ValidationError('邮箱地址不能为空')

        # 基本邮箱格式验证
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            raise ValidationError('邮箱格式不正确')

    @validates('priority')
    def validate_priority(self, value):
        """验证优先级"""
        if value is not None and (value < 1 or value > 999):
            raise ValidationError('优先级必须在1-999之间')
        return value
    
    @pre_load
    def process_input(self, data, **kwargs):
        """预处理输入数据"""
        if isinstance(data, dict):
            # 处理邮箱地址，去除首尾空格并转为小写
            if 'email' in data and data['email']:
                data['email'] = data['email'].strip().lower()
            
            # 处理描述，去除首尾空格
            if 'description' in data and data['description']:
                data['description'] = data['description'].strip()
        
        return data


class HREmailConfigOutSchema(ma.Schema):
    """HR邮箱配置输出序列化模型"""
    
    id = fields.Integer()
    email = fields.String()
    description = fields.String()
    is_active = fields.Boolean()
    is_active_text = fields.Method("get_is_active_text")
    priority = fields.Integer()
    created_by = fields.Integer()
    created_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    updated_by = fields.Integer()
    updated_at = fields.DateTime(format='%Y-%m-%d %H:%M:%S')
    
    def get_is_active_text(self, obj):
        """获取启用状态的文本显示"""
        return "启用" if obj.is_active else "禁用"
