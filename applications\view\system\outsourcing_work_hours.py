from flask import Blueprint, render_template, request, jsonify
from flask_login import current_user
from sqlalchemy import and_, or_
from datetime import datetime
from sqlalchemy import func, and_,text
from applications.common import curd
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import OutsourcingWorkHours, OutsourcingInfo, Dept, User, DictData, ProjectManageDept, Import_project
from applications.schemas.outsourcing_work_hours import OutsourcingWorkHoursOutSchema

bp = Blueprint('outsourcing_work_hours', __name__, url_prefix='/outsourcing_work_hours')


def get_user_dept_and_children(user_dept_id):
    """获取用户部门及其所有下级部门ID列表"""
    dept_ids = [user_dept_id]
    
    def get_children_recursive(parent_id):
        children = Dept.query.filter_by(parent_id=parent_id).all()
        for child in children:
            dept_ids.append(child.id)
            get_children_recursive(child.id)
    
    get_children_recursive(user_dept_id)
    return dept_ids


@bp.get('/')
@authorize("system:outsourcing_work_hours:main", log=True)
def main():
    """外协工时管理主页面"""
    return render_template('system/outsourcing_work_hours/main.html')


@bp.get('/data')
@authorize("system:outsourcing_work_hours:main", log=True)
def data():
    """获取外协工时数据"""
    # 获取查询参数
    project_code = str_escape(request.args.get('projectCode', type=str))
    project_type = str_escape(request.args.get('projectType', type=str))
    outsourcing_name = str_escape(request.args.get('outsourcingName', type=str))
    work_location = str_escape(request.args.get('workLocation', type=str))
    dept_name = str_escape(request.args.get('deptName', type=str))
    
    # 获取当前用户部门及其下级部门
    user_dept_id = current_user.dept_id
    if not user_dept_id:
        return table_api(msg="用户未分配部门", data=[], count=0)
    
    dept_ids = get_user_dept_and_children(user_dept_id)
    
    # 构建查询条件
    filters = [OutsourcingWorkHours.dept_id.in_(dept_ids), OutsourcingWorkHours.status == 1]
    
    if project_code:
        filters.append(OutsourcingWorkHours.project_code.contains(project_code))
    if project_type:
        filters.append(OutsourcingWorkHours.project_type == project_type)
    if work_location:
        filters.append(OutsourcingWorkHours.work_location == work_location)
    if outsourcing_name:
        # 通过外协信息表进行关联查询
        filters.append(OutsourcingWorkHours.outsourcing_info.has(
            OutsourcingInfo.outsourcing_name.contains(outsourcing_name)
        ))
    if dept_name:
        # 通过部门表进行关联查询
        filters.append(OutsourcingWorkHours.dept.has(
            Dept.dept_name == dept_name
        ))
    
    # 执行查询
    work_hours_query = OutsourcingWorkHours.query.filter(and_(*filters)).order_by(
        OutsourcingWorkHours.create_at.desc()
    ).layui_paginate()
    
    count = work_hours_query.total
    data = curd.model_to_dicts(schema=OutsourcingWorkHoursOutSchema, data=work_hours_query.items)
    
    return table_api(data=data, count=count)


@bp.get('/add')
@authorize("system:outsourcing_work_hours:add", log=True)
def add():
    """新增外协工时页面"""
    return render_template('system/outsourcing_work_hours/add.html')


@bp.post('/save')
@authorize("system:outsourcing_work_hours:add", log=True)
def save():
    """保存外协工时记录"""
    req_json = request.get_json(force=True)
    
    try:
        # 数据验证
        required_fields = ['projectCode', 'outsourcingId', 'workLocation', 'workHours', 'workStartDate', 'workEndDate']
        for field in required_fields:
            if not req_json.get(field):
                return fail_api(msg=f"参数 {field} 不能为空")
        
        # 日期格式转换
        work_start_date = datetime.strptime(req_json.get('workStartDate'), '%Y-%m-%d').date()
        work_end_date = datetime.strptime(req_json.get('workEndDate'), '%Y-%m-%d').date()
        
        if work_start_date > work_end_date:
            return fail_api(msg="开始日期不能大于结束日期")
        
        # 获取项目类型名称
        project_type_id = req_json.get('projectType')
        project_type_name = ""
        if project_type_id:
            project_type = ProjectManageDept.query.get(project_type_id)
            project_type_name = project_type.dept_name if project_type else ""

        # 计算总费用
        work_hours = float(req_json.get('workHours'))
        hourly_rate = float(req_json.get('hourlyRate', 0))
        total_cost = work_hours * hourly_rate if hourly_rate > 0 else 0

        # 获取部门ID，支持自定义选择
        dept_id = req_json.get('deptId')
        if dept_id:
            dept_id = int(dept_id)
            # 验证用户是否有权限选择该部门
            user_dept_ids = get_user_dept_and_children(current_user.dept_id)
            if dept_id not in user_dept_ids:
                return fail_api(msg="无权限选择该部门")
        else:
            # 如果没有选择部门，默认使用用户所属部门
            dept_id = current_user.dept_id

        # 创建工时记录
        work_hour = OutsourcingWorkHours(
            project_code=str_escape(req_json.get('projectCode')),
            project_type=project_type_name,
            outsourcing_id=int(req_json.get('outsourcingId')),
            work_location=str_escape(req_json.get('workLocation')),
            work_hours=work_hours,
            work_start_date=work_start_date,
            work_end_date=work_end_date,
            work_description=str_escape(req_json.get('workDescription', '')),
            hourly_rate=hourly_rate,
            total_cost=total_cost,
            dept_id=dept_id,
            creator_id=current_user.id,
            remark=str_escape(req_json.get('remark', ''))
        )
        
        db.session.add(work_hour)
        db.session.commit()
        
        return success_api(msg="外协工时记录添加成功")
        
    except ValueError as e:
        return fail_api(msg="数据格式错误")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"添加失败: {str(e)}")


@bp.get('/edit')
@authorize("system:outsourcing_work_hours:edit", log=True)
def edit():
    """编辑外协工时页面"""
    work_hour_id = request.args.get("id")
    work_hour = curd.get_one_by_id(model=OutsourcingWorkHours, id=work_hour_id)
    
    if not work_hour:
        return fail_api(msg="记录不存在")
    
    # 检查权限：只能编辑本部门及下级部门的数据
    user_dept_id = current_user.dept_id
    dept_ids = get_user_dept_and_children(user_dept_id)
    
    if work_hour.dept_id not in dept_ids:
        return fail_api(msg="无权限编辑此记录")
    
    return render_template('system/outsourcing_work_hours/edit.html', work_hour=work_hour)


@bp.put('/update')
@authorize("system:outsourcing_work_hours:edit", log=True)
def update():
    """更新外协工时记录"""
    req_json = request.get_json(force=True)
    work_hour_id = req_json.get('id')
    
    if not work_hour_id:
        return fail_api(msg="记录ID不能为空")
    
    work_hour = OutsourcingWorkHours.query.get(work_hour_id)
    if not work_hour:
        return fail_api(msg="记录不存在")
    
    # 检查权限
    user_dept_id = current_user.dept_id
    dept_ids = get_user_dept_and_children(user_dept_id)
    
    if work_hour.dept_id not in dept_ids:
        return fail_api(msg="无权限编辑此记录")
    
    try:
        # 获取项目类型名称
        project_type_id = req_json.get('projectType')
        project_type_name = ""
        if project_type_id:
            project_type = ProjectManageDept.query.get(project_type_id)
            project_type_name = project_type.dept_name if project_type else ""

        # 获取部门ID，支持自定义选择
        dept_id = req_json.get('deptId')
        if dept_id:
            dept_id = int(dept_id)
            # 验证用户是否有权限选择该部门
            user_dept_ids = get_user_dept_and_children(current_user.dept_id)
            if dept_id not in user_dept_ids:
                return fail_api(msg="无权限选择该部门")
        else:
            # 如果没有选择部门，保持原有部门
            dept_id = work_hour.dept_id

        # 更新数据
        work_hour.project_code = str_escape(req_json.get('projectCode'))
        work_hour.project_type = project_type_name
        work_hour.outsourcing_id = int(req_json.get('outsourcingId'))
        work_hour.work_location = str_escape(req_json.get('workLocation'))
        work_hour.work_hours = float(req_json.get('workHours'))
        work_hour.work_start_date = datetime.strptime(req_json.get('workStartDate'), '%Y-%m-%d').date()
        work_hour.work_end_date = datetime.strptime(req_json.get('workEndDate'), '%Y-%m-%d').date()
        work_hour.work_description = str_escape(req_json.get('workDescription', ''))
        work_hour.hourly_rate = float(req_json.get('hourlyRate', 0))
        work_hour.total_cost = work_hour.work_hours * work_hour.hourly_rate if work_hour.hourly_rate > 0 else 0
        work_hour.dept_id = dept_id
        work_hour.remark = str_escape(req_json.get('remark', ''))

        db.session.commit()
        return success_api(msg="更新成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"更新失败: {str(e)}")


@bp.delete('/remove/<int:work_hour_id>')
@authorize("system:outsourcing_work_hours:remove", log=True)
def remove(work_hour_id):
    """删除外协工时记录"""
    work_hour = OutsourcingWorkHours.query.get(work_hour_id)
    if not work_hour:
        return fail_api(msg="记录不存在")
    
    # 检查权限
    user_dept_id = current_user.dept_id
    dept_ids = get_user_dept_and_children(user_dept_id)
    
    if work_hour.dept_id not in dept_ids:
        return fail_api(msg="无权限删除此记录")
    
    try:
        # 软删除
        work_hour.status = 0
        db.session.commit()
        return success_api(msg="删除成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败: {str(e)}")


@bp.get('/outsourcing_list')
@authorize("system:outsourcing_work_hours:main", log=True)
def outsourcing_list():
    """获取外协信息列表"""
    outsourcing_infos = OutsourcingInfo.query.filter_by(cooperation_status=1).all()
    data = []
    for info in outsourcing_infos:
        data.append({
            'id': info.id,
            'outsourcing_name': info.outsourcing_name,
            'contact_person': info.contact_person,
            'contact_phone': info.contact_phone,
            'hourly_rate': info.hourly_rate or 0
        })
    return success_api(data=data)


@bp.get('/project_types')
@authorize("system:outsourcing_work_hours:main", log=True)
def project_types():
    """获取项目类型列表"""
    # 从project_manage_dept获取项目类型
    project_types = ProjectManageDept.query.filter_by(status=1).all()
    data = []
    for pt in project_types:
        data.append({
            'value': pt.id,
            'label': pt.dept_name
        })
    return success_api(data=data)


@bp.get('/project_codes/<int:project_type_id>')
@authorize("system:outsourcing_work_hours:main", log=True)
def project_codes(project_type_id):
    """根据项目类型获取项目编号列表"""
    projects = Import_project.query.filter_by(dept_id=project_type_id).all()
    data = []
    for project in projects:
        data.append({
            'value': project.project_code,
            'label': f"{project.project_code} - {project.project_name}"
        })
    return success_api(data=data)


@bp.get('/manageable_depts')
@authorize("system:outsourcing_work_hours:main", log=True)
def manageable_depts():
    """获取当前用户可管理的部门列表（本部门及下级部门）"""
    user_dept_id = current_user.dept_id
    if not user_dept_id:
        return success_api(data=[])

    # 获取用户部门及其所有下级部门
    dept_ids = get_user_dept_and_children(user_dept_id)

    # 查询这些部门的详细信息
    depts = Dept.query.filter(Dept.id.in_(dept_ids), Dept.status == 1).order_by(text("CONVERT(dept_name USING gbk)")).all()

    data = []
    for dept in depts:
        data.append({
            'value': dept.id,
            'label': dept.dept_name
        })

    return success_api(data=data)
