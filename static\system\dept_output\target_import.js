/**
 * 部门产出目标值管理页面脚本
 * 处理目标值的增删改查和Excel导入功能
 */
layui.use(['table', 'form', 'layer', 'upload', 'element'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var upload = layui.upload;
    var element = layui.element;
    var $ = layui.$;

    // 初始化年度选择器
    function initYearSelector() {
        var currentYear = new Date().getFullYear();
        var yearSelect = $('#filterYear');
        for (var i = currentYear + 10; i >= 2025; i--) {
            yearSelect.append('<option value="' + i + '">' + i + '年</option>');
        }
        yearSelect.val(currentYear);
        form.render('select');

        // 同时更新页面上的当前年度显示
        $('#currentYear').text(currentYear);

        // 设置单个录入表单的默认年度
        $('#singleYearInput').val(currentYear);

        // 更新年度范围提示
        var maxYear = currentYear + 10;
        $('#yearRangeText').text('2024-' + maxYear);

        // 更新输入框的年度范围限制
        $('#singleYearInput').attr('max', maxYear);
    }

    // 初始化数据表格
    var targetTable = table.render({
        elem: '#targetTable',
        url: '/system/dept_output/target/data',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'Content-Type': 'application/json'
        },
        where: {
            year: new Date().getFullYear()
        },
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'dept_name', title: '部门名称', width: 260},
            {field: 'year', title: '年度', width: 200, sort: true},
            {field: 'target_value', title: '目标值', width:143, sort: true, templet: function(d){
                return '<span style="color: #1890ff; font-weight: bold;">' + d.target_value + '</span>';
            }},
            // {field: 'remark', title: '备注', minWidth: 200},
            {field: 'import_user_name', title: '录入人', width: 220},
            {field: 'updated_at', title: '更新时间', width: 260, sort: true},
            {title: '操作', width: 350, align: 'center', toolbar: '#barDemo'}
        ]],
        page: true,
        limits: [10, 20, 50, 100],
        limit: 20,
        loading: true,
        done: function(res, curr, count) {
            // 更新统计信息
            updateStats(res.data);
        }
    });

    // 更新统计信息
    function updateStats(data) {
        if (!data || data.length === 0) {
            $('#targetDeptCount').text('0');
            $('#avgTarget').text('-');
            $('#lastUpdate').text('-');
            return;
        }

        // 统计已设置目标的部门数
        var deptCount = new Set(data.map(item => item.dept_id)).size;
        $('#targetDeptCount').text(deptCount);

        // 计算平均目标值
        var totalTarget = data.reduce((sum, item) => sum + parseFloat(item.target_value || 0), 0);
        var avgTarget = (totalTarget / data.length).toFixed(2);
        $('#avgTarget').text(avgTarget);

        // 最后更新时间
        var latestUpdate = data.reduce((latest, item) => {
            return new Date(item.updated_at) > new Date(latest) ? item.updated_at : latest;
        }, data[0].updated_at);
        $('#lastUpdate').text(latestUpdate ? latestUpdate.substring(5, 16) : '-');
    }

    // 工具栏事件
    table.on('toolbar(targetTable)', function(obj){
        switch(obj.event){
            case 'refresh':
                targetTable.reload();
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(targetTable)', function(obj){
        var data = obj.data;
        switch(obj.event){
            case 'edit':
                editTarget(data);
                break;
            case 'del':
                deleteTarget(data);
                break;
        }
    });

    // 编辑目标值
    function editTarget(data) {
        layer.open({
            type: 1,
            title: '编辑目标值',
            area: ['500px', '400px'],
            content: `
                <form class="layui-form" style="padding: 20px;" lay-filter="editForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">部门名称</label>
                        <div class="layui-input-block">
                            <input type="text" value="${data.dept_name}" readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">年度</label>
                        <div class="layui-input-block">
                            <input type="number" name="year" value="${data.year}" readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">目标值</label>
                        <div class="layui-input-block">
                            <input type="number" name="target_value" value="${data.target_value}" 
                                   lay-verify="required|number" class="layui-input" step="0.01" min="0.01">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <input type="text" name="remark" value="${data.remark || ''}" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="editSubmit">保存修改</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                        </div>
                    </div>
                </form>
            `,
            success: function(layero, index) {
                form.render();
                
                // 编辑表单提交
                form.on('submit(editSubmit)', function(formData){
                    var submitData = {
                        dept_id: data.dept_id,
                        year: data.year,
                        target_value: formData.field.target_value,
                        remark: formData.field.remark
                    };
                    
                    $.ajax({
                        url: '/system/dept_output/target/save',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(submitData),
                        success: function(res) {
                            if (res.success === true) {
                                layer.msg('修改成功', {icon: 1});
                                layer.close(index);
                                targetTable.reload();
                            } else {
                                layer.msg(res.msg || '修改失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('网络错误', {icon: 2});
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 删除目标值
    function deleteTarget(data) {
        layer.confirm('确定要删除该目标值吗？', {icon: 3, title: '提示'}, function(index){
            $.ajax({
                url: '/system/dept_output/target/delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({id: data.id}),
                success: function(res) {
                    if (res.success === true) {
                        layer.msg('删除成功', {icon: 1});
                        layer.close(index);
                        targetTable.reload();
                    } else {
                        layer.msg(res.msg || '删除失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误', {icon: 2});
                }
            });
        });
    }

    // 查询按钮事件
    $(document).on('click', '#searchBtn', function() {
        var year = $('#filterYear').val();
        var deptId = $('#filterDept').val();

        targetTable.reload({
            where: {
                year: year,
                dept_id: deptId
            },
            page: {
                curr: 1
            }
        });
    });

    // 重置按钮事件
    $(document).on('click', '#resetBtn', function() {
        var currentYear = new Date().getFullYear();
        $('#filterYear').val(currentYear);
        $('#filterDept').val('');
        form.render('select');

        targetTable.reload({
            where: {
                year: currentYear,
                dept_id: ''
            },
            page: {
                curr: 1
            }
        });
    });

    // 单个录入表单提交
    form.on('submit(singleSubmit)', function(data){
        $.ajax({
            url: '/system/dept_output/target/save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res) {
                if (res.success === true) {
                    layer.msg('保存成功', {icon: 1});
                    $('#singleForm')[0].reset();
                    form.render();
                    targetTable.reload();
                    // 切换到列表页面查看结果
                    $('.tab-nav li').removeClass('active');
                    $('.tab-nav li:first-child').addClass('active');
                    $('.tab-panel').hide();
                    $('#listPanel').show();
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误', {icon: 2});
            }
        });
        return false;
    });

    // Excel上传
    upload.render({
        elem: '#uploadBtn',
        url: '/system/dept_output/target/import/excel',
        accept: 'file',
        exts: 'xlsx|xls',
        before: function(obj){
            layer.load();
        },
        done: function(res){
            layer.closeAll('loading');
            if(res.success === true){
                // 显示详细的导入成功结果
                layer.alert(res.msg || '导入成功', {
                    icon: 1,
                    title: '🎉 导入成功',
                    area: ['600px', 'auto'],
                    btn: ['查看数据', '继续导入'],
                    yes: function(index) {
                        layer.close(index);
                        // 切换到列表页面查看结果
                        $('.tab-nav li').removeClass('active');
                        $('.tab-nav li:first-child').addClass('active');
                        $('.tab-panel').hide();
                        $('#listPanel').show();
                        targetTable.reload();
                    },
                    btn2: function(index) {
                        layer.close(index);
                        targetTable.reload();
                    }
                });
            } else {
                // 显示详细的导入失败结果
                layer.alert(res.msg || '导入失败', {
                    icon: 2,
                    title: '❌ 导入失败',
                    area: ['600px', 'auto'],
                    btn: ['重新尝试', '取消'],
                    yes: function(index) {
                        layer.close(index);
                        // 可以在这里重新触发文件选择
                    }
                });
            }
        },
        error: function(){
            layer.closeAll('loading');
            layer.msg('上传失败', {icon: 2});
        }
    });

    // 初始化
    initYearSelector();
});
