<!DOCTYPE html>
<html>
<head>
    <title>用户管理</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<body class="pear-container">
{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="user-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">用户</label>
                <div class="layui-input-inline">
                    <input type="text" name="name" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">手机号</label>
                <div class="layui-input-inline">
                    <input type="text" name="phone" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">试用期状态</label>
                <div class="layui-input-inline">
                    <select name="probation_status">
                        <option value="">全部</option>
                        <option value="urgent">紧急(已到期)</option>
                        <option value="warning">即将到期(7天内)</option>
                        <option value="normal">正常</option>
                        <option value="formal">正式员工</option>
                    </select>
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="user-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="user-left user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="button button-primary user-group" user-group=""> 全 部 用 户</div>
            <div class="button button-default user-group" user-group="-1"> 默 认 分 组</div>
            <div style="overflow: auto">
                <ul id="dept-tree" class="dept-tree" data-id="0"></ul>
            </div>
        </div>
    </div>
</div>
{# 用户表格 #}
<div class="user-main user-collasped">
    <div class="layui-card">
        <div class="layui-card-body">
            <table id="user-table" lay-filter="user-table"></table>
        </div>
    </div>
</div>
</body>

{% include 'system/common/footer.html' %}
{# 表格操作 #}
<script type="text/html" id="user-toolbar">
    {% if authorize("system:YG:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="pear-icon pear-icon-add"></i>
            新增
        </button>
    {% endif %}
    {% if authorize("system:YG:import") %}
    <button class="layui-btn layui-btn-sm" lay-event="import">
        <i class="pear-icon pear-icon-upload"></i>
        导入
    </button>
    {% endif %}
    {% if authorize("system:YG:edit") %}
    <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="remove_duplicates">
        <i class="pear-icon pear-icon-refresh"></i>
        数据去重
    </button>
    {% endif %}
    {% if authorize("system:YG:check_reminders") %}
    <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="check_probation">
        <i class="layui-icon layui-icon-notice"></i>
        检查试用期提醒
    </button>
    {% endif %}
    {% if authorize("system:hr_email:main") %}
    <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="hr_email_config">
        <i class="layui-icon layui-icon-email"></i>
        HR邮箱配置
    </button>
    {% endif %}
    <button class="layui-btn layui-btn-sm" lay-event="collasped">
        <i class="pear-icon pear-icon-modular"></i>
        高级
    </button>
</script>

{# 用户修改操作 #}
{% raw %}
<script type="text/html" id="user-bar">
{% endraw %}
    {% if authorize("system:YG:edit") %}
{% raw %}
        <button class="layui-btn layui-btn-xs" lay-event="edit"><i class="pear-icon pear-icon-edit"> 编辑</i>
        </button>
{% endraw %}
    {% endif %}
{% raw %}
    {{# if(d.is_formal === '试用' || d.is_formal === '实习') { }}
{% endraw %}
        {% if authorize("system:YG:send_reminder") %}
{% raw %}
            <button class="layui-btn layui-btn-warm layui-btn-xs" lay-event="send_reminder" title="发送试用期提醒">
                <i class="layui-icon layui-icon-notice"> 提醒</i>
            </button>
{% endraw %}
        {% endif %}
        {% if authorize("system:YG:edit") %}
{% raw %}
            <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="convert_formal" title="转正">
                <i class="layui-icon layui-icon-ok"> 转正</i>
            </button>
{% endraw %}
        {% endif %}
{% raw %}
    {{# } }}
{% endraw %}
    {% if authorize("system:YG:remove") %}
{% raw %}
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="remove"><i
                class="pear-icon pear-icon-ashbin"> 删除</i>
        </button>
{% endraw %}
    {% endif %}
{% raw %}
</script>
{% endraw %}

{% raw %}
<script type="text/html" id="user-enable">
    <input type="checkbox" name="enable" value="{{ d.id }}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="user-enable"
            {{# if(d.enable==1){ }} checked {{# } }} />
</script>

<script type="text/html" id="probation-status">
    {{# if(d.probation_status === 'urgent') { }}
        <span class="layui-badge layui-bg-red">紧急</span>
    {{# } else if(d.probation_status === 'warning') { }}
        <span class="layui-badge layui-bg-orange">即将到期</span>
    {{# } else if(d.probation_status === 'normal') { }}
        <span class="layui-badge layui-bg-green">正常</span>
    {{# } else if(d.probation_status === 'formal') { }}
        <span class="layui-badge layui-bg-gray">正式员工</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">-</span>
    {{# } }}
</script>

<script type="text/html" id="probation-end-date">
    {{# if(d.probation_end_date) { }}
        {{layui.util.toDateString(d.probation_end_date, "yyyy-MM-dd")}}
    {{# } else { }}
        -
    {{# } }}
</script>

<script type="text/html" id="user-createTime">
    {{layui.util.toDateString(d.create_at,  "yyyy-MM-dd HH:mm:ss")}}
</script>

<script type="text/html" id="user-updateTime">
    {{layui.util.toDateString(d.update_at,  "yyyy-MM-dd HH:mm:ss")}}
</script>
{% endraw %}


<script>
    layui.use(['table', 'dtree', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table
        let form = layui.form
        let $ = layui.jquery
        let dtree = layui.dtree
        let popup = layui.popup
        let common = layui.common
        let MODULE_PATH = "{{ url_for('system.yg_info.index') }}"
        // 表格数据
        let cols = [
            [
                {title: '编号', field: 'id', align: 'center'},
                {title: '姓名', field: 'name', align: 'center', width: 110},
                {title: '手机号', field: 'phone', align: 'center'},
                {title: '部门', field: 'dept_name', align: 'center'},
                {title: '职位', field: 'position', align: 'center'},
                {title: '入职日期', field: 'hire_date', align: 'center', templet: function(d){
                    return layui.util.toDateString(d.hire_date, "yyyy-MM-dd");
                }},
                {title: '性别', field: 'gender', align: 'center'},
                {title: '出生日期', field: 'birth_date', align: 'center', templet: function(d){
                    return layui.util.toDateString(d.birth_date, "yyyy-MM-dd");
                }},
                {title: '年龄', field: 'age', align: 'center'},
                {title: '身份证号码', field: 'id_card', align: 'center'},
                {title: '是否正式', field: 'is_formal', align: 'center'},
                {title: '试用期状态', field: 'probation_status', align: 'center', templet: '#probation-status', width: 120},
                {title: '到期日期', field: 'probation_end_date', align: 'center', templet: '#probation-end-date', width: 120},
                {title: '启用', field: 'enable', align: 'center', templet: '#user-enable', width: 120},
                {title: '注册时间', field: 'create_at', templet: '#user-createTime', align: 'center'},
                {title: '更新时间', field: 'update_at', templet: '#user-updateTime', align: 'center'},
                {title: '操作', toolbar: '#user-bar', align: 'center', width: 180}
            ]
        ]

        // 渲染表格数据
        table.render({
            elem: '#user-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#user-toolbar', /*工具栏*/
            text: {none: '暂无人员信息'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'] /*默认工具栏*/
        })

        // 公司部门树状图菜单
        dtree.render({
            elem: '#dept-tree',
            method: 'get',
            url: '/system/dept/tree',
            dataFormat: 'list',
            line: true,
            skin: 'laySimple',
            icon: '-1',
            response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'},
        })

        // 菜单栏渲染
        dtree.on('node(\'dept-tree\')', function (obj) {
            let field = form.val('user-query-form') /*用户账号查询*/
            field.deptId = obj.param.nodeId
            window.refresh(field)
        })

        //
        $('.user-group').click(function () {
            let group = $(this).attr('user-group')
            let field = form.val('user-query-form')
            if (group === '-1') {
                field.deptId = group
                $(this).removeClass('button-default')
                $(this).prev().removeClass('button-primary')
                $(this).prev().addClass('button-default')
                $(this).addClass('button-primary')
            } else {
                field.deptId = group
                $(this).removeClass('button-default')
                $(this).next().removeClass('button-primary')
                $(this).next().addClass('button-default')
                $(this).addClass('button-primary')
            }
            window.refresh(field)
        })

        table.on('tool(user-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj)
            } else if (obj.event === 'edit') {
                window.edit(obj)
            } else if (obj.event === 'send_reminder') {
                // 发送试用期提醒
                layer.confirm('确定要发送试用期提醒邮件吗？', {
                    icon: 3,
                    title: '发送提醒确认'
                }, function (index) {
                    layer.close(index);
                    let loading = layer.load();
                    $.ajax({
                        url: MODULE_PATH + 'send_probation_reminder/' + obj.data.id,
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(loading);
                            if (result.success) {
                                popup.success(result.msg);
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            layer.close(loading);
                            popup.failure('发送提醒失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'convert_formal') {
                // 员工转正
                layer.confirm('确定要将该员工转为正式员工吗？', {
                    icon: 3,
                    title: '转正确认'
                }, function (index) {
                    layer.close(index);

                    // 显示转正备注输入框
                    layer.prompt({
                        title: '转正备注（可选）',
                        formType: 2, // 多行文本
                        area: ['400px', '200px']
                    }, function(note, promptIndex) {
                        layer.close(promptIndex);

                        let loading = layer.load();
                        $.ajax({
                            url: MODULE_PATH + 'convert_to_formal/' + obj.data.id,
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                formal_note: note || ''
                            }),
                            success: function (result) {
                                layer.close(loading);
                                if (result.success) {
                                    popup.success(result.msg, function() {
                                        window.refresh(); // 刷新表格
                                    });
                                } else {
                                    popup.failure(result.msg);
                                }
                            },
                            error: function () {
                                layer.close(loading);
                                popup.failure('转正操作失败');
                            }
                        });
                    });
                });
            }
        })

        table.on('toolbar(user-table)', function (obj) {
            if (obj.event === 'add') {
                window.add()
            } else if (obj.event === 'refresh') {
                window.refresh()
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj)
            } else if (obj.event === 'collasped') {
                $('.user-left').toggleClass('user-collasped')
                $('.user-main').toggleClass('user-collasped')
                table.resize()
            } else if (obj.event === 'import') {
                layer.open({
                    type: 1,
                    title: '导入员工',
                    area: ['400px', '250px'],
                    content: $('#import-modal')
                });
            } else if (obj.event === 'remove_duplicates') {
                layer.confirm('确定要执行数据去重操作吗？此操作将删除重复的员工记录，保留最新的记录。', {
                    icon: 3,
                    title: '数据去重确认'
                }, function (index) {
                    layer.close(index);
                    let loading = layer.load();
                    $.ajax({
                        url: MODULE_PATH + 'remove_duplicates',
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(loading);
                            if (result.success) {
                                popup.success(result.msg, function () {
                                    window.refresh(); // 刷新表格
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            layer.close(loading);
                            popup.failure('数据去重失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'check_probation') {
                // 批量检查试用期提醒
                layer.confirm('确定要执行试用期提醒检查吗？系统将自动检查所有试用期员工并发送提醒邮件。', {
                    icon: 3,
                    title: '批量检查确认'
                }, function (index) {
                    layer.close(index);
                    let loading = layer.load();
                    $.ajax({
                        url: MODULE_PATH + 'check_probation_reminders',
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(loading);
                            if (result.success) {
                                popup.success(result.msg, function () {
                                    window.refresh(); // 刷新表格
                                });
                            } else {
                                popup.failure(result.msg);
                            }
                        },
                        error: function () {
                            layer.close(loading);
                            popup.failure('检查试用期提醒失败，请稍后重试');
                        }
                    });
                });
            } else if (obj.event === 'hr_email_config') {
                // HR邮箱配置
                layer.open({
                    type: 2,
                    title: 'HR邮箱配置管理',
                    area: ['90%', '80%'],
                    content: '/system/hr_email_config/',
                    maxmin: true
                });
            }
        })

        form.on('submit(user-query)', function (data) {
            window.refresh(data.field)
            return false
        })

        form.on('switch(user-enable)', function (obj) {
            let operate
            if (obj.elem.checked) {
                operate = 'enable'
            } else {
                operate = 'disable'
            }
            let loading = layer.load()
            $.ajax({
                url: '{{ url_for('system.yg_info.index') }}' + operate,
                data: JSON.stringify({id: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading)
                    if (result.success) {
                        popup.success(result.msg)
                    } else {
                        popup.failure(result.msg)
                    }
                }
            })
        })

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['550px', '550px'],
                content: MODULE_PATH + 'add'
            })
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['550px', '500px'],
                content: MODULE_PATH + 'edit/' + obj.data['id']
            })
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该用户', {icon: 3, title: '提示'}, function (index) {
                layer.close(index)
                let loading = layer.load()
                $.ajax({
                    url: MODULE_PATH + 'remove/' + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading)
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del()
                            })
                        } else {
                            popup.failure(result.msg)
                        }
                    }
                })
            })
        }


        window.refresh = function (param) {
            table.reload('user-table', {where: param})
        }

        // 添加表单提交事件
        form.on('submit(import-submit)', function (data) {
            let formData = new FormData($('#import-form')[0]);
            let loading = layer.load();
            
            $.ajax({
                url: '{{ url_for("system.yg_info.import_excel") }}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (res) {
                    layer.close(loading);
                    if (res.success) {
                        popup.success(res.msg);
                        window.refresh(); // 刷新表格
                        layer.closeAll(); // 关闭所有弹层
                    } else {
                        popup.failure(res.msg);
                    }
                },
                error: function () {
                    layer.close(loading);
                    popup.failure('导入失败，请检查文件格式');
                }
            });
            return false;
        });
    })
</script>

<!-- 在页面底部添加导入模态框 -->
<div id="import-modal" style="display: none; padding: 20px;">
    <form class="layui-form" id="import-form">
        <div class="layui-form-item">
            <label class="layui-form-label">选择文件</label>
            <div class="layui-input-block">
                <input type="file" name="file" class="layui-input" accept=".xlsx,.xls" required>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="import-submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
</html>