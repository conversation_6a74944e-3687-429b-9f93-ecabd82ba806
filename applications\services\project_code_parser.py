"""
智能项目编号解析器
处理各种复杂的项目编号格式，包括前缀、多项目、简写等格式
"""

import re
import logging
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class ParsedProjectCode:
    """解析后的项目编号结果"""
    original_code: str  # 原始编号
    parsed_codes: List[str]  # 解析后的编号列表
    format_type: str  # 格式类型
    confidence: float  # 置信度 (0.0-1.0)
    notes: str  # 解析说明
    project_type: Optional[str] = None  # 解析出的项目类型（如果有前缀）
    normalized_project_type: Optional[str] = None  # 标准化的项目类型（去除"厂外"前缀）


class ProjectCodeParser:
    """智能项目编号解析器"""
    
    def __init__(self):
        # 前缀模式：匹配各种前缀格式，包括"厂外"前缀
        self.prefix_patterns = [
            r'^(厂外)?(GS|GSM|GSCC|Gs|gS|gs|gsm|GSm|Gsm|gSM|Gscc|GScc|GsCC|gSCC|gScc|gsCC|gscc)(\d+)$',  # 厂外GS2427, GS2427, 厂外GSM2406, GSCC2425等
            r'^(厂外)?([A-Za-z]+)(\d+)$'  # 其他字母前缀，可能带厂外前缀
        ]

        # 多项目分隔符（包括中文逗号、顿号等）
        self.multi_project_separators = ['.', '/', '-', ',', '，', '、', ';', '；', ' ']

        # 简写模式：2414/15 -> 2414, 2415
        self.shorthand_pattern = r'^(\d{2,4})/(\d{1,2})$'

        # 连续简写模式：2425/26/27 -> 2425, 2426, 2427
        self.continuous_shorthand_pattern = r'^(\d{2,4})(?:/(\d{1,2}))+$'

        # 主项目-子项目模式：2405-4 -> 2405, 2405_4 -> 2405, 2405-444 -> 2405
        self.main_sub_project_pattern = r'^(\d{3,4})[-_](\d{1,3})$'

        # 纯数字模式
        self.pure_number_pattern = r'^\d+$'
    
    def parse_project_code(self, code: str) -> ParsedProjectCode:
        """
        解析项目编号
        
        Args:
            code: 原始项目编号
            
        Returns:
            ParsedProjectCode: 解析结果
        """
        if not code or not isinstance(code, str):
            return ParsedProjectCode(
                original_code=code or '',
                parsed_codes=[],
                format_type='invalid',
                confidence=0.0,
                notes='无效的项目编号'
            )
        
        # 清理输入
        cleaned_code = code.strip()
        if not cleaned_code:
            return ParsedProjectCode(
                original_code=code,
                parsed_codes=[],
                format_type='empty',
                confidence=0.0,
                notes='空项目编号'
            )
        
        logger.debug(f"开始解析项目编号: {cleaned_code}")
        
        # 1. 检查是否为连续简写格式 (2425/26/27) - 最高优先级
        continuous_shorthand_result = self._parse_continuous_shorthand_format(cleaned_code)
        if continuous_shorthand_result.parsed_codes:
            logger.debug(f"识别为连续简写格式: {continuous_shorthand_result.parsed_codes}")
            return continuous_shorthand_result

        # 2. 检查是否为简写格式 (2414/15)
        shorthand_result = self._parse_shorthand_format(cleaned_code)
        if shorthand_result.parsed_codes:
            logger.debug(f"识别为简写格式: {shorthand_result.parsed_codes}")
            return shorthand_result

        # 3. 检查是否为主项目-子项目格式 (2405-4, 2405_4)
        main_sub_result = self._parse_main_sub_project_format(cleaned_code)
        if main_sub_result.parsed_codes:
            logger.debug(f"识别为主项目-子项目格式: {main_sub_result.parsed_codes}")
            return main_sub_result

        # 4. 检查是否包含多项目分隔符
        multi_project_result = self._parse_multi_project_format(cleaned_code)
        if multi_project_result.parsed_codes:
            logger.debug(f"识别为多项目格式: {multi_project_result.parsed_codes}")
            return multi_project_result
        
        # 5. 检查是否为前缀格式
        prefix_result = self._parse_prefix_format(cleaned_code)
        if prefix_result.parsed_codes:
            logger.debug(f"识别为前缀格式: {prefix_result.parsed_codes}")
            return prefix_result

        # 6. 检查是否为纯数字格式
        pure_number_result = self._parse_pure_number_format(cleaned_code)
        if pure_number_result.parsed_codes:
            logger.debug(f"识别为纯数字格式: {pure_number_result.parsed_codes}")
            return pure_number_result

        # 7. 无法识别的格式
        logger.warning(f"无法识别的项目编号格式: {cleaned_code}")
        return ParsedProjectCode(
            original_code=code,
            parsed_codes=[cleaned_code],  # 保留原始编号
            format_type='unknown',
            confidence=0.3,
            notes=f'无法识别的格式，保留原始编号: {cleaned_code}'
        )
    
    def _parse_shorthand_format(self, code: str) -> ParsedProjectCode:
        """
        解析简写格式：2414/15 -> 2414, 2415
        
        Args:
            code: 项目编号
            
        Returns:
            ParsedProjectCode: 解析结果
        """
        match = re.match(self.shorthand_pattern, code)
        if not match:
            return ParsedProjectCode(code, [], 'not_shorthand', 0.0, '')
        
        base_number = match.group(1)
        suffix_number = match.group(2)
        
        try:
            # 计算完整的第二个编号
            base_len = len(base_number)
            suffix_len = len(suffix_number)
            
            if suffix_len >= base_len:
                # 如果后缀长度大于等于基础长度，直接使用
                second_number = suffix_number
            else:
                # 替换基础编号的后几位
                prefix = base_number[:-suffix_len]
                second_number = prefix + suffix_number
            
            parsed_codes = [base_number, second_number]
            
            return ParsedProjectCode(
                original_code=code,
                parsed_codes=parsed_codes,
                format_type='shorthand',
                confidence=0.95,
                notes=f'简写格式解析: {code} -> {", ".join(parsed_codes)}'
            )
            
        except Exception as e:
            logger.error(f"简写格式解析失败: {code}, 错误: {str(e)}")
            return ParsedProjectCode(code, [], 'shorthand_error', 0.0, f'简写格式解析错误: {str(e)}')

    def _parse_continuous_shorthand_format(self, code: str) -> ParsedProjectCode:
        """
        解析连续简写格式：2425/26/27 -> 2425, 2426, 2427

        Args:
            code: 项目编号

        Returns:
            ParsedProjectCode: 解析结果
        """
        try:
            # 分割所有部分，过滤空字符串
            parts = [part for part in code.split('/') if part.strip()]
            if len(parts) < 3:  # 至少需要3个部分才算连续简写
                return ParsedProjectCode(code, [], 'not_continuous_shorthand', 0.0, '')

            base_number = parts[0]
            base_len = len(base_number)

            # 检查基础编号是否为纯数字
            if not base_number.isdigit():
                return ParsedProjectCode(code, [], 'not_continuous_shorthand', 0.0, '')

            # 检查所有后缀是否都是简写格式（长度小于等于基础长度且为纯数字）
            for suffix in parts[1:]:
                if not suffix.isdigit():
                    return ParsedProjectCode(code, [], 'not_continuous_shorthand', 0.0, '')
                # 连续简写的后缀应该比基础编号短，或者长度相等但数值合理
                if len(suffix) > base_len:
                    return ParsedProjectCode(code, [], 'not_continuous_shorthand', 0.0, '')

            parsed_codes = [base_number]

            # 处理每个简写部分
            for suffix in parts[1:]:
                suffix_len = len(suffix)

                if suffix_len == base_len:
                    # 如果后缀长度等于基础长度，直接使用
                    full_number = suffix
                else:
                    # 替换基础编号的后几位
                    prefix = base_number[:-suffix_len]
                    full_number = prefix + suffix

                parsed_codes.append(full_number)

            return ParsedProjectCode(
                original_code=code,
                parsed_codes=parsed_codes,
                format_type='continuous_shorthand',
                confidence=0.95,
                notes=f'连续简写格式解析: {code} -> {", ".join(parsed_codes)}'
            )

        except Exception as e:
            logger.error(f"连续简写格式解析失败: {code}, 错误: {str(e)}")
            return ParsedProjectCode(code, [], 'continuous_shorthand_error', 0.0, f'连续简写格式解析错误: {str(e)}')

    def _parse_main_sub_project_format(self, code: str) -> ParsedProjectCode:
        """
        解析主项目-子项目格式：2405-4 -> 2405, 2405_4 -> 2405
        只提取主项目编号，忽略子项目部分

        Args:
            code: 项目编号

        Returns:
            ParsedProjectCode: 解析结果
        """
        match = re.match(self.main_sub_project_pattern, code)
        if not match:
            return ParsedProjectCode(code, [], 'not_main_sub_project', 0.0, '')

        main_project = match.group(1)  # 主项目编号
        sub_project = match.group(2)   # 子项目编号（忽略）

        return ParsedProjectCode(
            original_code=code,
            parsed_codes=[main_project],
            format_type='main_sub_project',
            confidence=0.9,
            notes=f'主项目-子项目格式解析: {code} -> {main_project} (忽略子项目: {sub_project})'
        )

    def _parse_multi_project_format(self, code: str) -> ParsedProjectCode:
        """
        解析多项目格式：2425.2452, 2415/2512, 2417、2430、2431、2418

        Args:
            code: 项目编号

        Returns:
            ParsedProjectCode: 解析结果
        """
        # 检查是否包含分隔符
        separator_found = None
        for sep in self.multi_project_separators:
            if sep in code:
                separator_found = sep
                break

        if not separator_found:
            return ParsedProjectCode(code, [], 'not_multi_project', 0.0, '')

        # 分割项目编号
        if separator_found == ' ':
            # 对于空格分隔，需要特殊处理，避免误分割
            # 只有当空格两边都是数字时才认为是分隔符
            import re
            space_pattern = r'\b\d+\s+\d+\b'
            if not re.search(space_pattern, code):
                return ParsedProjectCode(code, [], 'not_multi_project', 0.0, '')
            parts = re.split(r'\s+', code)
        else:
            parts = code.split(separator_found)

        parsed_codes = []

        for part in parts:
            part = part.strip()
            if not part:
                continue

            # 对每个部分进行前缀处理
            cleaned_part = self._extract_number_from_prefix(part)
            if cleaned_part:
                parsed_codes.append(cleaned_part)

        if not parsed_codes:
            return ParsedProjectCode(code, [], 'multi_project_empty', 0.0, '多项目格式解析后为空')

        # 去重并保持顺序
        unique_codes = []
        seen = set()
        for pc in parsed_codes:
            if pc not in seen:
                unique_codes.append(pc)
                seen.add(pc)

        return ParsedProjectCode(
            original_code=code,
            parsed_codes=unique_codes,
            format_type='multi_project',
            confidence=0.9,
            notes=f'多项目格式解析: {code} -> {", ".join(unique_codes)} (分隔符: {separator_found})'
        )
    
    def _parse_prefix_format(self, code: str) -> ParsedProjectCode:
        """
        解析前缀格式：厂外GS2427, GS2427, 厂外GSM2406等

        Args:
            code: 项目编号

        Returns:
            ParsedProjectCode: 解析结果
        """
        for pattern in self.prefix_patterns:
            match = re.match(pattern, code)
            if match:
                # 处理新的分组结构
                if len(match.groups()) == 3:
                    # 格式：(厂外)?(项目类型)(\d+)
                    location_prefix = match.group(1)  # "厂外" 或 None
                    project_type = match.group(2)    # "GS", "GSM" 等
                    number = match.group(3)          # "2427" 等

                    # 构建完整的项目类型
                    full_project_type = f"{location_prefix}{project_type}" if location_prefix else project_type
                    # 标准化的项目类型（去除厂外前缀）
                    normalized_project_type = project_type

                    return ParsedProjectCode(
                        original_code=code,
                        parsed_codes=[number],
                        format_type='prefix',
                        confidence=0.85,
                        notes=f'前缀格式解析: {code} -> 项目类型:{full_project_type}, 编号:{number}',
                        project_type=full_project_type,
                        normalized_project_type=normalized_project_type
                    )
                else:
                    # 兼容旧格式
                    prefix = match.group(1)
                    number = match.group(2)

                    return ParsedProjectCode(
                        original_code=code,
                        parsed_codes=[number],
                        format_type='prefix',
                        confidence=0.85,
                        notes=f'前缀格式解析: {code} -> {number} (前缀: {prefix})',
                        project_type=prefix,
                        normalized_project_type=prefix
                    )

        return ParsedProjectCode(code, [], 'not_prefix', 0.0, '')
    
    def _parse_pure_number_format(self, code: str) -> ParsedProjectCode:
        """
        解析纯数字格式：2410, 41
        
        Args:
            code: 项目编号
            
        Returns:
            ParsedProjectCode: 解析结果
        """
        if re.match(self.pure_number_pattern, code):
            return ParsedProjectCode(
                original_code=code,
                parsed_codes=[code],
                format_type='pure_number',
                confidence=1.0,
                notes=f'纯数字格式: {code}'
            )
        
        return ParsedProjectCode(code, [], 'not_pure_number', 0.0, '')
    
    def _extract_number_from_prefix(self, code: str) -> Optional[str]:
        """
        从带前缀的编号中提取数字部分

        Args:
            code: 项目编号

        Returns:
            str: 提取的数字部分，如果无法提取则返回None
        """
        # 先尝试前缀模式
        for pattern in self.prefix_patterns:
            match = re.match(pattern, code)
            if match:
                # 适应新的分组结构
                if len(match.groups()) == 3:
                    return match.group(3)  # 数字部分在第3组
                else:
                    return match.group(2)  # 兼容旧格式

        # 如果是纯数字，直接返回
        if re.match(self.pure_number_pattern, code):
            return code

        return None

    def batch_parse_project_codes(self, codes: List[str]) -> List[ParsedProjectCode]:
        """
        批量解析项目编号

        Args:
            codes: 项目编号列表

        Returns:
            List[ParsedProjectCode]: 解析结果列表
        """
        results = []
        for code in codes:
            try:
                result = self.parse_project_code(code)
                results.append(result)
            except Exception as e:
                logger.error(f"解析项目编号失败: {code}, 错误: {str(e)}")
                results.append(ParsedProjectCode(
                    original_code=code,
                    parsed_codes=[],
                    format_type='error',
                    confidence=0.0,
                    notes=f'解析异常: {str(e)}'
                ))

        return results

    def get_all_possible_codes(self, code: str) -> Set[str]:
        """
        获取一个项目编号的所有可能匹配编号

        Args:
            code: 原始项目编号

        Returns:
            Set[str]: 所有可能的编号集合
        """
        result = self.parse_project_code(code)
        possible_codes = set()

        # 添加原始编号
        possible_codes.add(code.strip())

        # 添加解析后的编号
        for parsed_code in result.parsed_codes:
            possible_codes.add(parsed_code)

        # 移除空字符串
        possible_codes.discard('')

        return possible_codes
