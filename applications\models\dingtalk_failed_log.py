import datetime
from applications.extensions import db


class DingTalkFailedLog(db.Model):
    """钉钉日志导入失败记录表"""
    __tablename__ = 'dingtalk_failed_log'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    report_id = db.Column(db.String(100), nullable=False, comment='钉钉报告ID')
    creator_name = db.Column(db.String(50), nullable=False, comment='创建人姓名')
    create_time = db.Column(db.DateTime, nullable=True, comment='原始创建时间')
    original_data = db.Column(db.Text, nullable=False, comment='原始钉钉日志数据(JSON格式)')
    failure_reason = db.Column(db.Text, nullable=False, comment='失败原因')
    work_date = db.Column(db.Date, nullable=True, comment='工作日期')
    project_type = db.Column(db.String(50), nullable=True, comment='项目类型')
    project_number = db.Column(db.String(50), nullable=True, comment='项目编号')
    work_content = db.Column(db.Text, nullable=True, comment='工作内容')
    total_hours = db.Column(db.Float, nullable=True, comment='总工时')
    department = db.Column(db.String(100), nullable=True, comment='部门')
    failed_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='失败时间')

    def __init__(self, **kwargs):
        super(DingTalkFailedLog, self).__init__(**kwargs)

    def __repr__(self):
        return f'<DingTalkFailedLog {self.id}: {self.creator_name} - {self.failure_reason}>'

    def to_dict(self):
        """转换为字典格式"""
        # 获取部门信息，如果当前记录没有部门信息，尝试通过员工姓名查找
        department = self.department
        if not department and self.creator_name:
            try:
                from applications.models import ygong, Dept
                employee = ygong.query.filter_by(name=self.creator_name, enable=1).first()
                if employee and employee.dept_id:
                    dept = Dept.query.get(employee.dept_id)
                    if dept:
                        department = dept.dept_name
            except Exception:
                # 如果查询失败，保持原值
                pass

        return {
            'id': self.id,
            'report_id': self.report_id,
            'creator_name': self.creator_name,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'original_data': self.original_data,
            'failure_reason': self.failure_reason,
            'work_date': self.work_date.strftime('%Y-%m-%d') if self.work_date else None,
            'project_type': self.project_type,
            'project_number': self.project_number,
            'work_content': self.work_content,
            'total_hours': self.total_hours,
            'department': department,
            'failed_at': self.failed_at.strftime('%Y-%m-%d %H:%M:%S') if self.failed_at else None
        }
