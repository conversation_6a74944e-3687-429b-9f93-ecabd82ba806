"""
质量异常项目绑定记录模型
记录质量异常单与项目的绑定关系，支持多项目绑定和绑定历史
"""

import datetime
from applications.extensions import db
from flask_login import UserMixin


class QualityExceptionProjectBinding(db.Model, UserMixin):
    """质量异常项目绑定记录"""
    __tablename__ = 'quality_exception_project_binding'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='绑定记录ID')
    
    # 关联字段
    quality_exception_id = db.Column(db.Integer, db.ForeignKey('quality_exception.id'), nullable=False, comment='质量异常单ID')
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), nullable=False, comment='项目ID')
    
    # 绑定信息
    binding_type = db.Column(db.String(20), nullable=False, default='auto', comment='绑定类型：auto=自动绑定, manual=手动绑定')
    binding_source = db.Column(db.String(50), nullable=True, comment='绑定来源：sync=同步时绑定, manual_bind=手动绑定, batch_bind=批量绑定')
    match_confidence = db.Column(db.Float, nullable=True, comment='匹配置信度(0.0-1.0)')
    match_method = db.Column(db.String(50), nullable=True, comment='匹配方法：exact, parsed, fuzzy等')
    
    # 原始匹配信息
    original_project_code = db.Column(db.String(100), nullable=True, comment='原始项目编号')
    parsed_project_code = db.Column(db.String(100), nullable=True, comment='解析后的项目编号')
    project_type_matched = db.Column(db.String(100), nullable=True, comment='匹配时的项目类型')
    
    # 状态字段
    is_active = db.Column(db.Boolean, default=True, nullable=False, comment='是否有效（用于软删除）')
    status = db.Column(db.String(20), default='active', nullable=False, comment='绑定状态：active=有效, inactive=无效, pending=待确认')
    
    # 操作信息
    created_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=True, comment='创建人ID')
    updated_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=True, comment='更新人ID')
    
    # 时间字段
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 备注信息
    notes = db.Column(db.Text, nullable=True, comment='绑定说明或备注')
    match_details = db.Column(db.Text, nullable=True, comment='匹配详情（JSON格式）')
    
    # 关联关系
    quality_exception = db.relationship('QualityException', backref='project_bindings', lazy=True)
    project = db.relationship('Import_project', backref='quality_exception_bindings', lazy=True)
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_bindings', lazy=True)
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_bindings', lazy=True)
    
    # 唯一约束：同一个异常单和项目只能有一个有效绑定
    __table_args__ = (
        db.UniqueConstraint('quality_exception_id', 'project_id', 'is_active', 
                          name='uk_exception_project_active'),
        db.Index('idx_exception_binding_active', 'quality_exception_id', 'is_active'),
        db.Index('idx_project_binding_active', 'project_id', 'is_active'),
        db.Index('idx_binding_type', 'binding_type'),
        db.Index('idx_binding_status', 'status'),
    )
    
    def __repr__(self):
        return f'<QualityExceptionProjectBinding {self.quality_exception_id}-{self.project_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'quality_exception_id': self.quality_exception_id,
            'project_id': self.project_id,
            'binding_type': self.binding_type,
            'binding_source': self.binding_source,
            'match_confidence': self.match_confidence,
            'match_method': self.match_method,
            'original_project_code': self.original_project_code,
            'parsed_project_code': self.parsed_project_code,
            'project_type_matched': self.project_type_matched,
            'is_active': self.is_active,
            'status': self.status,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'notes': self.notes,
            'match_details': self.match_details
        }
    
    @classmethod
    def create_binding(cls, quality_exception_id: int, project_id: int,
                      binding_type: str = 'auto', binding_source: str = 'sync',
                      match_confidence: float = None, match_method: str = None,
                      original_project_code: str = None, parsed_project_code: str = None,
                      project_type_matched: str = None, created_by: int = None,
                      notes: str = None, match_details: str = None,
                      is_active: bool = True, status: str = 'active'):
        """
        创建项目绑定记录
        
        Args:
            quality_exception_id: 质量异常单ID
            project_id: 项目ID
            binding_type: 绑定类型
            binding_source: 绑定来源
            match_confidence: 匹配置信度
            match_method: 匹配方法
            original_project_code: 原始项目编号
            parsed_project_code: 解析后的项目编号
            project_type_matched: 匹配时的项目类型
            created_by: 创建人ID
            notes: 备注
            match_details: 匹配详情
            is_active: 是否活跃
            status: 状态

        Returns:
            QualityExceptionProjectBinding: 创建的绑定记录
        """
        binding = cls(
            quality_exception_id=quality_exception_id,
            project_id=project_id,
            binding_type=binding_type,
            binding_source=binding_source,
            match_confidence=match_confidence,
            match_method=match_method,
            original_project_code=original_project_code,
            parsed_project_code=parsed_project_code,
            project_type_matched=project_type_matched,
            created_by=created_by,
            notes=notes,
            match_details=match_details,
            is_active=is_active,
            status=status
        )
        
        return binding
    
    @classmethod
    def get_active_bindings(cls, quality_exception_id: int):
        """
        获取质量异常单的所有有效绑定
        
        Args:
            quality_exception_id: 质量异常单ID
            
        Returns:
            List[QualityExceptionProjectBinding]: 有效绑定列表
        """
        return cls.query.filter_by(
            quality_exception_id=quality_exception_id,
            is_active=True,
            status='active'
        ).all()
    
    @classmethod
    def deactivate_binding(cls, binding_id: int, updated_by: int = None, notes: str = None):
        """
        停用绑定记录
        
        Args:
            binding_id: 绑定记录ID
            updated_by: 更新人ID
            notes: 停用原因
            
        Returns:
            bool: 是否成功停用
        """
        binding = cls.query.get(binding_id)
        if not binding:
            return False
        
        binding.is_active = False
        binding.status = 'inactive'
        binding.updated_by = updated_by
        if notes:
            binding.notes = f"{binding.notes or ''}\n停用原因: {notes}".strip()
        
        return True
    
    @classmethod
    def get_binding_statistics(cls):
        """
        获取绑定统计信息
        
        Returns:
            dict: 统计信息
        """
        from sqlalchemy import func
        
        # 总绑定数
        total_bindings = cls.query.filter_by(is_active=True).count()
        
        # 按绑定类型统计
        type_stats = db.session.query(
            cls.binding_type,
            func.count(cls.id).label('count')
        ).filter_by(is_active=True).group_by(cls.binding_type).all()
        
        # 按状态统计
        status_stats = db.session.query(
            cls.status,
            func.count(cls.id).label('count')
        ).filter_by(is_active=True).group_by(cls.status).all()
        
        # 平均置信度
        avg_confidence = db.session.query(
            func.avg(cls.match_confidence)
        ).filter(cls.is_active == True, cls.match_confidence.isnot(None)).scalar()
        
        return {
            'total_bindings': total_bindings,
            'type_statistics': {stat.binding_type: stat.count for stat in type_stats},
            'status_statistics': {stat.status: stat.count for stat in status_stats},
            'average_confidence': round(avg_confidence or 0.0, 3)
        }
