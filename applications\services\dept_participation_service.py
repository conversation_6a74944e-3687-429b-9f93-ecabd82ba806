"""
部门项目参与比例服务
处理部门项目参与比例的业务逻辑
"""
import datetime
from sqlalchemy import func, and_, or_
from applications.extensions.init_sqlalchemy import db
from applications.models.dept_project_participation import DeptProjectParticipation
from applications.models.import_project import Import_project
from applications.models.admin_dept import Dept
from applications.models.project_manage_dept import ProjectManageDept
from applications.models.admin_user import User


def get_design_dept_ids():
    """获取设计开发部门的ID列表"""
    design_dept_names = ['设计开发一部', '设计开发二部', '设计开发三部']
    design_depts = Dept.query.filter(Dept.dept_name.in_(design_dept_names)).all()
    return [dept.id for dept in design_depts]


def validate_participation_rate(project_id, dept_id, new_rate, exclude_id=None):
    """
    验证部门参与比例的合理性
    
    Args:
        project_id: 项目ID
        dept_id: 部门ID
        new_rate: 新的参与比例
        exclude_id: 排除的记录ID（用于编辑时排除当前记录）
    
    Returns:
        dict: 验证结果 {'valid': bool, 'message': str, 'current_total': float}
    """
    # 基础验证
    if new_rate < 0 or new_rate > 100:
        return {
            'valid': False, 
            'message': '参与比例必须在0-100%之间',
            'current_total': 0
        }
    
    # 获取设计开发部门ID列表
    design_dept_ids = get_design_dept_ids()
    
    if dept_id in design_dept_ids:
        # 设计开发部门需要联合验证
        return validate_design_dept_group(project_id, dept_id, new_rate, design_dept_ids, exclude_id)
    else:
        # 其他部门独立验证
        return {
            'valid': True, 
            'message': '验证通过',
            'current_total': new_rate
        }


def validate_design_dept_group(project_id, current_dept_id, new_rate, design_dept_ids, exclude_id=None):
    """验证设计开发部门组的参与比例"""
    # 构建查询条件
    query = DeptProjectParticipation.query.filter(
        DeptProjectParticipation.project_id == project_id,
        DeptProjectParticipation.dept_id.in_(design_dept_ids),
        DeptProjectParticipation.dept_id != current_dept_id
    )
    
    # 如果是编辑操作，排除当前记录
    if exclude_id:
        query = query.filter(DeptProjectParticipation.id != exclude_id)
    
    existing_rates = query.all()
    
    # 计算其他设计开发部门的总比例
    total_existing = sum(float(rate.participation_rate) for rate in existing_rates)
    total_with_new = total_existing + new_rate
    
    if total_with_new > 100:
        dept_details = []
        for rate in existing_rates:
            dept_name = rate.dept.dept_name if rate.dept else f"部门ID:{rate.dept_id}"
            dept_details.append(f"{dept_name}({rate.participation_rate}%)")
        
        detail_msg = "，".join(dept_details) if dept_details else "无"
        
        return {
            'valid': False, 
            'message': f'设计开发部门总参与比例不能超过100%。当前其他设计开发部门已占用{total_existing}%（{detail_msg}），新增{new_rate}%后总计{total_with_new}%',
            'current_total': total_with_new
        }
    
    return {
        'valid': True, 
        'message': f'验证通过，设计开发部门总比例将为{total_with_new}%',
        'current_total': total_with_new
    }


def save_participation_rate(project_id, dept_id, participation_rate, remark=None, created_by=None):
    """
    保存部门项目参与比例
    
    Args:
        project_id: 项目ID
        dept_id: 部门ID
        participation_rate: 参与比例
        remark: 备注
        created_by: 创建人ID
    
    Returns:
        dict: 操作结果 {'success': bool, 'message': str, 'data': dict}
    """
    try:
        # 验证参与比例
        validation = validate_participation_rate(project_id, dept_id, participation_rate)
        if not validation['valid']:
            return {
                'success': False,
                'message': validation['message'],
                'data': None
            }
        
        # 检查是否已存在相同记录
        existing = DeptProjectParticipation.query.filter_by(
            project_id=project_id,
            dept_id=dept_id
        ).first()
        
        if existing:
            return {
                'success': False,
                'message': '该项目的部门参与比例已存在，请使用编辑功能修改',
                'data': None
            }
        
        # 创建新记录
        new_participation = DeptProjectParticipation(
            project_id=project_id,
            dept_id=dept_id,
            participation_rate=participation_rate,
            remark=remark,
            created_by=created_by
        )
        
        db.session.add(new_participation)
        db.session.commit()
        
        return {
            'success': True,
            'message': '保存成功',
            'data': new_participation.to_dict()
        }
        
    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'保存失败：{str(e)}',
            'data': None
        }


def update_participation_rate(participation_id, participation_rate, remark=None):
    """
    更新部门项目参与比例
    
    Args:
        participation_id: 参与比例记录ID
        participation_rate: 新的参与比例
        remark: 备注
    
    Returns:
        dict: 操作结果 {'success': bool, 'message': str, 'data': dict}
    """
    try:
        # 查找记录
        participation = DeptProjectParticipation.query.get(participation_id)
        if not participation:
            return {
                'success': False,
                'message': '记录不存在',
                'data': None
            }
        
        # 验证参与比例（排除当前记录）
        validation = validate_participation_rate(
            participation.project_id, 
            participation.dept_id, 
            participation_rate,
            exclude_id=participation_id
        )
        
        if not validation['valid']:
            return {
                'success': False,
                'message': validation['message'],
                'data': None
            }
        
        # 更新记录
        participation.participation_rate = participation_rate
        participation.remark = remark
        participation.updated_at = datetime.datetime.now()
        
        db.session.commit()
        
        return {
            'success': True,
            'message': '更新成功',
            'data': participation.to_dict()
        }
        
    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'更新失败：{str(e)}',
            'data': None
        }


def delete_participation_rate(participation_id):
    """
    删除部门项目参与比例

    Args:
        participation_id: 参与比例记录ID

    Returns:
        dict: 操作结果 {'success': bool, 'message': str}
    """
    try:
        # 查找记录
        participation = DeptProjectParticipation.query.get(participation_id)
        if not participation:
            return {
                'success': False,
                'message': '记录不存在'
            }

        # 删除记录
        db.session.delete(participation)
        db.session.commit()

        return {
            'success': True,
            'message': '删除成功'
        }

    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'删除失败：{str(e)}'
        }


def get_participation_list(page=1, per_page=20, project_type_id=None, project_code=None, dept_id=None):
    """
    获取部门项目参与比例列表

    Args:
        page: 页码
        per_page: 每页数量
        project_type_id: 项目类型ID
        project_code: 项目编号
        dept_id: 部门ID

    Returns:
        dict: 分页数据 {'items': list, 'total': int, 'page': int, 'per_page': int}
    """
    try:
        # 构建查询
        query = db.session.query(DeptProjectParticipation)\
            .join(Import_project, DeptProjectParticipation.project_id == Import_project.id)\
            .join(Dept, DeptProjectParticipation.dept_id == Dept.id)\
            .join(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id)

        # 添加筛选条件
        if project_type_id:
            query = query.filter(Import_project.dept_id == project_type_id)

        if project_code:
            query = query.filter(Import_project.project_code.like(f'%{project_code}%'))

        if dept_id:
            if isinstance(dept_id, list):
                # 如果dept_id是列表，使用in_查询
                query = query.filter(DeptProjectParticipation.dept_id.in_(dept_id))
            else:
                # 如果dept_id是单个值，使用等于查询
                query = query.filter(DeptProjectParticipation.dept_id == dept_id)

        # 排序
        query = query.order_by(DeptProjectParticipation.created_at.desc())

        # 分页
        try:
            # 尝试新版本的分页方法
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False,
                max_per_page=None
            )
        except TypeError:
            # 如果新版本方法失败，尝试旧版本方法
            pagination = query.paginate(page, per_page, False)

        # 转换数据格式
        items = []
        for participation in pagination.items:
            item_data = participation.to_dict()
            # 添加项目类型信息
            if participation.project and participation.project.dept_id:
                project_type = ProjectManageDept.query.get(participation.project.dept_id)
                item_data['project_type_name'] = project_type.dept_name if project_type else '未知类型'
            else:
                item_data['project_type_name'] = '未知类型'

            items.append(item_data)

        return {
            'items': items,
            'total': pagination.total,
            'page': pagination.page,
            'per_page': pagination.per_page,
            'pages': pagination.pages
        }

    except Exception as e:
        print(f"❌ 获取参与比例列表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'items': [],
            'total': 0,
            'page': page,
            'per_page': per_page,
            'pages': 0,
            'error': str(e)
        }


def get_project_participation_summary(project_id):
    """
    获取项目的部门参与比例汇总

    Args:
        project_id: 项目ID

    Returns:
        dict: 汇总信息 {'participations': list, 'total_rate': float, 'design_dept_total': float}
    """
    try:
        # 获取项目的所有参与比例
        participations = db.session.query(DeptProjectParticipation)\
            .join(Dept, DeptProjectParticipation.dept_id == Dept.id)\
            .filter(DeptProjectParticipation.project_id == project_id)\
            .order_by(Dept.dept_name)\
            .all()

        # 计算总比例
        total_rate = sum(float(p.participation_rate) for p in participations)

        # 计算设计开发部门总比例
        design_dept_ids = get_design_dept_ids()
        design_dept_total = sum(
            float(p.participation_rate)
            for p in participations
            if p.dept_id in design_dept_ids
        )

        # 转换数据格式
        participation_list = [p.to_dict() for p in participations]

        return {
            'participations': participation_list,
            'total_rate': total_rate,
            'design_dept_total': design_dept_total,
            'design_dept_ids': design_dept_ids
        }

    except Exception as e:
        return {
            'participations': [],
            'total_rate': 0,
            'design_dept_total': 0,
            'design_dept_ids': [],
            'error': str(e)
        }
