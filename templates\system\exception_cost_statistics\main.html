<!DOCTYPE html>
<html>
<head>
    <title>异常成本统计</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .search-form .layui-form-item {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .search-form .layui-form-label {
            width: 80px;
            height: 38px;
            line-height: 38px;
            padding: 0 10px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }
        .search-form .layui-input-block {
            margin-left: 90px;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .search-form .layui-input, .search-form .layui-select {
            height: 38px;
            line-height: 38px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.3s;
            width: 200px;
        }
        .search-form .layui-input:focus {
            border-color: #009688;
        }
        .search-form .btn-group {
            display: flex;
            align-items: center;
            gap: 10px;
            height: 38px;
        }
        .search-form .btn-group .layui-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 15px;
            margin: 0;
        }
        .stats-container {
            background: #fff;
            padding: 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
            margin-bottom: 15px;
        }
        .stats-summary {
            display: flex;
            justify-content: space-around;
            text-align: center;
            margin-bottom: 20px;
        }
        .stats-item {
            flex: 1;
            padding: 15px;
            border-radius: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            margin: 0 10px;
        }
        .stats-item:first-child {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stats-item:nth-child(2) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stats-item:last-child {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stats-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .table-container {
            background: #fff;
            padding: 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
            margin-bottom: 15px;
        }
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .table-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        .table-actions {
            display: flex;
            gap: 10px;
        }
        .chart-container {
            background: #fff;
            padding: 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
            margin-bottom: 15px;
        }
        .chart-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-item {
            flex: 1;
            min-height: 400px;
        }
        .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        /* Layui表格自定义样式 */
        .layui-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            font-weight: 600;
            text-align: center;
            border: none;
        }
        .layui-table td {
            text-align: center;
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .layui-table tbody tr {
            transition: all 0.3s ease;
        }
        .layui-table tbody tr:hover {
            background-color: #f8f9fa !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .layui-table-even {
            background-color: #fafafa;
        }
        .layui-table-page {
            border-top: 2px solid #f0f0f0;
            padding: 15px 15px;
        }
        /* 表格容器圆角 */
        .layui-table-view {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        /* 表格对齐优化 */
        .layui-table-view .layui-table-header {
            overflow: visible !important;
        }
        .layui-table-view .layui-table-body {
            overflow-x: auto;
        }
        .layui-table th, .layui-table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
        }
        /* 确保表格宽度一致 */
        .layui-table-header table, .layui-table-body table {
            table-layout: fixed;
            width: 100% !important;
        }
        /* 移除固定列样式冲突 */
        .layui-table-fixed {
            display: none !important;
        }
        /* 表格容器宽度控制 */
        #person-table + .layui-table-view {
            width: 100%;
            overflow: hidden;
        }
        .pagination-container {
            margin-top: 20px;
            text-align: center;
        }
        @media (max-width: 768px) {
            .stats-summary {
                flex-direction: column;
            }
            .stats-item {
                margin: 5px 0;
            }
            .chart-row {
                flex-direction: column;
            }
            .search-form .layui-input-block {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }
            .search-form .layui-input, .search-form .layui-select {
                width: 100%;
            }
        }
    </style>
</head>

<body class="pear-container">
    <!-- 页面标题 -->
    <div class="stats-container">
        <h2 style="margin: 0; color: #333; font-size: 18px; font-weight: 500;">
            <i class="layui-icon layui-icon-chart-screen" style="margin-right: 8px;"></i>
            异常成本统计分析
        </h2>
    </div>

    <!-- 筛选条件 -->
    <div class="search-form">
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-form-item">
                <label class="layui-form-label">时间范围</label>
                <div class="layui-input-block">
                    <input type="text" name="start_date" placeholder="开始时间" class="layui-input" id="start_date">
                    <span style="margin: 0 10px;">至</span>
                    <input type="text" name="end_date" placeholder="结束时间" class="layui-input" id="end_date">
                    <select name="dept_id" lay-filter="dept_select">
                        <option value="">全部部门</option>
                        {% for dept in depts %}
                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                        {% endfor %}
                    </select>
                    <div class="btn-group">
                        <button type="button" class="layui-btn" lay-submit lay-filter="search-btn">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                        {% if authorize('system:exception_cost:unit_cost_config') %}
                        <button type="button" class="layui-btn layui-btn-normal" id="unit-cost-config-btn">
                            <i class="layui-icon layui-icon-set"></i>工时成本配置
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 统计概览 -->
    <div class="stats-container">
        <div class="stats-summary">
            <div class="stats-item">
                <div class="stats-value" id="total-exceptions">0</div>
                <div class="stats-label">异常总数</div>
            </div>
            <div class="stats-item">
                <div class="stats-value" id="total-hours">0</div>
                <div class="stats-label">总预估工时(小时)</div>
            </div>
            <div class="stats-item">
                <div class="stats-value" id="total-cost">0</div>
                <div class="stats-label">总成本影响</div>
            </div>
        </div>
    </div>

    <!-- 图表展示 -->
    <div class="chart-container">
        <div class="chart-row">
            <div class="chart-item">
                <div class="chart-title">部门异常分布</div>
                <div id="dept-pie-chart" style="width: 100%; height: 400px;"></div>
            </div>
            <div class="chart-item">
                <div class="chart-title">月度成本影响趋势</div>
                <div id="cost-trend-chart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
        <div class="chart-row">
            <div class="chart-item" style="flex: 2;">
                <div class="chart-title">部门异常柏拉图分析</div>
                <div id="pareto-chart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
    </div>



 

    <!-- 部门异常统计表格 -->
    <div class="table-container">
        <div class="table-header">
            <div class="table-title">部门异常统计</div>
            <!-- <div class="table-actions">
                <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportPersonData()">
                    <i class="layui-icon layui-icon-export"></i>导出Excel
                </button>
            </div> -->
        </div>
        <table id="person-table" lay-filter="person-table"></table>
    </div>

       <!-- 部门成本对比分析 -->
       <div class="table-container">
        <div class="table-header">
            <div class="table-title">部门成本对比分析</div>
            <div class="table-actions">
                <!-- <button class="layui-btn layui-btn-sm layui-btn-normal" id="export-comparison-btn">
                    <i class="layui-icon layui-icon-export"></i>导出对比数据
                </button> -->
            </div>
        </div>
        <table id="comparison-table" lay-filter="comparison-table"></table>
    </div>

    {% include 'system/common/footer.html' %}
    <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
    <script>
        layui.use(['form', 'laydate', 'laypage', 'table', 'jquery'], function(){
            var form = layui.form;
            var laydate = layui.laydate;
            var laypage = layui.laypage;
            var table = layui.table;
            var $ = layui.jquery;

            // 设置当年默认时间范围
            var currentYear = new Date().getFullYear();
            var startDate = currentYear + '-01-01';
            var endDate = currentYear + '-12-31';

            // 初始化日期选择器并设置默认值
            laydate.render({
                elem: '#start_date',
                type: 'date',
                value: startDate
            });

            laydate.render({
                elem: '#end_date',
                type: 'date',
                value: endDate
            });

            // 搜索表单提交
            form.on('submit(search-btn)', function(data){
                loadData();
                return false;
            });

            // 加载数据
            function loadData() {
                var formData = form.val('search-form');
                loadPersonStatistics(formData);
                loadChartData(formData);
                loadSummaryData(formData);
                loadComparisonData(formData);
            }

            // 加载统计概览数据
            function loadSummaryData(params) {
                $.ajax({
                    url: '/system/exception_cost_statistics/api/dept_statistics',
                    type: 'GET',
                    data: params,
                    success: function(res) {
                        if (res.code === 0) {
                            updateSummary(res.summary);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('加载统计概览数据失败', {icon: 2});
                    }
                });
            }

            // 更新统计概览
            function updateSummary(summary) {
                $('#total-exceptions').text(summary.total_exceptions);
                $('#total-hours').text(summary.total_hours);
                $('#total-cost').text(summary.total_cost);
            }

            // 加载责任人统计数据
            function loadPersonStatistics(params) {
                $.ajax({
                    url: '/system/exception_cost_statistics/api/person_statistics',
                    type: 'GET',
                    data: params,
                    success: function(res) {
                        if (res.code === 0) {
                            renderPersonTable(res.data);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('加载责任人统计数据失败', {icon: 2});
                    }
                });
            }

            // 初始化部门成本对比表格
            var comparisonTableIns = table.render({
                elem: '#comparison-table',
                id: 'comparison-table',
                even: true,
                size: 'lg',
                autoSort: false,
                cellMinWidth: 80,
                toolbar: '#toolbarDemo',
                defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'],
                cols: [[
                    {field: 'dept_name', title: '部门名称', width: 150, align: 'center', fixed: 'left',
                     templet: function(d) {
                         return '<span style="font-weight: 600; color: #333;">' + (d.dept_name || '未分配') + '</span>';
                     }
                    },
                    {field: 'responsible_count', title: '被开责任异常数', width: 150, align: 'center', sort: true,
                     templet: function(d) {
                         var count = d.responsible_count || 0;
                         var color = count > 15 ? '#e74c3c' : count > 5 ? '#f39c12' : '#27ae60';
                         return '<span style="color: ' + color + '; font-weight: 500;">' + count + '</span>';
                     }
                    },
                    {field: 'responsible_cost', title: '被开责任成本', width: 150, align: 'center', sort: true,
                     templet: function(d) {
                         var cost = parseFloat(d.responsible_cost || 0);
                         var color = cost > 5000 ? '#e74c3c' : cost > 1000 ? '#f39c12' : cost > 0 ? '#27ae60' : '#95a5a6';
                         return '<span style="color: ' + color + '; font-weight: 600;">¥' + cost.toFixed(0) + '</span>';
                     }
                    },
                    {field: 'originator_count', title: '发起异常数', width: 120, align: 'center', sort: true,
                     templet: function(d) {
                         var count = d.originator_count || 0;
                         var color = count > 15 ? '#e74c3c' : count > 5 ? '#f39c12' : '#3498db';
                         return '<span style="color: ' + color + '; font-weight: 500;">' + count + '</span>';
                     }
                    },
                    {field: 'originator_cost', title: '处理异常成本', width: 150, align: 'center', sort: true,
                     templet: function(d) {
                         var cost = parseFloat(d.originator_cost || 0);
                         var color = cost > 5000 ? '#e74c3c' : cost > 1000 ? '#f39c12' : cost > 0 ? '#3498db' : '#95a5a6';
                         return '<span style="color: ' + color + '; font-weight: 600;">¥' + cost.toFixed(0) + '</span>';
                     }
                    },
                    {field: 'cost_gap', title: '成本差距', width: 130, align: 'center', sort: true,
                     templet: function(d) {
                         var gap = parseFloat(d.cost_gap || 0);
                         var color, icon, bgColor;
                         if (gap > 1000) {
                             color = '#e74c3c'; icon = '⬆️'; bgColor = 'rgba(231, 76, 60, 0.1)';
                         } else if (gap < -1000) {
                             color = '#3498db'; icon = '⬇️'; bgColor = 'rgba(52, 152, 219, 0.1)';
                         } else {
                             color = '#27ae60'; icon = '⚖️'; bgColor = 'rgba(39, 174, 96, 0.1)';
                         }
                         return '<div style="background: ' + bgColor + '; padding: 4px 8px; border-radius: 4px;">' +
                                '<span style="color: ' + color + '; font-weight: 600;">' + icon + ' ¥' + Math.abs(gap).toFixed(0) + '</span></div>';
                     }
                    },
                    {field: 'cost_gap_ratio', title: '差距比例', width: 120, align: 'center', sort: true,
                     templet: function(d) {
                         var ratio = parseFloat(d.cost_gap_ratio || 0);
                         var color = Math.abs(ratio) > 50 ? '#e74c3c' : Math.abs(ratio) > 20 ? '#f39c12' : '#27ae60';
                         return '<span style="color: ' + color + '; font-weight: 500;">' + ratio.toFixed(1) + '%</span>';
                     }
                    }
                ]],
                data: [],
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    curr: 1,
                    limit: 15,
                    limits: [10, 15, 20, 30],
                    groups: 5
                },
                loading: true,
                text: {
                    none: '<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-chart" style="font-size: 40px; display: block; margin-bottom: 15px; opacity: 0.5;"></i><div style="font-size: 16px;">暂无对比数据</div><div style="font-size: 12px; margin-top: 5px; opacity: 0.7;">请调整筛选条件后重试</div></div>'
                }
            });

            // 渲染部门成本对比表格数据
            function renderComparisonTable(data) {
                table.reload('comparison-table', {
                    data: data
                });
            }

            // 初始化部门异常统计表格
            var personTableIns = table.render({
                elem: '#person-table',
                id: 'person-table',
                // height: 500,
                even: true, // 开启隔行背景
                size: 'lg', // 表格尺寸
                autoSort: false, // 禁用前端自动排序
                cellMinWidth: 80, // 最小列宽
                cols: [[
                    {field: 'responsible_dept_name', title: '部门名称', width: 180, align: 'center',
                     templet: function(d) {
                         return '<span style="font-weight: 500; color: #333;">' + (d.responsible_dept_name || '未分配') + '</span>';
                     }
                    },
                    {field: 'exception_count', title: '异常数量', width: 140, align: 'center', sort: true,
                     templet: function(d) {
                         var count = d.exception_count || 0;
                         var color = count > 20 ? '#ff5722' : count > 10 ? '#ff9800' : '#4caf50';
                         return '<span style="color: ' + color + '; font-weight: 500;">' + count + '</span>';
                     }
                    },
                    {field: 'total_hours', title: '总预估工时(小时)', width: 180, align: 'center', sort: true,
                     templet: function(d) {
                         var hours = parseFloat(d.total_hours || 0);
                         return '<span style="color: #2196f3; font-weight: 500;">' + hours.toFixed(1) + '</span>';
                     }
                    },
                    {field: 'avg_hours', title: '平均预估工时(小时)', width: 180, align: 'center', sort: true,
                     templet: function(d) {
                         var avgHours = parseFloat(d.avg_hours || 0);
                         return '<span style="color: #607d8b;">' + avgHours.toFixed(2) + '</span>';
                     }
                    },
                    {field: 'cost_impact', title: '总成本影响', width: 160, align: 'center', sort: true,
                     templet: function(d) {
                         var cost = parseFloat(d.cost_impact || 0);
                         var color = cost > 5000 ? '#d32f2f' : cost > 1000 ? '#f57c00' : cost > 0 ? '#388e3c' : '#999';
                         var icon = cost > 0 ? '💰' : '➖';
                         return '<span style="color: ' + color + '; font-weight: 600; font-size: 14px;">' +
                                icon + ' ' + cost.toFixed(2) + '元</span>';
                     }
                    }
                ]],
                toolbar: '#toolbarDemo',
                defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'],
                data: [],
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    curr: 1,
                    limit: 20,
                    limits: [10, 20, 30, 50, 100],
                    groups: 5
                },
                loading: true,
                text: {
                    none: '<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-face-cry" style="font-size: 40px; display: block; margin-bottom: 15px; opacity: 0.5;"></i><div style="font-size: 16px;">暂无数据</div><div style="font-size: 12px; margin-top: 5px; opacity: 0.7;">请调整筛选条件后重试</div></div>'
                },
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    // 添加表格行悬停效果
                    $('.layui-table-body tr').hover(
                        function() {
                            $(this).css('background-color', '#f8f9fa');
                        },
                        function() {
                            $(this).css('background-color', '');
                        }
                    );

                    // 修复表格对齐问题
                    setTimeout(function() {
                        adjustTableAlignment();
                    }, 100);
                }

            });

            // 监听部门异常统计表格排序事件
            table.on('sort(person-table)', function(obj){
                var formData = form.val('search-form');
                formData.sort_field = obj.field;
                formData.sort_order = obj.type; // asc 或 desc
                loadPersonStatistics(formData);
            });

            // 监听部门成本对比表格排序事件
            table.on('sort(comparison-table)', function(obj){
                var formData = form.val('search-form');
                formData.sort_field = obj.field;
                formData.sort_order = obj.type; // asc 或 desc
                loadComparisonData(formData);
            });

            // 监听部门异常统计表格工具栏事件
            table.on('toolbar(person-table)', function(obj){
                switch(obj.event){
                    case 'refresh':
                        var formData = form.val('search-form');
                        loadPersonStatistics(formData);
                        break;
                }
            });

            // 监听部门成本对比表格工具栏事件
            table.on('toolbar(comparison-table)', function(obj){
                switch(obj.event){
                    case 'refresh':
                        var formData = form.val('search-form');
                        loadComparisonData(formData);
                        break;
                }
            });

            // 加载部门成本对比数据
            function loadComparisonData(params) {
                $.ajax({
                    url: '/system/exception_cost_statistics/api/dept_comparison',
                    type: 'GET',
                    data: params,
                    success: function(res) {
                        if (res.code === 0) {
                            renderComparisonTable(res.data);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('加载部门对比数据失败', {icon: 2});
                    }
                });
            }

            // 渲染部门异常统计表格数据
            function renderPersonTable(data) {
                // 重载表格数据
                table.reload('person-table', {
                    data: data
                });
            }



            // 加载图表数据
            function loadChartData(params) {
                $.ajax({
                    url: '/system/exception_cost_statistics/api/chart_data',
                    type: 'GET',
                    data: params,
                    success: function(res) {
                        if (res.code === 0) {
                            renderCharts(res.data);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('加载图表数据失败', {icon: 2});
                    }
                });
            }

            // 渲染图表
            function renderCharts(data) {
                // 确保ECharts已加载
                if (typeof echarts === 'undefined') {
                    layer.msg('图表库加载失败，请刷新页面重试', {icon: 2});
                    return;
                }

                // 部门异常分布饼图
                var deptPieChart = echarts.init(document.getElementById('dept-pie-chart'));
                var deptPieOption = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        right: 10,
                        top: 20,
                        bottom: 20,
                        itemWidth: 14,
                        itemHeight: 14,
                        textStyle: {
                            fontSize: 12
                        }
                    },
                    series: [{
                        name: '部门异常分布',
                        type: 'pie',
                        radius: ['20%', '60%'],
                        center: ['40%', '50%'],
                        data: data.pie_data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}: {c}',
                            fontSize: 11
                        },
                        labelLine: {
                            show: true,
                            length: 10,
                            length2: 5
                        }
                    }]
                };
                deptPieChart.setOption(deptPieOption);

                // 月度成本影响趋势图
                var costTrendChart = echarts.init(document.getElementById('cost-trend-chart'));

                // 处理月度成本数据
                var costTrendData = {
                    months: [],
                    costs: [],
                    counts: []
                };

                if (data.cost_trend_data && data.cost_trend_data.length > 0) {
                    // 检查成本数据来源
                    var hasCalculatedCost = data.cost_trend_data.some(item => item.cost_source === 'calculated');
                    var hasDatabaseCost = data.cost_trend_data.some(item => item.cost_source === 'database');
                    var hasRealCostData = hasCalculatedCost || hasDatabaseCost;

                    data.cost_trend_data.forEach(item => {
                        costTrendData.months.push(item.month);
                        costTrendData.counts.push(parseInt(item.exception_count || 0));
                        costTrendData.costs.push(parseFloat(item.total_cost || 0));
                    });

                    // 设置全局变量用于标题显示
                    window.costDataSource = hasCalculatedCost ? 'calculated' : (hasDatabaseCost ? 'database' : 'estimated');
                } else {
                    // 如果没有专门的成本趋势数据，使用现有趋势数据估算
                    if (data.trend_data && data.trend_data.months) {
                        costTrendData.months = data.trend_data.months;
                        costTrendData.counts = data.trend_data.counts;
                        // 基于异常数量估算成本（平均每个异常1500元）
                        costTrendData.costs = data.trend_data.counts.map(count => count * 1500);
                        window.costDataSource = 'estimated';
                    }
                }

                // 根据数据来源设置标题
                var costDataSource = window.costDataSource || 'estimated';
                var subtitleText = '';
                var subtitleColor = '#666';

                switch(costDataSource) {
                    case 'calculated':
                        subtitleText = '基于部门真实时薪计算 (预估工时 × 部门时薪)';
                        subtitleColor = '#27ae60';
                        break;
                    case 'database':
                        subtitleText = '数据库中记录的实际成本影响';
                        subtitleColor = '#3498db';
                        break;
                    case 'estimated':
                    default:
                        subtitleText = '估算成本影响 (按1500元/异常估算)';
                        subtitleColor = '#ff9800';
                        break;
                }

                var costTrendOption = {
                    title: {
                        text: '',
                        subtext: subtitleText,
                        left: 'center',
                        textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            fontSize: 12,
                            color: subtitleColor
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        },
                        formatter: function(params) {
                            var result = '<div style="font-weight: bold; margin-bottom: 8px; font-size: 14px;">' + params[0].name + '</div>';
                            params.forEach(function(item) {
                                if (item.seriesName === '成本影响') {
                                    result += '<div style="margin-bottom: 4px;">' + item.marker + item.seriesName + ': <span style="font-weight: bold; color: #e74c3c;">¥' + item.value.toLocaleString() + '</span></div>';
                                } else {
                                    result += '<div style="margin-bottom: 4px;">' + item.marker + item.seriesName + ': <span style="font-weight: bold; color: #3498db;">' + item.value + '个</span></div>';
                                }
                            });

                            // 添加成本计算说明
                            var costSource = window.costDataSource || 'estimated';
                            var sourceText = '';
                            switch(costSource) {
                                case 'calculated':
                                    sourceText = '<div style="margin-top: 8px; padding-top: 6px; border-top: 1px solid #eee; font-size: 11px; color: #27ae60;">💡 基于部门真实时薪计算</div>';
                                    break;
                                case 'database':
                                    sourceText = '<div style="margin-top: 8px; padding-top: 6px; border-top: 1px solid #eee; font-size: 11px; color: #3498db;">📊 数据库实际记录</div>';
                                    break;
                                case 'estimated':
                                    sourceText = '<div style="margin-top: 8px; padding-top: 6px; border-top: 1px solid #eee; font-size: 11px; color: #ff9800;">📈 估算数据</div>';
                                    break;
                            }
                            result += sourceText;

                            return result;
                        }
                    },
                    legend: {
                        data: ['成本影响', '异常数量'],
                        top: 40
                    },
                    xAxis: {
                        type: 'category',
                        data: costTrendData.months,
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 45,
                            fontSize: 11
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        name: '成本影响(元)',
                        position: 'left',
                        axisLabel: {
                            formatter: function(value) {
                                if (value >= 10000) {
                                    return (value / 10000).toFixed(1) + '万';
                                }
                                return value.toLocaleString();
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed'
                            }
                        }
                    }, {
                        type: 'value',
                        name: '异常数量',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}个'
                        }
                    }],
                    series: [{
                        name: '成本影响',
                        type: 'bar',
                        data: costTrendData.costs,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#ff6b6b'},
                                {offset: 0.5, color: '#ee5a52'},
                                {offset: 1, color: '#e74c3c'}
                            ])
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                if (params.value >= 10000) {
                                    return (params.value / 10000).toFixed(1) + '万';
                                }
                                return params.value.toLocaleString();
                            },
                            fontSize: 10,
                            color: '#e74c3c',
                            fontWeight: 'bold'
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(231, 76, 60, 0.5)'
                            }
                        }
                    }, {
                        name: '异常数量',
                        type: 'line',
                        yAxisIndex: 1,
                        data: costTrendData.counts,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        lineStyle: {
                            color: '#3498db',
                            width: 3
                        },
                        itemStyle: {
                            color: '#3498db',
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}',
                            fontSize: 10,
                            color: '#3498db',
                            fontWeight: 'bold'
                        }
                    }]
                };
                costTrendChart.setOption(costTrendOption);

                // 部门异常柏拉图分析
                var paretoChart = echarts.init(document.getElementById('pareto-chart'));

                // 处理柏拉图数据
                var paretoData = [];
                if (data.pie_data && data.pie_data.length > 0) {
                    // 按异常数量降序排序
                    var sortedData = data.pie_data.slice().sort((a, b) => b.value - a.value);

                    // 计算总数和累积百分比
                    var total = sortedData.reduce((sum, item) => sum + item.value, 0);
                    var cumulative = 0;

                    paretoData = sortedData.map(item => {
                        cumulative += item.value;
                        return {
                            name: item.name,
                            value: item.value,
                            percentage: ((cumulative / total) * 100).toFixed(1)
                        };
                    });
                }

                var paretoOption = {
                    title: {
                        text: '',
                        subtext: '',
                        left: 'center',
                        textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            fontSize: 12,
                            color: '#666'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        },
                        formatter: function(params) {
                            var result = '<div style="font-weight: bold; margin-bottom: 5px;">' + params[0].name + '</div>';
                            params.forEach(function(item) {
                                if (item.seriesName === '异常数量') {
                                    result += '<div>' + item.marker + item.seriesName + ': <span style="font-weight: bold; color: #188df0;">' + item.value + '</span></div>';
                                } else {
                                    result += '<div>' + item.marker + item.seriesName + ': <span style="font-weight: bold; color: #ff7f0e;">' + item.value + '%</span></div>';
                                }
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['异常数量', '累积百分比'],
                        top: 10
                    },
                    xAxis: {
                        type: 'category',
                        data: paretoData.map(item => item.name),
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            rotate: 45,
                            fontSize: 11
                        }
                    },
                    yAxis: [{
                        type: 'value',
                        name: '异常数量',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }, {
                        type: 'value',
                        name: '累积百分比(%)',
                        position: 'right',
                        min: 0,
                        max: 100,
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }],
                    series: [{
                        name: '异常数量',
                        type: 'bar',
                        data: paretoData.map(item => item.value),
                        itemStyle: {
                            color: function(params) {
                                // 根据数据大小设置不同颜色
                                var colors = ['#d32f2f', '#f57c00', '#388e3c', '#1976d2', '#7b1fa2'];
                                return colors[params.dataIndex % colors.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}',
                            fontSize: 11,
                            fontWeight: 'bold'
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        barWidth: '60%'
                    }, {
                        name: '累积百分比',
                        type: 'line',
                        yAxisIndex: 1,
                        data: paretoData.map(item => parseFloat(item.percentage)),
                        smooth: false,
                        symbol: 'circle',
                        symbolSize: 8,
                        lineStyle: {
                            color: '#ff7f0e',
                            width: 4
                        },
                        itemStyle: {
                            color: '#ff7f0e',
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%',
                            fontSize: 10,
                            color: '#ff7f0e',
                            fontWeight: 'bold'
                        },
                        markLine: {
                            data: [{
                                yAxis: 80,
                                lineStyle: {
                                    color: '#ff4444',
                                    type: 'dashed',
                                    width: 3
                                },
                                label: {
                                    formatter: '80%关键线',
                                    position: 'end',
                                    color: '#ff4444',
                                    fontWeight: 'bold',
                                    backgroundColor: 'rgba(255, 68, 68, 0.1)',
                                    padding: [4, 8],
                                    borderRadius: 4
                                }
                            }]
                        },
                        markArea: {
                            silent: true,
                            data: [[{
                                yAxis: 0,
                                itemStyle: {
                                    color: 'rgba(255, 68, 68, 0.05)'
                                }
                            }, {
                                yAxis: 80
                            }]]
                        }
                    }]
                };
                paretoChart.setOption(paretoOption);

                // 响应式处理
                window.addEventListener('resize', function() {
                    deptPieChart.resize();
                    costTrendChart.resize();
                    paretoChart.resize();

                    // 重新调整表格对齐
                    setTimeout(function() {
                        adjustTableAlignment();
                    }, 200);
                });
            }

            // 页面加载时自动查询
            loadData();

            // 工时成本配置按钮点击事件
            $('#unit-cost-config-btn').click(function() {
                openUnitCostConfigDialog();
            });

            // 导出对比数据按钮点击事件
            $('#export-comparison-btn').click(function() {
                exportComparisonData();
            });
        });

        // 导出责任部门数据
        function exportPersonData() {
            var formData = layui.form.val('search-form');
            var params = new URLSearchParams(formData).toString();
            window.open('/system/exception_cost_statistics/export/person_statistics?' + params);
        }

        // 导出部门对比数据
        function exportComparisonData() {
            var formData = layui.form.val('search-form');
            var params = new URLSearchParams(formData).toString();
            window.open('/system/exception_cost_statistics/export/dept_comparison?' + params);
        }

        // 调整表格对齐
        function adjustTableAlignment() {
            try {
                // 获取表格容器
                var tableView = $('#person-table').next('.layui-table-view');
                if (tableView.length > 0) {
                    // 移除可能导致对齐问题的样式
                    tableView.find('.layui-table-header').css('overflow', 'visible');
                    tableView.find('.layui-table-body').css('overflow-x', 'auto');

                    // 确保表头和表体宽度一致
                    var headerTable = tableView.find('.layui-table-header table');
                    var bodyTable = tableView.find('.layui-table-body table');

                    if (headerTable.length > 0 && bodyTable.length > 0) {
                        var bodyWidth = bodyTable.width();
                        headerTable.width(bodyWidth);
                    }
                }
            } catch (e) {
                // 表格对齐调整失败，忽略错误
            }
        }

        // 打开工时成本配置弹窗
        function openUnitCostConfigDialog() {
            // 先获取当前配置
            $.ajax({
                url: '/system/exception_cost_statistics/api/unit_cost_config',
                type: 'GET',
                success: function(res) {
                    if (res.success === true || res.code === 0) {
                        showUnitCostConfigDialog(res.data);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('获取配置失败', {icon: 2});
                }
            });
        }

        // 显示工时成本配置弹窗
        function showUnitCostConfigDialog(config) {
            // 获取权限信息
            var permissions = config.permissions || {config: false, trigger: false};

            // 构建频率选项
            var frequencyOptions = '';
            for (var i = 0; i < config.frequency_options.length; i++) {
                var option = config.frequency_options[i];
                var selected = config.frequency === option.value ? 'selected' : '';
                frequencyOptions += '<option value="' + option.value + '" ' + selected + '>' + option.label + '</option>';
            }

            // 构建小时选项
            var hourOptions = '';
            for (var h = 0; h < 24; h++) {
                var selected = config.hour === h ? 'selected' : '';
                var hourStr = h < 10 ? '0' + h : h.toString();
                hourOptions += '<option value="' + h + '" ' + selected + '>' + hourStr + '</option>';
            }

            // 构建分钟选项
            var minuteOptions = '';
            for (var m = 0; m < 60; m++) {
                var selected = config.minute === m ? 'selected' : '';
                var minuteStr = m < 10 ? '0' + m : m.toString();
                minuteOptions += '<option value="' + m + '" ' + selected + '>' + minuteStr + '</option>';
            }

            // 构建每月日期选项
            var dayOfMonthOptions = '';
            for (var d = 1; d <= 31; d++) {
                var selected = config.day_of_month === d ? 'selected' : '';
                dayOfMonthOptions += '<option value="' + d + '" ' + selected + '>' + d + '号</option>';
            }

            // 构建每周日期选项
            var dayOfWeekOptions = '';
            for (var w = 0; w < config.day_of_week_options.length; w++) {
                var weekOption = config.day_of_week_options[w];
                var selected = config.day_of_week === weekOption.value ? 'selected' : '';
                dayOfWeekOptions += '<option value="' + weekOption.value + '" ' + selected + '>' + weekOption.label + '</option>';
            }

            // 构建月份跨度选项
            var monthsSpanOptions = '';
            for (var s = 1; s <= 12; s++) {
                var selected = config.months_span === s ? 'selected' : '';
                monthsSpanOptions += '<option value="' + s + '" ' + selected + '>' + s + '个月</option>';
            }

            var monthlyDisplay = config.frequency === 'monthly' ? 'block' : 'none';
            var weeklyDisplay = config.frequency === 'weekly' ? 'block' : 'none';

            // 表单禁用状态
            var formDisabled = permissions.config ? '' : 'disabled';
            var formClass = permissions.config ? '' : 'layui-disabled';

            var content = '<form class="layui-form ' + formClass + '" lay-filter="unit-cost-config-form" style="padding: 20px;">' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">启用状态</label>' +
                    '<div class="layui-input-block">' +
                        '<input type="checkbox" name="enabled" lay-skin="switch" lay-text="启用|禁用" ' + (config.enabled ? 'checked' : '') + ' ' + formDisabled + '>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">执行频率</label>' +
                    '<div class="layui-input-block">' +
                        '<select name="frequency" lay-filter="frequency-select" ' + formDisabled + '>' +
                            frequencyOptions +
                        '</select>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">执行时间</label>' +
                    '<div class="layui-input-block">' +
                        '<div style="display: flex; align-items: center; gap: 10px;">' +
                            '<select name="hour" style="width: 80px;" ' + formDisabled + '>' +
                                hourOptions +
                            '</select>' +
                            '<span>时</span>' +
                            '<select name="minute" style="width: 80px;" ' + formDisabled + '>' +
                                minuteOptions +
                            '</select>' +
                            '<span>分</span>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item" id="monthly-config" style="display: ' + monthlyDisplay + ';">' +
                    '<label class="layui-form-label">每月执行日期</label>' +
                    '<div class="layui-input-block">' +
                        '<select name="day_of_month" ' + formDisabled + '>' +
                            dayOfMonthOptions +
                        '</select>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item" id="weekly-config" style="display: ' + weeklyDisplay + ';">' +
                    '<label class="layui-form-label">每周执行日期</label>' +
                    '<div class="layui-input-block">' +
                        '<select name="day_of_week" ' + formDisabled + '>' +
                            dayOfWeekOptions +
                        '</select>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">计算月份跨度</label>' +
                    '<div class="layui-input-block">' +
                        '<select name="months_span" ' + formDisabled + '>' +
                            monthsSpanOptions +
                        '</select>' +
                        '<div class="layui-form-mid layui-word-aux">计算最近几个月的数据（不包含当前月份，因为当月数据可能不完整）</div>' +
                    '</div>' +
                '</div>' +
                (!permissions.config ? '<div class="layui-form-item"><div class="layui-input-block"><div class="layui-word-aux" style="color: #ff5722;"><i class="layui-icon layui-icon-tips"></i> 您没有配置修改权限，只能查看当前设置</div></div></div>' : '') +
                '<div class="layui-form-item">' +
                    '<div class="layui-input-block">' +
                        (permissions.config ? '<button type="button" class="layui-btn" lay-submit lay-filter="save-config">保存配置</button>' : '') +
                        (permissions.trigger ? '<button type="button" class="layui-btn layui-btn-primary" id="manual-calculate">手动计算</button>' : '') +
                        (!permissions.config && !permissions.trigger ? '<div class="layui-word-aux" style="color: #ff5722;"><i class="layui-icon layui-icon-tips"></i> 您没有工时成本配置权限</div>' : '') +
                    '</div>' +
                '</div>' +
                '</form>';

            layer.open({
                type: 1,
                title: '部门工时单位成本定时任务配置',
                content: content,
                area: ['600px', '500px'],
                success: function(layero, index) {
                    // 重新渲染表单
                    layui.form.render();

                    // 监听频率选择变化
                    layui.form.on('select(frequency-select)', function(data) {
                        var frequency = data.value;
                        $('#monthly-config').toggle(frequency === 'monthly');
                        $('#weekly-config').toggle(frequency === 'weekly');
                    });

                    // 监听保存配置
                    layui.form.on('submit(save-config)', function(data) {
                        saveUnitCostConfig(data.field, index);
                        return false;
                    });

                    // 监听手动计算
                    $('#manual-calculate').click(function() {
                        manualCalculateUnitCost();
                    });
                }
            });
        }

        // 保存工时成本配置
        function saveUnitCostConfig(formData, layerIndex) {
            $.ajax({
                url: '/system/exception_cost_statistics/api/unit_cost_config',
                type: 'POST',
                data: JSON.stringify(formData),
                contentType: 'application/json',
                success: function(res) {
                    if (res.success === true || res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        layer.close(layerIndex);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('保存配置失败', {icon: 2});
                }
            });
        }

        // 手动计算工时成本
        function manualCalculateUnitCost() {
            layer.confirm('确定要手动触发部门工时单位成本计算吗？', {icon: 3, title: '确认'}, function(index) {
                layer.close(index);

                var loadIndex = layer.load(2, {content: '计算中...'});

                $.ajax({
                    url: '/system/exception_cost_statistics/api/manual_calculate',
                    type: 'POST',
                    success: function(res) {
                        layer.close(loadIndex);
                        if (res.success === true || res.code === 0) {
                            var result = res.data;
                            var message = '计算完成！总计' + result.total_count + '项，成功' + result.success_count + '项，失败' + result.failed_count + '项';
                            layer.msg(message, {icon: 1, time: 3000});
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadIndex);
                        layer.msg('计算失败', {icon: 2});
                    }
                });
            });
        }
    </script>
</body>
</html>
