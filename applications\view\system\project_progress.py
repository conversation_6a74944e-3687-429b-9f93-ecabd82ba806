from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, or_, desc, cast

from applications.common import curd
from applications.common.utils import validate
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.common.admin import admin_log
from applications.schemas import ProjectManageDeptSchema
from applications.models import ProjectManageDept, Import_project, DictData, Dept, ProjectProgress, AdminLog, User, ygong, ProjectUserLog, Role

# 默认状态名称映射
DEFAULT_STATUS_NAMES = {
    0: '未开始',
    1: '生产中',
    2: '生产完成',
    3: '入库完成',
    4: '发货中',
    5: '发货完成',
    6: '安装调试中',
    7: '安装调试完成',
    8: '调试中',
    9: '调试完成',
    10: '验收中',
    11: '验收完成',
    12: '项目已完成'
}


def calculate_project_status(progress):
    """
    根据项目进度信息计算项目状态，使用关键日期字段：
    - project_start_date (合同开始日)
    - warehouse_completion_date (入库完成日)
    - shipping_end_date (发货完成日)
    - installation_start_date (开始安装日)
    - installation_end_date (安装完成日)
    - acceptance_date (验收完成日)

    状态映射：
    0: 未开始
    1: 生产中 (合同已开始)
    3: 入库完成 (入库完成日期已过)
    5: 发货完成 (发货完成日期已过)
    6: 安装调试中 (安装开始日期已到或安装完成日期未到)
    7: 安装调试完成 (安装完成日期已过)
    10: 验收中 (验收日期未到)
    12: 项目已完成 (验收日期已过)
    """
    if not progress:
        return 0

    today = datetime.now().date()

    # 如果填写了验收日期
    if progress.acceptance_date:
        # 如果验收日期已过，则状态为"项目已完成"
        if progress.acceptance_date <= today:
            return 12
        # 如果验收日期未到，则状态为"验收中"
        else:
            return 10

    # 如果填写了安装完成日期
    if progress.installation_end_date:
        # 如果安装完成日期已过，则状态为"安装调试完成"
        if progress.installation_end_date <= today:
            return 7
        # 如果安装完成日期未到，则状态为"安装调试中"
        else:
            return 6

    # 如果填写了安装开始日期，则状态为"安装调试中"
    if progress.installation_start_date:
        return 6

    # 如果填写了发货完成日期
    if progress.shipping_end_date:
        # 如果发货完成日期已过，则状态为"发货完成"
        if progress.shipping_end_date <= today:
            return 5
        # 如果发货完成日期未到，但有入库完成日期且已过，则状态为"发货中"
        elif progress.warehouse_completion_date and progress.warehouse_completion_date <= today:
            return 4
        # 否则状态为"生产中"
        else:
            return 1

    # 如果填写了入库完成日期
    if progress.warehouse_completion_date:
        # 如果入库完成日期已过，则状态为"入库完成"
        if progress.warehouse_completion_date <= today:
            return 3
        # 如果入库完成日期未到，则状态为"生产中"
        else:
            return 1

    # 如果填写了项目开始日期，则状态为"生产中"
    if progress.project_start_date:
        return 1

    # 默认状态为"未开始"
    return 0


def is_sales_clerk(user):
    """检查用户是否具有销售文员角色"""
    if not user:
        return False

    # 检查用户是否具有销售文员角色（code='xswy'）
    for role in user.role:
        if role.enable == 1 and role.code == 'xswy':
            return True
    return False


bp = Blueprint('project_progress', __name__, url_prefix='/project_progress')

@bp.get('/')
@authorize("system:project_progress:main", log=True)
def main():
    # 获取所有启用的项目类别，按排序和ID排序
    project_categories = ProjectManageDept.query.filter_by(status=1).order_by(ProjectManageDept.sort, ProjectManageDept.id).all()

    # 获取项目状态字典数据
    project_status_options = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).order_by(cast(DictData.data_value, db.Integer)).all()

    return render_template('system/project_progress/main.html',
                         project_categories=project_categories,
                         project_status_options=project_status_options)


@bp.get('/data')
@authorize("system:project_progress:main", log=True)
def data():
    """获取项目列表数据，支持分页、筛选和搜索"""
    # 获取请求参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    status = request.args.get('status', '', type=str)
    keyword = request.args.get('keyword', '', type=str)

    # 构建查询
    query = db.session.query(
        Import_project,
        ProjectManageDept.dept_name.label('project_type_name'),
        DictData.data_label.label('status_name')
    ).outerjoin(
        ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
    ).outerjoin(
        DictData, (DictData.type_code == 'project_status') & (DictData.data_value == func.cast(Import_project.project_status, db.String))
    )

    # 关键字搜索
    if keyword:
        query = query.filter(
            or_(
                Import_project.project_name.like(f'%{keyword}%'),
                Import_project.project_code.like(f'%{keyword}%')
            )
        )

    # 执行查询（不分页，因为我们需要先计算状态）
    all_projects = query.all()

    # 准备项目列表，包含计算后的状态
    filtered_projects = []
    for project, project_type_name, status_name in all_projects:
        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=project.id).first()

        # 根据项目进度信息计算项目状态
        calculated_status = calculate_project_status(progress) if progress else 0

        # 应用状态筛选条件
        if status:
            if status == 'in_progress':
                # 进行中项目（状态为1-10）
                if calculated_status not in range(1, 11):
                    continue
            elif status == 'completed':
                # 已完成项目（状态为11）
                if calculated_status != 11:
                    continue
            else:
                # 特定状态筛选
                try:
                    status_value = int(status)
                    if calculated_status != status_value:
                        continue
                except ValueError:
                    pass  # 如果状态值不是整数，则忽略筛选

        # 将项目添加到筛选后的列表中
        filtered_projects.append((project, project_type_name, status_name, calculated_status))

    # 计算总数
    total_count = len(filtered_projects)

    # 手动分页
    start_index = (page - 1) * limit
    end_index = min(start_index + limit, total_count)
    paginated_projects = filtered_projects[start_index:end_index]

    # 准备返回数据
    items = []
    for project, project_type_name, status_name, calculated_status in paginated_projects:
        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=project.id).first()

        # 获取状态名称
        status_dict = DictData.query.filter_by(type_code='project_status').all()
        status_map = {int(item.data_value): item.data_label for item in status_dict}
        calculated_status_name = status_map.get(calculated_status, '')

        # 如果状态名称为空，则使用默认名称
        if not calculated_status_name:
            calculated_status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

        # 计算项目进度
        progress_percent = 0
        # 根据项目状态计算进度百分比（按工作量权重分配）
        status_progress_map = {
            0: 0,     # 未开始
            1: 15,    # 生产中（生产工作量较大）
            3: 25,    # 入库完成
            4: 35,    # 发货中
            5: 45,    # 发货完成
            6: 60,    # 安装调试中
            7: 75,    # 安装调试完成（安装调试工作量大）
            10: 90,   # 验收中
            12: 100   # 项目已完成
        }
        progress_percent = status_progress_map.get(calculated_status, 0)

        # 格式化交付日期
        delivery_date = project.delivery_date.strftime('%Y-%m-%d') if project.delivery_date else ''

        # 构建项目数据
        item = {
            'id': project.id,
            'project_code': project.project_code,
            'project_name': project.project_name,
            'project_type': project_type_name or '',
            'status_name': calculated_status_name,  # 使用计算得到的状态名称
            'project_status': calculated_status,  # 使用计算得到的状态值
            'db_status_value': project.project_status,  # 保留数据库中的状态值，用于调试
            'delivery_date': delivery_date,
            'project_manager': project.project_manager or (ProjectManageDept.query.get(project.dept_id).leader if project.dept_id else ''),
            'progress': progress_percent,
            'create_at': project.create_at.strftime('%Y-%m-%d') if project.create_at else '',
            'update_at': project.update_at.strftime('%Y-%m-%d') if project.update_at else ''
        }
        items.append(item)

    # 返回数据
    return table_api(
        data=items,
        count=total_count,  # 使用筛选后的总数
        limit=limit
    )


@bp.get('/statistics')
@authorize("system:project_progress:main", log=True)
def statistics():
    """获取项目统计数据"""
    try:
        # 获取所有项目
        projects = Import_project.query.all()

        # 初始化计数器
        total_projects = len(projects)
        not_started_projects = 0  # 新增：未开始的项目数
        in_progress_projects = 0  # 修改：只包括真正进行中的项目
        completed_projects = 0

        # 遍历项目，根据进度信息计算状态
        for project in projects:
            # 获取项目进度详情
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress) if progress else 0

            # 统计未开始、进行中和已完成的项目数
            if calculated_status == 0:  # 未开始
                not_started_projects += 1
            elif calculated_status == 12:  # 已完成
                completed_projects += 1
            else:  # 进行中（状态为1-11）
                in_progress_projects += 1

        # 计算完成率
        completion_rate = round((completed_projects / total_projects) * 100) if total_projects > 0 else 0

        # 获取上个月的统计数据（用于计算环比增长）
        last_month = datetime.now() - timedelta(days=30)

        # 上个月的项目
        last_month_projects = Import_project.query.filter(
            Import_project.create_at < last_month
        ).all()

        # 初始化上个月的计数器
        last_month_total = len(last_month_projects)
        last_month_not_started = 0  # 新增：上个月未开始的项目数
        last_month_in_progress = 0  # 修改：只包括真正进行中的项目
        last_month_completed = 0

        # 遍历上个月的项目，根据进度信息计算状态
        for project in last_month_projects:
            # 获取项目进度详情
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress) if progress else 0

            # 统计未开始、进行中和已完成的项目数
            if calculated_status == 0:  # 未开始
                last_month_not_started += 1
            elif calculated_status == 12:  # 已完成
                last_month_completed += 1
            else:  # 进行中（状态为1-11）
                last_month_in_progress += 1

        # 计算环比增长率
        total_growth = round(((total_projects - last_month_total) / last_month_total) * 100) if last_month_total > 0 else 0
        not_started_growth = round(((not_started_projects - last_month_not_started) / last_month_not_started) * 100) if last_month_not_started > 0 else 0
        in_progress_growth = round(((in_progress_projects - last_month_in_progress) / last_month_in_progress) * 100) if last_month_in_progress > 0 else 0
        completed_growth = round(((completed_projects - last_month_completed) / last_month_completed) * 100) if last_month_completed > 0 else 0

        # 返回统计数据
        return jsonify({
            'success': True,
            'data': {
                'total_projects': total_projects,
                'not_started_projects': not_started_projects,  # 新增：未开始的项目数
                'in_progress_projects': in_progress_projects,
                'completed_projects': completed_projects,
                'completion_rate': completion_rate,
                'total_growth': total_growth,
                'not_started_growth': not_started_growth,  # 新增：未开始项目的环比增长率
                'in_progress_growth': in_progress_growth,
                'completed_growth': completed_growth
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        })


@bp.get('/gantt')
@authorize("system:project_progress:main", log=True)
def gantt():
    """获取项目甘特图数据，支持筛选"""
    try:
        # 获取筛选参数
        category = request.args.get('category', '')
        status = request.args.get('status', '')

        # 构建查询
        query = db.session.query(
            Import_project,
            ProjectManageDept.dept_name.label('project_type_name'),
            DictData.data_label.label('status_name')
        ).outerjoin(
            ProjectManageDept, Import_project.dept_id == ProjectManageDept.id
        ).outerjoin(
            DictData, (DictData.type_code == 'project_status') & (DictData.data_value == func.cast(Import_project.project_status, db.String))
        )

        # 应用筛选条件
        if category:
            query = query.filter(Import_project.dept_id == category)

        if status:
            status_values = [int(s) for s in status.split(',')]
            query = query.filter(Import_project.project_status.in_(status_values))

        # 执行查询
        projects = query.all()

        # 准备甘特图数据
        gantt_data = []
        for project, project_type_name, status_name in projects:
            # 获取项目进度详情
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress) if progress else 0

            # 获取状态名称
            status_dict = DictData.query.filter_by(type_code='project_status').all()
            status_map = {int(item.data_value): item.data_label for item in status_dict}
            calculated_status_name = status_map.get(calculated_status, '')

            # 如果状态名称为空，则使用默认名称
            if not calculated_status_name:
                calculated_status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

            # 计算项目进度
            progress_percent = 0
            # 根据项目状态计算进度百分比（按工作量权重分配）
            status_progress_map = {
                0: 0,     # 未开始
                1: 15,    # 生产中（生产工作量较大）
                3: 25,    # 入库完成
                4: 35,    # 发货中
                5: 45,    # 发货完成
                6: 60,    # 安装调试中
                7: 75,    # 安装调试完成（安装调试工作量大）
                10: 90,   # 验收中
                12: 100   # 项目已完成
            }
            progress_percent = status_progress_map.get(calculated_status, 0)

            # 项目开始日期（使用创建日期）
            start_date = project.create_at.strftime('%Y-%m-%d') if project.create_at else datetime.now().strftime('%Y-%m-%d')

            # 项目结束日期（使用交付日期，如果没有则根据状态估算）
            if project.delivery_date:
                end_date = project.delivery_date.strftime('%Y-%m-%d')
            else:
                # 根据项目状态估算结束日期
                if calculated_status == 12:  # 已完成
                    end_date = (project.update_at or datetime.now()).strftime('%Y-%m-%d')
                else:
                    # 根据创建日期和进度估算结束日期
                    days_to_add = int(30 * (100 - progress_percent) / 100)  # 假设项目总周期为30天
                    estimated_end_date = (datetime.now() + timedelta(days=days_to_add)).strftime('%Y-%m-%d')
                    end_date = estimated_end_date

            # 构建项目数据
            gantt_item = {
                'id': project.id,
                'name': f"{project_type_name or ''}-{project.project_code}" if project_type_name else project.project_code,
                'start': start_date,
                'end': end_date,
                'progress': progress_percent,
                'status': calculated_status_name,  # 使用计算得到的状态名称
                'project_name': project.project_name
            }
            gantt_data.append(gantt_item)

        # 返回甘特图数据
        return jsonify({
            'success': True,
            'data': gantt_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取甘特图数据失败: {str(e)}'
        })


@bp.get('/add')
@authorize("system:project_progress:main", log=True)
def add():
    """添加项目页面（重定向到现有的项目添加页面）"""
    return render_template('system/import_project/add.html')


@bp.get('/detail/<int:id>')
@authorize("system:project_progress:main", log=True)
def detail(id):
    """项目进度详情页面"""
    project = Import_project.query.get_or_404(id)
    return render_template('system/project_progress/detail_new.html', project=project)


@bp.get('/api/project/<int:project_id>')
@authorize("system:project_progress:main", log=True)
def get_project_progress(project_id):
    """获取项目进度详情"""
    try:
        # 获取项目基本信息
        project = Import_project.query.get_or_404(project_id)

        # 获取项目类型和状态名称
        project_type = ProjectManageDept.query.get(project.dept_id)
        project_type_name = project_type.dept_name if project_type else ''

        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=project_id).first()

        # 根据项目进度信息计算项目状态
        calculated_status = calculate_project_status(progress) if progress else 0

        # 获取状态名称
        status_dict = DictData.query.filter_by(type_code='project_status').all()
        status_map = {int(item.data_value): item.data_label for item in status_dict}

        # 使用计算得到的状态值
        status_name = status_map.get(calculated_status, '')

        # 如果状态名称为空，则使用默认名称
        if not status_name:
            status_name = DEFAULT_STATUS_NAMES.get(calculated_status, f'状态{calculated_status}')

        # 获取项目负责人
        project_manager = project_type.leader if project_type else ''

        # 检查当前用户是否为销售文员
        user_is_sales_clerk = is_sales_clerk(current_user)

        # 权限检查完成

        # 构建返回数据
        data = {
            'id': project.id,
            'project_code': project.project_code,
            'project_name': project.project_name,
            'project_type_name': project_type_name,
            'status_name': status_name,
            'status_value': calculated_status,  # 使用计算得到的状态值
            'db_status_value': project.project_status,  # 保留数据库中的状态值，用于调试
            'price': project.price,
            'machine_number': project.machine_number,
            'delivery_date': project.delivery_date.strftime('%Y-%m-%d') if project.delivery_date else '',
            'project_manager': project_manager,
            'user_permissions': {
                'can_edit_warehouse_completion_date': user_is_sales_clerk
            },
            'progress_details': {
                'project_description': progress.project_description if progress else '',
                'production_start_date': progress.production_start_date.strftime('%Y-%m-%d') if progress and progress.production_start_date else '',
                'production_end_date': progress.production_end_date.strftime('%Y-%m-%d') if progress and progress.production_end_date else '',
                'warehouse_completion_date': progress.warehouse_completion_date.strftime('%Y-%m-%d') if progress and progress.warehouse_completion_date else '',
                'shipping_start_date': progress.shipping_start_date.strftime('%Y-%m-%d') if progress and progress.shipping_start_date else '',
                'shipping_end_date': progress.shipping_end_date.strftime('%Y-%m-%d') if progress and progress.shipping_end_date else '',
                'installation_start_date': progress.installation_start_date.strftime('%Y-%m-%d') if progress and progress.installation_start_date else '',
                'installation_end_date': progress.installation_end_date.strftime('%Y-%m-%d') if progress and progress.installation_end_date else '',
                'debugging_start_date': progress.debugging_start_date.strftime('%Y-%m-%d') if progress and progress.debugging_start_date else '',
                'debugging_end_date': progress.debugging_end_date.strftime('%Y-%m-%d') if progress and progress.debugging_end_date else '',
                'project_start_date': progress.project_start_date.strftime('%Y-%m-%d') if progress and progress.project_start_date else '',
                'acceptance_date': progress.acceptance_date.strftime('%Y-%m-%d') if progress and progress.acceptance_date else '',
                'project_end_date': progress.project_end_date.strftime('%Y-%m-%d') if progress and progress.project_end_date else ''
            }
        }

        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取项目进度详情失败: {str(e)}'})


@bp.post('/api/project/<int:project_id>/progress')
@authorize("system:project_progress:main", log=True)
def update_project_progress(project_id):
    """更新项目进度信息"""
    try:
        data = request.get_json()

        # 获取项目进度详情，如果不存在则创建
        progress = ProjectProgress.query.filter_by(project_id=project_id).first()
        if not progress:
            progress = ProjectProgress(project_id=project_id)
            db.session.add(progress)

        # 更新项目进度信息
        progress.project_description = data.get('project_description', '')

        # 验证日期的合理性
        date_fields = [
            ('production_start_date', '生产开始日期', None),
            ('production_end_date', '生产完成日期', 'production_start_date'),
            ('warehouse_completion_date', '入库完成日期', 'production_end_date'),
            ('shipping_start_date', '发货开始日期', 'warehouse_completion_date'),
            ('shipping_end_date', '发货完成日期', 'shipping_start_date'),
            ('installation_start_date', '安装开始日期', 'shipping_end_date'),
            ('installation_end_date', '安装完成日期', 'installation_start_date'),
            ('debugging_start_date', '调试开始日期', 'installation_end_date'),
            ('debugging_end_date', '调试完成日期', 'debugging_start_date'),
            ('acceptance_date', '验收日期', 'debugging_end_date'),
            ('project_end_date', '项目完成日期', 'acceptance_date'),
            ('project_start_date', '项目开始日期', None)  # 项目开始日期可以单独设置，不依赖其他日期
        ]

        # 解析日期字段
        parsed_dates = {}
        for field_name, field_label, _ in date_fields:
            if field_name in data and data[field_name]:
                try:
                    parsed_dates[field_name] = datetime.strptime(data[field_name], '%Y-%m-%d').date()
                except ValueError:
                    return jsonify({'success': False, 'message': f'{field_label}日期格式无效'})

        # 验证日期顺序
        for field_name, field_label, depends_on in date_fields:
            if field_name in parsed_dates and depends_on and depends_on in parsed_dates:
                if parsed_dates[field_name] < parsed_dates[depends_on]:
                    depends_label = next((label for name, label, _ in date_fields if name == depends_on), depends_on)
                    return jsonify({'success': False, 'message': f'{field_label}不能早于{depends_label}'})

        # 验证入库完成日期的前置条件：如果没有入库完成时间，后面所有状态时间都不能填写
        warehouse_dependent_fields = [
            ('shipping_start_date', '发货开始日期'),
            ('shipping_end_date', '发货完成日期'),
            ('installation_start_date', '安装开始日期'),
            ('installation_end_date', '安装完成日期'),
            ('debugging_start_date', '调试开始日期'),
            ('debugging_end_date', '调试完成日期'),
            ('acceptance_date', '验收日期'),
            ('project_end_date', '项目完成日期')
        ]

        # 检查是否有入库完成日期（包括现有的和新提交的）
        current_warehouse_date = progress.warehouse_completion_date if progress else None
        submitted_warehouse_date = parsed_dates.get('warehouse_completion_date')

        # 如果用户提交了入库完成日期字段，以提交的为准（包括清空的情况）
        if 'warehouse_completion_date' in data:
            has_warehouse_date = submitted_warehouse_date is not None
        else:
            # 如果用户没有提交入库完成日期字段，使用现有的
            has_warehouse_date = current_warehouse_date is not None

        # 如果没有入库完成日期，检查是否试图填写后续字段
        if not has_warehouse_date:
            for field_name, field_label in warehouse_dependent_fields:
                if field_name in parsed_dates:
                    return jsonify({'success': False, 'message': f'请先填写入库完成日期，才能填写{field_label}'})

        # 检查入库完成日期的权限（只有当用户实际修改了入库完成日期时才检查权限）
        if 'warehouse_completion_date' in data:
            # 获取现有的入库完成日期
            current_warehouse_date = progress.warehouse_completion_date if progress else None

            # 获取用户提交的入库完成日期
            submitted_warehouse_date = None
            if data['warehouse_completion_date']:
                try:
                    submitted_warehouse_date = datetime.strptime(data['warehouse_completion_date'], '%Y-%m-%d').date()
                except ValueError:
                    pass  # 日期格式错误会在后续的日期解析中处理

            # 只有当入库完成日期确实发生变化时才检查权限
            if current_warehouse_date != submitted_warehouse_date:
                if not is_sales_clerk(current_user):
                    return jsonify({'success': False, 'message': '您没有权限修改入库完成日期'})

        # 更新日期字段
        for field_name, _, _ in date_fields:
            if field_name in data:
                if data[field_name]:
                    try:
                        setattr(progress, field_name, parsed_dates[field_name])
                    except:
                        pass
                else:
                    setattr(progress, field_name, None)

        # 根据进度信息更新项目状态
        project = Import_project.query.get(project_id)
        if project:
            new_status = calculate_project_status(progress)
            project.project_status = new_status

        db.session.commit()
        return jsonify({'success': True, 'message': '更新项目进度信息成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新项目进度信息失败: {str(e)}'})


@bp.post('/api/project/<int:project_id>/update_manager_with_log')
@authorize("system:project_progress:main", log=True)
def update_project_manager_with_log(project_id):
    """更新项目负责人（带日志记录）"""
    try:
        data = request.get_json()
        new_manager = data.get('manager', '')

        if not new_manager:
            return jsonify({'success': False, 'message': '负责人不能为空'})

        # 获取项目
        project = Import_project.query.get_or_404(project_id)

        # 记录旧负责人
        old_manager = project.project_manager

        # 创建变更日志
        log = ProjectUserLog(
            project_id=project_id,
            old_manager=old_manager,
            new_manager=new_manager,
            operator_id=current_user.id,
            operator_name=current_user.username
        )
        db.session.add(log)

        # 更新项目负责人
        project.project_manager = new_manager
        db.session.commit()

        return jsonify({'success': True, 'message': '更新项目负责人成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新项目负责人失败: {str(e)}'})


@bp.get('/api/project/<int:project_id>/manager_logs_detailed')
@authorize("system:project_progress:main", log=True)
def get_project_manager_logs_detailed(project_id):
    """获取项目负责人变更历史（详细）"""
    try:
        logs = ProjectUserLog.query.filter_by(project_id=project_id).order_by(ProjectUserLog.operation_time.desc()).all()

        log_list = []
        for log in logs:
            log_list.append({
                'id': log.id,
                'project_id': log.project_id,
                'old_manager': log.old_manager,
                'new_manager': log.new_manager,
                'operator': log.operator_name,
                'operation_time': log.operation_time.strftime('%Y-%m-%d %H:%M:%S'),
                'remark': log.remark,
                'description': f'将项目负责人从 "{log.old_manager or "--"}" 更改为 "{log.new_manager}"'
            })

        return jsonify({'success': True, 'data': log_list})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取项目负责人变更历史失败: {str(e)}'})


@bp.get('/api/employees')
@authorize("system:project_progress:main", log=True)
def get_employees():
    """获取员工列表"""
    try:
        keyword = request.args.get('keyword', '')

        # 构建查询
        query = db.session.query(ygong).filter(ygong.enable == 1)

        # 如果有关键字，则按姓名筛选
        if keyword:
            query = query.filter(ygong.name.like(f'%{keyword}%'))

        # 执行查询
        employees = query.all()

        # 构建返回数据
        employee_list = []
        for emp in employees:
            # 获取部门名称
            dept_name = ''
            if emp.dept_id:
                dept = Dept.query.get(emp.dept_id)
                if dept:
                    dept_name = dept.dept_name

            employee_list.append({
                'id': emp.id,
                'name': emp.name,
                'employee_id': emp.employee_id,
                'position': emp.position,
                'dept_name': dept_name
            })

        return jsonify({'success': True, 'data': employee_list})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取员工列表失败: {str(e)}'})


@bp.get('/api/project/<int:project_id>/update_status')
@authorize("system:project_progress:main", log=True)
def update_project_status(project_id):
    """更新项目状态"""
    try:
        # 获取项目
        project = Import_project.query.get_or_404(project_id)

        # 获取项目进度详情
        progress = ProjectProgress.query.filter_by(project_id=project_id).first()

        # 根据项目进度信息计算项目状态
        calculated_status = calculate_project_status(progress) if progress else 0

        # 更新项目状态
        project.project_status = calculated_status
        db.session.commit()

        return jsonify({'success': True, 'message': '更新项目状态成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新项目状态失败: {str(e)}'})


@bp.get('/api/update_all_project_status')
@authorize("system:project_progress:main", log=True)
def update_all_project_status():
    """更新所有项目状态"""
    try:
        # 获取所有项目
        projects = Import_project.query.all()

        # 更新计数器
        updated_count = 0

        # 遍历项目，更新状态
        for project in projects:
            # 获取项目进度详情
            progress = ProjectProgress.query.filter_by(project_id=project.id).first()

            # 根据项目进度信息计算项目状态
            calculated_status = calculate_project_status(progress)

            # 如果计算得到的状态与数据库中的状态不同，则更新
            if project.project_status != calculated_status:
                project.project_status = calculated_status
                updated_count += 1

        # 提交更改
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功更新 {updated_count} 个项目的状态'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新项目状态失败: {str(e)}'
        })


@bp.post('/api/project/<int:project_id>/update_manager')
@authorize("system:project_progress:main", log=True)
def update_project_manager(project_id):
    """更新项目负责人"""
    try:
        data = request.get_json()
        new_manager = data.get('manager', '')

        # 获取项目信息
        project = Import_project.query.get_or_404(project_id)
        project_type = ProjectManageDept.query.get(project.dept_id)

        if not project_type:
            return jsonify({'success': False, 'message': '项目类型不存在'})

        # 获取旧的项目负责人
        old_manager = project_type.leader

        # 更新项目负责人
        project_type.leader = new_manager
        db.session.commit()

        # 记录操作日志
        log_desc = f"项目负责人变更：项目[{project.project_code}] {project.project_name}，负责人从 [{old_manager}] 变更为 [{new_manager}]"
        admin_log(request=request, is_access=True, desc=log_desc)

        return jsonify({'success': True, 'message': '更新项目负责人成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新项目负责人失败: {str(e)}'})


@bp.get('/api/project/<int:project_id>/manager_logs')
@authorize("system:project_progress:main", log=True)
def get_project_manager_logs(project_id):
    """获取项目负责人变更历史"""
    try:
        # 获取项目信息
        project = Import_project.query.get_or_404(project_id)

        # 查询与该项目相关的负责人变更日志
        logs = AdminLog.query.filter(
            AdminLog.desc.like(f"%项目负责人变更：项目[{project.project_code}]%")
        ).order_by(AdminLog.create_time.desc()).all()

        # 格式化日志数据
        log_data = []
        for log in logs:
            user = User.query.get(log.uid)
            log_data.append({
                'id': log.id,
                'operator': user.username if user else '未知用户',
                'operation_time': log.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'description': log.desc
            })

        return jsonify({'success': True, 'data': log_data})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取项目负责人变更历史失败: {str(e)}'})