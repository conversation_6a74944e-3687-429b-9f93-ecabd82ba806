---
title: "GosunTec企业管理系统完整项目说明文档"
subtitle: "综合业务功能与操作指南"
author: "GosunTec技术团队"
date: "2025年1月3日"
version: "v2.0"
documentclass: "article"
geometry: "margin=2.5cm"
fontsize: "12pt"
linestretch: 1.5
toc: true
toc-depth: 3
numbersections: true
---

\newpage

# 文档信息

| 项目 | 内容 |
|------|------|
| **文档标题** | GosunTec企业管理系统完整项目说明文档 |
| **文档版本** | v2.0 |
| **创建日期** | 2025年1月3日 |
| **最后更新** | 2025年1月3日 |
| **适用系统** | GosunTec企业管理系统 |
| **文档类型** | 综合业务说明文档 |
| **编制部门** | 技术开发部 |
| **审核部门** | 产品管理部 |
| **批准部门** | 总经理办公室 |

## 修订记录

| 版本 | 日期 | 修订内容 | 修订人 |
|------|------|----------|--------|
| v1.0 | 2024年12月15日 | 初始版本创建 | 技术团队 |
| v2.0 | 2025年1月3日 | 全面重构，增加详细业务规则和计算公式 | 技术团队 |

\newpage

# 目录

1. [系统概览](#1-系统概览)
   1. [系统简介](#11-系统简介)
   2. [技术架构](#12-技术架构)
   3. [核心特色](#13-核心特色)
   4. [业务全景图](#14-业务全景图)

2. [核心业务模块](#2-核心业务模块)
   1. [用户管理和权限系统](#21-用户管理和权限系统)
   2. [项目管理模块](#22-项目管理模块)
   3. [成本管理模块](#23-成本管理模块)
   4. [工时统计模块](#24-工时统计模块)
   5. [薪资管理模块](#25-薪资管理模块)
   6. [支付管理模块](#26-支付管理模块)
   7. [外包管理模块](#27-外包管理模块)
   8. [统计数据模块](#28-统计数据模块)

3. [计算规则手册](#3-计算规则手册)
   1. [成本计算规则](#31-成本计算规则)
   2. [工时计算规则](#32-工时计算规则)
   3. [薪资计算规则](#33-薪资计算规则)
   4. [支付状态计算](#34-支付状态计算)
   5. [项目进度计算](#35-项目进度计算)

4. [流程管理体系](#4-流程管理体系)
   1. [项目生命周期管理](#41-项目生命周期管理)
   2. [成本核算流程](#42-成本核算流程)
   3. [工时管理流程](#43-工时管理流程)
   4. [支付审批流程](#44-支付审批流程)

5. [使用场景指南](#5-使用场景指南)
   1. [管理员操作指南](#51-管理员操作指南)
   2. [项目经理操作指南](#52-项目经理操作指南)
   3. [财务人员操作指南](#53-财务人员操作指南)
   4. [普通员工操作指南](#54-普通员工操作指南)

6. [系统配置和管理](#6-系统配置和管理)
   1. [系统参数配置](#61-系统参数配置)
   2. [权限配置管理](#62-权限配置管理)
   3. [数据备份和恢复](#63-数据备份和恢复)

\newpage

# 执行摘要

GosunTec企业管理系统是一个基于Flask框架开发的综合性企业管理平台，专为项目型企业设计。系统通过集成化的管理模式，实现了从项目立项到结算的全生命周期管理，为企业提供了完整的数字化解决方案。

## 核心价值

1. **提升管理效率** - 通过自动化工时统计、成本核算等功能，显著减少人工操作
2. **精细化成本控制** - 多维度成本分析，实现项目盈利的精确掌控
3. **智能化决策支持** - 丰富的统计报表和数据分析，为管理决策提供科学依据
4. **规范化业务流程** - 标准化的审批流程和权限管理，确保业务合规性

## 系统特色

- **8大核心模块** - 覆盖企业管理的各个方面
- **30+计算规则** - 精确的业务逻辑和算法支持
- **多级权限控制** - 基于角色的精细化权限管理
- **实时数据分析** - 支持多维度的统计分析和报表生成

## 适用范围

本系统特别适用于中小型项目型企业，包括工程项目管理、制造业项目管理、服务业项目管理等领域。

\newpage

# 1. 系统概览

## 1.1 系统简介

GosunTec企业管理系统是一个基于Flask框架开发的综合性企业管理平台，专注于项目型企业的全生命周期管理。系统集成了人员管理、项目管理、成本控制、工时统计、薪资核算、支付管理、外包管理等核心业务功能，为企业提供一站式的数字化管理解决方案。

### 1.1.1 系统定位

| 维度 | 内容 |
|------|------|
| **目标用户** | 中小型项目型企业 |
| **应用场景** | 工程项目管理、制造业项目管理、服务业项目管理 |
| **核心价值** | 提升项目管理效率、降低运营成本、优化资源配置 |

### 1.1.2 业务覆盖范围

**企业管理全流程覆盖：**

1. **人力资源管理** - 员工信息、部门架构、权限分配
2. **项目全生命周期** - 立项、执行、验收、结算
3. **成本精细化管控** - BOM成本、人工成本、外协成本
4. **工时智能统计** - 工时录入、审核、分析、报表
5. **薪资自动核算** - 基本工资、绩效工资、加班费计算
6. **支付流程管理** - 回款跟踪、状态计算、激励管理
7. **外包协作管理** - 外协信息、工时管理、成本核算
8. **数据统计分析** - 多维度报表、趋势分析、决策支持

## 1.2 技术架构

### 1.2.1 后端技术栈

**核心框架层：**

| 技术组件 | 功能描述 |
|----------|----------|
| Python Flask | 轻量级Web应用框架 |
| SQLAlchemy ORM | 数据库对象关系映射 |
| MySQL数据库 | 主数据存储 |
| Redis缓存 | 会话管理和数据缓存 |
| Celery异步任务 | 后台任务处理 |

**业务服务层：**

| 服务模块 | 文件名 | 功能描述 |
|----------|--------|----------|
| 成本计算服务 | cost_calculation.py | 项目成本核算和分析 |
| 工时计算服务 | working_hours_calculator.py | 工时分类和计算 |
| 节假日服务 | holiday_service.py | 节假日配置和管理 |
| 权限管理服务 | rights.py | 用户权限控制 |
| 数据导入导出服务 | 多个模块 | 支持Excel批量操作 |

### 1.2.2 前端技术栈

**UI框架层：**

| 技术组件 | 功能描述 |
|----------|----------|
| Layui UI框架 | 企业级UI组件库 |
| jQuery JavaScript库 | DOM操作和AJAX交互 |
| ECharts图表库 | 数据可视化展示 |
| 响应式布局设计 | 支持多终端访问 |

**交互体验层：**

| 组件类型 | 功能特性 |
|----------|----------|
| 表格组件 | 支持分页、排序、筛选 |
| 表单组件 | 数据验证、联动选择 |
| 图表组件 | 柱状图、饼图、折线图 |
| 弹窗组件 | 模态对话框、确认框 |

### 1.2.3 部署架构

**生产环境部署：**

| 部署组件 | 功能描述 |
|----------|----------|
| Docker容器化部署 | 环境一致性保障 |
| Nginx反向代理 | 负载均衡和静态资源服务 |
| Gunicorn WSGI服务器 | Python应用服务器 |
| 集群部署支持 | 高可用性保障 |

## 1.3 核心特色

### 1.3.1 业务特色

| 序号 | 特色功能 | 功能描述 |
|------|----------|----------|
| 1 | 项目全生命周期管理 | 从立项到结算的完整流程控制 |
| 2 | 成本精细化核算 | 多维度成本分析和控制 |
| 3 | 智能工时统计 | 自动计算正常工时和加班工时 |
| 4 | 灵活薪资体系 | 支持多种薪资计算模式 |
| 5 | 支付状态智能跟踪 | 自动计算回款状态和逾期提醒 |
| 6 | 外包协作管理 | 外协成本独立核算 |
| 7 | 多维度统计分析 | 丰富的报表和数据分析功能 |

### 1.3.2 技术特色

| 序号 | 技术特色 | 技术描述 |
|------|----------|----------|
| 1 | 模块化架构设计 | 高内聚低耦合的系统架构 |
| 2 | 插件化扩展机制 | 支持功能模块的灵活扩展 |
| 3 | 权限精细化控制 | 基于角色和功能的权限管理 |
| 4 | 数据安全保障 | 完善的数据备份和恢复机制 |
| 5 | 高性能优化 | 缓存机制和数据库优化 |
| 6 | 移动端适配 | 响应式设计支持移动设备访问 |

## 1.4 业务全景图

```
GosunTec企业管理系统业务全景图

                    ┌─────────────────┐
                    │   用户管理系统   │
                    │  权限控制中心   │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │   项目管理核心   │
                    │  生命周期控制   │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   ┌────┴────┐         ┌─────┴─────┐         ┌────┴────┐
   │成本管理 │         │工时统计   │         │支付管理 │
   │核算中心 │◄────────┤管理中心   ├────────►│回款跟踪 │
   └────┬────┘         └─────┬─────┘         └────┬────┘
        │                    │                    │
        │              ┌─────┴─────┐              │
        │              │薪资管理   │              │
        │              │核算系统   │              │
        │              └─────┬─────┘              │
        │                    │                    │
   ┌────┴────┐         ┌─────┴─────┐         ┌────┴────┐
   │外包管理 │         │统计数据   │         │审批流程 │
   │协作平台 │         │分析中心   │         │管控系统 │
   └─────────┘         └───────────┘         └─────────┘

数据流转关系：
• 项目管理 → 成本管理：项目信息、预算数据
• 工时统计 → 成本管理：人工成本计算
• 工时统计 → 薪资管理：工时数据、加班统计
• 成本管理 → 支付管理：成本数据、盈利分析
• 支付管理 → 审批流程：回款申请、审批记录
• 外包管理 → 成本管理：外协成本数据
• 所有模块 → 统计数据：业务数据汇总分析
```

# 2. 核心业务模块

## 2.1 用户管理和权限系统

### 2.1.1 业务介绍

用户管理和权限系统是整个平台的基础模块，负责用户身份认证、权限分配、部门管理等核心功能。系统采用基于角色的访问控制（RBAC）模型，实现精细化的权限管理。

### 2.1.2 核心功能架构

**用户管理体系：**

1. **用户基础管理**
   1. 用户信息维护（姓名、工号、联系方式）
   2. 账户状态管理（启用/禁用/锁定）
   3. 密码策略控制（复杂度、有效期）
   4. 登录日志记录（时间、IP、设备）

2. **部门组织架构**
   1. 部门层级管理（支持多级部门）
   2. 部门负责人设置
   3. 员工部门归属
   4. 部门权限继承

3. **角色权限管理**
   1. 角色定义和分类
   2. 权限点精细化配置
   3. 角色权限分配
   4. 用户角色绑定

4. **权限控制机制**
   1. 菜单访问控制
   2. 功能操作权限
   3. 数据访问权限
   4. API接口权限

### 2.1.3 权限模型设计

**RBAC权限模型：**
用户(User) ←→ 用户角色(UserRole) ←→ 角色(Role) ←→ 角色权限(RolePermission) ←→ 权限(Permission)

**权限分类：**

1. **系统管理权限**
   1. 用户管理：增删改查用户信息
   2. 角色管理：角色创建、权限分配
   3. 部门管理：部门结构维护
   4. 系统配置：参数设置、字典管理

2. **业务功能权限**
   1. 项目管理：项目创建、编辑、删除、查看
   2. 成本管理：成本录入、审核、分析
   3. 工时管理：工时录入、审核、统计
   4. 支付管理：回款录入、审批、查询

3. **数据访问权限**
   1. 全部数据：超级管理员权限
   2. 部门数据：部门经理权限
   3. 个人数据：普通员工权限
   4. 项目数据：项目成员权限

### 2.1.4 使用场景

1. **系统管理员**：负责整个系统的用户和权限管理
2. **部门经理**：管理本部门员工和相关业务数据
3. **项目经理**：管理项目相关的人员和数据权限
4. **普通员工**：访问个人相关的业务功能

## 2.2 项目管理模块

### 2.2.1 业务介绍

项目管理模块是系统的核心业务模块，负责项目的全生命周期管理，包括项目立项、进度跟踪、状态管理、验收结算等功能。系统支持多种项目类型和灵活的项目配置。

### 2.2.2 项目生命周期管理

**项目状态流转图：**
未开始(0) → 生产中(1) → 发货中(3) → 发货完成(4) → 安装中(5) → 安装完成(6) → 验收中(9) → 项目完成(11)

**状态计算规则：**

1. **未开始**：项目创建但未填写开始日期
2. **生产中**：填写了合同开始日期但未到发货日期
3. **发货中**：到达发货日期但发货未完成
4. **发货完成**：发货完成日期已过
5. **安装中**：安装开始但未完成
6. **安装完成**：安装完成日期已过
7. **验收中**：进入验收阶段但验收未完成
8. **项目完成**：验收完成日期已过

**关键时间节点：**

1. project_start_date - 合同开始日期
2. shipping_end_date - 发货完成日期
3. installation_start_date - 开始安装日期
4. installation_end_date - 安装完成日期
5. acceptance_date - 验收完成日期

### 2.2.3 项目信息管理

**项目基础信息：**

1. **项目标识信息**
   1. 项目编号（唯一标识）
   2. 项目名称
   3. 项目类型（从project_manage_dept表加载）
   4. 项目状态（自动计算）

2. **合同信息**
   1. 合同金额
   2. 合同开始日期
   3. 预计交付日期
   4. 实际交付日期

3. **客户信息**
   1. 客户名称
   2. 联系人信息
   3. 客户地址
   4. 合同条款

4. **项目团队**
   1. 项目经理
   2. 技术负责人
   3. 项目成员
   4. 外协人员

5. **预算信息**
   1. 总预算金额
   2. BOM成本预算
   3. 人工成本预算
   4. 其他成本预算

### 2.2.4 项目进度跟踪

**进度管理功能：**

1. **进度计划制定**
   1. 里程碑设置
   2. 任务分解
   3. 时间节点规划
   4. 资源分配计划

2. **进度实时跟踪**
   1. 任务完成状态
   2. 时间进度对比
   3. 资源使用情况
   4. 风险预警提醒

3. **进度报告生成**
   1. 项目进度报表
   2. 里程碑达成情况
   3. 延期风险分析
   4. 资源利用率统计

**进度计算公式：**

进度百分比 = 根据项目状态映射

1. 未开始: 0%
2. 生产中: 10%
3. 发货中: 30%
4. 发货完成: 40%
5. 安装中: 50%
6. 安装完成: 60%
7. 验收中: 90%
8. 项目完成: 100%

### 2.2.5 项目类型管理

**项目类型配置：**

1. 项目类型从project_manage_dept表动态加载
2. 支持项目类型的启用/禁用控制
3. 项目类型与部门关联，便于权限控制
4. 选择项目类型后自动搜索匹配的项目代码

**项目代码管理：**

1. 项目代码从import_project表自动搜索
2. 支持模糊匹配和精确匹配
3. 项目代码与项目类型关联
4. 自动验证项目代码的唯一性

## 2.3 成本管理模块

### 2.3.1 业务介绍

成本管理模块是系统的财务核心，负责项目成本的精细化核算和控制。系统支持多种成本类型的分别核算，提供实时的成本分析和预警功能。

### 2.3.2 成本分类体系

**成本分类结构：**
总成本 = BOM成本 + 人工成本 + 外协成本 + 其他成本

1. **BOM成本（物料成本）**
   1. 机械BOM成本
   2. 电气BOM成本
   3. 软件BOM成本
   4. 其他物料成本

2. **人工成本（劳动力成本）**
   1. 正常工时成本
   2. 加班工时成本
   3. 项目奖金成本
   4. 社保公积金成本

3. **外协成本（外包成本）**
   1. 外协工时成本
   2. 外协服务成本
   3. 外协材料成本
   4. 外协管理成本

4. **其他成本**
   1. 差旅费用
   2. 设备折旧
   3. 项目激励
   4. 管理费用分摊

### 2.3.3 BOM成本核算规则

**BOM成本导入规则：**

1. 支持Excel批量导入BOM清单
2. 同一项目同一月份多次导入会累加成本金额
3. 自动按月份分摊成本，支持跨月项目成本分配
4. 机械BOM和电气BOM分别统计，便于成本分析

**BOM成本计算：**

1. 月度BOM成本 = 当月导入的所有BOM项目成本之和
2. 项目总BOM成本 = 所有月份BOM成本累计

**导入流程：**

1. 上传Excel文件（支持.xlsx格式）
2. 系统自动解析BOM数据
3. 验证数据格式和完整性
4. 确认导入并更新项目成本
5. 生成导入记录和成本变更日志

### 2.3.4 人工成本核算规则

**人工成本计算方法（支持三种计算模式）：**

1. **基于工时计算（timesheet）：**
   - 正常工时成本 = (应发工资 - 加班费) ÷ 月度总正工时 × 项目正工时
   - 加班工时成本 = 加班时薪 × 项目加班工时
   - 项目人工成本 = 正常工时成本 + 加班工时成本

2. **基于薪资分摊（salary）：**
   - 人工成本 = 员工月薪 × (项目工时 ÷ 员工总工时)

3. **基于实际薪资（actual_salary）：**
   - 使用员工实际应发工资进行精确分摊计算

**计算参数：**

1. 月度标准工时：21.75天 × 8小时 = 174小时
2. 加班倍率：工作日1.5倍，周末2.0倍，节假日3.0倍
3. 薪资构成：基本工资 + 绩效工资 + 各项补贴
4. 成本分摊：按实际工时比例分摊到各项目

### 2.3.5 外协成本核算规则

**外协成本计算规则：**

1. **工时成本计算：**
   - 外协工时成本 = 外协工时 × 外协时薪

2. **服务成本计算：**
   - 外协服务成本 = 固定服务费 + 变动服务费

3. **总外协成本：**
   - 总外协成本 = 外协工时成本 + 外协服务成本

**外协成本在项目成本中的处理：**

1. 外协成本作为独立的成本类别
2. 在项目成本分析中单独显示外协成本占比
3. 外协成本与人工成本分离统计
4. 支持外协成本的预算控制和预警

**外协成本结算：**

1. 按月结算外协费用
2. 生成外协费用结算单
3. 支持外协费用的审批流程
4. 记录外协费用支付历史

### 2.3.6 项目激励成本管理

**项目激励成本规则：**

1. 项目激励成本归类为其他成本
2. 支持多种激励类型：完成奖励、质量奖励、效率奖励
3. 激励金额可按项目、按人员、按部门设置
4. 激励成本计入项目总成本，影响项目盈利分析

**激励计算方式：**

1. 固定金额激励：按预设金额发放
2. 比例激励：按项目收入或利润的一定比例
3. 阶梯激励：根据完成情况分档发放
4. 团队激励：按团队贡献度分配

**激励审批流程：**

项目经理申请 → 部门主管审核 → 财务确认 → 系统记录

## 2.4 工时统计模块

### 2.4.1 业务介绍

工时统计模块负责员工工时的录入、审核、统计和分析。系统集成了节假日配置，能够自动计算正常工时和加班工时，为薪资核算和成本分析提供准确的数据基础。

### 2.4.2 工时计算规则

**工时计算逻辑（集成节假日配置系统）：**

1. **工作日（周一至周五）：**
   - 总工时 ≤ 8小时：全部为正常工时，加班工时 = 0
   - 总工时 > 8小时：正常工时 = 8小时，加班工时 = 总工时 - 8小时

2. **周末（周六、周日）：**
   - 全部时间为加班工时，正常工时 = 0
   - 加班倍率：2.0倍

3. **法定节假日：**
   - 全部时间为加班工时，正常工时 = 0
   - 加班倍率：3.0倍

4. **调休日：**
   - 按工作日规则计算
   - 支持调休日配置和管理

**工时验证规则：**

1. 单日工时不能超过24小时
2. 正常工时 + 加班工时 = 总工时
3. 工时必须为正数
4. 支持小数点后2位精度

### 2.4.3 工时统计规则

**工时统计维度：**

1. **个人工时统计：**
   1. 个人日/周/月工时汇总
   2. 项目工时分布统计
   3. 工时效率分析

2. **项目工时统计：**
   1. 项目总工时和人员分布
   2. 项目工时进度分析
   3. 项目工时成本核算

3. **部门工时统计：**
   1. 部门总工时和人员分布
   2. 部门工时效率对比
   3. 部门工时成本分析

**特殊统计规则：**

1. 试用期员工工时不计入部门统计
2. 外协工时单独统计，不与员工工时混合
3. 支持按项目类型、部门层级等多维度统计
4. 统计数据支持导出和报表生成

### 2.4.4 工时管理流程

**工时录入流程：**

1. **员工日常工时录入**
   1. 每日必须录入工时
   2. 录入截止时间：次日上午10点
   3. 支持批量录入和模板复制

2. **工时审核流程**
   1. 员工录入 → 项目经理审核 → 部门主管确认
   2. 异常工时需要说明原因
   3. 超时录入需要特殊审批

3. **工时确认和锁定**
   1. 审核通过后工时数据锁定
   2. 锁定后的工时用于成本核算
   3. 特殊情况下支持工时修正

**工时数据质量控制：**

1. 工时录入完整性检查
2. 工时数据合理性验证
3. 异常工时自动预警
4. 工时数据一致性校验

## 2.5 薪资管理模块

### 2.5.1 业务介绍

薪资管理模块负责员工薪资的核算、发放和管理。系统支持多种薪资结构和计算方式，能够自动计算基本工资、绩效工资、加班费等各项薪资组成部分。

### 2.5.2 薪资结构体系

**薪资构成结构：**
总薪资 = 基本工资 + 绩效工资 + 加班费 + 各项补贴 - 各项扣除

1. **基本工资**
   1. 岗位工资
   2. 技能工资
   3. 工龄工资
   4. 学历津贴

2. **绩效工资**
   1. 个人绩效
   2. 部门绩效
   3. 项目奖金
   4. 年终奖金

3. **加班费**
   1. 平时加班费（1.5倍）
   2. 周末加班费（2.0倍）
   3. 节假日加班费（3.0倍）

4. **各项补贴**
   1. 交通补贴
   2. 餐饮补贴
   3. 通讯补贴
   4. 住房补贴

5. **各项扣除**
   1. 社会保险
   2. 住房公积金
   3. 个人所得税
   4. 其他扣除

### 2.5.3 薪资计算规则

**加班费计算公式：**
加班费 = 基本工资 ÷ 21.75 ÷ 8 × 加班工时 × 加班倍率

**其中：**
1. 21.75 = 月平均工作天数
2. 8 = 每日标准工作小时
3. 加班倍率：平时1.5倍，周末2.0倍，节假日3.0倍

**应发工资计算：**
应发工资 = 基本工资 + 绩效工资 + 加班费 + 各项补贴

**实发工资计算：**
实发工资 = 应发工资 - 社保个人部分 - 公积金个人部分 - 个人所得税 - 其他扣除

**薪资发放控制：**
1. 试用期员工按试用期薪资标准
2. 离职员工按实际工作天数计算
3. 新入职员工按入职日期计算
4. 特殊情况需要人工调整

## 2.6 支付管理模块

### 2.6.1 业务介绍

支付管理模块负责项目回款的全流程管理，包括支付计划制定、回款记录管理、支付状态跟踪、验收激励计算等功能。系统提供智能化的支付状态计算和时间基础的提醒功能。

### 2.6.2 支付类型定义

**支付类型分类：**

1. **预付款（Prepayment）**
   1. 合同签订后客户支付的预付款项
   2. 通常占合同总额的20-30%
   3. 付款期限从合同开始日期算起

2. **发货款（Delivery Payment）**
   1. 产品发货后客户应支付的款项
   2. 通常占合同总额的40-50%
   3. 付款期限从发货完成日期算起

3. **验收款（Acceptance Payment）**
   1. 项目验收完成后客户应支付的款项
   2. 通常占合同总额的20-30%
   3. 付款期限从验收完成日期算起
   4. 支持验收激励计算

4. **质保金（Warranty Payment）**
   1. 质保期结束后客户支付的尾款
   2. 通常占合同总额的5-10%
   3. 付款期限从验收完成日期算起（通常1-2年后）
   4. 特殊处理：质保金为0时显示"无需回款"

### 2.6.3 支付状态计算规则

**支付状态定义（5种状态）：**

1. **0: 未回款** - 已超过付款期限但未收到款项（逾期状态）
2. **1: 已回款** - 实际回款金额 >= 应收金额（完成状态）
3. **2: 未到期** - 尚未到达付款期限（正常状态）
4. **3: 部分回款** - 实际回款金额 > 0 但 < 应收金额（部分完成状态）
5. **4: 无需回款** - 应收金额为0（特殊状态，如质保金为0的情况）

**支付状态计算逻辑：**

1. **首先检查应收金额是否为0**
   - 如果为0，状态为"无需回款"(4)

2. **比较实际回款金额与应收金额**
   - 如果实际回款 >= 应收金额，状态为"已回款"(1)
   - 如果实际回款 > 0 但 < 应收金额，状态为"部分回款"(3)

3. **检查付款期限**
   - 如果实际回款 = 0 且当前日期 > 付款截止日期，状态为"未回款"(0)
   - 如果实际回款 = 0 且当前日期 <= 付款截止日期，状态为"未到期"(2)

**付款期限计算：**

1. 预付款期限 = 合同开始日期 + 预付款天数
2. 发货款期限 = 发货完成日期 + 发货款天数
3. 验收款期限 = 验收完成日期 + 验收款天数
4. 质保金期限 = 验收完成日期 + 质保金天数

### 2.6.4 验收激励计算规则

**验收激励计算规则：**
以验收日期后第100天为基准

1. 提前回款：每提前1天奖励 验收款额 × 0.1‰
2. 延迟回款：每延迟1天扣除 验收款额 × 0.1‰
3. 分次回款：按每次金额和时间单独计算后累加

**激励计算公式：**
激励金额 = 验收款金额 × 0.1‰ × 天数差异

**其中：**
1. 天数差异 = 100天基准 - 实际回款天数
2. 正数表示提前，负数表示延迟
3. 分次回款按加权平均计算

**激励状态：**
1. 提前回款：显示奖励金额和提前天数
2. 按时回款：显示"按时回款"
3. 延迟回款：显示扣除金额和延迟天数
4. 未回款：显示"未回款"
5. 无验收款：显示"无验收款"

### 2.6.5 支付审批流程

**回款审批流程：**

1. **回款申请提交**
   1. 财务人员录入回款信息
   2. 选择回款类型和金额
   3. 填写回款日期和备注
   4. 选择累计回款或覆盖回款

2. **审批流程**
   1. 系统自动创建审批请求
   2. 部门主管或财务主管审批
   3. 审批通过后更新回款记录
   4. 审批拒绝则返回修改

3. **回款记录管理**
   1. 自动生成回款历史记录
   2. 更新项目支付状态
   3. 触发相关业务逻辑
   4. 发送状态变更通知

**审批权限控制：**
1. 不同金额级别需要不同审批权限
2. 支持多级审批流程
3. 审批记录完整可追溯
4. 支持审批流程自定义配置

## 2.7 外包管理模块

### 2.7.1 业务介绍

外包管理模块负责外协供应商信息管理、外协工时记录、外协成本核算等功能。系统将外协成本与内部人工成本分离管理，提供独立的外协数据统计和分析。

### 2.7.2 外协信息管理

**外协供应商信息：**

1. **基础信息**
   1. 外协名称
   2. 联系人信息
   3. 联系电话和邮箱
   4. 公司地址

2. **业务信息**
   1. 业务范围
   2. 资质等级
   3. 合作状态
   4. 时薪标准

3. **合作记录**
   1. 合作项目历史
   2. 工时统计记录
   3. 费用结算记录
   4. 评价反馈信息

### 2.7.3 外协工时管理

**外协工时特点：**
1. 外协工时不受正常工作时间限制
2. 外协工时不区分正常工时和加班工时
3. 外协工时按实际工作时间和约定时薪计算费用
4. 外协工时数据在统计报表中单独显示

**外协工时录入：**
1. 支持按项目录入外协工时
2. 可设置不同外协人员的时薪标准
3. 工时数据需要项目经理确认
4. 支持批量导入和批量操作

**外协成本计算：**
外协成本 = 外协工时 × 外协时薪

## 2.8 统计数据模块

### 2.8.1 业务介绍

统计数据模块提供全面的数据统计和分析功能，支持多维度的数据查询和报表生成，为管理决策提供数据支持。系统集成了工时统计、成本分析、项目进度统计等功能。

### 2.8.2 统计维度体系

**统计维度分类：**

1. **时间维度：**
   1. 日统计 - 每日工时、成本、进度数据
   2. 周统计 - 周度汇总和趋势分析
   3. 月统计 - 月度报表和同比分析
   4. 季度统计 - 季度总结和环比分析
   5. 年度统计 - 年度报告和年度对比

2. **组织维度：**
   1. 个人统计 - 个人工时、项目参与、绩效数据
   2. 部门统计 - 部门工时、成本、项目完成情况
   3. 项目组统计 - 项目团队的协作效率和产出
   4. 公司统计 - 全公司的整体运营数据

3. **业务维度：**
   1. 项目统计 - 项目进度、成本、收益分析
   2. 成本统计 - 各类成本的构成和趋势
   3. 工时统计 - 工时分布、效率、利用率
   4. 收益统计 - 项目收益、利润率、回报率

---

# 3. 计算规则手册

## 3.1 成本计算规则

### 3.1.1 总成本计算公式

**项目总成本计算：**
总成本 = BOM成本 + 人工成本 + 外协成本 + 其他成本

**其中：**
1. BOM成本 = 所有月份BOM导入成本累计
2. 人工成本 = 所有员工项目工时成本累计
3. 外协成本 = 所有外协工时成本累计
4. 其他成本 = 差旅费 + 设备折旧 + 项目激励 + 管理费用分摊

### 3.1.2 BOM成本计算规则

**BOM成本累加规则：**
1. 同一项目同一月份多次导入BOM成本会累加
2. 不同月份的BOM成本分别记录和统计
3. 支持BOM成本的修正和调整
4. BOM成本变更会自动更新项目总成本

**BOM成本分摊规则：**
1. 按项目实际使用量分摊BOM成本
2. 支持跨项目BOM成本分摊
3. 共用物料按使用比例分摊成本
4. 废料和损耗按一定比例计入成本

### 3.1.3 人工成本计算规则

**基于工时的人工成本计算：**
1. 正常工时成本 = (应发工资 - 加班费) ÷ 月度总正工时 × 项目正工时
2. 加班工时成本 = 加班时薪 × 项目加班工时 × 加班倍率
3. 项目人工成本 = 正常工时成本 + 加班工时成本

**其中：**
1. 月度总正工时 = 员工当月所有项目的正常工时总和
2. 加班时薪 = 基本工资 ÷ 21.75 ÷ 8
3. 加班倍率：平时1.5倍，周末2.0倍，节假日3.0倍

**基于薪资分摊的人工成本计算：**
项目人工成本 = 员工月薪 × (项目工时 ÷ 员工总工时)

**成本分摊原则：**
1. 按实际工时比例分摊人工成本
2. 试用期员工成本单独计算
3. 离职员工按实际工作时间分摊
4. 兼职员工按工时比例分摊

## 3.2 工时计算规则

### 3.2.1 工时分类计算

**工时分类规则：**

1. **工作日工时计算：**
   ```
   if 总工时 <= 8小时:
       正常工时 = 总工时
       加班工时 = 0
   else:
       正常工时 = 8小时
       加班工时 = 总工时 - 8小时
   ```

2. **周末工时计算：**
   - 正常工时 = 0
   - 加班工时 = 总工时

3. **法定节假日工时计算：**
   - 正常工时 = 0
   - 加班工时 = 总工时

4. **调休日工时计算：**
   - 按工作日规则计算

### 3.2.2 工时验证规则

**工时数据验证：**
1. 单日工时不能超过24小时
2. 工时必须为非负数
3. 工时精度支持到小数点后2位
4. 正常工时 + 加班工时 = 总工时
5. 工时录入必须关联具体项目

**工时合理性检查：**
1. 连续工作时间不能超过规定限制
2. 月度工时总量合理性检查
3. 异常工时自动标记和预警
4. 工时数据一致性校验

## 3.3 薪资计算规则

### 3.3.1 基本薪资计算

**应发工资计算公式：**
应发工资 = 基本工资 + 绩效工资 + 加班费 + 各项补贴

**加班费计算公式：**
加班费 = 基本工资 ÷ 21.75 ÷ 8 × 加班工时 × 加班倍率

**其中：**
1. 21.75 = 月平均工作天数（365天÷12月÷7天×5天）
2. 8 = 每日标准工作小时
3. 加班倍率：平时1.5倍，周末2.0倍，节假日3.0倍

**实发工资计算公式：**
实发工资 = 应发工资 - 社保个人部分 - 公积金个人部分 - 个人所得税 - 其他扣除

### 3.3.2 特殊情况处理

**试用期薪资计算：**
1. 试用期员工按试用期薪资标准计算
2. 试用期工时不计入部门统计
3. 转正后重新计算历史数据

**离职员工薪资计算：**
1. 按实际工作天数计算当月薪资
2. 离职当月工时按比例分摊
3. 未休年假等按规定折算

**新入职员工薪资计算：**
1. 按入职日期计算当月薪资
2. 入职当月工时按比例分摊
3. 社保公积金按实际缴费天数计算

## 3.4 支付状态计算

### 3.4.1 支付状态算法

**支付状态计算算法：**
```javascript
function calculatePaymentStatus(expectedAmount, actualAmount, dueDate, currentDate) {
    if (expectedAmount <= 0) {
        return 4; // 无需回款
    }

    if (actualAmount >= expectedAmount) {
        return 1; // 已回款
    }

    if (actualAmount > 0) {
        return 3; // 部分回款
    }

    if (currentDate > dueDate) {
        return 0; // 未回款（逾期）
    }

    return 2; // 未到期
}
```

### 3.4.2 逾期天数计算

**逾期天数计算：**
逾期天数 = 当前日期 - 付款截止日期

**剩余天数计算：**
剩余天数 = 付款截止日期 - 当前日期

**状态显示规则：**
1. 未回款：显示"未回款（已超期X天）"
2. 未到期：显示"未到期（还剩X天）"
3. 部分回款：显示"部分回款（还剩X天）"或"部分回款（已超期X天）"
4. 已回款：显示"已回款"
5. 无需回款：显示"无需回款"

## 3.5 项目进度计算

### 3.5.1 项目状态自动计算

**项目状态计算逻辑：**
```javascript
function calculateProjectStatus(progress, currentDate) {
    if (!progress) return 0; // 未开始

    // 检查验收日期
    if (progress.acceptance_date) {
        return progress.acceptance_date <= currentDate ? 11 : 9;
    }

    // 检查安装完成日期
    if (progress.installation_end_date) {
        return progress.installation_end_date <= currentDate ? 6 : 5;
    }

    // 检查安装开始日期
    if (progress.installation_start_date && progress.installation_start_date <= currentDate) {
        return 5;
    }

    // 检查发货完成日期
    if (progress.shipping_end_date) {
        return progress.shipping_end_date <= currentDate ? 4 : 3;
    }

    // 检查项目开始日期
    if (progress.project_start_date && progress.project_start_date <= currentDate) {
        return 1;
    }

    return 0; // 未开始
}
```

### 3.5.2 进度百分比计算

**进度百分比映射：**

| 状态码 | 进度百分比 | 状态描述 |
|--------|------------|----------|
| 0 | 0% | 未开始 |
| 1 | 10% | 生产中 |
| 3 | 30% | 发货中 |
| 4 | 40% | 发货完成 |
| 5 | 50% | 安装中 |
| 6 | 60% | 安装完成 |
| 9 | 90% | 验收中 |
| 11 | 100% | 项目完成 |

# 4. 流程管理体系

## 4.1 项目生命周期管理

### 4.1.1 项目立项流程

**项目立项标准流程：**

1. **项目申请阶段**
   1. 填写项目基本信息
   2. 设定项目预算和时间计划
   3. 指定项目经理和核心团队
   4. 提交立项申请

2. **项目评审阶段**
   1. 技术可行性评估
   2. 商务条件评估
   3. 资源可用性评估
   4. 风险评估和控制措施

3. **项目批准阶段**
   1. 部门主管审批
   2. 技术总监审批
   3. 总经理最终批准
   4. 项目正式启动

4. **项目启动阶段**
   1. 项目团队组建
   2. 项目计划细化
   3. 资源分配确认
   4. 项目启动会议

### 4.1.2 项目执行监控

**项目执行监控机制：**

1. **日常监控**
   1. 工时录入和审核
   2. 进度更新和跟踪
   3. 成本发生和控制
   4. 质量检查和改进

2. **周期性检查**
   1. 周度进度会议
   2. 月度成本分析
   3. 季度项目评估
   4. 年度项目总结

3. **里程碑管控**
   1. 关键节点检查
   2. 交付物质量验收
   3. 客户满意度调查
   4. 经验教训总结

4. 风险管控
   ├── 风险识别和评估
   ├── 风险应对措施
   ├── 风险监控和预警
   └── 风险处置和总结
```

## 4.2 成本核算流程

### 4.2.1 成本数据收集

**成本数据收集流程：**

1. **BOM成本收集**
   1. 采购部门提供BOM清单
   2. 财务部门核实采购价格
   3. 项目经理确认使用数量
   4. 系统录入BOM成本数据

2. **人工成本收集**
   1. 员工录入项目工时
   2. 项目经理审核工时数据
   3. 人事部门提供薪资数据
   4. 系统自动计算人工成本

3. **外协成本收集**
   1. 项目经理录入外协工时
   2. 采购部门提供外协价格
   3. 财务部门审核外协费用
   4. 系统计算外协成本

4. **其他成本收集**
   1. 各部门提供费用发生数据
   2. 财务部门审核费用合理性
   3. 项目经理确认费用归属
   4. 系统录入其他成本数据

### 4.2.2 成本分析报告

**成本分析报告生成：**

1. **月度成本分析**
   1. 当月成本发生情况
   2. 累计成本与预算对比
   3. 成本结构分析
   4. 成本控制建议

2. **项目成本分析**
   1. 项目总成本统计
   2. 各类成本占比分析
   3. 成本效率评估
   4. 盈利能力分析

3. **部门成本分析**
   1. 部门成本构成
   2. 部门成本趋势
   3. 部门间成本对比
   4. 成本优化建议

4. **公司成本分析**
   1. 整体成本水平
   2. 成本控制效果
   3. 行业对比分析
   4. 战略调整建议

## 4.3 工时管理流程

### 4.3.1 工时录入审核流程

**工时管理标准流程：**

1. **工时录入阶段**
   1. 员工每日录入工时
   2. 选择对应项目和任务
   3. 填写工作内容描述
   4. 提交工时数据

2. **初级审核阶段**
   1. 项目经理审核项目工时
   2. 检查工时合理性
   3. 确认工作内容真实性
   4. 批准或退回工时

3. **高级审核阶段**
   1. 部门主管审核部门工时
   2. 检查工时分布合理性
   3. 确认资源配置效率
   4. 最终确认工时数据

4. **工时锁定阶段**
   1. 审核通过后工时锁定
   2. 锁定工时用于成本计算
   3. 生成工时统计报表
   4. 触发薪资计算流程

## 4.4 支付审批流程

### 4.4.1 回款审批标准流程
**回款审批管理流程：**

1. **回款申请阶段**
   1. 财务人员录入回款信息
   2. 选择回款类型和项目
   3. 填写回款金额和日期
   4. 提交审批申请

2. **初级审批阶段**
   1. 项目经理确认回款真实性
   2. 检查回款金额合理性
   3. 验证回款时间准确性
   4. 提交上级审批

3. **高级审批阶段**
   1. 财务主管审核回款合规性
   2. 检查回款账务处理
   3. 确认回款影响分析
   4. 最终批准或拒绝

4. **回款确认阶段**
   1. 审批通过后更新回款记录
   2. 自动计算支付状态
   3. 生成回款历史记录
   4. 发送状态变更通知

# 5. 使用场景指南

## 5.1 管理员操作指南

### 5.1.1 系统初始化配置
**系统管理员主要职责：**

1. **用户账户管理**
   1. 创建和管理用户账户
   2. 分配用户角色和权限
   3. 管理部门组织架构
   4. 监控用户登录状态

2. **系统参数配置**
   1. 配置系统基础参数
   2. 设置业务规则参数
   3. 管理数据字典
   4. 配置节假日信息

3. **数据维护管理**
   1. 数据备份和恢复
   2. 数据清理和归档
   3. 系统性能监控
   4. 安全策略管理

4. **系统功能维护**
   1. 功能模块配置
   2. 权限策略调整
   3. 业务流程优化
   4. 系统升级维护

## 5.2 项目经理操作指南

### 5.2.1 项目管理核心操作
**项目经理主要职责：**

1. **项目信息管理**
   1. 创建和维护项目信息
   2. 设置项目团队成员
   3. 制定项目计划和预算
   4. 跟踪项目进度状态

2. **团队工时管理**
   1. 审核团队成员工时
   2. 分配项目任务和工时
   3. 监控工时分布合理性
   4. 处理工时异常情况

3. **项目成本控制**
   1. 监控项目成本发生
   2. 分析成本构成和趋势
   3. 控制成本超支风险
   4. 优化资源配置效率

4. **项目进度跟踪**
   1. 更新项目关键节点
   2. 监控项目执行进度
   3. 处理项目风险问题
   4. 协调项目资源需求

## 5.3 财务人员操作指南

### 5.3.1 财务管理核心操作

**财务人员主要职责：**

1. **成本数据管理**
   1. 录入和审核BOM成本
   2. 导入和处理成本数据
   3. 生成成本分析报表
   4. 监控成本控制效果

2. **支付管理操作**
   1. 录入项目回款信息
   2. 处理支付审批流程
   3. 跟踪回款状态变化
   4. 生成支付分析报表

3. **薪资核算管理**
   1. 核算员工月度薪资
   2. 处理薪资调整申请
   3. 生成薪资发放清单
   4. 处理薪资相关问题

4. **财务报表分析**
   1. 生成各类财务报表
   2. 分析财务数据趋势
   3. 提供决策支持信息
   4. 配合审计和检查

## 5.4 普通员工操作指南

### 5.4.1 日常操作指南
**普通员工主要操作：**

1. **工时录入管理**
   1. 每日及时录入工时
   2. 选择正确的项目和任务
   3. 填写详细的工作内容
   4. 确保工时数据准确性

2. **个人信息维护**
   1. 更新个人基本信息
   2. 修改登录密码
   3. 查看个人工时统计
   4. 查看个人薪资信息

3. **项目信息查询**
   1. 查看参与项目信息
   2. 了解项目进度状态
   3. 查看项目团队成员
   4. 获取项目相关通知

4. **报表查询功能**
   1. 查看个人工时报表
   2. 查看项目参与情况
   3. 导出个人数据报表
   4. 查看系统通知消息

# 6. 系统配置和管理

## 6.1 系统参数配置

### 6.1.1 基础参数配置

**系统基础参数：**

1. **工时计算参数**
   1. 标准工作时间：8小时/天
   2. 月平均工作天数：21.75天
   3. 加班倍率设置：平时1.5倍，周末2.0倍，节假日3.0倍
   4. 工时录入截止时间：次日上午10点

2. **成本计算参数**
   1. 默认工时成本：100元/小时
   2. 成本分摊方式：按工时比例
   3. 外协成本计算方式：工时×时薪
   4. 项目激励计算基准：验收后100天

3. **支付管理参数**
   1. 预付款默认比例：20-30%
   2. 发货款默认比例：40-50%
   3. 验收款默认比例：20-30%
   4. 质保金默认比例：5-10%

4. **审批流程参数**
   1. 工时审批层级：项目经理→部门主管
   2. 回款审批层级：财务主管→总经理
   3. 成本审批层级：项目经理→财务主管
   4. 审批超时处理：自动提醒和升级

### 6.2 权限配置管理

### 6.2.1 权限配置策略

**权限配置原则：**

1. **最小权限原则**
   1. 用户只获得完成工作所需的最小权限
   2. 定期审查和调整用户权限
   3. 及时回收离职员工权限
   4. 监控权限使用情况

2. **角色分离原则**
   1. 不同角色承担不同职责
   2. 关键操作需要多人审批
   3. 避免权限过度集中
   4. 建立权限制衡机制

3. **数据访问控制**
   1. 按部门层级控制数据访问
   2. 按项目参与度控制数据访问
   3. 敏感数据需要特殊权限
   4. 数据访问日志记录和审计

4. **功能权限控制**
   1. 按业务需要分配功能权限
   2. 关键功能需要特殊授权
   3. 权限变更需要审批流程
   4. 权限配置文档化管理

## 6.3 数据备份和恢复

### 6.3.1 数据安全保障
**数据备份策略：**

1. **定期备份计划**
   1. 每日增量备份
   2. 每周全量备份
   3. 每月归档备份
   4. 每季度异地备份

2. **备份数据验证**
   1. 备份完整性检查
   2. 备份数据可用性测试
   3. 恢复流程定期演练
   4. 备份监控和报警

3. **数据恢复机制**
   1. 快速恢复方案
   2. 灾难恢复预案
   3. 数据恢复测试
   4. 恢复时间目标控制

4. **数据安全措施**
   1. 数据加密存储
   2. 访问权限控制
   3. 操作日志记录
   4. 安全审计机制

---

## 📚 附录

### 常用术语解释
- **BOM成本**：Bill of Materials，物料清单成本
- **工时**：员工实际工作时间，分为正常工时和加班工时
- **外协**：外部协作，指委托外部供应商完成的工作
- **应发工资**：员工应该获得的工资总额，扣除各项费用前的金额
- **实发工资**：员工实际到手的工资金额
- **回款**：客户向公司支付的项目款项

### 联系方式
- **技术支持**：请联系系统管理员
- **业务咨询**：请联系项目经理
- **问题反馈**：请通过系统内置反馈功能提交

---

---

## 📚 附录

### 常用术语解释

| 术语 | 英文 | 解释 |
|------|------|------|
| BOM成本 | Bill of Materials | 物料清单成本，包含项目所需的所有物料费用 |
| 工时 | Working Hours | 员工实际工作时间，分为正常工时和加班工时 |
| 外协 | Outsourcing | 外部协作，指委托外部供应商完成的工作 |
| 应发工资 | Gross Salary | 员工应该获得的工资总额，扣除各项费用前的金额 |
| 实发工资 | Net Salary | 员工实际到手的工资金额 |
| 回款 | Payment Collection | 客户向公司支付的项目款项 |

### 联系方式

| 类型 | 联系方式 |
|------|----------|
| 技术支持 | 请联系系统管理员 |
| 业务咨询 | 请联系项目经理 |
| 问题反馈 | 请通过系统内置反馈功能提交 |

### 文档维护

| 项目 | 说明 |
|------|------|
| 文档更新频率 | 根据系统功能更新情况定期维护 |
| 版本控制 | 采用语义化版本号管理 |
| 审核流程 | 技术团队编写 → 产品团队审核 → 管理层批准 |

---

**文档结束**

*本文档为GosunTec企业管理系统的完整说明文档，涵盖了系统的所有核心功能、业务规则、计算方式和操作流程。文档采用标准Word格式编写，便于打印和分发。如有疑问或需要更新，请及时联系相关负责人。*

---

**© 2025 GosunTec技术团队 版权所有**
