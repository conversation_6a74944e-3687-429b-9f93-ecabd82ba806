<!DOCTYPE html>
<html>
<head>
    <title>转正管理</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">

{# 查询表单 #}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="formal-query-form">
            <div class="layui-form-item" style="margin-bottom: unset;">
                <label class="layui-form-label">员工姓名</label>
                <div class="layui-input-inline">
                    <input type="text" name="name" placeholder="" class="layui-input">
                </div>
                <label class="layui-form-label">部门</label>
                <div class="layui-input-inline">
                    <select name="dept_id">
                        <option value="">全部部门</option>
                        <!-- 部门选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <label class="layui-form-label">到期状态</label>
                <div class="layui-input-inline">
                    <select name="status_filter">
                        <option value="">全部</option>
                        <option value="urgent">已到期</option>
                        <option value="warning">即将到期(7天内)</option>
                        <option value="normal">正常</option>
                    </select>
                </div>
                <button class="layui-btn layui-btn-md" lay-submit lay-filter="formal-query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

{# 转正管理表格 #}
<div class="layui-card">
    <div class="layui-card-body">
        <table id="formal-table" lay-filter="formal-table"></table>
    </div>
</div>

</body>

{% include 'system/common/footer.html' %}

{# 表格工具栏 #}
<script type="text/html" id="formal-toolbar">
    {% if authorize("system:YG:edit") %}
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="batch_formal">
            <i class="layui-icon layui-icon-ok"></i>
            批量转正
        </button>
    {% endif %}
    <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh">
        <i class="layui-icon layui-icon-refresh"></i>
        刷新
    </button>
</script>

{# 操作按钮 #}
<script type="text/html" id="formal-bar">
    {% if authorize("system:YG:edit") %}
        <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="convert_formal" title="转正">
            <i class="layui-icon layui-icon-ok"> 转正</i>
        </button>
        <button class="layui-btn layui-btn-warm layui-btn-xs" lay-event="extend_probation" title="延时转正">
            <i class="layui-icon layui-icon-time"> 延时</i>
        </button>
        <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete_employee" title="离职删除">
            <i class="layui-icon layui-icon-delete"> 离职</i>
        </button>
    {% endif %}
</script>

{# 试用期状态模板 #}
{% raw %}
<script type="text/html" id="probation-status-template">
    {{# if(d.probation_status === 'urgent') { }}
        <span class="layui-badge layui-bg-red">已到期</span>
    {{# } else if(d.probation_status === 'warning') { }}
        <span class="layui-badge layui-bg-orange">即将到期</span>
    {{# } else if(d.probation_status === 'normal') { }}
        <span class="layui-badge layui-bg-green">正常</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未知</span>
    {{# } }}
</script>
{% endraw %}

{# 入职日期模板 #}
{% raw %}
<script type="text/html" id="hire-date-template">
    {{# if(d.hire_date) { }}
        {{#
            var date = new Date(d.hire_date);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
        }}
        {{ year }}年{{ month }}月{{ day }}日
    {{# } else { }}
        <span style="color: #999;">-</span>
    {{# } }}
</script>
{% endraw %}

{# 到期日期模板 #}
{% raw %}
<script type="text/html" id="probation-end-template">
    {{# if(d.probation_end_date) { }}
        {{#
            var date = new Date(d.probation_end_date);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
        }}
        {{ year }}年{{ month }}月{{ day }}日
    {{# } else { }}
        <span style="color: #999;">-</span>
    {{# } }}
</script>
{% endraw %}

{# 备注模板 #}
{% raw %}
<script type="text/html" id="formal-note-template">
    {{# if(d.formal_note && d.formal_note.trim()) { }}
        {{# if(d.formal_note.length > 20) { }}
            <span title="{{ d.formal_note }}" style="cursor: help; color: #333;">
                {{ d.formal_note.substring(0, 20) }}...
            </span>
        {{# } else { }}
            <span style="color: #333;">{{ d.formal_note }}</span>
        {{# } }}
    {{# } else { }}
        <span style="color: #999;">-</span>
    {{# } }}
</script>
{% endraw %}

<script>
    layui.use(['table', 'form', 'jquery', 'popup'], function () {
        let table = layui.table
        let form = layui.form
        let $ = layui.jquery
        let popup = layui.popup

        const MODULE_PATH = '/system/yg_info/'

        // 表格列配置
        let cols = [
            [
                {type: 'checkbox', fixed: 'left'},
                {title: '姓名', field: 'name', align: 'center', width: 100},
                {title: '工号', field: 'employee_id', align: 'center', width: 100},
                {title: '部门', field: 'dept_name', align: 'center', width: 100},
                {title: '职位', field: 'position', align: 'center', width: 100},
                {title: '员工类型', field: 'is_formal', align: 'center', width: 100},
                {title: '试用期时长', field: 'probation_duration', align: 'center', width: 100, templet: function(d) {
                    return d.probation_duration ? d.probation_duration + '天' : '-';
                }},
                {title: '入职日期', field: 'hire_date', align: 'center', width: 180, templet: '#hire-date-template'},
                {title: '到期日期', field: 'probation_end_date', align: 'center', width: 180, templet: '#probation-end-template'},
                {title: '试用期状态', field: 'probation_status', align: 'center', width: 100, templet: '#probation-status-template'},
                {title: '备注', field: 'formal_note', align: 'center', width: 200, templet: '#formal-note-template'},
                {title: '操作', toolbar: '#formal-bar', align: 'center', width: 260, fixed: 'right'}
            ]
        ]

        // 渲染表格
        table.render({
            elem: '#formal-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#formal-toolbar',
            text: {none: '暂无试用期员工'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'exports'],
            where: {
                // 只查询试用期和实习期员工
                is_formal_filter: 'probation'
            }
        })

        // 加载部门选项
        function loadDepartments() {
            $.ajax({
                url: MODULE_PATH + 'departments',
                type: 'GET',
                success: function(result) {
                    if (result.success) {
                        let deptSelect = $('select[name="dept_id"]');
                        deptSelect.empty().append('<option value="">全部部门</option>');

                        // 处理简单的部门列表数据
                        result.data.forEach(function(dept) {
                            deptSelect.append(`<option value="${dept.id}">${dept.dept_name}</option>`);
                        });

                        form.render('select');
                    } else {
                        console.error('加载部门失败:', result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('部门API请求失败:', error);
                }
            });
        }

        // 初始化加载部门
        loadDepartments();

        // 查询表单提交
        form.on('submit(formal-query)', function (data) {
            // 添加试用期员工筛选条件
            data.field.is_formal_filter = 'probation';
            table.reload('formal-table', {where: data.field})
            return false
        })

        // 重置按钮事件
        $('button[type="reset"]').on('click', function() {
            // 重置表单
            $('form[lay-filter="formal-query-form"]')[0].reset();
            form.render();

            // 重新加载表格，只保留试用期筛选条件
            table.reload('formal-table', {
                where: {
                    is_formal_filter: 'probation'
                }
            });
        });

        // 工具栏事件
        table.on('toolbar(formal-table)', function (obj) {
            if (obj.event === 'batch_formal') {
                // 批量转正
                let checkStatus = table.checkStatus('formal-table')
                let data = checkStatus.data
                
                if (data.length === 0) {
                    popup.failure('请选择要转正的员工')
                    return
                }
                
                layer.confirm(`确定要将选中的 ${data.length} 名员工转为正式员工吗？`, {
                    icon: 3,
                    title: '批量转正确认'
                }, function (index) {
                    layer.close(index);
                    
                    // 显示批量转正备注输入框
                    layer.prompt({
                        title: '批量转正备注（可选）',
                        formType: 2,
                        area: ['400px', '200px']
                    }, function(note, promptIndex) {
                        layer.close(promptIndex);
                        
                        let employee_ids = data.map(item => item.id);
                        let loading = layer.load();
                        
                        $.ajax({
                            url: MODULE_PATH + 'batch_convert_to_formal',
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                employee_ids: employee_ids,
                                formal_note: note || ''
                            }),
                            success: function (result) {
                                layer.close(loading);
                                if (result.success) {
                                    popup.success(result.msg, function() {
                                        table.reload('formal-table'); // 刷新表格
                                    });
                                } else {
                                    popup.failure(result.msg);
                                }
                            },
                            error: function () {
                                layer.close(loading);
                                popup.failure('批量转正操作失败');
                            }
                        });
                    });
                });
            } else if (obj.event === 'refresh') {
                table.reload('formal-table')
            }
        })

        // 行工具事件
        table.on('tool(formal-table)', function (obj) {
            if (obj.event === 'convert_formal') {
                // 单个员工转正
                layer.confirm('确定要将该员工转为正式员工吗？', {
                    icon: 3,
                    title: '转正确认'
                }, function (index) {
                    layer.close(index);

                    layer.prompt({
                        title: '转正备注（可选）',
                        formType: 2,
                        area: ['400px', '200px']
                    }, function(note, promptIndex) {
                        layer.close(promptIndex);

                        let loading = layer.load();
                        $.ajax({
                            url: MODULE_PATH + 'convert_to_formal/' + obj.data.id,
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                formal_note: note || ''
                            }),
                            success: function (result) {
                                layer.close(loading);
                                if (result.success) {
                                    popup.success(result.msg, function() {
                                        table.reload('formal-table');
                                    });
                                } else {
                                    popup.failure(result.msg);
                                }
                            },
                            error: function () {
                                layer.close(loading);
                                popup.failure('转正操作失败');
                            }
                        });
                    });
                });
            } else if (obj.event === 'extend_probation') {
                // 延时转正
                layer.open({
                    type: 1,
                    title: '延时转正',
                    area: ['500px', '400px'],
                    content: `
                        <div style="padding: 20px;">
                            <form class="layui-form" lay-filter="extend-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">延时选项</label>
                                    <div class="layui-input-block">
                                        <input type="radio" name="extend_type" value="30" title="30天" checked>
                                        <input type="radio" name="extend_type" value="60" title="60天">
                                        <input type="radio" name="extend_type" value="90" title="90天">
                                        <input type="radio" name="extend_type" value="custom" title="自定义">
                                    </div>
                                </div>
                                <div class="layui-form-item" id="custom-days" style="display: none;">
                                    <label class="layui-form-label">自定义天数</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="custom_days" placeholder="请输入延时天数" class="layui-input" min="1" max="365">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">延时原因</label>
                                    <div class="layui-input-block">
                                        <textarea name="extend_reason" placeholder="请输入延时原因" class="layui-textarea" required></textarea>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit lay-filter="extend-submit">确认延时</button>
                                        <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    `,
                    success: function() {
                        form.render();

                        // 监听延时类型变化
                        form.on('radio()', function(data) {
                            if (data.elem.name === 'extend_type') {
                                if (data.value === 'custom') {
                                    $('#custom-days').show();
                                } else {
                                    $('#custom-days').hide();
                                }
                            }
                        });

                        // 监听表单提交
                        form.on('submit(extend-submit)', function(data) {
                            let extendDays;
                            if (data.field.extend_type === 'custom') {
                                extendDays = parseInt(data.field.custom_days);
                                if (!extendDays || extendDays < 1) {
                                    popup.failure('请输入有效的延时天数');
                                    return false;
                                }
                            } else {
                                extendDays = parseInt(data.field.extend_type);
                            }

                            if (!data.field.extend_reason.trim()) {
                                popup.failure('请输入延时原因');
                                return false;
                            }

                            layer.closeAll();
                            let loading = layer.load();

                            $.ajax({
                                url: MODULE_PATH + 'extend_probation/' + obj.data.id,
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    extend_days: extendDays,
                                    extend_reason: data.field.extend_reason
                                }),
                                success: function (result) {
                                    layer.close(loading);
                                    if (result.success) {
                                        popup.success(result.msg, function() {
                                            table.reload('formal-table');
                                        });
                                    } else {
                                        popup.failure(result.msg);
                                    }
                                },
                                error: function () {
                                    layer.close(loading);
                                    popup.failure('延时转正操作失败');
                                }
                            });

                            return false;
                        });
                    }
                });
            } else if (obj.event === 'delete_employee') {
                // 离职删除
                layer.confirm(`确定要删除员工 "${obj.data.name}" 吗？<br><span style="color: red;">此操作不可恢复！</span>`, {
                    icon: 0,
                    title: '离职删除确认',
                    btn: ['确认删除', '取消']
                }, function (index) {
                    layer.close(index);

                    // 二次确认
                    layer.prompt({
                        title: '离职原因（可选）',
                        formType: 2,
                        area: ['400px', '200px']
                    }, function(reason, promptIndex) {
                        layer.close(promptIndex);

                        let loading = layer.load();
                        $.ajax({
                            url: MODULE_PATH + 'delete_employee/' + obj.data.id,
                            type: 'DELETE',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                delete_reason: reason || ''
                            }),
                            success: function (result) {
                                layer.close(loading);
                                if (result.success) {
                                    popup.success(result.msg, function() {
                                        table.reload('formal-table');
                                    });
                                } else {
                                    popup.failure(result.msg);
                                }
                            },
                            error: function () {
                                layer.close(loading);
                                popup.failure('删除操作失败');
                            }
                        });
                    });
                });
            }
        })
    })
</script>
</body>
</html>
