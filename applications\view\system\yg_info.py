from flask import Blueprint, render_template, request, send_file
from flask_login import login_required, current_user
from sqlalchemy import desc
import pandas as pd
from io import BytesIO
import datetime
import re

from applications.common import curd
from applications.common.curd import enable_status, disable_status
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import Role, Dept, EmployeeSalary, SocialInsurance
from applications.models import ygong
from applications.services.probation_service import ProbationReminderService

bp = Blueprint('yg_info', __name__, url_prefix='/yg_info')






@bp.route('/')
@login_required
@authorize("system:YG:info", log=True)
def index():
    return render_template('system/YG/yg.html')


@bp.get('/formal_management')
@authorize("system:YG:formal")
def formal_management():
    """转正管理页面"""
    return render_template('system/YG/formal_management.html')

# 用户增加

@bp.get('/add')
@authorize("system:YG:add", log=True)
def add():
    roles = Role.query.all()
    return render_template('system/YG/add.html', roles=roles)


@bp.post('/save')
@authorize("system:YG:add", log=True)
def save():
    req_json = request.get_json(force=True)
    a = req_json.get("roleIds")
    name = str_escape(req_json.get('name'))
    employee_id = str_escape(req_json.get('employee_id'))
    phone = str_escape(req_json.get('phone'))
    dept_id = str_escape(req_json.get('deptId'))
    position = str_escape(req_json.get('position'))
    hire_date = str_escape(req_json.get('hire_date'))
    gender = str_escape(req_json.get('gender'))
    birth_date = str_escape(req_json.get('birth_date'))
    age = str_escape(req_json.get('age'))
    id_card = str_escape(req_json.get('id_card'))
    is_formal = str_escape(req_json.get('is_formal'))
    probation_duration = str_escape(req_json.get('probation_duration'))
    base_salary = str_escape(req_json.get('base_salary'))
    performance_salary = str_escape(req_json.get('performance_salary'))
    supervisor_assessment = str_escape(req_json.get('supervisor_assessment'))
    
    role_ids = a.split(',')
    
    if not name or not phone:
        return fail_api(msg="姓名和手机号不得为空")

    if len(phone) != 11:
        return fail_api(msg="请输入正确的手机号")

    # 改进重复检查逻辑，检查多个关键字段
    if employee_id and ygong.query.filter_by(employee_id=employee_id).count():
        return fail_api(msg="员工工号已存在")

    if ygong.query.filter_by(phone=phone).count():
        return fail_api(msg="手机号已存在")

    if id_card and ygong.query.filter_by(id_card=id_card).count():
        return fail_api(msg="身份证号已存在")

    if ygong.query.filter_by(name=name).count():
        return fail_api(msg="姓名已存在")

    user = ygong(
        name=name,
        employee_id=employee_id,
        phone=phone,
        enable=1,
        dept_id=dept_id,
        position=position,
        hire_date=hire_date,
        gender=gender,
        birth_date=birth_date,
        age=age,
        id_card=id_card,
        is_formal=is_formal,
        probation_duration=probation_duration,
        base_salary=base_salary,
        performance_salary=performance_salary,
        supervisor_assessment=supervisor_assessment
    )
    db.session.add(user)
    roles = Role.query.filter(Role.id.in_(role_ids)).all()
    for r in roles:
        user.role.append(r)

    db.session.commit()
    return success_api(msg="增加成功")

# 删除用户
@bp.delete('/remove/<int:id>')
@authorize("system:YG:remove", log=True)
def delete(id):
    user = ygong.query.filter_by(id=id).first()
    user.role = []

    res = ygong.query.filter_by(id=id).delete()
    db.session.commit()
    if not res:
        return fail_api(msg="删除失败")
    return success_api(msg="删除成功")


#  编辑用户
@bp.get('/edit/<int:id>')
@authorize("system:YG:edit", log=True)
def edit(id):
    yg = curd.get_one_by_id(ygong, id)
    roles = Role.query.all()
    checked_roles = []
    for r in yg.role:
        checked_roles.append(r.id)
    return render_template('system/YG/edit.html', user=yg, roles=roles, checked_roles=checked_roles)

@bp.put('/update')
@authorize("system:YG:edit", log=True)
def update():
    req_json = request.get_json(force=True)
    a = str_escape(req_json.get("roleIds", ""))
    id = str_escape(req_json.get("id"))
    name = str_escape(req_json.get('name'))
    employee_id = str_escape(req_json.get('employee_id'))
    phone = str_escape(req_json.get('phone'))
    dept_id = str_escape(req_json.get('deptId'))
    position = str_escape(req_json.get('position'))
    hire_date = str_escape(req_json.get('hire_date'))
    gender = str_escape(req_json.get('gender'))
    birth_date = str_escape(req_json.get('birth_date'))
    age = str_escape(req_json.get('age'))
    id_card = str_escape(req_json.get('id_card'))
    is_formal = str_escape(req_json.get('is_formal'))
    probation_duration = str_escape(req_json.get('probation_duration'))
    base_salary = str_escape(req_json.get('base_salary'))
    performance_salary = str_escape(req_json.get('performance_salary'))
    supervisor_assessment = str_escape(req_json.get('supervisor_assessment'))
    
    role_ids = a.split(',') if a else []
    
    ygong.query.filter_by(id=id).update({
        'name': name,
        'employee_id': employee_id,
        'phone': phone,
        'dept_id': dept_id,
        'position': position,
        'hire_date': hire_date,
        'gender': gender,
        'birth_date': birth_date,
        'age': age,
        'id_card': id_card,
        'is_formal': is_formal,
        'probation_duration': probation_duration,
        'base_salary': base_salary,
        'performance_salary': performance_salary,
        'supervisor_assessment': supervisor_assessment
    })
    
    u = ygong.query.filter_by(id=id).first()
    roles = Role.query.filter(Role.id.in_(role_ids)).all()
    u.role = roles

    db.session.commit()
    return success_api(msg="更新成功")


#   用户分页查询
@bp.get('/data')
# @authorize("system:user:main")
def data():
    # 获取请求参数
    name_raw = request.args.get('name', type=str)
    phone_raw = request.args.get('phone', type=str)
    dept_id = request.args.get('dept_id', type=int)  # 修复参数名
    probation_status = request.args.get('status_filter', type=str)  # 修复参数名
    is_formal_filter = request.args.get('is_formal_filter', type=str)  # 新增：员工类型筛选

    # 处理搜索参数，避免str_escape将空字符串转为None
    name = str_escape(name_raw) if name_raw else None
    phone = str_escape(phone_raw) if phone_raw else None

    filters = []
    if name:
        filters.append(ygong.name.contains(name))
    if phone:
        filters.append(ygong.phone.contains(phone))
    if dept_id:
        filters.append(ygong.dept_id == dept_id)

    # 添加员工类型筛选
    if is_formal_filter == 'probation':
        # 只查询试用期和实习期员工
        filters.append(ygong.is_formal.in_(['试用', '实习']))

    # 如果有试用期状态筛选，需要先过滤再分页
    if probation_status:
        # 获取所有符合条件的员工（不分页）
        all_employees = db.session.query(
            ygong,
            Dept
        ).filter(*filters).outerjoin(Dept, ygong.dept_id == Dept.id).distinct().all()

        # 过滤试用期状态
        filtered_employees = []
        for user, dept in all_employees:
            probation_status_value = user.get_probation_status()
            if probation_status_value == probation_status:
                filtered_employees.append((user, dept))

        # 手动分页
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        start = (page - 1) * limit
        end = start + limit

        paginated_employees = filtered_employees[start:end]
        total_count = len(filtered_employees)

        # 构建数据
        data = []
        for user, dept in paginated_employees:
            probation_status_value = user.get_probation_status()
            probation_end_date = user.calculate_probation_end_date()

            data.append({
                'id': user.id,
                'name': user.name,
                'employee_id': user.employee_id,
                'phone': user.phone,
                'dept_name': dept.dept_name if dept else None,
                'dept_id': dept.id if dept else None,
                'position': user.position,
                'hire_date': user.hire_date,
                'gender': user.gender,
                'birth_date': user.birth_date,
                'age': user.age,
                'id_card': user.id_card,
                'is_formal': user.is_formal,
                'probation_duration': user.probation_duration,
                'probation_status': probation_status_value,
                'probation_end_date': probation_end_date,
                'formal_note': user.formal_note,
                'base_salary': user.base_salary,
                'performance_salary': user.performance_salary,
                'supervisor_assessment': user.supervisor_assessment,
                'enable': user.enable,
                'create_at': user.create_at,
                'update_at': user.update_at
            })

        return table_api(data=data, count=total_count)

    else:
        # 没有试用期状态筛选，使用正常分页
        query = db.session.query(
            ygong,
            Dept
        ).filter(*filters).outerjoin(Dept, ygong.dept_id == Dept.id).distinct().layui_paginate()

        # 处理数据并计算试用期状态
        data = []
        for user, dept in query.items:
            probation_status_value = user.get_probation_status()
            probation_end_date = user.calculate_probation_end_date()

            data.append({
                'id': user.id,
                'name': user.name,
                'employee_id': user.employee_id,
                'phone': user.phone,
                'dept_name': dept.dept_name if dept else None,
                'dept_id': dept.id if dept else None,
                'position': user.position,
                'hire_date': user.hire_date,
                'gender': user.gender,
                'birth_date': user.birth_date,
                'age': user.age,
                'id_card': user.id_card,
                'is_formal': user.is_formal,
                'probation_duration': user.probation_duration,
                'probation_status': probation_status_value,
                'probation_end_date': probation_end_date,
                'formal_note': user.formal_note,
                'base_salary': user.base_salary,
                'performance_salary': user.performance_salary,
                'supervisor_assessment': user.supervisor_assessment,
                'enable': user.enable,
                'create_at': user.create_at,
                'update_at': user.update_at
            })

        return table_api(data=data, count=query.total)






































# 启用用户
@bp.put('/enable')
@authorize("system:YG:edit", log=True)
def enable():
    try:
        req_json = request.get_json(force=True)

        if not req_json:
            return fail_api(msg="请求数据为空")
            
        _id = req_json.get('id')
        if _id is None:
            return fail_api(msg="请求数据中缺少id字段，请检查请求格式")
            
        if not isinstance(_id, (int, str)):
            return fail_api(msg="ID格式不正确，必须是数字或字符串")
            
        _id = int(_id)  # 确保ID是整数
        if _id <= 0:
            return fail_api(msg="ID必须为正整数")
            
        res = enable_status(model=ygong, id=_id)
        if not res:
            return fail_api(msg="启用失败，用户可能不存在")
        return success_api(msg="启用成功")
    except ValueError:
        return fail_api(msg="ID必须是数字")
    except Exception as e:
        return fail_api(msg=f"启用出错: {str(e)}")


# 禁用用户
@bp.put('/disable')
@authorize("system:YG:edit", log=True)
def dis_enable():
    try:
        req_json = request.get_json(force=True)
        _id = req_json.get('id')
        if not _id:
            return fail_api(msg="ID字段不存在")
        res = disable_status(model=ygong, id=_id)
        if not res:
            return fail_api(msg="出错啦")
        return success_api(msg="禁用成功")
    except Exception as e:
        return fail_api(msg=f"禁用出错: {str(e)}")


@bp.post('/import_excel')
@authorize("system:YG:import", log=True)
def import_excel():
    try:
        if 'file' not in request.files:
            return fail_api(msg="请选择文件")
        
        file = request.files['file']
        if not file.filename.endswith(('.xlsx', '.xls')):
            return fail_api(msg="仅支持Excel文件")
        
        # 读取Excel文件，指定部门、手机号和身份证号码字段为字符串类型
        df = pd.read_excel(file, dtype={'部门': str, '电话号码': str, '身份证号码': str})
        
        # 在导入前添加验证
        for _, row in df.iterrows():
            # 检查部门字段是否为空
            if pd.isna(row['部门']) or not row['部门'].strip():
                return fail_api(msg="部门字段不能为空")
            
            # 清理部门字段
            dept_name = str(row['部门']).strip()
            
            # 验证部门
            dept = Dept.query.filter_by(dept_name=dept_name).first()
            if not dept:
                return fail_api(msg=f"部门 {dept_name} 不存在")
            
            # 验证手机号
            phone = re.sub(r'\D', '', str(row['电话号码']))  # 移除非数字字符
            if not re.match(r'^1[3-9]\d{9}$', phone):
                return fail_api(msg="手机号格式不正确，必须为11位数字且以1开头")
            
            # 验证身份证号码
            id_card = re.sub(r'\s', '', str(row['身份证号码']))  # 移除空格
            if not re.match(r'^\d{17}[\dXx]$', id_card):
                return fail_api(msg="身份证号码格式不正确，必须为18位数字且最后一位可以是X")
        
        # 处理导入数据
        for _, row in df.iterrows():
            # 清理部门字段
            dept_name = str(row['部门']).strip()
            
            # 获取部门ID
            dept = Dept.query.filter_by(dept_name=dept_name).first()
            if not dept:
                return fail_api(msg=f"部门 {dept_name} 不存在")
            
            # 处理手机号
            phone = re.sub(r'\D', '', str(row['电话号码']))
            
            # 处理身份证号码
            id_card = re.sub(r'\s', '', str(row['身份证号码']))
            
            # 处理出生日期
            birth_date_str = str(row['出生日期']).strip()
            try:
                # 尝试解析日期格式
                if len(birth_date_str) == 8 and birth_date_str.isdigit():  # 格式为19850501
                    birth_date = datetime.datetime.strptime(birth_date_str, '%Y%m%d').date()
                else:  # 其他格式（如2023-01-01）
                    birth_date = pd.to_datetime(birth_date_str).date()
            except Exception as e:
                return fail_api(msg=f"出生日期格式不正确: {birth_date_str}")
            
            # 计算年龄
            today = datetime.date.today()
            age = today.year - birth_date.year
            if (today.month, today.day) < (birth_date.month, birth_date.day):  # 如果生日还没到，年龄减1
                age -= 1
            
            # 检查是否已存在
            existing_user = ygong.query.filter_by(employee_id=row['工号']).first()
            if existing_user:
                # 更新现有员工数据
                existing_user.name = row['姓名']
                existing_user.phone = phone
                existing_user.dept_id = dept.id
                existing_user.position = row['职位']
                existing_user.hire_date = pd.to_datetime(row['入职日期']).date()
                existing_user.gender = row['性别']
                existing_user.birth_date = birth_date
                existing_user.age = age  # 更新年龄
                existing_user.id_card = id_card
                existing_user.is_formal = row['是否正式']
                existing_user.base_salary = row['基本工资']
                existing_user.performance_salary = row['绩效工资']
                existing_user.supervisor_assessment = row['主管考核项']
            else:
                # 创建新员工
                user = ygong(
                    name=row['姓名'],
                    employee_id=row['工号'],
                    dept_id=dept.id,
                    position=row['职位'],
                    hire_date=pd.to_datetime(row['入职日期']).date(),
                    gender=row['性别'],
                    birth_date=birth_date,
                    age=age,  # 使用计算出的年龄
                    id_card=id_card,
                    phone=phone,
                    is_formal=row['是否正式'],
                    base_salary=row['基本工资'],
                    performance_salary=row['绩效工资'],
                    supervisor_assessment=row['主管考核项'],
                    enable=1
                )
                db.session.add(user)
        
        db.session.commit()
        return success_api(msg="导入成功")
    
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"导入失败: {str(e)}")


@bp.post('/remove_duplicates')
@authorize("system:YG:edit", log=True)
def remove_duplicates():
    """
    数据去重功能：删除重复的员工记录
    保留最新创建的记录，删除较早的重复记录
    """
    try:
        # 查找重复的员工记录
        duplicates_removed = 0

        # 1. 按员工工号去重
        employee_id_duplicates = db.session.query(ygong.employee_id).filter(
            ygong.employee_id.isnot(None),
            ygong.employee_id != ''
        ).group_by(ygong.employee_id).having(db.func.count(ygong.id) > 1).all()

        for (employee_id,) in employee_id_duplicates:
            # 获取该工号的所有记录，按创建时间排序
            records = ygong.query.filter_by(employee_id=employee_id).order_by(ygong.create_at.desc()).all()
            # 保留最新的记录，删除其他的
            for record in records[1:]:
                db.session.delete(record)
                duplicates_removed += 1

        # 2. 按手机号去重
        phone_duplicates = db.session.query(ygong.phone).filter(
            ygong.phone.isnot(None),
            ygong.phone != ''
        ).group_by(ygong.phone).having(db.func.count(ygong.id) > 1).all()

        for (phone,) in phone_duplicates:
            # 获取该手机号的所有记录，按创建时间排序
            records = ygong.query.filter_by(phone=phone).order_by(ygong.create_at.desc()).all()
            # 保留最新的记录，删除其他的
            for record in records[1:]:
                # 检查是否已经被删除（可能在工号去重时已删除）
                if db.session.query(ygong).filter_by(id=record.id).first():
                    db.session.delete(record)
                    duplicates_removed += 1

        # 3. 按身份证号去重
        id_card_duplicates = db.session.query(ygong.id_card).filter(
            ygong.id_card.isnot(None),
            ygong.id_card != ''
        ).group_by(ygong.id_card).having(db.func.count(ygong.id) > 1).all()

        for (id_card,) in id_card_duplicates:
            # 获取该身份证号的所有记录，按创建时间排序
            records = ygong.query.filter_by(id_card=id_card).order_by(ygong.create_at.desc()).all()
            # 保留最新的记录，删除其他的
            for record in records[1:]:
                # 检查是否已经被删除
                if db.session.query(ygong).filter_by(id=record.id).first():
                    db.session.delete(record)
                    duplicates_removed += 1

        db.session.commit()
        return success_api(msg=f"数据去重完成，共删除 {duplicates_removed} 条重复记录")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"数据去重失败: {str(e)}")


# 手动发送试用期提醒
@bp.post('/send_probation_reminder/<int:employee_id>')
@authorize("system:YG:send_reminder", log=True)
def send_probation_reminder(employee_id):
    """手动发送试用期提醒"""
    try:
        employee = ygong.query.get_or_404(employee_id)

        # 检查员工是否需要提醒
        if employee.is_formal == '正式员工':
            return fail_api(msg="该员工已是正式员工，无需发送试用期提醒")

        # 计算试用期状态
        end_date = employee.calculate_probation_end_date()
        if not end_date:
            return fail_api(msg="无法计算该员工的试用期结束日期")

        today = datetime.date.today()
        days_remaining = (end_date - today).days

        # 确定提醒类型
        if days_remaining <= 0:
            reminder_type = 'overdue'
        elif days_remaining <= 1:
            reminder_type = 'today'
        elif days_remaining <= 3:
            reminder_type = '3days'
        elif days_remaining <= 7:
            reminder_type = '7days'
        else:
            reminder_type = 'manual'  # 手动提醒

        # 发送提醒
        success = ProbationReminderService._send_probation_reminder(
            employee, days_remaining, reminder_type, end_date
        )

        if success:
            return success_api(msg=f"成功发送提醒邮件给员工 {employee.name}")
        else:
            return fail_api(msg="发送提醒邮件失败，请检查邮件配置")

    except Exception as e:
        return fail_api(msg=f"发送提醒失败: {str(e)}")


# 批量检查试用期提醒
@bp.post('/check_probation_reminders')
@authorize("system:YG:check_reminders", log=True)
def check_probation_reminders():
    """手动触发试用期提醒检查"""
    try:
        # 获取详细的检查结果
        result = ProbationReminderService.check_and_send_reminders_with_details()

        # 构建详细的返回消息
        msg = f"检查完成，共发送 {result['reminder_count']} 条提醒"
        if result['details']:
            msg += f"\n详细信息:\n" + "\n".join(result['details'])

        return success_api(msg=msg, data=result)
    except Exception as e:
        return fail_api(msg=f"检查失败: {str(e)}")


# 获取试用期提醒统计
@bp.get('/probation_statistics')
@authorize("system:YG:statistics", log=True)
def probation_statistics():
    """获取试用期员工统计信息"""
    try:
        today = datetime.date.today()

        # 统计各状态员工数量
        stats = {
            'urgent': 0,    # 已到期
            'warning': 0,   # 7天内到期
            'normal': 0,    # 正常
            'total': 0      # 总数
        }

        # 查询所有试用期和实习期员工
        probation_employees = ygong.query.filter(
            ygong.is_formal.in_(['试用期', '实习期', '试用', '实习']),
            ygong.enable == 1
        ).all()

        for employee in probation_employees:
            end_date = employee.calculate_probation_end_date()
            if end_date:
                days_remaining = (end_date - today).days
                if days_remaining <= 0:
                    stats['urgent'] += 1
                elif days_remaining <= 7:
                    stats['warning'] += 1
                else:
                    stats['normal'] += 1
                stats['total'] += 1

        return success_api(data=stats)

    except Exception as e:
        return fail_api(msg=f"获取统计信息失败: {str(e)}")


# 员工转正
@bp.post('/convert_to_formal/<int:employee_id>')
@authorize("system:YG:edit", log=True)
def convert_to_formal(employee_id):
    """将试用期员工转为正式员工"""
    try:
        employee = ygong.query.get_or_404(employee_id)

        # 检查员工当前状态
        if employee.is_formal == '正式':
            return fail_api(msg="该员工已是正式员工")

        if employee.is_formal not in ['试用', '实习']:
            return fail_api(msg="只有试用期或实习期员工才能转正")

        # 获取转正备注
        req_json = request.get_json(force=True) if request.is_json else {}
        formal_note = str_escape(req_json.get('formal_note', ''))

        # 更新员工状态
        # 转正日期使用试用期结束日期，如果没有则使用当天
        probation_end_date = employee.calculate_probation_end_date()
        formal_date = probation_end_date if probation_end_date else datetime.date.today()

        employee.is_formal = '正式'
        employee.formal_date = formal_date
        employee.formal_note = formal_note

        # 清空试用期相关字段
        employee.probation_duration = None
        employee.probation_end_date = None
        employee.last_reminder_date = None
        employee.reminder_status = 'normal'

        db.session.commit()

        return success_api(msg=f"员工 {employee.name} 转正成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"转正失败: {str(e)}")


# 批量员工转正
@bp.post('/batch_convert_to_formal')
@authorize("system:YG:edit", log=True)
def batch_convert_to_formal():
    """批量将试用期员工转为正式员工"""
    try:
        req_json = request.get_json(force=True)
        employee_ids = req_json.get('employee_ids', [])
        formal_note = str_escape(req_json.get('formal_note', ''))

        if not employee_ids:
            return fail_api(msg="请选择要转正的员工")

        success_count = 0
        failed_employees = []

        for employee_id in employee_ids:
            try:
                employee = ygong.query.get(employee_id)
                if not employee:
                    failed_employees.append(f"员工ID {employee_id} 不存在")
                    continue

                # 检查员工状态
                if employee.is_formal == '正式':
                    failed_employees.append(f"{employee.name} 已是正式员工")
                    continue

                if employee.is_formal not in ['试用', '实习']:
                    failed_employees.append(f"{employee.name} 不是试用期或实习期员工")
                    continue

                # 转正操作
                # 转正日期使用试用期结束日期，如果没有则使用当天
                probation_end_date = employee.calculate_probation_end_date()
                formal_date = probation_end_date if probation_end_date else datetime.date.today()

                employee.is_formal = '正式'
                employee.formal_date = formal_date
                employee.formal_note = formal_note

                # 清空试用期相关字段
                employee.probation_duration = None
                employee.probation_end_date = None
                employee.last_reminder_date = None
                employee.reminder_status = 'normal'

                success_count += 1

            except Exception as e:
                failed_employees.append(f"{employee.name if 'employee' in locals() else f'ID {employee_id}'}: {str(e)}")

        db.session.commit()

        # 构建返回消息
        msg = f"批量转正完成：成功 {success_count} 人"
        if failed_employees:
            msg += f"，失败 {len(failed_employees)} 人"

        return success_api(msg=msg, data={
            'success_count': success_count,
            'failed_count': len(failed_employees),
            'failed_details': failed_employees
        })

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"批量转正失败: {str(e)}")


@bp.get('/departments')
def get_departments():
    """获取部门列表（用于下拉选择）"""
    try:
        from sqlalchemy import text

        # 查询所有启用的部门，按部门名称拼音排序
        departments = Dept.query.filter_by(status=1).order_by(text("CONVERT(dept_name USING gbk)")).all()

        # 转换为简单的列表格式
        dept_list = []
        for dept in departments:
            dept_list.append({
                'id': dept.id,
                'dept_name': dept.dept_name
            })

        return success_api(data=dept_list, msg="获取部门列表成功")

    except Exception as e:
        return fail_api(msg=f"获取部门列表失败: {str(e)}")


# 延时转正
@bp.post('/extend_probation/<int:employee_id>')
@authorize("system:YG:edit", log=True)
def extend_probation(employee_id):
    """延长员工试用期"""
    try:
        employee = ygong.query.get_or_404(employee_id)

        # 检查员工当前状态
        if employee.is_formal == '正式':
            return fail_api(msg="该员工已是正式员工，无法延时转正")

        if employee.is_formal not in ['试用', '实习']:
            return fail_api(msg="只有试用期或实习期员工才能延时转正")

        # 获取延时参数
        req_json = request.get_json(force=True) if request.is_json else {}
        extend_days = req_json.get('extend_days')
        extend_reason = str_escape(req_json.get('extend_reason', ''))

        if not extend_days or extend_days <= 0:
            return fail_api(msg="延时天数必须大于0")

        if not extend_reason.strip():
            return fail_api(msg="请输入延时原因")

        # 计算新的试用期时长
        current_duration = employee.probation_duration or (90 if employee.is_formal == '试用' else 180)
        new_duration = current_duration + extend_days

        # 更新员工信息
        employee.probation_duration = new_duration

        # 重新计算试用期结束日期
        if employee.hire_date:
            employee.probation_end_date = employee.hire_date + datetime.timedelta(days=new_duration)

        # 记录延时信息（可以添加到备注中）
        extend_note = f"延时转正 {extend_days} 天，原因：{extend_reason}"
        if employee.formal_note:
            employee.formal_note += f"\n{extend_note}"
        else:
            employee.formal_note = extend_note

        # 重置提醒状态
        employee.reminder_status = 'normal'
        employee.last_reminder_date = None

        db.session.commit()

        return success_api(msg=f"员工 {employee.name} 试用期延长 {extend_days} 天成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"延时转正失败: {str(e)}")


# 离职删除
@bp.delete('/delete_employee/<int:employee_id>')
@authorize("system:YG:edit", log=True)
def delete_employee(employee_id):
    """删除离职员工"""
    try:
        employee = ygong.query.get_or_404(employee_id)

        # 检查员工当前状态（只允许删除试用期和实习期员工）
        if employee.is_formal == '正式':
            return fail_api(msg="正式员工不能直接删除，请联系管理员处理")

        if employee.is_formal not in ['试用', '实习']:
            return fail_api(msg="只能删除试用期或实习期员工")

        # 获取删除原因
        req_json = request.get_json(force=True) if request.is_json else {}
        delete_reason = str_escape(req_json.get('delete_reason', ''))

        employee_name = employee.name
        employee_id_str = employee.employee_id

        # 删除员工记录
        db.session.delete(employee)
        db.session.commit()

        # 记录删除日志
        log_msg = f"删除员工：{employee_name}（工号：{employee_id_str}）"
        if delete_reason:
            log_msg += f"，离职原因：{delete_reason}"

        return success_api(msg=f"员工 {employee_name} 删除成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除员工失败: {str(e)}")


# 社保导入页面
@bp.get('/social_insurance')
@login_required
@authorize("system:YG:social_insurance", log=True)
def social_insurance():
    """社保导入页面"""
    depts = Dept.query.filter_by(status=1).all()
    return render_template('system/YG/social_insurance.html', depts=depts)


# 社保数据查询
@bp.get('/social_insurance/data')
@login_required
@authorize("system:YG:social_insurance:view", log=True)
def social_insurance_data():
    """获取社保数据"""
    # 获取筛选参数
    employee_name = request.args.get('employee_name', '')
    filter_type = request.args.get('filter_type', '')
    dept_id = request.args.get('dept_id', '')
    month = request.args.get('month', '')

    # 构建查询条件
    filters = []
    if employee_name:
        filters.append(SocialInsurance.employee_name.contains(employee_name))
    if dept_id:
        filters.append(SocialInsurance.dept_id == dept_id)
    if month:
        filters.append(SocialInsurance.month == month)

    # 根据筛选类型添加条件
    if filter_type == 'social_insurance':
        # 仅社保：社保扣款大于0，公积金扣款为0或null
        filters.append(SocialInsurance.social_insurance_deduction > 0)
        filters.append(db.or_(
            SocialInsurance.housing_fund_deduction == 0,
            SocialInsurance.housing_fund_deduction.is_(None)
        ))
    elif filter_type == 'housing_fund':
        # 仅公积金：公积金扣款大于0，社保扣款为0或null
        filters.append(SocialInsurance.housing_fund_deduction > 0)
        filters.append(db.or_(
            SocialInsurance.social_insurance_deduction == 0,
            SocialInsurance.social_insurance_deduction.is_(None)
        ))
    elif filter_type == 'both':
        # 社保+公积金：两者都大于0
        filters.append(SocialInsurance.social_insurance_deduction > 0)
        filters.append(SocialInsurance.housing_fund_deduction > 0)

    # 查询数据
    query = SocialInsurance.query.filter(*filters).order_by(SocialInsurance.create_at.desc()).layui_paginate()

    # 构建返回数据
    data = []
    for item in query.items:
        data.append({
            'id': item.id,
            'employee_name': item.employee_name,
            'employee_id': item.employee_id,
            'dept_name': item.dept_name,
            'social_insurance_deduction': item.social_insurance_deduction,
            'housing_fund_deduction': item.housing_fund_deduction,
            'month': item.month,
            'remark': item.remark,
            'create_at': item.create_at.strftime('%Y-%m-%d %H:%M:%S') if item.create_at else '',
            'update_at': item.update_at.strftime('%Y-%m-%d %H:%M:%S') if item.update_at else ''
        })

    return table_api(data=data, count=query.total)


# 单个社保数据保存
@bp.post('/social_insurance/save')
@login_required
@authorize("system:YG:social_insurance:add", log=True)
def social_insurance_save():
    """保存单个社保数据"""
    try:
        req_json = request.get_json(force=True)

        employee_name = str_escape(req_json.get('employee_name'))
        employee_id = str_escape(req_json.get('employee_id'))
        dept_id = str_escape(req_json.get('dept_id'))
        social_insurance_deduction = float(req_json.get('social_insurance_deduction', 0))
        housing_fund_deduction = float(req_json.get('housing_fund_deduction', 0))
        month = str_escape(req_json.get('month'))
        remark = str_escape(req_json.get('remark', ''))

        if not employee_name or not employee_id or not dept_id or not month:
            return fail_api(msg="员工姓名、工号、部门和月份不能为空")

        # 获取部门名称
        dept = Dept.query.get(dept_id)
        if not dept:
            return fail_api(msg="部门不存在")

        # 检查是否已存在相同记录
        existing = SocialInsurance.query.filter_by(
            employee_id=employee_id,
            month=month
        ).first()

        if existing:
            return fail_api(msg="该员工在此月份的社保数据已存在")

        # 创建新记录
        social_insurance = SocialInsurance(
            employee_name=employee_name,
            employee_id=employee_id,
            dept_id=dept_id,
            dept_name=dept.dept_name,
            social_insurance_deduction=social_insurance_deduction,
            housing_fund_deduction=housing_fund_deduction,
            month=month,
            remark=remark
        )

        db.session.add(social_insurance)
        db.session.commit()

        return success_api(msg="社保数据保存成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"保存失败: {str(e)}")


# 社保数据批量导入
@bp.post('/social_insurance/import')
@login_required
@authorize("system:YG:social_insurance:import", log=True)
def social_insurance_import():
    """批量导入社保数据"""
    try:
        if 'file' not in request.files:
            return fail_api(msg="请选择文件")

        file = request.files['file']
        if not file.filename.endswith(('.xlsx', '.xls')):
            return fail_api(msg="仅支持Excel文件")

        # 读取Excel文件
        df = pd.read_excel(file, dtype={'工号': str})

        if df.empty:
            return fail_api(msg="Excel文件为空")

        # 验证必要列是否存在
        required_columns = ['员工姓名', '工号', '部门', '社保单位扣款', '公积金单位扣款', '月份']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return fail_api(msg=f"Excel文件缺少必要列: {', '.join(missing_columns)}")

        success_count = 0
        update_count = 0
        error_messages = []

        for index, row in df.iterrows():
            try:
                # 验证数据
                employee_name = str(row['员工姓名']).strip()
                employee_id = str(row['工号']).strip()
                dept_name = str(row['部门']).strip()
                month = str(row['月份']).strip()

                if not employee_name or not employee_id or not dept_name or not month:
                    error_messages.append(f"第{index+2}行: 员工姓名、工号、部门和月份不能为空")
                    continue

                # 查找部门
                dept = Dept.query.filter_by(dept_name=dept_name).first()
                if not dept:
                    error_messages.append(f"第{index+2}行: 部门 '{dept_name}' 不存在")
                    continue

                # 处理金额字段
                social_insurance_deduction = float(row['社保单位扣款'] or 0)
                housing_fund_deduction = float(row['公积金单位扣款'] or 0)
                remark = str(row.get('备注', '')).strip()

                # 检查是否已存在
                existing = SocialInsurance.query.filter_by(
                    employee_id=employee_id,
                    month=month
                ).first()

                if existing:
                    # 更新现有记录
                    existing.employee_name = employee_name
                    existing.dept_id = dept.id
                    existing.dept_name = dept.dept_name
                    existing.social_insurance_deduction = social_insurance_deduction
                    existing.housing_fund_deduction = housing_fund_deduction
                    existing.remark = remark
                    existing.update_at = datetime.datetime.now()
                    update_count += 1
                else:
                    # 创建新记录
                    social_insurance = SocialInsurance(
                        employee_name=employee_name,
                        employee_id=employee_id,
                        dept_id=dept.id,
                        dept_name=dept.dept_name,
                        social_insurance_deduction=social_insurance_deduction,
                        housing_fund_deduction=housing_fund_deduction,
                        month=month,
                        remark=remark
                    )
                    db.session.add(social_insurance)
                    success_count += 1

            except Exception as e:
                error_messages.append(f"第{index+2}行: {str(e)}")

        db.session.commit()

        # 构建返回消息
        msg = f"导入完成: 新增 {success_count} 条，更新 {update_count} 条"
        if error_messages:
            msg += f"，错误 {len(error_messages)} 条"

        return success_api(msg=msg, data={
            'success_count': success_count,
            'update_count': update_count,
            'error_count': len(error_messages),
            'error_messages': error_messages[:10]  # 只返回前10条错误信息
        })

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"导入失败: {str(e)}")


# 删除社保数据
@bp.delete('/social_insurance/remove/<int:id>')
@login_required
@authorize("system:YG:social_insurance:delete", log=True)
def social_insurance_remove(id):
    """删除社保数据"""
    try:
        social_insurance = SocialInsurance.query.get_or_404(id)
        db.session.delete(social_insurance)
        db.session.commit()
        return success_api(msg="删除成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败: {str(e)}")


# 社保导入模板下载
@bp.get('/social_insurance/template/download')
@login_required
@authorize("system:YG:social_insurance:view", log=True)
def social_insurance_template_download():
    """下载社保导入Excel模板"""
    try:
        # 创建模板数据
        template_data = [
            {
                '员工姓名': '张三',
                '工号': 'EMP001',
                '部门': '技术部',
                '社保单位扣款': 500.00,
                '公积金单位扣款': 300.00,
                '月份': '2024-01',
                '备注': '示例数据，请删除此行后填入实际数据'
            },
            {
                '员工姓名': '李四',
                '工号': 'EMP002',
                '部门': '财务部',
                '社保单位扣款': 450.00,
                '公积金单位扣款': 280.00,
                '月份': '2024-01',
                '备注': '示例数据，请删除此行后填入实际数据'
            }
        ]

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 创建Excel文件到内存
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='社保数据', index=False)

            # 获取工作表对象以设置列宽
            worksheet = writer.sheets['社保数据']

            # 设置列宽
            column_widths = {
                'A': 12,  # 员工姓名
                'B': 12,  # 工号
                'C': 15,  # 部门
                'D': 18,  # 社保单位扣款
                'E': 18,  # 公积金单位扣款
                'F': 12,  # 月份
                'G': 30   # 备注
            }

            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width

        output.seek(0)

        # 生成文件名
        current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'社保导入模板_{current_time}.xlsx'

        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return fail_api(msg=f"模板下载失败: {str(e)}")


# 社保数据统计
@bp.get('/social_insurance/statistics')
@login_required
@authorize("system:YG:social_insurance:view", log=True)
def social_insurance_statistics():
    """获取社保数据统计信息"""
    try:
        # 获取筛选参数
        employee_name = request.args.get('employee_name', '')
        filter_type = request.args.get('filter_type', '')
        dept_id = request.args.get('dept_id', '')
        month = request.args.get('month', '')

        # 构建查询条件
        filters = []
        if employee_name:
            filters.append(SocialInsurance.employee_name.contains(employee_name))
        if dept_id:
            filters.append(SocialInsurance.dept_id == dept_id)
        if month:
            filters.append(SocialInsurance.month == month)

        # 根据筛选类型添加条件
        if filter_type == 'social_insurance':
            # 仅社保：社保扣款大于0，公积金扣款为0或null
            filters.append(SocialInsurance.social_insurance_deduction > 0)
            filters.append(db.or_(
                SocialInsurance.housing_fund_deduction == 0,
                SocialInsurance.housing_fund_deduction.is_(None)
            ))
        elif filter_type == 'housing_fund':
            # 仅公积金：公积金扣款大于0，社保扣款为0或null
            filters.append(SocialInsurance.housing_fund_deduction > 0)
            filters.append(db.or_(
                SocialInsurance.social_insurance_deduction == 0,
                SocialInsurance.social_insurance_deduction.is_(None)
            ))
        elif filter_type == 'both':
            # 社保+公积金：两者都大于0
            filters.append(SocialInsurance.social_insurance_deduction > 0)
            filters.append(SocialInsurance.housing_fund_deduction > 0)

        # 如果没有指定月份筛选，使用智能月份选择
        if not month:
            current_date = datetime.datetime.now()
            current_month = current_date.strftime('%Y-%m')

            # 先尝试查询当月数据
            current_month_test = SocialInsurance.query.filter_by(month=current_month).first()

            # 如果当月没有数据，查询最新月份的数据
            if not current_month_test:
                latest_month = db.session.query(SocialInsurance.month).order_by(SocialInsurance.month.desc()).first()
                if latest_month:
                    current_month = latest_month[0]

            # 如果没有手动指定月份，使用智能选择的月份
            display_month = current_month
        else:
            display_month = month

        # 查询筛选后的数据
        query = SocialInsurance.query.filter(*filters)
        filtered_data = query.all()

        # 统计筛选后的数据
        total_employees = len(set(item.employee_id for item in filtered_data))
        total_social_insurance = sum(item.social_insurance_deduction or 0 for item in filtered_data)
        total_housing_fund = sum(item.housing_fund_deduction or 0 for item in filtered_data)
        current_month_employees = total_employees

        # 统计最近更新时间
        latest_record = SocialInsurance.query.order_by(SocialInsurance.update_at.desc()).first()
        last_update = latest_record.update_at.strftime('%Y-%m-%d %H:%M') if latest_record else '暂无数据'

        # 统计各部门数据（基于筛选条件）
        dept_query = db.session.query(
            SocialInsurance.dept_name,
            db.func.count(SocialInsurance.id).label('count'),
            db.func.sum(SocialInsurance.social_insurance_deduction).label('total_social'),
            db.func.sum(SocialInsurance.housing_fund_deduction).label('total_housing')
        ).filter(*filters).group_by(SocialInsurance.dept_name)
        dept_stats = dept_query.all()

        return success_api(data={
            'total_employees': total_employees,
            'current_month': display_month,
            'current_month_employees': current_month_employees,
            'total_social_insurance': round(total_social_insurance, 2),
            'total_housing_fund': round(total_housing_fund, 2),
            'last_update': last_update,
            'is_filtered': bool(employee_name or filter_type or dept_id or month),
            'filter_info': {
                'employee_name': employee_name,
                'filter_type': filter_type,
                'dept_id': dept_id,
                'month': month
            },
            'dept_stats': [
                {
                    'dept_name': stat.dept_name,
                    'count': stat.count,
                    'total_social': round(stat.total_social or 0, 2),
                    'total_housing': round(stat.total_housing or 0, 2)
                } for stat in dept_stats
            ]
        })

    except Exception as e:
        return fail_api(msg=f"获取统计数据失败: {str(e)}")

