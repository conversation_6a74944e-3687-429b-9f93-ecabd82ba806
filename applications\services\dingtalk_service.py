import requests
import time
import json
from typing import List, Dict, Optional, Tuple
from flask import current_app
from applications.services.dingtalk_log_converter import DingTalkLogConverter


class DingTalkService:
    """钉钉API服务类"""

    def __init__(self):
        self.app_key = current_app.config.get('DINGTALK_APP_KEY')
        self.app_secret = current_app.config.get('DINGTALK_APP_SECRET')
        self.access_token = None
        self.token_expire_time = 0
        self.log_converter = DingTalkLogConverter()

        if not self.app_key or not self.app_secret:
            raise ValueError("钉钉API配置缺失，请检查DINGTALK_APP_KEY和DINGTALK_APP_SECRET配置")

    def _get_access_token(self) -> str:
        """获取/刷新access_token，带缓存机制"""
        if time.time() < self.token_expire_time - 60:  # 提前60秒刷新
            return self.access_token

        url = f"https://oapi.dingtalk.com/gettoken?appkey={self.app_key}&appsecret={self.app_secret}"
        try:
            response = requests.get(url, timeout=5)
            response.raise_for_status()
            data = response.json()

            if data.get('errcode') == 0:
                self.access_token = data['access_token']
                self.token_expire_time = time.time() + 7200  # 有效期2小时
                return self.access_token
            else:
                raise Exception(f"获取token失败: {data.get('errmsg')}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")

    def _call_api(self, endpoint: str, params: Dict, max_retries: int = 3) -> Dict:
        """调用钉钉API的通用方法，带重试机制"""
        token = self._get_access_token()
        url = f"https://oapi.dingtalk.com{endpoint}?access_token={token}"

        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=params, timeout=30)  # 增加超时时间到30秒
                response.raise_for_status()
                result = response.json()

                if result.get('errcode') != 0:
                    error_msg = result.get('errmsg', '未知错误')
                    # 某些错误可以重试
                    if attempt < max_retries - 1 and self._should_retry_error(result.get('errcode')):
                        time.sleep(1 * (attempt + 1))  # 递增延迟
                        continue
                    raise Exception(f"API错误: {error_msg}")

                return result

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    time.sleep(1 * (attempt + 1))  # 递增延迟
                    continue
                raise Exception(f"API请求失败: {str(e)}")

        # 理论上不会到达这里
        raise Exception("API调用失败：超过最大重试次数")

    def _should_retry_error(self, errcode: int) -> bool:
        """判断是否应该重试的错误码"""
        # 可重试的错误码（网络相关、频率限制等）
        retryable_errors = [
            88,    # 接口调用频率限制
            90018, # 接口调用频率限制
            40014, # 不合法的access_token
        ]
        return errcode in retryable_errors

    def _calculate_time_segments(self, start_time: int, end_time: int) -> List[Tuple[int, int]]:
        """
        计算时间分段，用于大时间跨度的智能分段查询

        :param start_time: 开始时间戳（毫秒）
        :param end_time: 结束时间戳（毫秒）
        :return: 时间段列表，每个元素为(start, end)元组
        """
        segments = []
        time_span_days = (end_time - start_time) / (24 * 60 * 60 * 1000)

        # 根据时间跨度选择分段策略
        if time_span_days <= 7:
            # 7天以内，不分段
            segments.append((start_time, end_time))
        elif time_span_days <= 30:
            # 7-30天，按7天分段
            segment_duration = 7 * 24 * 60 * 60 * 1000
        else:
            # 30天以上，按3天分段
            segment_duration = 3 * 24 * 60 * 60 * 1000

        # 执行分段
        if time_span_days > 7:
            current_start = start_time
            while current_start < end_time:
                current_end = min(current_start + segment_duration, end_time)
                segments.append((current_start, current_end))
                current_start = current_end

        return segments

    def get_logs(
        self,
        start_time: int,
        end_time: int,
        template_name: Optional[str] = None,
        userid: Optional[str] = None
    ) -> List[Dict]:
        """获取指定时间范围内的日志

        :param start_time: 开始时间戳（毫秒）
        :param end_time: 结束时间戳（毫秒）
        :param template_name: 日志模板名称（可选）
        :param userid: 用户ID（可选）
        :return: 日志数据列表
        """
        # 使用智能分段查询
        time_segments = self._calculate_time_segments(start_time, end_time)
        all_logs = []

        for segment_index, (segment_start, segment_end) in enumerate(time_segments):

            # 获取单个时间段的日志
            segment_logs = self._get_logs_for_segment(
                segment_start, segment_end, template_name, userid
            )
            all_logs.extend(segment_logs)

            # 分段之间添加延迟，避免API频率限制
            if segment_index < len(time_segments) - 1:
                time.sleep(0.5)

            # 数据量保护：单次最多返回10000条记录
            if len(all_logs) >= 10000:
                break
        return all_logs

    def _get_logs_for_segment(
        self,
        start_time: int,
        end_time: int,
        template_name: Optional[str] = None,
        userid: Optional[str] = None
    ) -> List[Dict]:
        """获取单个时间段内的日志"""
        segment_logs = []
        cursor = 0
        loop_count = 0
        max_loops = 1000  # 最大循环次数保护
        start_query_time = time.time()
        max_query_time = 300  # 5分钟总体超时

        while True:
            # 循环次数保护
            loop_count += 1
            if loop_count > max_loops:
                break

            # 总体超时保护
            if time.time() - start_query_time > max_query_time:
                break

            params = {
                "start_time": start_time,
                "end_time": end_time,
                "cursor": cursor,
                "size": 20  # 钉钉API单次最大返回数量
            }

            if template_name:
                params["template_name"] = template_name
            if userid:
                params["userid"] = userid

            try:
                result = self._call_api("/topapi/report/list", params)
                data = result.get("result", {})

                if not data.get("data_list"):
                    break

                segment_logs.extend(data["data_list"])
                next_cursor = data.get("next_cursor", 0)

                if next_cursor == 0:
                    break

                cursor = next_cursor

                # API调用间隔
                time.sleep(0.2)

            except Exception:
                # 发生错误时，返回已获取的部分数据
                break

        return segment_logs

    def format_logs_for_response(self, logs: List[Dict]) -> Dict:
        """格式化日志数据为标准响应格式"""
        formatted_logs = []

        for log in logs:
            log_data = {
                "report_id": log.get('report_id'),
                "creator_name": log.get('creator_name'),
                "create_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                           time.localtime(log.get('create_time', 0)/1000)),
                "create_timestamp": log.get('create_time'),
                "contents": []
            }

            # 处理日志内容字段
            for content in log.get('contents', []):
                log_data['contents'].append({
                    "key": content.get('key'),
                    "value": content.get('value')
                })

            formatted_logs.append(log_data)

        return {
            "status": "success",
            "total_count": len(formatted_logs),
            "logs": formatted_logs
        }

    def get_logs_by_days(self, days: int = 1) -> Dict:
        """获取最近指定天数的日志

        :param days: 天数，默认7天
        :return: 格式化的日志数据
        """
        end_time = int(time.time() * 1000)
        start_time = end_time - days * 24 * 60 * 60 * 1000

        try:
            logs = self.get_logs(start_time, end_time)
            return self.format_logs_for_response(logs)
        except Exception as e:
            return {
                "status": "error",
                "error_message": str(e),
                "total_count": 0,
                "logs": []
            }

    def get_user_templates(self, userid: Optional[str] = None) -> List[Dict]:
        """获取用户可见的日志模板列表

        :param userid: 用户ID（可选，不传则获取所有模板）
        :return: 模板列表
        """
        params = {}
        if userid:
            params["userid"] = userid

        try:
            result = self._call_api("/topapi/report/template/listbyuserid", params)
            templates = result.get("result", {}).get("template_list", [])

            # 格式化模板数据，使用正确的字段映射
            formatted_templates = []
            for template in templates:
                template_name = template.get("name")  # 实际字段是 'name'
                template_code = template.get("report_code")  # 实际字段是 'report_code'

                # 跳过无效的模板数据
                if not template_name or not template_code:
                    continue

                formatted_templates.append({
                    "template_id": template_code,  # 使用 report_code 作为 ID
                    "template_name": template_name,  # 使用 name 作为名称
                    "template_icon": template.get("icon_url", ""),  # 使用 icon_url
                    "template_url": template.get("url", ""),  # 添加模板URL
                    "report_code": template_code  # 保留原始 report_code
                })

            return formatted_templates

        except Exception as e:
            raise Exception(f"获取模板列表失败: {str(e)}")

    def format_templates_for_response(self, templates: List[Dict]) -> Dict:
        """格式化模板数据为标准响应格式"""
        return {
            "status": "success",
            "total_count": len(templates),
            "templates": templates
        }

    def save_logs_to_database(self, logs: List[Dict]) -> Dict:
        """
        将钉钉日志保存到本地数据库

        Args:
            logs (list): 钉钉日志数据列表

        Returns:
            dict: 保存结果
        """
        try:
            result = self.log_converter.batch_convert_and_save(logs)

            return {
                "status": "success",
                "message": f"批量保存完成：成功 {result['success']} 条，失败 {result['failed']} 条",
                "total": result['total'],
                "success_count": result['success'],
                "failed_count": result['failed'],
                "details": result['results']
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"保存日志失败: {str(e)}",
                "total": 0,
                "success_count": 0,
                "failed_count": 0,
                "details": []
            }

    def get_and_save_logs(
        self,
        start_time: int,
        end_time: int,
        template_name: Optional[str] = None,
        userid: Optional[str] = None
    ) -> Dict:
        """
        获取钉钉日志并保存到本地数据库

        Args:
            start_time (int): 开始时间戳（毫秒）
            end_time (int): 结束时间戳（毫秒）
            template_name (str): 日志模板名称（可选）
            userid (str): 用户ID（可选）

        Returns:
            dict: 获取和保存结果
        """
        try:
            # 获取钉钉日志
            logs = self.get_logs(start_time, end_time, template_name, userid)

            if not logs:
                return {
                    "status": "success",
                    "message": "未找到符合条件的日志",
                    "logs_count": 0,
                    "save_result": None
                }

            # 保存到数据库
            save_result = self.save_logs_to_database(logs)

            return {
                "status": "success",
                "message": f"获取到 {len(logs)} 条日志，{save_result['message']}",
                "logs_count": len(logs),
                "save_result": save_result
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"获取并保存日志失败: {str(e)}",
                "logs_count": 0,
                "save_result": None
            }

    def get_approval_instances(
        self,
        process_code: str,
        start_time: int,
        end_time: int,
        status: Optional[str] = None,
        userid: Optional[str] = None
    ) -> List[Dict]:
        """获取钉钉审批实例列表

        Args:
            process_code (str): 审批模板代码
            start_time (int): 开始时间戳（毫秒）
            end_time (int): 结束时间戳（毫秒）
            status (str): 审批状态（可选）NEW-新建,RUNNING-审批中,TERMINATED-终止,COMPLETED-完成,CANCELED-取消
            userid (str): 用户ID（可选）

        Returns:
            List[Dict]: 审批实例列表
        """
        # 使用智能分段查询
        time_segments = self._calculate_time_segments(start_time, end_time)
        all_instances = []

        for segment_index, (segment_start, segment_end) in enumerate(time_segments):

            # 获取单个时间段的审批实例
            segment_instances = self._get_approval_instances_for_segment(
                process_code, segment_start, segment_end, status, userid
            )
            all_instances.extend(segment_instances)

            # 分段之间添加延迟，避免API频率限制
            if segment_index < len(time_segments) - 1:
                time.sleep(0.5)

            # 数据量保护：单次最多返回10000条记录
            if len(all_instances) >= 10000:
                break

        # 由于钉钉API的状态过滤不生效，在后端进行状态过滤
        if status:
            filtered_instances = []
            for instance in all_instances:
                if isinstance(instance, dict) and 'process_instance' in instance:
                    instance_status = instance['process_instance'].get('status')
                    if instance_status == status:
                        filtered_instances.append(instance)
            return filtered_instances

        return all_instances

    def _get_approval_instances_for_segment(
        self,
        process_code: str,
        start_time: int,
        end_time: int,
        status: Optional[str] = None,
        userid: Optional[str] = None
    ) -> List[Dict]:
        """获取单个时间段内的审批实例"""
        segment_instances = []
        cursor = 0
        loop_count = 0
        max_loops = 1000  # 最大循环次数保护
        start_query_time = time.time()
        max_query_time = 300  # 5分钟总体超时

        while True:
            # 循环次数保护
            loop_count += 1
            if loop_count > max_loops:
                break

            # 总体超时保护
            if time.time() - start_query_time > max_query_time:
                break

            params = {
                "process_code": process_code,
                "start_time": start_time,
                "end_time": end_time,
                "cursor": cursor,
                "size": 20  # 钉钉API单次最大返回数量
            }

            # 钉钉API不支持状态过滤，移除status参数
            # 状态过滤将在获取数据后进行
            if status:
                print(f"[DEBUG] 目标状态过滤: {status} (将在后端进行)")
            else:
                print(f"[DEBUG] 无状态过滤")
            if userid:
                params["userid"] = userid

            try:
                # 调试日志：打印发送给钉钉API的参数
                print(f"[DEBUG] 发送给钉钉API的参数: {params}")

                # 钉钉API不支持状态过滤，使用标准API获取所有数据，然后在后端过滤
                print(f"[DEBUG] 使用标准API获取数据，状态过滤将在后端进行: {status or '无过滤'}")
                result = self._call_api("/topapi/processinstance/listids", params)

                # 检查返回结果结构
                if result.get("errcode") != 0:
                    break

                data = result.get("result", {})

                # listids API返回的是list字段，包含审批实例ID列表
                instance_ids = data.get("list", [])
                if not instance_ids:
                    break

                # 获取每个审批实例的详细信息
                for instance_id in instance_ids:
                    try:
                        # 获取审批实例详情
                        detail = self.get_approval_detail(instance_id)

                        if detail and 'process_instance' in detail:
                            # 将process_instance_id添加到详情数据中，并使用正确的数据结构
                            instance_data = {
                                'process_instance_id': instance_id,
                                'process_instance': detail['process_instance']
                            }
                            segment_instances.append(instance_data)
                        else:
                            # 如果获取详情失败，至少保留ID
                            segment_instances.append({"process_instance_id": instance_id})
                    except Exception:
                        # 保留ID，即使详情获取失败
                        segment_instances.append({"process_instance_id": instance_id})

                next_cursor = data.get("next_cursor", 0)

                if next_cursor == 0:
                    break

                cursor = next_cursor

                # API调用间隔
                time.sleep(0.2)

            except Exception:
                # 发生错误时，返回已获取的部分数据
                break

        return segment_instances

    def get_approval_detail(self, process_instance_id: str) -> Dict:
        """获取审批实例详情

        Args:
            process_instance_id (str): 审批实例ID

        Returns:
            Dict: 审批实例详情
        """
        params = {
            "process_instance_id": process_instance_id
        }

        try:
            result = self._call_api("/topapi/processinstance/get", params)

            if result.get("errcode") != 0:
                return {}

            # 钉钉审批详情API返回的数据在process_instance字段中
            process_instance = result.get("process_instance", {})
            if process_instance:
                return {"process_instance": process_instance}
            else:
                return {}
        except Exception as e:
            raise Exception(f"获取审批详情失败: {str(e)}")

    def get_department_info(self, dept_id: str) -> Dict:
        """获取部门信息

        Args:
            dept_id (str): 部门ID

        Returns:
            Dict: 部门信息
        """
        params = {
            "dept_id": dept_id
        }

        try:
            result = self._call_api("/topapi/v2/department/get", params)

            if result.get("errcode") != 0:
                return {}

            dept_info = result.get("result", {})
            return dept_info

        except Exception as e:
            print(f"获取部门信息失败 (dept_id: {dept_id}): {str(e)}")
            return {}

    def get_user_info(self, userid: str) -> Dict:
        """获取用户信息

        Args:
            userid (str): 用户ID

        Returns:
            Dict: 用户信息
        """
        params = {
            "userid": userid
        }

        try:
            result = self._call_api("/topapi/v2/user/get", params)

            if result.get("errcode") != 0:
                return {}

            user_info = result.get("result", {})
            return user_info

        except Exception as e:
            print(f"获取用户信息失败 (userid: {userid}): {str(e)}")
            return {}

    def get_approval_templates(self, userid: Optional[str] = None) -> List[Dict]:
        """获取用户可见的审批模板列表

        Args:
            userid (str): 用户ID（可选，不传则获取所有模板）

        Returns:
            List[Dict]: 审批模板列表
        """
        # 使用缓存键，包含userid信息
        cache_key = f"approval_templates_{userid or 'all'}"

        # 尝试从缓存获取
        try:
            from flask import current_app
            if hasattr(current_app, 'cache'):
                cached_result = current_app.cache.get(cache_key)
                if cached_result:
                    print(f"从缓存获取审批模板列表: {cache_key}")
                    return cached_result
        except Exception as e:
            print(f"缓存获取失败: {str(e)}")

        params = {
            "offset": 0,
            "size": 50  # 减少获取数量，提高响应速度
        }
        if userid:
            params["userid"] = userid

        try:
            print(f"调用钉钉API获取审批模板列表: {params}")
            result = self._call_api("/topapi/process/listbyuserid", params)
            templates = result.get("result", {}).get("process_list", [])

            # 格式化模板数据
            formatted_templates = []
            for template in templates:
                process_code = template.get("process_code")
                name = template.get("name")

                # 跳过无效的模板数据
                if not process_code or not name:
                    continue

                formatted_templates.append({
                    "process_code": process_code,
                    "name": name,
                    "description": template.get("description", ""),
                    "icon_url": template.get("icon_url", ""),
                    "url": template.get("url", "")
                })

            # 缓存结果（缓存10分钟）
            try:
                if hasattr(current_app, 'cache'):
                    current_app.cache.set(cache_key, formatted_templates, timeout=600)
                    print(f"缓存审批模板列表: {cache_key}, 数量: {len(formatted_templates)}")
            except Exception as e:
                print(f"缓存设置失败: {str(e)}")

            return formatted_templates

        except Exception as e:
            print(f"获取审批模板列表失败: {str(e)}")
            # 返回空列表而不是抛出异常，避免影响页面加载
            return []

    def format_approval_instances_for_response(self, instances: List[Dict]) -> Dict:
        """格式化审批实例数据为标准响应格式"""
        try:
            if not isinstance(instances, list):
                error_msg = f"输入参数类型错误，期望list，实际{type(instances)}"
                return {
                    "status": "error",
                    "message": error_msg,
                    "total_count": 0,
                    "instances": []
                }

            formatted_instances = []

            for instance in instances:
                try:
                    if not isinstance(instance, dict):
                        continue

                    # 处理两种数据结构：
                    # 1. 嵌套结构：{'process_instance_id': 'xxx', 'process_instance': {...}}
                    # 2. 扁平结构：{'process_instance_id': 'xxx', 'business_id': 'xxx', ...}
                    if 'process_instance' in instance:
                        # 嵌套结构
                        process_instance = instance['process_instance']
                        process_instance_id = instance.get('process_instance_id')
                    else:
                        # 扁平结构，详细信息直接在顶层
                        process_instance = instance
                        process_instance_id = instance.get('process_instance_id')

                    # 确保process_instance是字典类型
                    if not isinstance(process_instance, dict):
                        continue

                    instance_data = {
                        "process_instance_id": process_instance_id,
                        "business_id": process_instance.get('business_id'),
                        "title": process_instance.get('title'),
                        "originator_userid": process_instance.get('originator_userid'),
                        "originator_dept_id": process_instance.get('originator_dept_id'),
                        "originator_dept_name": process_instance.get('originator_dept_name'),
                        "status": process_instance.get('status'),
                        "create_time": process_instance.get('create_time', ''),
                        "finish_time": process_instance.get('finish_time', ''),
                        "form_component_values": process_instance.get('form_component_values', [])
                    }

                    formatted_instances.append(instance_data)

                except Exception:
                    continue

            return {
                "status": "success",
                "total_count": len(formatted_instances),
                "instances": formatted_instances
            }

        except Exception as e:
            error_msg = f"格式化审批实例失败: {str(e)}"
            return {
                "status": "error",
                "message": error_msg,
                "total_count": 0,
                "instances": []
            }

    def get_quality_exceptions(
        self,
        process_code: str,
        start_time: int,
        end_time: int,
        status: Optional[str] = None
    ) -> Dict:
        """获取质量异常单审批数据

        Args:
            process_code (str): 质量异常单审批模板代码
            start_time (int): 开始时间戳（毫秒）
            end_time (int): 结束时间戳（毫秒）
            status (str): 审批状态（可选）

        Returns:
            Dict: 格式化的质量异常单数据
        """
        try:
            # 获取审批实例
            instances = self.get_approval_instances(process_code, start_time, end_time, status)

            if not instances:
                return {
                    "status": "success",
                    "message": "未找到符合条件的质量异常单",
                    "total_count": 0,
                    "instances": []
                }

            # 格式化为质量异常单数据
            formatted_result = self.format_approval_instances_for_response(instances)

            # 确保返回格式一致
            if formatted_result.get("status") == "success":
                return {
                    "status": "success",
                    "message": "获取质量异常单成功",
                    "total_count": formatted_result.get("total_count", 0),
                    "instances": formatted_result.get("instances", [])
                }
            else:
                return {
                    "status": "error",
                    "message": formatted_result.get("message", "格式化数据失败"),
                    "total_count": 0,
                    "instances": []
                }

        except Exception as e:
            error_msg = f"获取质量异常单失败: {str(e)}"
            return {
                "status": "error",
                "message": error_msg,
                "total_count": 0,
                "instances": []
            }
