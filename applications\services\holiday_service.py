from applications.models.holiday_config import HolidayConfig, HolidayOperationLog
from applications.extensions import db
from datetime import datetime, date
from flask import request
from flask_login import current_user
import holidays
import json


class HolidayService:
    """节假日配置服务类"""

    # 英文到中文的节假日名称映射字典
    HOLIDAY_NAME_MAPPING = {
        # 元旦
        "New Year's Day": "元旦",
        "New Year": "元旦",

        # 春节相关
        "Chinese New Year": "春节",
        "Spring Festival": "春节",
        "Chinese New Year's Eve": "除夕",
        "New Year's Eve": "除夕",
        "Lunar New Year": "春节",
        "Lunar New Year's Day": "春节",

        # 清明节
        "Qingming Festival": "清明节",
        "Tomb Sweeping Day": "清明节",
        "Tomb-Sweeping Day": "清明节",
        "Ching Ming Festival": "清明节",

        # 劳动节
        "Labor Day": "劳动节",
        "Labour Day": "劳动节",
        "International Workers' Day": "劳动节",
        "May Day": "劳动节",
        "Workers' Day": "劳动节",

        # 端午节
        "Dragon Boat Festival": "端午节",
        "Duanwu Festival": "端午节",
        "Double Fifth Festival": "端午节",

        # 中秋节
        "Mid-Autumn Festival": "中秋节",
        "Moon Festival": "中秋节",
        "Mooncake Festival": "中秋节",
        "Autumn Moon Festival": "中秋节",

        # 国庆节
        "National Day": "国庆节",
        "China National Day": "国庆节",
        "National Day of the People's Republic of China": "国庆节",
        "PRC National Day": "国庆节",

        # 调休和观察日相关
        "observed": "调休",
        "(observed)": "调休",
        "in lieu": "调休",
        "substitute": "调休",
        "makeup": "调休",
        "substitute holiday": "调休",
        "compensatory holiday": "调休",

        # 其他可能的表达
        "Golden Week": "黄金周",
        "Holiday": "节假日",
        "Public Holiday": "公共假期",
        "Bank Holiday": "法定假日",
    }

    @staticmethod
    def _translate_holiday_name(english_name):
        """
        将英文节假日名称转换为中文名称

        Args:
            english_name: 英文节假日名称

        Returns:
            str: 中文节假日名称
        """
        if not english_name:
            return english_name

        # 直接匹配完整名称
        if english_name in HolidayService.HOLIDAY_NAME_MAPPING:
            return HolidayService.HOLIDAY_NAME_MAPPING[english_name]

        # 处理包含 "observed" 的情况
        if "observed" in english_name.lower():
            # 提取主要节假日名称
            base_name = english_name.replace(" (observed)", "").replace(" observed", "").strip()
            if base_name in HolidayService.HOLIDAY_NAME_MAPPING:
                return HolidayService.HOLIDAY_NAME_MAPPING[base_name] + "（调休）"
            else:
                # 如果基础名称也没有匹配，尝试部分匹配
                for eng_key, chn_value in HolidayService.HOLIDAY_NAME_MAPPING.items():
                    if eng_key.lower() in base_name.lower():
                        return chn_value + "（调休）"

        # 部分匹配：检查是否包含已知的节假日关键词
        english_lower = english_name.lower()
        for eng_key, chn_value in HolidayService.HOLIDAY_NAME_MAPPING.items():
            if eng_key.lower() in english_lower:
                return chn_value

        # 如果都没有匹配，返回原英文名称
        return english_name

    @staticmethod
    def get_overtime_rate(work_date):
        """
        获取指定日期的加班倍率

        Args:
            work_date: 工作日期，可以是datetime或date对象

        Returns:
            float: 加班倍率 (1.5, 2.0, 3.0)
        """
        # 处理时区问题：如果是datetime对象，需要转换为中国本地日期
        if isinstance(work_date, datetime):
            # 如果是UTC时间，转换为中国时间
            if work_date.tzinfo is None:
                # 假设是UTC时间，加8小时转换为中国时间
                from datetime import timedelta
                china_datetime = work_date + timedelta(hours=8)
                work_date = china_datetime.date()
            else:
                work_date = work_date.date()

        # 从数据库查询配置
        holiday = HolidayConfig.query.filter_by(
            holiday_date=work_date,
            status=1
        ).first()

        if holiday:
            # 根据节假日类型返回对应的加班倍率
            if holiday.holiday_type == 'legal':
                return 3.0  # 法定节假日
            elif holiday.holiday_type == 'makeup':
                return 2.0  # 法定节假日调休
            elif holiday.holiday_type == 'weekend':
                return 2.0  # 周末
            elif holiday.holiday_type == 'company_rest':
                return 2.0  # 公司调休休息日（原工作日调为休息日）
            elif holiday.holiday_type == 'company_work':
                return 1.5  # 公司调休工作日（原休息日调为工作日）
            else:
                return float(holiday.overtime_rate)  # 兜底使用数据库配置的倍率

        # 默认逻辑：周末2倍，工作日1.5倍
        weekday = work_date.weekday()  # 0=周一, 6=周日
        return 2.0 if weekday >= 5 else 1.5
    
    @staticmethod
    def get_holiday_info(work_date):
        """
        获取指定日期的节假日信息

        Args:
            work_date: 工作日期

        Returns:
            dict: 节假日信息
        """
        # 处理时区问题：如果是datetime对象，需要转换为中国本地日期
        if isinstance(work_date, datetime):
            # 如果是UTC时间，转换为中国时间
            if work_date.tzinfo is None:
                # 假设是UTC时间，加8小时转换为中国时间
                from datetime import timedelta
                china_datetime = work_date + timedelta(hours=8)
                work_date = china_datetime.date()
            else:
                work_date = work_date.date()

        holiday = HolidayConfig.query.filter_by(
            holiday_date=work_date,
            status=1
        ).first()
        
        if holiday:
            return {
                'is_holiday': True,
                'holiday_name': holiday.holiday_name,
                'holiday_type': holiday.holiday_type,
                'overtime_rate': float(holiday.overtime_rate),
                'description': holiday.description
            }
        
        # 默认判断
        weekday = work_date.weekday()
        if weekday >= 5:  # 周末
            return {
                'is_holiday': True,
                'holiday_name': '周末',
                'holiday_type': 'weekend',
                'overtime_rate': 2.0,
                'description': '周末'
            }
        else:  # 工作日
            return {
                'is_holiday': False,
                'holiday_name': '工作日',
                'holiday_type': 'workday',
                'overtime_rate': 1.5,
                'description': '正常工作日'
            }
    
    @staticmethod
    def import_from_holidays_lib(year, operator_id, override_existing=False):
        """
        从holidays库批量导入节假日
        
        Args:
            year: 年份
            operator_id: 操作人ID
            override_existing: 是否覆盖已存在的记录
            
        Returns:
            dict: 导入结果统计
        """
        try:
            cn_holidays = holidays.China(years=year)
            imported_count = 0
            updated_count = 0
            skipped_count = 0
            
            # 真正的法定节假日列表（需要根据年份手动维护）
            legal_holidays = HolidayService._get_legal_holidays(year)
            
            for holiday_date, holiday_name in cn_holidays.items():
                # 检查是否已存在
                existing = HolidayConfig.query.filter_by(holiday_date=holiday_date).first()
                
                if existing and not override_existing:
                    skipped_count += 1
                    continue
                
                # 判断是否为真正的法定节假日
                date_str = holiday_date.strftime('%Y-%m-%d')
                is_legal = date_str in legal_holidays
                
                if existing:
                    # 更新现有记录
                    old_data = existing.to_dict()
                    # 将英文节假日名称转换为中文
                    chinese_name = HolidayService._translate_holiday_name(holiday_name)
                    existing.holiday_name = chinese_name
                    existing.holiday_type = 'legal' if is_legal else 'makeup'
                    existing.overtime_rate = 3.0 if is_legal else 2.0
                    existing.updated_by = operator_id
                    existing.updated_at = datetime.now()
                    
                    # 记录操作日志
                    HolidayService._log_operation(
                        'update', holiday_date, old_data, existing.to_dict(), 
                        operator_id, f'批量导入更新{year}年节假日'
                    )
                    updated_count += 1
                else:
                    # 创建新记录
                    # 将英文节假日名称转换为中文
                    chinese_name = HolidayService._translate_holiday_name(holiday_name)
                    holiday_config = HolidayConfig(
                        holiday_date=holiday_date,
                        holiday_name=chinese_name,
                        holiday_type='legal' if is_legal else 'makeup',
                        overtime_rate=3.0 if is_legal else 2.0,
                        year=year,
                        description=f'从holidays库导入的{year}年节假日',
                        created_by=operator_id,
                        status=1
                    )
                    
                    db.session.add(holiday_config)
                    
                    # 记录操作日志
                    HolidayService._log_operation(
                        'import', holiday_date, None, holiday_config.to_dict(), 
                        operator_id, f'批量导入{year}年节假日'
                    )
                    imported_count += 1
            
            db.session.commit()
            
            return {
                'success': True,
                'imported_count': imported_count,
                'updated_count': updated_count,
                'skipped_count': skipped_count,
                'total_count': len(cn_holidays)
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': str(e),
                'imported_count': 0,
                'updated_count': 0,
                'skipped_count': 0
            }
    
    @staticmethod
    def _get_legal_holidays(year):
        """
        获取指定年份的真正法定节假日列表
        
        Args:
            year: 年份
            
        Returns:
            set: 法定节假日日期字符串集合
        """
        # 这里需要根据每年的具体情况手动维护
        # 只有真正的法定节假日才是3倍工资，调休日是2倍工资
        legal_holidays_map = {
            2025: {
                f'{year}-01-01',  # 元旦
                f'{year}-05-01',  # 劳动节
                f'{year}-10-01', f'{year}-10-02', f'{year}-10-03',  # 国庆节前3天
                # 春节、清明、端午、中秋等需要根据农历计算，这里简化处理
            },
            2024: {
                f'{year}-01-01',  # 元旦
                f'{year}-05-01',  # 劳动节
                f'{year}-10-01', f'{year}-10-02', f'{year}-10-03',  # 国庆节前3天
            }
        }
        
        return legal_holidays_map.get(year, {f'{year}-01-01', f'{year}-05-01', f'{year}-10-01', f'{year}-10-02', f'{year}-10-03'})
    
    @staticmethod
    def _log_operation(operation_type, holiday_date, old_data, new_data, operator_id, remark=None):
        """
        记录操作日志
        
        Args:
            operation_type: 操作类型
            holiday_date: 节假日日期
            old_data: 修改前数据
            new_data: 修改后数据
            operator_id: 操作人ID
            remark: 备注
        """
        try:
            # 获取操作人姓名
            from applications.models.admin_user import User
            operator = User.query.get(operator_id)
            operator_name = operator.realname if operator else '未知用户'
            
            # 获取请求信息
            ip_address = request.remote_addr if request else None
            user_agent = request.headers.get('User-Agent') if request else None
            
            log = HolidayOperationLog(
                operation_type=operation_type,
                holiday_date=holiday_date,
                old_data=old_data,
                new_data=new_data,
                operator_id=operator_id,
                operator_name=operator_name,
                ip_address=ip_address,
                user_agent=user_agent,
                remark=remark
            )
            
            db.session.add(log)
            
        except Exception as e:
            # 日志记录失败不应该影响主要业务
            print(f"记录操作日志失败: {str(e)}")
    
    @staticmethod
    def batch_operation(ids, operation, operator_id):
        """
        批量操作节假日配置
        
        Args:
            ids: 节假日配置ID列表
            operation: 操作类型 (enable, disable, delete)
            operator_id: 操作人ID
            
        Returns:
            dict: 操作结果
        """
        try:
            holidays = HolidayConfig.query.filter(HolidayConfig.id.in_(ids)).all()
            
            if not holidays:
                return {'success': False, 'message': '未找到要操作的记录'}
            
            success_count = 0
            
            for holiday in holidays:
                old_data = holiday.to_dict()
                
                if operation == 'enable':
                    holiday.status = 1
                elif operation == 'disable':
                    holiday.status = 0
                elif operation == 'delete':
                    db.session.delete(holiday)
                    HolidayService._log_operation(
                        'delete', holiday.holiday_date, old_data, None, 
                        operator_id, f'批量删除节假日配置'
                    )
                    success_count += 1
                    continue
                
                if operation in ['enable', 'disable']:
                    holiday.updated_by = operator_id
                    holiday.updated_at = datetime.now()
                    
                    HolidayService._log_operation(
                        'update', holiday.holiday_date, old_data, holiday.to_dict(), 
                        operator_id, f'批量{operation}节假日配置'
                    )
                
                success_count += 1
            
            db.session.commit()
            
            return {
                'success': True,
                'message': f'成功操作{success_count}条记录',
                'count': success_count
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'批量操作失败: {str(e)}',
                'count': 0
            }
