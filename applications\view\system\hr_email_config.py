from flask import Blueprint, render_template, request
from flask_login import current_user

from applications.common.curd import model_to_dicts
from applications.common.helper import ModelFilter
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import HREmailConfig
from applications.schemas import HREmailConfigSchema, HREmailConfigOutSchema
from applications.common.admin import admin_log

bp = Blueprint('hr_email_config', __name__, url_prefix='/hr_email_config')


@bp.get('/')
@authorize("system:hr_email:main")
def main():
    """HR邮箱配置主页面"""
    return render_template('system/hr_email/main.html')


@bp.get('/data')
@authorize("system:hr_email:main")
def data():
    """获取HR邮箱配置数据"""
    # 获取请求参数
    email = str_escape(request.args.get("email"))
    description = str_escape(request.args.get('description'))
    is_active = request.args.get('is_active')
    
    # 查询参数构造
    mf = ModelFilter()
    if email:
        mf.contains(field_name="email", value=email)
    if description:
        mf.contains(field_name="description", value=description)
    if is_active is not None and is_active != '':
        mf.exact(field_name="is_active", value=is_active == '1')
    
    # orm查询
    configs = HREmailConfig.query.filter(mf.get_filter(HREmailConfig)).order_by(
        HREmailConfig.priority.asc(), HREmailConfig.id.asc()
    ).layui_paginate()
    count = configs.total
    
    # 返回api
    return table_api(data=model_to_dicts(schema=HREmailConfigOutSchema, data=configs.items), count=count)


@bp.post('/save')
@authorize("system:hr_email:add", log=True)
def save():
    """新增HR邮箱配置"""
    try:
        # 获取请求数据
        req_json = request.get_json(force=True) if request.is_json else {}
        
        # 数据验证
        schema = HREmailConfigSchema()
        try:
            validated_data = schema.load(req_json)
        except Exception as e:
            return fail_api(msg=f"数据验证失败: {str(e)}")
        
        # 检查邮箱是否已存在
        existing_config = HREmailConfig.query.filter_by(email=validated_data['email']).first()
        if existing_config:
            return fail_api(msg="该邮箱已存在，请勿重复添加")
        
        # 创建新配置
        config = HREmailConfig(
            email=validated_data['email'],
            description=validated_data.get('description'),
            is_active=validated_data.get('is_active', True),
            priority=validated_data.get('priority', 1),
            created_by=current_user.id
        )
        
        db.session.add(config)
        db.session.commit()
        
        # 记录操作日志
        admin_log(request=request, is_access=True,
                 desc=f"新增HR邮箱配置: {config.email}")
        
        return success_api(msg="HR邮箱配置添加成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"添加失败: {str(e)}")


@bp.put('/update')
@authorize("system:hr_email:edit", log=True)
def update():
    """更新HR邮箱配置"""
    try:
        # 获取请求数据
        req_json = request.get_json(force=True) if request.is_json else {}
        config_id = req_json.get('id')
        
        if not config_id:
            return fail_api(msg="缺少配置ID")
        
        # 查找配置
        config = HREmailConfig.query.get(config_id)
        if not config:
            return fail_api(msg="HR邮箱配置不存在")
        
        # 数据验证
        schema = HREmailConfigSchema()
        try:
            validated_data = schema.load(req_json)
        except Exception as e:
            return fail_api(msg=f"数据验证失败: {str(e)}")
        
        # 检查邮箱是否被其他配置使用
        if validated_data['email'] != config.email:
            existing_config = HREmailConfig.query.filter_by(email=validated_data['email']).first()
            if existing_config:
                return fail_api(msg="该邮箱已被其他配置使用")
        
        # 更新配置
        old_email = config.email
        config.email = validated_data['email']
        config.description = validated_data.get('description')
        config.is_active = validated_data.get('is_active', True)
        config.priority = validated_data.get('priority', 1)
        config.updated_by = current_user.id
        
        db.session.commit()
        
        # 记录操作日志
        admin_log(request=request, is_access=True,
                 desc=f"更新HR邮箱配置: {old_email} -> {config.email}")
        
        return success_api(msg="HR邮箱配置更新成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"更新失败: {str(e)}")


@bp.delete('/remove/<int:config_id>')
@authorize("system:hr_email:remove", log=True)
def remove(config_id):
    """删除HR邮箱配置"""
    try:
        config = HREmailConfig.query.get(config_id)
        if not config:
            return fail_api(msg="HR邮箱配置不存在")
        
        # 检查是否是最后一个启用的配置
        active_count = HREmailConfig.query.filter_by(is_active=True).count()
        if config.is_active and active_count <= 1:
            return fail_api(msg="不能删除最后一个启用的HR邮箱配置")
        
        email = config.email
        db.session.delete(config)
        db.session.commit()
        
        # 记录操作日志
        admin_log(request=request, is_access=True,
                 desc=f"删除HR邮箱配置: {email}")
        
        return success_api(msg="HR邮箱配置删除成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"删除失败: {str(e)}")


@bp.post('/toggle_status/<int:config_id>')
@authorize("system:hr_email:edit", log=True)
def toggle_status(config_id):
    """切换HR邮箱配置启用状态"""
    try:
        config = HREmailConfig.query.get(config_id)
        if not config:
            return fail_api(msg="HR邮箱配置不存在")
        
        # 如果要禁用，检查是否是最后一个启用的配置
        if config.is_active:
            active_count = HREmailConfig.query.filter_by(is_active=True).count()
            if active_count <= 1:
                return fail_api(msg="至少需要保留一个启用的HR邮箱配置")
        
        # 切换状态
        config.is_active = not config.is_active
        config.updated_by = current_user.id
        
        db.session.commit()
        
        status_text = "启用" if config.is_active else "禁用"
        
        # 记录操作日志
        admin_log(request=request, is_access=True,
                 desc=f"{status_text}HR邮箱配置: {config.email}")
        
        return success_api(msg=f"HR邮箱配置{status_text}成功")
        
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"操作失败: {str(e)}")


@bp.get('/active_emails')
@authorize("system:hr_email:main")
def active_emails():
    """获取所有启用的HR邮箱地址"""
    try:
        emails = HREmailConfig.get_active_email_addresses()
        return success_api(data=emails, msg="获取成功")
    except Exception as e:
        return fail_api(msg=f"获取失败: {str(e)}")
