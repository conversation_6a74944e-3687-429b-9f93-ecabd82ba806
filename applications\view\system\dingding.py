from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, or_, desc
import time
from applications.common.utils.rights import authorize
from applications.common.utils.http import success_api, fail_api, table_api
from applications.services.dingtalk_service import DingTalkService
from applications.services.quality_exception_processor import QualityExceptionProcessor
from applications.services.manual_project_binding_service import ManualProjectBindingService
from applications.models import DingTalkFailedLog, Dept, QualityException, Import_project, ProjectManageDept, QualityExceptionImportLog, QualityExceptionProjectBinding
from applications.services.quality_exception_import_service import QualityExceptionImportService
from applications.extensions import db
from flask import send_file


bp = Blueprint('dingding', __name__, url_prefix='/dindin')


def validate_sync_config(req_data):
    """验证和解析同步配置"""
    # 基础配置
    sync_config = {
        'days': req_data.get('days', 7),
        'sync_mode': req_data.get('sync_mode', 'incremental'),
        'sync_status': req_data.get('sync_status', ['NEW', 'RUNNING', 'COMPLETED']),
        'enable_schedule': req_data.get('enable_schedule', False),
        'frequency_type': req_data.get('frequency_type', 'daily'),
        'schedule_time': req_data.get('schedule_time', '02:00')
    }

    # 验证频率类型
    valid_frequencies = ['hourly', 'daily', 'weekly', 'monthly']
    if sync_config['frequency_type'] not in valid_frequencies:
        raise ValueError(f"无效的频率类型: {sync_config['frequency_type']}")

    # 验证时间格式
    try:
        hour, minute = map(int, sync_config['schedule_time'].split(':'))
        if not (0 <= hour <= 23 and 0 <= minute <= 59):
            raise ValueError("时间超出有效范围")
    except (ValueError, AttributeError):
        raise ValueError(f"无效的时间格式: {sync_config['schedule_time']}")

    # 根据频率类型验证和设置特定参数
    if sync_config['frequency_type'] == 'weekly':
        week_day = req_data.get('week_day')
        if week_day is not None:
            try:
                week_day = int(week_day)
                if not (0 <= week_day <= 6):
                    raise ValueError("周几必须在0-6之间")
                sync_config['week_day'] = week_day
            except (ValueError, TypeError):
                raise ValueError(f"无效的周几设置: {week_day}")
        else:
            sync_config['week_day'] = 6  # 默认周日

    elif sync_config['frequency_type'] == 'monthly':
        month_day = req_data.get('month_day')
        if month_day is not None:
            if month_day == 'last':
                sync_config['month_day'] = 'last'
            else:
                try:
                    month_day = int(month_day)
                    if not (1 <= month_day <= 31):
                        raise ValueError("月份日期必须在1-31之间")
                    sync_config['month_day'] = month_day
                except (ValueError, TypeError):
                    raise ValueError(f"无效的月份日期设置: {month_day}")
        else:
            sync_config['month_day'] = 1  # 默认1号

    return sync_config


def get_frequency_description(sync_config):
    """获取频率配置的中文描述"""
    frequency_type = sync_config.get('frequency_type', 'daily')

    if frequency_type == 'hourly':
        return '每小时'
    elif frequency_type == 'daily':
        return '每天'
    elif frequency_type == 'weekly':
        week_day = sync_config.get('week_day', 6)
        week_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        return f'每周{week_names[int(week_day)]}'
    elif frequency_type == 'monthly':
        month_day = sync_config.get('month_day', 1)
        if month_day == 'last':
            return '每月月末'
        else:
            return f'每月{month_day}号'
    else:
        return frequency_type

@bp.get('/')
@authorize("system:dingtalk:logs:view", log=True)
def main():
    """钉钉日志管理页面"""
    return render_template('system/dindin/main.html')


@bp.get('/logs')
@authorize("system:dingtalk:logs:query", log=True)
def get_logs():
    """获取钉钉日志数据API"""
    try:
        # 获取请求参数
        days = request.args.get('days', 1, type=int)
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        template_name = request.args.get('template_name')
        userid = request.args.get('userid')

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 15, type=int)

        # 参数验证
        if page < 1:
            return fail_api("页码必须大于0")
        if limit < 1 or limit > 100:
            return fail_api("每页数量必须在1-100之间")

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        if start_time and end_time:
            # 使用自定义时间范围
            try:
                start_timestamp = int(datetime.fromisoformat(start_time).timestamp() * 1000)
                end_timestamp = int(datetime.fromisoformat(end_time).timestamp() * 1000)

                # 时间范围验证
                if start_timestamp >= end_timestamp:
                    return fail_api("开始时间必须早于结束时间")

                # 时间跨度警告
                time_span_days = (end_timestamp - start_timestamp) / (24 * 60 * 60 * 1000)
                if time_span_days > 90:
                    return fail_api("查询时间跨度不能超过90天，请缩小查询范围")


                logs = dingtalk_service.get_logs(start_timestamp, end_timestamp, template_name, userid)
                result = dingtalk_service.format_logs_for_response(logs)

            except ValueError as e:
                return fail_api(f"时间格式错误: {str(e)}")
            except Exception as e:
                return fail_api(f"获取日志失败: {str(e)}")
        else:
            # 使用天数范围，传递筛选参数
            if days > 90:
                return fail_api("查询天数不能超过90天")

            end_timestamp = int(time.time() * 1000)
            start_timestamp = end_timestamp - days * 24 * 60 * 60 * 1000

            try:
                logs = dingtalk_service.get_logs(start_timestamp, end_timestamp, template_name, userid)
                result = dingtalk_service.format_logs_for_response(logs)
            except Exception as e:
                return fail_api(f"获取日志失败: {str(e)}")

        if result["status"] == "success":
            # 获取所有日志数据
            all_logs = result["logs"]
            total_count = result["total_count"]

            # 计算分页
            start_index = (page - 1) * limit
            end_index = start_index + limit

            # 对数据进行分页切片
            paginated_logs = all_logs[start_index:end_index]

            success_msg = "获取日志成功"
            if total_count >= 10000:
                success_msg += "（注意：数据量较大，已限制返回前10000条记录）"

            return table_api(
                data=paginated_logs,
                count=total_count,
                msg=success_msg
            )
        else:
            return fail_api(result.get("error_message", "获取日志失败"))

    except Exception as e:
        return fail_api(f"获取日志失败: {str(e)}")


@bp.post('/test-connection')
@authorize("system:dingtalk:logs:test", log=True)
def test_connection():
    """测试钉钉API连接"""
    try:
        dingtalk_service = DingTalkService()
        # 尝试获取token来测试连接
        token = dingtalk_service._get_access_token()
        if token:
            return success_api("钉钉API连接测试成功")
        else:
            return fail_api("钉钉API连接测试失败")
    except Exception as e:
        return fail_api(f"连接测试失败: {str(e)}")


@bp.get('/templates')
@authorize("system:dingtalk:logs:templates", log=True)
def get_templates():
    """获取用户可见的日志模板列表"""
    try:
        # 获取请求参数
        userid = request.args.get('userid')

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        # 获取模板列表
        templates = dingtalk_service.get_user_templates(userid)
        result = dingtalk_service.format_templates_for_response(templates)

        if result["status"] == "success":
            return table_api(
                data=result["templates"],
                count=result["total_count"],
                msg="获取模板列表成功"
            )
        else:
            return fail_api("获取模板列表失败")

    except Exception as e:
        return fail_api(f"获取模板列表失败: {str(e)}")


@bp.post('/save-logs')
@authorize("system:dingtalk:logs:save", log=True)
def save_logs():
    """保存钉钉日志到本地数据库API"""
    try:
        # 获取请求参数
        req_data = request.get_json()
        try:
            days = int(req_data.get('days', 1))  # 确保转换为整数
        except (ValueError, TypeError):
            days = 1  # 默认值
        start_time = req_data.get('start_time')
        end_time = req_data.get('end_time')
        template_name = req_data.get('template_name')
        userid = req_data.get('userid')  # 添加获取userid参数

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        if start_time and end_time:
            # 使用自定义时间范围
            try:
                start_timestamp = int(datetime.fromisoformat(start_time).timestamp() * 1000)
                end_timestamp = int(datetime.fromisoformat(end_time).timestamp() * 1000)
                result = dingtalk_service.get_and_save_logs(start_timestamp, end_timestamp, template_name, userid)
            except ValueError as e:
                return fail_api(f"时间格式错误: {str(e)}")
        else:
            # 使用天数范围
            end_time = int(time.time() * 1000)
            start_time = end_time - days * 24 * 60 * 60 * 1000
            try:
                result = dingtalk_service.get_and_save_logs(start_time, end_time, template_name, userid)
            except Exception as e:
                return fail_api(f"保存日志失败: {str(e)}")

        if result["status"] == "success":
            return success_api(
                data=result.get("save_result", {}),
                msg=result["message"]
            )
        else:
            return fail_api(result["message"])

    except Exception as e:
        return fail_api(f"保存日志失败: {str(e)}")


@bp.get('/failed-logs')
@authorize("system:dingtalk:logs:failed", log=True)
def failed_logs_page():
    """失败日志管理页面"""
    return render_template('system/dindin/failed_logs.html')


@bp.get('/failed-logs/data')
@authorize("system:dingtalk:logs:failed", log=True)
def get_failed_logs():
    """获取失败日志数据API"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 15, type=int)

        # 获取筛选参数
        creator_name = request.args.get('creator_name', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        department = request.args.get('department', '').strip()

        # 构建查询
        query = DingTalkFailedLog.query

        # 添加筛选条件
        if creator_name:
            query = query.filter(DingTalkFailedLog.creator_name.like(f'%{creator_name}%'))

        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(DingTalkFailedLog.failed_at >= start_datetime)
            except ValueError:
                pass

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(DingTalkFailedLog.failed_at <= end_datetime)
            except ValueError:
                pass

        if department:
            query = query.filter(DingTalkFailedLog.department.like(f'%{department}%'))

        # 按失败时间倒序排列
        query = query.order_by(desc(DingTalkFailedLog.failed_at))

        # 分页
        total = query.count()
        failed_logs = query.offset((page - 1) * limit).limit(limit).all()

        # 转换为字典格式
        data = [log.to_dict() for log in failed_logs]

        return table_api(
            data=data,
            count=total,
            msg="获取失败日志成功"
        )

    except Exception as e:
        return fail_api(f"获取失败日志失败: {str(e)}")


@bp.get('/departments')
@authorize("system:dept:main", log=True)
def get_departments():
    """获取启用状态的部门列表API"""
    try:
        # 查询启用状态的部门，按排序字段排序
        departments = Dept.query.filter_by(status=1).order_by(Dept.sort).all()

        # 转换为字典格式
        dept_list = []
        for dept in departments:
            dept_list.append({
                'id': dept.id,
                'name': dept.dept_name
            })

        return success_api(data=dept_list, msg="获取部门列表成功")

    except Exception as e:
        return fail_api(f"获取部门列表失败: {str(e)}")


@bp.delete('/failed-logs/<int:log_id>')
@authorize("system:dingtalk:logs:failed", log=True)
def delete_failed_log(log_id):
    """删除单个失败日志"""
    try:
        failed_log = DingTalkFailedLog.query.get(log_id)
        if not failed_log:
            return fail_api("失败日志不存在")

        db.session.delete(failed_log)
        db.session.commit()

        return success_api(msg="删除失败日志成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(f"删除失败日志失败: {str(e)}")


@bp.post('/failed-logs/batch-delete')
@authorize("system:dingtalk:logs:failed", log=True)
def batch_delete_failed_logs():
    """批量删除失败日志"""
    try:
        req_data = request.get_json()
        log_ids = req_data.get('ids', [])

        if not log_ids:
            return fail_api("请选择要删除的日志")

        # 删除指定的失败日志
        deleted_count = DingTalkFailedLog.query.filter(DingTalkFailedLog.id.in_(log_ids)).delete(synchronize_session=False)
        db.session.commit()

        return success_api(msg=f"成功删除 {deleted_count} 条失败日志")

    except Exception as e:
        db.session.rollback()
        return fail_api(f"批量删除失败日志失败: {str(e)}")


@bp.get('/failed-logs/<int:log_id>/detail')
@authorize("system:dingtalk:logs:failed", log=True)
def get_failed_log_detail(log_id):
    """获取失败日志详情"""
    try:
        failed_log = DingTalkFailedLog.query.get(log_id)
        if not failed_log:
            return fail_api("失败日志不存在")

        return success_api(data=failed_log.to_dict(), msg="获取失败日志详情成功")

    except Exception as e:
        return fail_api(f"获取失败日志详情失败: {str(e)}")


@bp.get('/quality-exceptions')
@authorize("system:dingtalk:quality_exceptions:view", log=True)
def quality_exceptions():
    """质量异常单管理页面"""
    return render_template('system/dindin/quality_exceptions.html')


@bp.get('/quality-exceptions-v2')
@authorize("system:dingtalk:quality_exceptions:view", log=True)
def quality_exceptions_v2():
    """质量异常单管理页面 - 智能同步版"""
    return render_template('system/dindin/quality_exceptions_v2.html')


@bp.get('/quality-exceptions-binding')
@authorize("system:dingtalk:project_binding:view", log=True)
def quality_exceptions_binding():
    """质量异常项目绑定管理页面"""
    return render_template('system/dindin/quality_exceptions_binding_simple.html')




@bp.post('/refresh-permissions')
@authorize("system:dingtalk:project_binding:view", log=True)
def refresh_permissions():
    """刷新用户权限到session"""
    try:
        from flask import session
        from flask_login import current_user

        # 重新加载用户权限到session
        role = current_user.role
        user_power = []
        for i in role:
            if i.enable == 0:
                continue
            for p in i.power:
                if p.enable == 0:
                    continue
                user_power.append(p.code)

        session['permissions'] = user_power

        return success_api(msg="权限刷新成功", data={
            'permissions_count': len(user_power),
            'has_binding_permission': 'system:dingtalk:quality_exceptions_binding' in user_power
        })

    except Exception as e:
        return fail_api(f"权限刷新失败: {str(e)}")



@bp.post('/api/quality-exceptions')
@authorize("system:dingtalk:quality_exceptions:view", log=True)
def get_quality_exceptions_api():
    """获取质量异常单API"""
    try:
        # 获取请求参数
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        process_code = req_data.get('process_code')  # 质量异常单审批模板代码

        if not process_code:
            return fail_api("缺少审批模板代码参数")

        try:
            days = int(req_data.get('days', 7))  # 默认查询最近7天
        except (ValueError, TypeError):
            days = 7

        start_time = req_data.get('start_time')
        end_time = req_data.get('end_time')
        status = req_data.get('status')  # 审批状态

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        # 处理时间参数
        if start_time and end_time:
            try:
                # 转换为时间戳（毫秒）
                start_timestamp = int(datetime.strptime(start_time, '%Y-%m-%d').timestamp() * 1000)
                end_timestamp = int(datetime.strptime(end_time + ' 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp() * 1000)


                result = dingtalk_service.get_quality_exceptions(process_code, start_timestamp, end_timestamp, status)

            except ValueError as e:
                return fail_api(f"时间格式错误: {str(e)}")
            except Exception as e:
                return fail_api(f"获取质量异常单失败: {str(e)}")
        else:
            # 使用天数范围
            if days > 90:
                return fail_api("查询天数不能超过90天")

            end_timestamp = int(time.time() * 1000)
            start_timestamp = end_timestamp - days * 24 * 60 * 60 * 1000

            try:
                result = dingtalk_service.get_quality_exceptions(process_code, start_timestamp, end_timestamp, status)
            except Exception as e:
                return fail_api(f"获取质量异常单失败: {str(e)}")

        # 确保result是字典类型
        if not isinstance(result, dict):
            return fail_api("服务返回数据格式错误")

        if result.get("status") == "success":
            # 确保数据格式正确
            instances = result.get("instances", [])
            total_count = result.get("total_count", 0)

            # 验证数据类型
            if not isinstance(instances, list):
                instances = []

            if not isinstance(total_count, int):
                try:
                    total_count = int(total_count)
                except (ValueError, TypeError):
                    total_count = 0



            return table_api(
                data=instances,
                count=total_count,
                msg="获取质量异常单成功"
            )
        else:
            error_msg = result.get("message", "获取质量异常单失败")
            return fail_api(error_msg)

    except Exception as e:
        error_msg = f"获取质量异常单失败: {str(e)}"
        return fail_api(error_msg)


@bp.post('/api/quality-exceptions/sync')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def sync_quality_exceptions():
    """同步质量异常单到本地数据库API"""
    try:
        # 获取请求参数
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        process_code = req_data.get('process_code')  # 质量异常单审批模板代码

        if not process_code:
            return fail_api("缺少审批模板代码参数")

        try:
            days = int(req_data.get('days', 7))  # 默认同步最近7天
        except (ValueError, TypeError):
            days = 7

        start_time = req_data.get('start_time')
        end_time = req_data.get('end_time')
        status = req_data.get('status')  # 审批状态

        # 初始化服务
        dingtalk_service = DingTalkService()
        processor = QualityExceptionProcessor()

        # 处理时间参数
        if start_time and end_time:
            try:
                # 转换为时间戳（毫秒）
                start_timestamp = int(datetime.strptime(start_time, '%Y-%m-%d').timestamp() * 1000)
                end_timestamp = int(datetime.strptime(end_time + ' 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp() * 1000)

            except ValueError as e:
                return fail_api(f"时间格式错误: {str(e)}")
        else:
            # 使用天数范围
            if days > 90:
                return fail_api("同步天数不能超过90天")

            end_timestamp = int(time.time() * 1000)
            start_timestamp = end_timestamp - days * 24 * 60 * 60 * 1000



        try:
            # 获取钉钉审批实例
            instances = dingtalk_service.get_approval_instances(process_code, start_timestamp, end_timestamp, status)

            if not instances:
                return success_api(msg="未找到符合条件的质量异常单", data={
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'instances': []
                })

            # 处理并保存到数据库
            result = processor.process_quality_exceptions(instances)

            # 确保result是字典类型
            if not isinstance(result, dict):
                return fail_api("数据处理服务返回格式错误")

            if result.get('status') == 'success':
                # 格式化审批实例数据用于前端显示
                formatted_result = dingtalk_service.format_approval_instances_for_response(instances)

                # 获取格式化后的实例数据
                formatted_instances = []
                if formatted_result.get("status") == "success":
                    formatted_instances = formatted_result.get("instances", [])

                success_msg = result.get('message', '同步完成')
                return success_api(msg=success_msg, data={
                    'total': result.get('total', 0),
                    'success_count': result.get('success_count', 0),
                    'failed_count': result.get('failed_count', 0),
                    'instances': formatted_instances  # 返回格式化的实例数据
                })
            else:
                error_msg = result.get('message', '同步失败')
                return fail_api(error_msg)

        except Exception as e:
            error_msg = f"同步质量异常单失败: {str(e)}"
            return fail_api(error_msg)

    except Exception as e:
        error_msg = f"同步质量异常单失败: {str(e)}"
        return fail_api(error_msg)


@bp.get('/api/approval-templates')
@authorize("system:dingtalk:logs:templates", log=True)
def get_approval_templates():
    """获取审批模板列表API"""
    import time
    start_time = time.time()

    try:
        # 获取请求参数
        userid = request.args.get('userid')
        print(f"获取审批模板列表请求: userid={userid}")

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        # 获取审批模板列表
        templates = dingtalk_service.get_approval_templates(userid)

        end_time = time.time()
        duration = end_time - start_time
        print(f"获取审批模板列表完成，耗时: {duration:.2f}秒，模板数量: {len(templates)}")

        return table_api(
            data=templates,
            count=len(templates),
            msg=f"获取审批模板列表成功 (耗时: {duration:.2f}秒)"
        )

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        error_msg = f"获取审批模板列表失败 (耗时: {duration:.2f}秒): {str(e)}"
        print(error_msg)
        return fail_api(error_msg)


@bp.post('/api/quality-exceptions/sync-direct')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def sync_quality_exceptions_direct():
    """直接同步质量异常单到本地数据库API（使用已获取的数据，避免重复调用钉钉API）"""
    try:
        # 获取请求参数
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        instances = req_data.get('instances')
        if not instances or not isinstance(instances, list):
            return fail_api("缺少实例数据或数据格式错误")

        if len(instances) == 0:
            return success_api(msg="没有数据需要同步", data={
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'instances': []
            })

        # 初始化处理器
        processor = QualityExceptionProcessor()

        # 直接处理传入的实例数据，无需调用钉钉API
        print(f"开始直接同步 {len(instances)} 条质量异常单数据")
        result = processor.process_quality_exceptions(instances)

        # 确保result是字典类型
        if not isinstance(result, dict):
            return fail_api("数据处理服务返回格式错误")

        if result.get('status') == 'success':
            success_msg = result.get('message', '同步完成')
            return success_api(msg=success_msg, data={
                'total': result.get('total', 0),
                'success_count': result.get('success_count', 0),
                'failed_count': result.get('failed_count', 0),
                'instances': instances  # 返回原始实例数据
            })
        else:
            error_msg = result.get('message', '同步失败')
            return fail_api(error_msg)

    except Exception as e:
        return fail_api(f"直接同步失败: {str(e)}")


@bp.post('/api/quality-exceptions/auto-sync')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def auto_sync_quality_exceptions():
    """自动同步质量异常单API（后台智能同步）"""
    start_time = time.time()
    try:
        from applications.services.quality_exception_sync_service import QualityExceptionSyncService

        # 获取请求参数
        req_data = request.get_json() or {}

        # 验证和解析同步配置
        try:
            sync_config = validate_sync_config(req_data)
        except ValueError as e:
            return fail_api(f"配置验证失败: {str(e)}")

        print(f"智能同步配置: {sync_config}")

        # 初始化同步服务
        sync_service = QualityExceptionSyncService()

        # 执行自动同步
        result = sync_service.auto_sync_quality_exceptions_with_config(sync_config)

        end_time = time.time()
        duration = end_time - start_time
        print(f"智能同步完成，耗时: {duration:.2f}秒")

        if result.get('status') == 'success':
            success_msg = result.get('message', '智能同步成功')
            if sync_config.get('enable_schedule'):
                frequency_desc = get_frequency_description(sync_config)
                success_msg += f" (已设置{frequency_desc}定时同步)"

            return success_api(msg=success_msg, data={
                'total': result.get('total', 0),
                'success_count': result.get('success_count', 0),
                'failed_count': result.get('failed_count', 0),
                'duration': f"{duration:.2f}秒",
                'config': sync_config
            })
        else:
            return fail_api(result.get('message', '自动同步失败'))

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        error_msg = f"自动同步失败 (耗时: {duration:.2f}秒): {str(e)}"
        print(error_msg)
        return fail_api(error_msg)


@bp.get('/api/sync-config')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def get_sync_config():
    """获取当前钉钉同步配置API"""
    try:
        from applications.services.dingtalk_sync_config_service import DingTalkSyncConfigService

        # 获取配置
        config = DingTalkSyncConfigService.get_sync_config_dict()

        return success_api(msg="获取同步配置成功", data=config)

    except Exception as e:
        return fail_api(f"获取同步配置失败: {str(e)}")


@bp.post('/api/sync-config/save')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def save_sync_config():
    """保存钉钉同步配置API"""
    try:
        from applications.services.dingtalk_sync_config_service import DingTalkSyncConfigService

        # 获取请求参数
        req_data = request.get_json() or {}

        # 验证和解析同步配置
        try:
            sync_config = validate_sync_config(req_data)
        except ValueError as e:
            return fail_api(f"配置验证失败: {str(e)}")

        # 保存配置到字典表
        save_success = DingTalkSyncConfigService.save_config(sync_config)

        if save_success:
            # 如果启用了定时同步，更新任务调度
            if sync_config.get('enable_schedule'):
                try:
                    from applications.extensions.init_scheduler import add_quality_exception_sync_job_with_config
                    add_quality_exception_sync_job_with_config(sync_config)
                    frequency_desc = get_frequency_description(sync_config)
                    return success_api(msg=f"配置保存成功，已设置{frequency_desc}定时同步", data=sync_config)
                except Exception as e:
                    return success_api(msg=f"配置保存成功，但定时任务设置失败: {str(e)}", data=sync_config)
            else:
                # 如果禁用了定时同步，移除现有任务
                try:
                    from applications.extensions.init_scheduler import remove_quality_exception_sync_job
                    remove_quality_exception_sync_job()
                except Exception:
                    pass  # 忽略移除任务的错误
                return success_api(msg="配置保存成功，定时同步已禁用", data=sync_config)
        else:
            return fail_api("配置保存失败")

    except Exception as e:
        return fail_api(f"保存同步配置失败: {str(e)}")


@bp.get('/api/quality-exceptions/sync-status')
# @authorize("system:dingtalk:quality_exceptions:view", log=False)  # 临时移除权限验证
def get_quality_exceptions_sync_status():
    """获取质量异常单同步状态API"""
    try:
        from applications.services.quality_exception_sync_service import QualityExceptionSyncService

        sync_service = QualityExceptionSyncService()
        result = sync_service.get_sync_status()

        if result.get('status') == 'success':
            return success_api(msg="获取同步状态成功", data=result)
        else:
            return fail_api(result.get('message', '获取同步状态失败'))

    except Exception as e:
        return fail_api(f"获取同步状态失败: {str(e)}")


@bp.post('/api/quality-exceptions/manual-sync')
@authorize("system:dingtalk:quality_exceptions:sync", log=True)
def manual_sync_quality_exceptions():
    """手动同步质量异常单API（紧急情况使用）"""
    try:
        from applications.services.quality_exception_sync_service import QualityExceptionSyncService

        # 获取请求参数
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        start_date = req_data.get('start_date')
        end_date = req_data.get('end_date')

        if not start_date or not end_date:
            return fail_api("请提供开始日期和结束日期")

        # 初始化同步服务
        sync_service = QualityExceptionSyncService()

        # 执行手动同步
        result = sync_service.manual_sync_quality_exceptions(start_date, end_date)

        if result.get('status') == 'success':
            return success_api(msg=result.get('message'), data={
                'total': result.get('total', 0),
                'success_count': result.get('success_count', 0),
                'failed_count': result.get('failed_count', 0)
            })
        else:
            return fail_api(result.get('message', '手动同步失败'))

    except Exception as e:
        return fail_api(f"手动同步失败: {str(e)}")




@bp.get('/api/quality-exceptions/list')
@authorize("system:dingtalk:quality_exceptions:view", log=False)  # 临时移除权限验证
def get_quality_exceptions_list():
    """获取质量异常单列表API（包含绑定状态信息）"""
    try:
        from applications.models.quality_exception import QualityException
        from applications.models.admin_dept import Dept
        from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
        from sqlalchemy.orm import joinedload
        from sqlalchemy import func

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        status = request.args.get('status', '')
        project_type = request.args.get('project_type', '')
        project_code = request.args.get('project_code', '')
        binding_status = request.args.get('binding_status', '')

        # 构建查询，预加载责任部门信息
        query = QualityException.query.options(joinedload(QualityException.responsible_dept))

        # 状态过滤
        if status:
            query = query.filter(QualityException.approval_status == status)

        # 项目类型过滤
        if project_type:
            query = query.filter(QualityException.project_type.like(f'%{project_type}%'))

        # 项目编号过滤
        if project_code:
            query = query.filter(QualityException.project_code.like(f'%{project_code}%'))

        # 绑定状态过滤
        if binding_status:
            if binding_status == 'bound':
                # 已绑定：有有效绑定记录
                query = query.join(QualityExceptionProjectBinding).filter(
                    QualityExceptionProjectBinding.is_active == True,
                    QualityExceptionProjectBinding.status == 'active'
                )
            elif binding_status == 'unbound':
                # 未绑定：没有有效绑定记录
                query = query.outerjoin(QualityExceptionProjectBinding).filter(
                    QualityExceptionProjectBinding.id.is_(None)
                )

        # 分页查询
        pagination = query.order_by(QualityException.create_time.desc()).paginate(
            page=page, per_page=limit, error_out=False
        )

        # 获取所有异常ID，用于批量查询绑定信息
        exception_ids = [item.id for item in pagination.items]

        # 批量查询绑定统计信息
        binding_stats = {}
        if exception_ids:
            binding_query = db.session.query(
                QualityExceptionProjectBinding.quality_exception_id,
                func.count(QualityExceptionProjectBinding.id).label('binding_count'),
                func.avg(QualityExceptionProjectBinding.match_confidence).label('avg_confidence')
            ).filter(
                QualityExceptionProjectBinding.quality_exception_id.in_(exception_ids),
                QualityExceptionProjectBinding.is_active == True,
                QualityExceptionProjectBinding.status == 'active'
            ).group_by(QualityExceptionProjectBinding.quality_exception_id).all()

            for stat in binding_query:
                binding_stats[stat.quality_exception_id] = {
                    'binding_count': stat.binding_count,
                    'avg_confidence': float(stat.avg_confidence) if stat.avg_confidence else None
                }

        # 格式化数据
        data = []
        for item in pagination.items:
            # 获取发起部门名称
            originator_dept_name = item.originator_dept_name
            if not originator_dept_name and item.originator_dept_id:
                try:
                    dept_id = int(item.originator_dept_id)
                    dept = Dept.query.filter_by(id=dept_id).first()
                    if dept:
                        originator_dept_name = dept.dept_name
                except (ValueError, TypeError):
                    pass

            # 获取责任部门名称
            responsible_dept_name = ''
            if item.responsible_dept:
                responsible_dept_name = item.responsible_dept.dept_name

            # 获取绑定统计信息
            binding_info = binding_stats.get(item.id, {'binding_count': 0, 'avg_confidence': None})

            data.append({
                'id': item.id,
                'process_instance_id': item.process_instance_id,
                'title': item.title,
                'project_type': item.project_type,
                'project_code': item.project_code,
                'machine_number': item.machine_number,
                'part_drawing_number': item.part_drawing_number,
                'exception_quantity': item.exception_quantity,
                'estimated_hours': item.estimated_hours,
                'originator_dept_name': originator_dept_name or '',
                'responsible_dept_name': responsible_dept_name,
                'responsible_person': item.responsible_person or '',
                'approval_status': item.approval_status,
                'exception_description': item.exception_description,
                'create_time': item.create_time.strftime('%Y-%m-%d %H:%M:%S') if item.create_time else '',
                'finish_time': item.finish_time.strftime('%Y-%m-%d %H:%M:%S') if item.finish_time else '',
                # 绑定状态信息
                'binding_count': binding_info['binding_count'],
                'avg_confidence': binding_info['avg_confidence']
            })

        return table_api(msg="获取成功", data=data, count=pagination.total)

    except Exception as e:
        return fail_api(f"获取质量异常单列表失败: {str(e)}")


@bp.get('/api/quality-exceptions/<int:exception_id>/binding-info')
@authorize("system:dingtalk:quality_exceptions:view", log=False)
def get_exception_binding_info(exception_id):
    """获取质量异常单的绑定信息"""
    try:
        from applications.models.quality_exception import QualityException
        from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
        from applications.services.project_code_parser import ProjectCodeParser

        # 查询异常单
        exception = QualityException.query.get(exception_id)
        if not exception:
            return fail_api("质量异常单不存在")

        # 解析项目编号
        parser = ProjectCodeParser()
        parsed_result = parser.parse_project_code(exception.project_code or '')

        # 查询当前绑定数量
        current_binding_count = QualityExceptionProjectBinding.query.filter_by(
            quality_exception_id=exception_id,
            is_active=True,
            status='active'
        ).count()

        return success_api(msg="获取成功", data={
            'exception_id': exception_id,
            'project_code': exception.project_code,
            'project_type': exception.project_type,
            'parsed_project_codes': parsed_result.parsed_codes,
            'parsed_project_count': len(parsed_result.parsed_codes),
            'format_type': parsed_result.format_type,
            'current_binding_count': current_binding_count,
            'allow_multiple_binding': len(parsed_result.parsed_codes) > 1
        })

    except Exception as e:
        return fail_api(f"获取绑定信息失败: {str(e)}")


# ==================== 手动项目绑定相关API ====================

@bp.post('/api/quality-exceptions/<int:exception_id>/bind-project')
@authorize("system:dingtalk:project_binding:bind", log=True)
def bind_project_to_exception(exception_id):
    """手动绑定项目到质量异常单"""
    try:
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        project_id = req_data.get('project_id')
        notes = req_data.get('notes', '')

        if not project_id:
            return fail_api("请选择要绑定的项目")

        # 初始化手动绑定服务
        binding_service = ManualProjectBindingService()

        # 执行绑定
        result = binding_service.bind_project_to_exception(exception_id, project_id, notes)

        if result['success']:
            return success_api(msg=result['message'], data=result.get('project_info'))
        else:
            return fail_api(result['message'])

    except Exception as e:
        return fail_api(f"绑定项目失败: {str(e)}")


@bp.delete('/api/quality-exceptions/<int:exception_id>/unbind-project/<int:project_id>')
@authorize("system:dingtalk:project_binding:bind", log=True)
def unbind_project_from_exception(exception_id, project_id):
    """解绑项目与质量异常单的关联"""
    try:
        req_data = request.get_json() or {}
        notes = req_data.get('notes', '')

        # 初始化手动绑定服务
        binding_service = ManualProjectBindingService()

        # 执行解绑
        result = binding_service.unbind_project_from_exception(exception_id, project_id, notes)

        if result['success']:
            return success_api(msg=result['message'])
        else:
            return fail_api(result['message'])

    except Exception as e:
        return fail_api(f"解绑项目失败: {str(e)}")


@bp.post('/api/quality-exceptions/batch-bind-project')
@authorize("system:dingtalk:project_binding:bind", log=True)
def batch_bind_project():
    """批量绑定项目到多个质量异常单"""
    try:
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        exception_ids = req_data.get('exception_ids', [])
        project_id = req_data.get('project_id')
        notes = req_data.get('notes', '')

        if not exception_ids:
            return fail_api("请选择要绑定的质量异常单")

        if not project_id:
            return fail_api("请选择要绑定的项目")

        # 初始化手动绑定服务
        binding_service = ManualProjectBindingService()

        # 执行批量绑定
        result = binding_service.batch_bind_projects(exception_ids, project_id, notes)

        if result['success']:
            return success_api(msg=result['message'], data={
                'total': result['total'],
                'success_count': result['success_count'],
                'failed_count': result['failed_count'],
                'results': result['results']
            })
        else:
            return fail_api(result['message'])

    except Exception as e:
        return fail_api(f"批量绑定项目失败: {str(e)}")


@bp.get('/api/quality-exceptions/<int:exception_id>/binding-suggestions')
@authorize("system:dingtalk:quality_exceptions:view", log=False)
def get_binding_suggestions(exception_id):
    """获取项目绑定建议"""
    try:
        # 初始化手动绑定服务
        binding_service = ManualProjectBindingService()

        # 获取绑定建议
        result = binding_service.get_binding_suggestions(exception_id)

        if result['success']:
            return success_api(msg="获取绑定建议成功", data={
                'suggestions': result['suggestions'],
                'total': result['total']
            })
        else:
            return fail_api(result['message'])

    except Exception as e:
        return fail_api(f"获取绑定建议失败: {str(e)}")


@bp.get('/api/quality-exceptions/<int:exception_id>/bindings')
@authorize("system:dingtalk:quality_exceptions:view", log=False)
def get_exception_bindings(exception_id):
    """获取质量异常单的所有项目绑定"""
    try:
        # 初始化手动绑定服务
        binding_service = ManualProjectBindingService()

        # 获取绑定信息
        result = binding_service.get_exception_bindings(exception_id)

        if result['success']:
            return success_api(msg="获取绑定信息成功", data={
                'bindings': result['bindings'],
                'total': result['total']
            })
        else:
            return fail_api(result['message'])

    except Exception as e:
        return fail_api(f"获取绑定信息失败: {str(e)}")


@bp.post('/api/quality-exceptions/bindings/<int:binding_id>/update-notes')
@authorize("system:dingtalk:project_binding:manage", log=True)
def update_binding_notes(binding_id):
    """更新绑定备注"""
    try:
        req_data = request.get_json()
        if not req_data:
            return fail_api("请求数据格式错误")

        notes = req_data.get('notes', '')

        # 查找绑定记录
        binding = QualityExceptionProjectBinding.query.get(binding_id)
        if not binding:
            return fail_api("绑定记录不存在")

        # 更新备注
        binding.notes = notes
        binding.updated_by = current_user.id if current_user.is_authenticated else None
        binding.updated_at = datetime.datetime.now()

        db.session.commit()

        return success_api(msg="备注更新成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(f"更新备注失败: {str(e)}")


@bp.post('/api/quality-exceptions/bindings/<int:binding_id>/delete')
@authorize("system:dingtalk:project_binding:manage", log=True)
def delete_binding(binding_id):
    """删除绑定关系"""
    try:
        req_data = request.get_json()
        notes = req_data.get('notes', '手动删除绑定') if req_data else '手动删除绑定'

        # 查找绑定记录
        binding = QualityExceptionProjectBinding.query.get(binding_id)
        if not binding:
            return fail_api("绑定记录不存在")

        # 停用绑定
        success = QualityExceptionProjectBinding.deactivate_binding(
            binding_id,
            updated_by=current_user.id if current_user.is_authenticated else None,
            notes=notes
        )

        if not success:
            return fail_api("删除绑定失败")

        # 检查是否需要更新异常单的主项目ID
        exception = QualityException.query.get(binding.quality_exception_id)
        if exception and exception.project_id == binding.project_id:
            # 如果删除的是主项目，尝试设置其他活跃绑定为主项目
            remaining_bindings = QualityExceptionProjectBinding.get_active_bindings(binding.quality_exception_id)
            if remaining_bindings:
                exception.project_id = remaining_bindings[0].project_id
            else:
                exception.project_id = None

        db.session.commit()

        return success_api(msg="绑定删除成功")

    except Exception as e:
        db.session.rollback()
        return fail_api(f"删除绑定失败: {str(e)}")


@bp.get('/api/projects/search')
@authorize("system:dingtalk:quality_exceptions:view", log=False)
def search_projects():
    """搜索项目（用于绑定选择）"""
    try:
        # 获取搜索参数
        keyword = request.args.get('keyword', '').strip()
        project_type_id = request.args.get('project_type_id', type=int)
        limit = request.args.get('limit', 20, type=int)

        # 构建查询
        query = Import_project.query

        # 关键词搜索（项目名称或编号）
        if keyword:
            query = query.filter(
                or_(
                    Import_project.project_name.like(f'%{keyword}%'),
                    Import_project.project_code.like(f'%{keyword}%')
                )
            )

        # 项目类型过滤
        if project_type_id:
            query = query.filter(Import_project.dept_id == project_type_id)

        # 限制结果数量并排序
        projects = query.order_by(Import_project.create_at.desc()).limit(limit).all()

        # 格式化结果
        project_list = []
        for project in projects:
            # 获取项目类型名称
            project_type = ProjectManageDept.query.get(project.dept_id)
            project_type_name = project_type.dept_name if project_type else '未知类型'

            project_list.append({
                'id': project.id,
                'project_name': project.project_name,
                'project_code': project.project_code,
                'project_type_id': project.dept_id,
                'project_type_name': project_type_name,
                'create_time': project.create_at.strftime('%Y-%m-%d') if project.create_at else ''
            })

        return success_api(msg="搜索项目成功", data={
            'projects': project_list,
            'total': len(project_list)
        })

    except Exception as e:
        return fail_api(f"搜索项目失败: {str(e)}")


@bp.get('/api/project-types')
@authorize("system:dingtalk:quality_exceptions:view", log=False)
def get_project_types():
    """获取项目类型列表（用于绑定筛选）"""
    try:
        # 查询启用的项目类型
        project_types = ProjectManageDept.query.filter_by(status=1).order_by(ProjectManageDept.sort).all()

        # 格式化结果
        type_list = []
        for ptype in project_types:
            type_list.append({
                'id': ptype.id,
                'name': ptype.dept_name,
                'sort': ptype.sort
            })

        return success_api(msg="获取项目类型成功", data={
            'project_types': type_list,
            'total': len(type_list)
        })

    except Exception as e:
        return fail_api(f"获取项目类型失败: {str(e)}")


# ==================== Excel导入相关API ====================

@bp.post('/api/quality-exceptions/import')
@authorize("system:dingtalk:quality_exceptions:import", log=True)
def import_quality_exceptions():
    """Excel导入质量异常单"""
    try:
        if 'file' not in request.files:
            return fail_api(msg='未选择文件')

        file = request.files['file']
        import_mode = request.form.get('import_mode', 'insert')

        if file.filename == '':
            return fail_api(msg='未选择文件')

        # 执行导入
        import_service = QualityExceptionImportService()
        result = import_service.process_import(file, current_user.id, import_mode)

        if result['valid']:
            return success_api(data=result['data'], msg=result['message'])
        else:
            return fail_api(msg=result['message'])

    except Exception as e:
        return fail_api(msg=f'导入失败: {str(e)}')


@bp.get('/api/quality-exceptions/template')
@authorize("system:dingtalk:quality_exceptions:template", log=True)
def download_import_template():
    """下载Excel导入模板"""
    try:
        import_service = QualityExceptionImportService()
        template_file = import_service.generate_template()

        return send_file(
            template_file,
            as_attachment=True,
            download_name=f'质量异常单导入模板_{datetime.now().strftime("%Y%m%d")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return fail_api(msg=f'模板下载失败: {str(e)}')


@bp.post('/api/quality-exceptions/validate')
@authorize("system:dingtalk:quality_exceptions:import", log=True)
def validate_import_data():
    """验证导入数据"""
    try:
        if 'file' not in request.files:
            return fail_api(msg='未选择文件')

        file = request.files['file']

        # 读取并验证数据
        import_service = QualityExceptionImportService()

        # 文件验证
        file_validation = import_service.validate_file(file)
        if not file_validation['valid']:
            return fail_api(msg=file_validation['message'])

        # 读取数据
        import pandas as pd
        df = pd.read_excel(file, dtype=str)

        # 数据验证
        validation_result = import_service.validate_data(df, current_user.id)

        # 返回验证结果和数据预览
        preview_data = df.head(5).to_dict('records')  # 前5行预览

        # 根据验证结果返回不同的响应
        if validation_result['valid']:
            return success_api(data={
                'validation': validation_result,
                'preview': preview_data,
                'total_count': len(df)
            }, msg='数据验证通过')
        else:
            # 验证失败时返回错误响应
            error_msg = '数据验证失败：' + '；'.join(validation_result.get('errors', []))
            return fail_api(msg=error_msg)

    except Exception as e:
        return fail_api(msg=f'数据验证失败: {str(e)}')


@bp.get('/api/quality-exceptions/import-logs')
@authorize("system:dingtalk:quality_exceptions:import", log=True)
def get_import_logs():
    """获取导入日志"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))

        # 查询导入日志
        query = QualityExceptionImportLog.query

        # 非管理员只能查看自己的导入日志
        if current_user.username != 'admin':
            query = query.filter_by(import_user_id=current_user.id)

        logs = query.order_by(desc(QualityExceptionImportLog.created_at)).paginate(
            page=page, per_page=limit, error_out=False
        )

        return table_api(
            data=[log.to_dict() for log in logs.items],
            count=logs.total,
            msg='获取导入日志成功'
        )

    except Exception as e:
        return fail_api(msg=f'获取导入日志失败: {str(e)}')

