import datetime
from applications.extensions import db
from flask_login import UserMixin


class QualityException(db.Model, UserMixin):
    """质量异常单数据模型"""
    __tablename__ = 'quality_exception'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='质量异常单ID')
    
    # 钉钉审批相关字段
    process_instance_id = db.Column(db.String(100), unique=True, nullable=False, comment='钉钉审批实例ID')
    business_id = db.Column(db.String(100), nullable=True, comment='业务ID')
    title = db.Column(db.String(255), nullable=False, comment='异常单标题')
    originator_userid = db.Column(db.String(100), nullable=False, comment='发起人钉钉用户ID')
    originator_name = db.Column(db.String(50), nullable=True, comment='发起人姓名')
    originator_dept_id = db.Column(db.String(100), nullable=True, comment='发起人钉钉部门ID')
    originator_dept_name = db.Column(db.String(100), nullable=True, comment='发起人部门名称')
    originator_local_dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=True, comment='发起人系统部门ID')
    
    # 审批状态相关
    approval_status = db.Column(db.String(50), nullable=False, comment='审批状态')
    create_time = db.Column(db.DateTime, nullable=False, comment='创建时间')
    finish_time = db.Column(db.DateTime, nullable=True, comment='完成时间')
    
    # 质量异常单业务字段
    exception_type = db.Column(db.String(100), nullable=True, comment='异常类型')
    severity_level = db.Column(db.String(50), nullable=True, comment='严重程度')
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), nullable=True, comment='关联项目ID')
    project_type = db.Column(db.String(50), nullable=True, comment='项目类型')
    project_code = db.Column(db.String(100), nullable=True, comment='项目编号')
    machine_number = db.Column(db.String(100), nullable=True, comment='机台编号')
    part_drawing_number = db.Column(db.String(100), nullable=True, comment='料件图号')
    exception_quantity = db.Column(db.Integer, nullable=True, comment='异常数量')
    estimated_hours = db.Column(db.Float, nullable=True, comment='预估工时(小时)')
    responsible_dept_id = db.Column(db.Integer, db.ForeignKey('admin_dept.id'), nullable=True, comment='责任部门ID')
    responsible_dept_name = db.Column(db.String(100), nullable=True, comment='责任部门名称')
    responsible_person = db.Column(db.String(100), nullable=True, comment='责任人')
    
    # 异常描述和处理
    exception_description = db.Column(db.Text, nullable=True, comment='异常描述')
    root_cause = db.Column(db.Text, nullable=True, comment='根本原因')
    corrective_action = db.Column(db.Text, nullable=True, comment='纠正措施')
    preventive_action = db.Column(db.Text, nullable=True, comment='预防措施')
    
    # 处理时间相关
    discovery_time = db.Column(db.DateTime, nullable=True, comment='发现时间')
    expected_completion_time = db.Column(db.DateTime, nullable=True, comment='预期完成时间')
    actual_completion_time = db.Column(db.DateTime, nullable=True, comment='实际完成时间')
    
    # 影响评估
    quality_impact = db.Column(db.String(255), nullable=True, comment='质量影响')
    cost_impact = db.Column(db.Float, nullable=True, comment='成本影响')
    schedule_impact = db.Column(db.String(255), nullable=True, comment='进度影响')
    
    # 附件和备注
    images = db.Column(db.Text, nullable=True, comment='图片信息(JSON格式)')
    attachments = db.Column(db.Text, nullable=True, comment='附件信息(JSON格式)')
    remark = db.Column(db.Text, nullable=True, comment='备注')
    
    # 数据同步相关
    sync_time = db.Column(db.DateTime, default=datetime.datetime.now, comment='同步时间')
    sync_status = db.Column(db.Integer, default=1, comment='同步状态(1正常,0失败)')

    # 数据来源相关
    import_source = db.Column(db.String(20), default='dingtalk', comment='数据来源：dingtalk/excel/manual')
    import_user_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=True, comment='导入用户ID')
    import_time = db.Column(db.DateTime, nullable=True, comment='导入时间')

    # 系统字段
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='记录创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='记录更新时间')
    
    # 关联关系
    project = db.relationship('Import_project', backref='quality_exceptions', lazy=True)
    responsible_dept = db.relationship('Dept', foreign_keys=[responsible_dept_id], backref='responsible_quality_exceptions', lazy=True)
    originator_dept = db.relationship('Dept', foreign_keys=[originator_local_dept_id], backref='originated_quality_exceptions', lazy=True)
    import_user = db.relationship('User', foreign_keys=[import_user_id], backref='imported_quality_exceptions', lazy=True)
    
    def __repr__(self):
        return f'<QualityException {self.title}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'process_instance_id': self.process_instance_id,
            'business_id': self.business_id,
            'title': self.title,
            'originator_userid': self.originator_userid,
            'originator_name': self.originator_name,
            'originator_dept_id': self.originator_dept_id,
            'approval_status': self.approval_status,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'finish_time': self.finish_time.strftime('%Y-%m-%d %H:%M:%S') if self.finish_time else None,
            'exception_type': self.exception_type,
            'severity_level': self.severity_level,
            'project_id': self.project_id,
            'project_code': self.project_code,
            'responsible_dept_id': self.responsible_dept_id,
            'responsible_person': self.responsible_person,
            'exception_description': self.exception_description,
            'root_cause': self.root_cause,
            'corrective_action': self.corrective_action,
            'preventive_action': self.preventive_action,
            'discovery_time': self.discovery_time.strftime('%Y-%m-%d %H:%M:%S') if self.discovery_time else None,
            'expected_completion_time': self.expected_completion_time.strftime('%Y-%m-%d %H:%M:%S') if self.expected_completion_time else None,
            'actual_completion_time': self.actual_completion_time.strftime('%Y-%m-%d %H:%M:%S') if self.actual_completion_time else None,
            'quality_impact': self.quality_impact,
            'cost_impact': self.cost_impact,
            'schedule_impact': self.schedule_impact,
            'attachments': self.attachments,
            'remark': self.remark,
            'sync_time': self.sync_time.strftime('%Y-%m-%d %H:%M:%S') if self.sync_time else None,
            'sync_status': self.sync_status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
