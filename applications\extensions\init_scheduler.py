"""
定时任务调度器初始化
用于管理试用期提醒等定时任务
"""
from flask import Flask
from flask_apscheduler import APScheduler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建调度器实例
scheduler = APScheduler()

# 存储Flask应用实例，用于在定时任务中提供应用上下文
_app_instance = None


def init_scheduler(app: Flask):
    """初始化定时任务调度器"""
    global _app_instance
    try:
        # 检查是否已经初始化过
        if hasattr(scheduler, '_initialized') and scheduler._initialized:
            logger.info("定时任务调度器已经初始化，跳过重复初始化")
            return

        # 存储应用实例
        _app_instance = app

        # 配置调度器
        app.config.setdefault('SCHEDULER_API_ENABLED', True)
        app.config.setdefault('SCHEDULER_TIMEZONE', 'Asia/Shanghai')

        # 初始化调度器
        scheduler.init_app(app)

        # 启动调度器
        if not scheduler.running:
            scheduler.start()
            logger.info("定时任务调度器启动成功")

        # 添加试用期提醒任务
        add_probation_reminder_job()

        # 尝试添加项目成本自动计算任务（如果失败则跳过，稍后通过API手动添加）
        try:
            add_auto_labor_calculation_job()
        except Exception as e:
            logger.warning(f"启动时添加自动计算任务失败，将在首次访问时初始化: {str(e)}")

        # 添加质量异常单自动同步任务
        try:
            add_quality_exception_sync_job()
        except Exception as e:
            logger.warning(f"启动时添加质量异常单同步任务失败: {str(e)}")

        # 添加部门工时单位成本计算任务
        try:
            add_dept_unit_cost_calculation_job()
        except Exception as e:
            logger.warning(f"启动时添加部门工时单位成本计算任务失败: {str(e)}")

        # 初始化钉钉同步配置
        try:
            init_dingtalk_sync_config()
        except Exception as e:
            logger.warning(f"启动时初始化钉钉同步配置失败: {str(e)}")

        # 标记已初始化
        scheduler._initialized = True

    except Exception as e:
        logger.error(f"初始化定时任务调度器失败: {str(e)}")


def probation_reminder_job_with_context():
    """带应用上下文的试用期提醒任务包装函数"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法执行定时任务")
        return 0

    with _app_instance.app_context():
        try:
            from applications.services.probation_service import check_probation_reminders
            return check_probation_reminders()
        except Exception as e:
            logger.error(f"执行试用期提醒任务失败: {str(e)}")
            return 0


def add_probation_reminder_job():
    """添加试用期提醒定时任务"""
    try:
        # 检查任务是否已存在
        if scheduler.get_job('probation_reminder_job'):
            logger.info("试用期提醒任务已存在，跳过添加")
            return

        # 添加定时任务：每天上午10点10分执行
        scheduler.add_job(
            id='probation_reminder_job',
            func=probation_reminder_job_with_context,
            trigger='cron',
            hour=10,
            minute=10,
            name='试用期到期提醒检查',
            replace_existing=True,
            misfire_grace_time=300  # 允许5分钟的延迟执行
        )

        logger.info("试用期提醒定时任务添加成功 - 每天上午10:10执行")

    except Exception as e:
        logger.error(f"添加试用期提醒定时任务失败: {str(e)}")


def remove_probation_reminder_job():
    """移除试用期提醒定时任务"""
    try:
        if scheduler.get_job('probation_reminder_job'):
            scheduler.remove_job('probation_reminder_job')
            logger.info("试用期提醒定时任务已移除")
        else:
            logger.info("试用期提醒定时任务不存在")
    except Exception as e:
        logger.error(f"移除试用期提醒定时任务失败: {str(e)}")


def get_scheduler_status():
    """获取调度器状态信息"""
    try:
        jobs = scheduler.get_jobs()
        status = {
            'running': scheduler.running,
            'jobs_count': len(jobs),
            'jobs': []
        }
        
        for job in jobs:
            job_info = {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                'trigger': str(job.trigger)
            }
            status['jobs'].append(job_info)
        
        return status
        
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        return {'error': str(e)}


def manual_trigger_probation_check():
    """手动触发试用期检查"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法执行手动检查")
        return 0

    with _app_instance.app_context():
        try:
            from applications.services.probation_service import check_probation_reminders
            result = check_probation_reminders()
            logger.info(f"手动触发试用期检查完成，发送提醒数量: {result}")
            return result
        except Exception as e:
            logger.error(f"手动触发试用期检查失败: {str(e)}")
            return 0


def auto_labor_calculation_job_with_context(trigger_type='auto', operator_id=None, operator_name=None):
    """带应用上下文的项目成本自动计算任务包装函数"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法执行项目成本计算任务")
        return {'success': False, 'error': 'Flask应用实例未初始化'}

    with _app_instance.app_context():
        try:
            from applications.services.cost_calculation import auto_calculate_all_projects_labor_cost
            from applications.services.auto_calc_config_service import AutoCalcConfigService
            from applications.models.auto_calc_log import AutoCalcLog

            # 检查是否启用自动计算
            if not AutoCalcConfigService.is_enabled():
                logger.info("自动计算功能已禁用，跳过执行")
                return {'success': True, 'message': '自动计算功能已禁用'}

            # 记录执行开始日志
            log_id = AutoCalcLog.log_execution_start(
                operator_id=operator_id,
                operator_name=operator_name,
                trigger_type=trigger_type
            ).id

            # 获取配置参数
            delay_seconds = AutoCalcConfigService.get_delay_seconds()

            logger.info("开始执行项目成本自动计算任务")
            result = auto_calculate_all_projects_labor_cost(delay_seconds=delay_seconds)

            # 记录执行结果日志
            AutoCalcLog.log_execution_result(
                log_id=log_id,
                total_projects=result.get('total_projects', 0),
                success_count=result.get('success_count', 0),
                failed_count=result.get('failed_count', 0),
                execution_duration=result.get('duration', ''),
                error_message=result.get('error') if not result['success'] else None
            )

            if result['success']:
                logger.info(f"项目成本自动计算任务完成: {result['message']}")
            else:
                logger.error(f"项目成本自动计算任务失败: {result.get('error', '未知错误')}")

            return result

        except Exception as e:
            logger.error(f"执行项目成本自动计算任务时发生异常: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {'success': False, 'error': str(e)}


def add_auto_labor_calculation_job():
    """添加项目成本自动计算定时任务"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法添加自动计算定时任务")
        return

    with _app_instance.app_context():
        try:
            from applications.services.auto_calc_config_service import AutoCalcConfigService

            # 初始化配置数据（如果不存在）
            AutoCalcConfigService.init_config_data()

            # 检查是否启用
            if not AutoCalcConfigService.is_enabled():
                logger.info("自动计算功能已禁用，不添加定时任务")
                return

            # 获取调度配置
            hour, minute = AutoCalcConfigService.get_schedule_time()
            interval_days = AutoCalcConfigService.get_interval_days()

            # 根据间隔天数选择触发器类型
            if interval_days == 1:
                # 每天执行，使用cron触发器
                scheduler.add_job(
                    id='auto_labor_calculation_job',
                    func=auto_labor_calculation_job_with_context,
                    trigger='cron',
                    hour=hour,
                    minute=minute,
                    name='项目成本自动计算',
                    replace_existing=True,
                    misfire_grace_time=1800  # 允许30分钟的延迟执行
                )
                logger.info(f"项目成本自动计算定时任务添加成功 - 每天{hour:02d}:{minute:02d}执行")
            else:
                # 间隔执行，使用interval触发器
                import datetime
                # 计算下次执行时间（从今天的指定时间开始）
                now = datetime.datetime.now()
                start_date = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                if start_date <= now:
                    # 如果今天的执行时间已过，从明天开始
                    start_date += datetime.timedelta(days=1)

                scheduler.add_job(
                    id='auto_labor_calculation_job',
                    func=auto_labor_calculation_job_with_context,
                    trigger='interval',
                    days=interval_days,
                    start_date=start_date,
                    name='项目成本自动计算',
                    replace_existing=True,
                    misfire_grace_time=1800  # 允许30分钟的延迟执行
                )
                logger.info(f"项目成本自动计算定时任务添加成功 - 每{interval_days}天{hour:02d}:{minute:02d}执行")

        except Exception as e:
            logger.error(f"添加项目成本自动计算定时任务失败: {str(e)}")


def remove_auto_labor_calculation_job():
    """移除项目成本自动计算定时任务"""
    try:
        if scheduler.get_job('auto_labor_calculation_job'):
            scheduler.remove_job('auto_labor_calculation_job')
            logger.info("项目成本自动计算定时任务已移除")
        else:
            logger.info("项目成本自动计算定时任务不存在，无需移除")
    except Exception as e:
        logger.error(f"移除项目成本自动计算定时任务失败: {str(e)}")


def quality_exception_sync_job_with_context():
    """带应用上下文的质量异常单同步任务"""
    global _app_instance
    if _app_instance is None:
        logger.error("应用实例未初始化，无法执行质量异常单同步任务")
        return

    with _app_instance.app_context():
        try:
            from applications.services.quality_exception_sync_service import QualityExceptionSyncService
            from applications.services.dingtalk_sync_config_service import DingTalkSyncConfigService

            # 从配置服务获取同步配置
            config = DingTalkSyncConfigService.get_sync_config_dict()
            logger.info(f"使用配置执行同步任务: {config}")

            sync_service = QualityExceptionSyncService()
            result = sync_service.auto_sync_quality_exceptions_with_config(config)

            if result.get('status') == 'success':
                logger.info(f"质量异常单自动同步完成: {result.get('message')}")
            else:
                logger.error(f"质量异常单自动同步失败: {result.get('message')}")

        except Exception as e:
            logger.error(f"执行质量异常单同步任务时发生错误: {str(e)}")


def add_quality_exception_sync_job():
    """添加质量异常单自动同步定时任务"""
    try:
        # 检查任务是否已存在
        if scheduler.get_job('quality_exception_sync_job'):
            logger.info("质量异常单同步任务已存在，跳过添加")
            return

        # 添加定时任务：每2小时执行一次
        scheduler.add_job(
            id='quality_exception_sync_job',
            func=quality_exception_sync_job_with_context,
            trigger='interval',
            hours=2,
            name='质量异常单自动同步',
            replace_existing=True,
            misfire_grace_time=600  # 允许10分钟的延迟执行
        )

        logger.info("质量异常单自动同步定时任务添加成功 - 每2小时执行一次")

    except Exception as e:
        logger.error(f"添加质量异常单同步定时任务失败: {str(e)}")


def remove_quality_exception_sync_job():
    """移除质量异常单自动同步定时任务"""
    try:
        if scheduler.get_job('quality_exception_sync_job'):
            scheduler.remove_job('quality_exception_sync_job')
            logger.info("质量异常单自动同步定时任务已移除")
        else:
            logger.info("质量异常单自动同步定时任务不存在，无需移除")
    except Exception as e:
        logger.error(f"移除质量异常单自动同步定时任务失败: {str(e)}")


def init_dingtalk_sync_config():
    """初始化钉钉同步配置"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法初始化钉钉同步配置")
        return

    with _app_instance.app_context():
        try:
            from applications.services.dingtalk_sync_config_service import DingTalkSyncConfigService

            # 初始化配置数据（如果不存在）
            DingTalkSyncConfigService.init_config_data()
            logger.info("钉钉同步配置初始化完成")

            # 如果启用了定时同步，尝试加载并启动任务
            if DingTalkSyncConfigService.is_enabled():
                config = DingTalkSyncConfigService.get_sync_config_dict()
                add_quality_exception_sync_job_with_config(config)
                logger.info("已加载钉钉同步定时任务配置")

        except Exception as e:
            logger.error(f"初始化钉钉同步配置失败: {str(e)}")


def add_quality_exception_sync_job_with_config(config):
    """根据配置添加质量异常单自动同步定时任务"""
    try:
        # 移除现有任务
        remove_quality_exception_sync_job()

        # 解析配置
        frequency_type = config.get('frequency_type', 'daily')
        schedule_time = config.get('schedule_time', '02:00')

        # 解析时间
        try:
            hour, minute = map(int, schedule_time.split(':'))
        except (ValueError, AttributeError):
            hour, minute = 2, 0
            logger.warning(f"无效的时间格式: {schedule_time}，使用默认时间 02:00")

        # 根据频率类型创建不同的触发器
        if frequency_type == 'hourly':
            # 每小时执行
            scheduler.add_job(
                id='quality_exception_sync_job',
                func=quality_exception_sync_job_with_context,
                trigger='interval',
                hours=1,
                name='质量异常单自动同步(每小时)',
                replace_existing=True,
                misfire_grace_time=600
            )
            logger.info("质量异常单自动同步定时任务添加成功 - 每小时执行")

        elif frequency_type == 'daily':
            # 每天执行
            scheduler.add_job(
                id='quality_exception_sync_job',
                func=quality_exception_sync_job_with_context,
                trigger='cron',
                hour=hour,
                minute=minute,
                name='质量异常单自动同步(每天)',
                replace_existing=True,
                misfire_grace_time=600
            )
            logger.info(f"质量异常单自动同步定时任务添加成功 - 每天{hour:02d}:{minute:02d}执行")

        elif frequency_type == 'weekly':
            # 每周执行
            week_day = int(config.get('week_day', 6))  # 默认周日
            scheduler.add_job(
                id='quality_exception_sync_job',
                func=quality_exception_sync_job_with_context,
                trigger='cron',
                day_of_week=week_day,
                hour=hour,
                minute=minute,
                name='质量异常单自动同步(每周)',
                replace_existing=True,
                misfire_grace_time=600
            )
            week_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            logger.info(f"质量异常单自动同步定时任务添加成功 - 每周{week_names[week_day]}{hour:02d}:{minute:02d}执行")

        elif frequency_type == 'monthly':
            # 每月执行
            month_day = config.get('month_day', 1)
            if month_day == 'last':
                # 月末执行
                scheduler.add_job(
                    id='quality_exception_sync_job',
                    func=quality_exception_sync_job_with_context,
                    trigger='cron',
                    day='last',
                    hour=hour,
                    minute=minute,
                    name='质量异常单自动同步(每月月末)',
                    replace_existing=True,
                    misfire_grace_time=600
                )
                logger.info(f"质量异常单自动同步定时任务添加成功 - 每月月末{hour:02d}:{minute:02d}执行")
            else:
                # 指定日期执行
                day = int(month_day)
                scheduler.add_job(
                    id='quality_exception_sync_job',
                    func=quality_exception_sync_job_with_context,
                    trigger='cron',
                    day=day,
                    hour=hour,
                    minute=minute,
                    name=f'质量异常单自动同步(每月{day}号)',
                    replace_existing=True,
                    misfire_grace_time=600
                )
                logger.info(f"质量异常单自动同步定时任务添加成功 - 每月{day}号{hour:02d}:{minute:02d}执行")

    except Exception as e:
        logger.error(f"添加质量异常单同步定时任务失败: {str(e)}")


def manual_trigger_auto_labor_calculation(operator_id=None, operator_name=None):
    """手动触发项目成本自动计算任务"""
    try:
        logger.info("手动触发项目成本自动计算任务")
        result = auto_labor_calculation_job_with_context(
            trigger_type='manual',
            operator_id=operator_id,
            operator_name=operator_name
        )
        return result
    except Exception as e:
        logger.error(f"手动触发项目成本自动计算任务失败: {str(e)}")
        return {'success': False, 'error': str(e)}


def update_auto_labor_calculation_job():
    """更新项目成本自动计算定时任务（配置变更后调用）"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法更新自动计算定时任务")
        return False

    try:
        # 先移除现有任务
        remove_auto_labor_calculation_job()

        # 重新添加任务（会读取最新配置）
        add_auto_labor_calculation_job()

        logger.info("项目成本自动计算定时任务更新成功")
        return True

    except Exception as e:
        logger.error(f"更新项目成本自动计算定时任务失败: {str(e)}")
        return False


def dept_unit_cost_calculation_job_with_context():
    """带应用上下文的部门工时单位成本计算任务"""
    global _app_instance
    if _app_instance is None:
        logger.error("应用实例未初始化，无法执行部门工时单位成本计算任务")
        return {'success': False, 'error': '应用实例未初始化'}

    with _app_instance.app_context():
        try:
            from applications.services.dept_unit_cost_service import DeptUnitCostService
            from applications.services.dept_unit_cost_config_service import DeptUnitCostConfigService

            # 检查是否启用
            if not DeptUnitCostConfigService.is_enabled():
                logger.info("部门工时单位成本计算任务已禁用，跳过执行")
                return {'success': True, 'message': '任务已禁用'}

            # 获取配置
            schedule_config = DeptUnitCostConfigService.get_schedule_config()
            months_span = schedule_config.get('months_span', 2)

            logger.info("开始执行部门工时单位成本计算任务")

            # 获取所有活跃部门
            dept_ids = DeptUnitCostService.get_all_active_depts()
            if not dept_ids:
                logger.warning("没有找到活跃的部门")
                return {'success': False, 'error': '没有找到活跃的部门'}

            # 生成最近几个月的年月列表
            year_months = DeptUnitCostService.generate_recent_months(months_span)
            if not year_months:
                logger.warning("没有生成有效的年月列表")
                return {'success': False, 'error': '没有生成有效的年月列表'}

            # 批量计算
            result = DeptUnitCostService.batch_calculate_dept_monthly_unit_cost(dept_ids, year_months)

            if result['success']:
                logger.info(f"部门工时单位成本计算任务完成: 总计{result['total_count']}项，成功{result['success_count']}项，失败{result['failed_count']}项")
            else:
                logger.error(f"部门工时单位成本计算任务失败: {result.get('errors', [])}")

            return result

        except Exception as e:
            logger.error(f"执行部门工时单位成本计算任务时发生异常: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {'success': False, 'error': str(e)}


def add_dept_unit_cost_calculation_job():
    """添加部门工时单位成本计算定时任务"""
    global _app_instance
    if _app_instance is None:
        logger.error("Flask应用实例未初始化，无法添加部门工时单位成本计算定时任务")
        return

    with _app_instance.app_context():
        try:
            from applications.services.dept_unit_cost_config_service import DeptUnitCostConfigService

            # 初始化配置数据（如果不存在）
            DeptUnitCostConfigService.init_config_data()

            # 检查是否启用
            if not DeptUnitCostConfigService.is_enabled():
                logger.info("部门工时单位成本计算功能已禁用，不添加定时任务")
                return

            # 获取调度配置
            schedule_config = DeptUnitCostConfigService.get_schedule_config()
            frequency = schedule_config['frequency']
            hour = schedule_config['hour']
            minute = schedule_config['minute']
            day_of_month = schedule_config['day_of_month']
            day_of_week = schedule_config['day_of_week']

            # 根据频率类型选择触发器
            if frequency == 'daily':
                # 每天执行
                scheduler.add_job(
                    id='dept_unit_cost_calculation_job',
                    func=dept_unit_cost_calculation_job_with_context,
                    trigger='cron',
                    hour=hour,
                    minute=minute,
                    name='部门工时单位成本计算(每天)',
                    replace_existing=True,
                    misfire_grace_time=1800  # 允许30分钟的延迟执行
                )
                logger.info(f"部门工时单位成本计算定时任务添加成功 - 每天{hour:02d}:{minute:02d}执行")

            elif frequency == 'weekly':
                # 每周执行
                scheduler.add_job(
                    id='dept_unit_cost_calculation_job',
                    func=dept_unit_cost_calculation_job_with_context,
                    trigger='cron',
                    day_of_week=day_of_week,
                    hour=hour,
                    minute=minute,
                    name='部门工时单位成本计算(每周)',
                    replace_existing=True,
                    misfire_grace_time=1800
                )
                week_names = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
                logger.info(f"部门工时单位成本计算定时任务添加成功 - 每周{week_names[day_of_week]}{hour:02d}:{minute:02d}执行")

            elif frequency == 'monthly':
                # 每月执行
                scheduler.add_job(
                    id='dept_unit_cost_calculation_job',
                    func=dept_unit_cost_calculation_job_with_context,
                    trigger='cron',
                    day=day_of_month,
                    hour=hour,
                    minute=minute,
                    name='部门工时单位成本计算(每月)',
                    replace_existing=True,
                    misfire_grace_time=1800
                )
                logger.info(f"部门工时单位成本计算定时任务添加成功 - 每月{day_of_month}号{hour:02d}:{minute:02d}执行")

        except Exception as e:
            logger.error(f"添加部门工时单位成本计算定时任务失败: {str(e)}")


def remove_dept_unit_cost_calculation_job():
    """移除部门工时单位成本计算定时任务"""
    try:
        if scheduler.get_job('dept_unit_cost_calculation_job'):
            scheduler.remove_job('dept_unit_cost_calculation_job')
            logger.info("部门工时单位成本计算定时任务已移除")
        else:
            logger.info("部门工时单位成本计算定时任务不存在，无需移除")
    except Exception as e:
        logger.error(f"移除部门工时单位成本计算定时任务失败: {str(e)}")


def update_dept_unit_cost_calculation_job():
    """更新部门工时单位成本计算定时任务"""
    try:
        # 先移除现有任务
        remove_dept_unit_cost_calculation_job()
        # 重新添加任务
        add_dept_unit_cost_calculation_job()
        logger.info("部门工时单位成本计算定时任务更新成功")
        return True
    except Exception as e:
        logger.error(f"更新部门工时单位成本计算定时任务失败: {str(e)}")
        return False


def manual_trigger_dept_unit_cost_calculation():
    """手动触发部门工时单位成本计算任务"""
    try:
        logger.info("手动触发部门工时单位成本计算任务")
        result = dept_unit_cost_calculation_job_with_context()
        return result
    except Exception as e:
        logger.error(f"手动触发部门工时单位成本计算任务失败: {str(e)}")
        return {'success': False, 'error': str(e)}


# 导出调度器实例供其他模块使用
__all__ = ['scheduler', 'init_scheduler', 'add_probation_reminder_job',
           'remove_probation_reminder_job', 'get_scheduler_status',
           'manual_trigger_probation_check', 'probation_reminder_job_with_context',
           'add_auto_labor_calculation_job', 'remove_auto_labor_calculation_job',
           'manual_trigger_auto_labor_calculation', 'auto_labor_calculation_job_with_context',
           'update_auto_labor_calculation_job', 'add_quality_exception_sync_job_with_config',
           'add_quality_exception_sync_job', 'remove_quality_exception_sync_job',
           'init_dingtalk_sync_config', 'add_dept_unit_cost_calculation_job',
           'remove_dept_unit_cost_calculation_job', 'update_dept_unit_cost_calculation_job',
           'manual_trigger_dept_unit_cost_calculation', 'dept_unit_cost_calculation_job_with_context']
