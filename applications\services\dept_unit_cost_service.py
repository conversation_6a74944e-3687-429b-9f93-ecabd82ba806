"""
部门工时单位成本计算服务
负责计算和管理部门月度工时单位成本数据
"""
import datetime
import logging
from typing import List, Dict, Optional, Tuple
from sqlalchemy import func, and_, or_, extract
from applications.extensions import db
from applications.models.dept_monthly_unit_cost import DeptMonthlyUnitCost
from applications.models.admin_dept import Dept
from applications.models.admin_yg import ygong
from applications.models.employee_salary import EmployeeSalary
from applications.models.save_log import LogInfo

# 配置日志
logger = logging.getLogger(__name__)


class DeptUnitCostService:
    """部门工时单位成本计算服务类"""
    
    @staticmethod
    def calculate_dept_monthly_unit_cost(dept_id: int, year_month: str) -> Dict:
        """计算指定部门指定月份的工时单位成本
        
        Args:
            dept_id: 部门ID
            year_month: 年月字符串，格式如 '2024-07'
            
        Returns:
            dict: 计算结果
        """
        try:
            # 解析年月
            year, month = map(int, year_month.split('-'))
            
            # 获取部门信息
            dept = Dept.query.get(dept_id)
            if not dept:
                return {
                    'success': False,
                    'error': f'部门ID {dept_id} 不存在'
                }
            
            # 获取该部门的所有员工
            employees = ygong.query.filter_by(dept_id=dept_id).all()
            if not employees:
                return {
                    'success': False,
                    'error': f'部门 {dept.dept_name} 没有员工数据'
                }
            
            employee_ids = [emp.id for emp in employees]
            
            # 计算该月的总应发工资
            # 注意：数据库中的月份格式可能是 'YYYY-M'（没有前导零），需要兼容处理
            year_month_alt = f"{year}-{month}"  # 生成没有前导零的格式，如 '2025-5'
            total_should_pay = db.session.query(
                func.sum(EmployeeSalary.should_pay)
            ).filter(
                EmployeeSalary.employee_id.in_(employee_ids),
                or_(
                    EmployeeSalary.month == year_month,      # 'YYYY-MM' 格式
                    EmployeeSalary.month == year_month_alt   # 'YYYY-M' 格式
                )
            ).scalar() or 0
            
            # 计算该月的总工时（正常工时+加班工时，排除会议、培训等）
            # 注意：LogInfo.employee_id 对应 ygong.employee_id（员工工号），不是 ygong.id
            employee_codes = [emp.employee_id for emp in employees if emp.employee_id]
            total_work_hours = db.session.query(
                func.sum(LogInfo.regularWorkingHours + LogInfo.overtimeWorkingHours)
            ).filter(
                LogInfo.employee_id.in_(employee_codes),
                extract('year', LogInfo.work_date) == year,
                extract('month', LogInfo.work_date) == month,
                ~LogInfo.projectPrefix.in_(['会议', '其它', '方案支持', '培训'])
            ).scalar() or 0
            
            # 计算单位成本
            unit_cost_per_hour = 0
            if total_work_hours > 0:
                unit_cost_per_hour = total_should_pay / total_work_hours
            
            # 统计参与计算的员工数量（有工资数据的员工）
            employee_count = db.session.query(
                func.count(EmployeeSalary.employee_id)
            ).filter(
                EmployeeSalary.employee_id.in_(employee_ids),
                or_(
                    EmployeeSalary.month == year_month,      # 'YYYY-MM' 格式
                    EmployeeSalary.month == year_month_alt   # 'YYYY-M' 格式
                )
            ).scalar() or 0
            
            return {
                'success': True,
                'dept_id': dept_id,
                'dept_name': dept.dept_name,
                'year_month': year_month,
                'total_should_pay': round(total_should_pay, 2),
                'total_work_hours': round(total_work_hours, 2),
                'unit_cost_per_hour': round(unit_cost_per_hour, 2),
                'employee_count': employee_count
            }
            
        except Exception as e:
            logger.error(f"计算部门 {dept_id} 月度工时单位成本失败: {str(e)}")
            return {
                'success': False,
                'error': f'计算失败: {str(e)}'
            }
    
    @staticmethod
    def batch_calculate_dept_monthly_unit_cost(dept_ids: List[int], year_months: List[str]) -> Dict:
        """批量计算多个部门多个月份的工时单位成本
        
        Args:
            dept_ids: 部门ID列表
            year_months: 年月列表，格式如 ['2024-06', '2024-07']
            
        Returns:
            dict: 批量计算结果
        """
        results = {
            'success': True,
            'total_count': 0,
            'success_count': 0,
            'failed_count': 0,
            'details': [],
            'errors': []
        }
        
        try:
            for dept_id in dept_ids:
                for year_month in year_months:
                    results['total_count'] += 1
                    
                    # 计算单个部门月度成本
                    calc_result = DeptUnitCostService.calculate_dept_monthly_unit_cost(dept_id, year_month)
                    
                    if calc_result['success']:
                        # 保存或更新数据
                        save_result = DeptUnitCostService.save_unit_cost_data(calc_result)
                        if save_result['success']:
                            results['success_count'] += 1
                            results['details'].append({
                                'dept_id': dept_id,
                                'year_month': year_month,
                                'status': 'success',
                                'unit_cost': calc_result['unit_cost_per_hour']
                            })
                        else:
                            results['failed_count'] += 1
                            results['errors'].append(f"部门{dept_id} {year_month}: {save_result['error']}")
                    else:
                        results['failed_count'] += 1
                        results['errors'].append(f"部门{dept_id} {year_month}: {calc_result['error']}")
            
            if results['failed_count'] > 0:
                results['success'] = False
                
        except Exception as e:
            logger.error(f"批量计算部门工时单位成本失败: {str(e)}")
            results['success'] = False
            results['errors'].append(f'批量计算失败: {str(e)}')
        
        return results
    
    @staticmethod
    def save_unit_cost_data(calc_result: Dict) -> Dict:
        """保存或更新工时单位成本数据
        
        Args:
            calc_result: 计算结果字典
            
        Returns:
            dict: 保存结果
        """
        try:
            # 查找是否已存在记录
            existing_record = DeptMonthlyUnitCost.query.filter_by(
                dept_id=calc_result['dept_id'],
                year_month=calc_result['year_month']
            ).first()
            
            if existing_record:
                # 更新现有记录
                existing_record.total_should_pay = calc_result['total_should_pay']
                existing_record.total_work_hours = calc_result['total_work_hours']
                existing_record.unit_cost_per_hour = calc_result['unit_cost_per_hour']
                existing_record.employee_count = calc_result['employee_count']
                existing_record.calculation_date = datetime.datetime.now()
                existing_record.status = 1
                existing_record.updated_at = datetime.datetime.now()
            else:
                # 创建新记录
                new_record = DeptMonthlyUnitCost(
                    dept_id=calc_result['dept_id'],
                    year_month=calc_result['year_month'],
                    total_should_pay=calc_result['total_should_pay'],
                    total_work_hours=calc_result['total_work_hours'],
                    unit_cost_per_hour=calc_result['unit_cost_per_hour'],
                    employee_count=calc_result['employee_count'],
                    calculation_date=datetime.datetime.now(),
                    status=1
                )
                db.session.add(new_record)
            
            db.session.commit()
            
            return {
                'success': True,
                'message': '数据保存成功'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存工时单位成本数据失败: {str(e)}")
            return {
                'success': False,
                'error': f'保存失败: {str(e)}'
            }
    
    @staticmethod
    def get_all_active_depts() -> List[int]:
        """获取所有活跃部门的ID列表
        
        Returns:
            List[int]: 部门ID列表
        """
        depts = Dept.query.filter_by(status=1).all()
        return [dept.id for dept in depts]
    
    @staticmethod
    def generate_recent_months(months_count: int = 2) -> List[str]:
        """生成最近几个月的年月列表（不包含当前月份）

        Args:
            months_count: 月份数量，默认2个月

        Returns:
            List[str]: 年月列表，格式如 ['2024-06', '2024-07']
        """
        months = []
        current_date = datetime.datetime.now()

        # 从上个月开始计算，不包含当前月份
        for i in range(1, months_count + 1):
            # 计算目标月份：当前月份往前推i个月
            if current_date.month > i:
                target_year = current_date.year
                target_month = current_date.month - i
            else:
                # 跨年处理
                target_year = current_date.year - 1
                target_month = 12 - (i - current_date.month)

            year_month = f"{target_year}-{target_month:02d}"
            months.append(year_month)

        return sorted(months)  # 按时间顺序排序
    
    @staticmethod
    def calculate_exception_cost(dept_id: int, estimated_hours: float, discovery_time: datetime.datetime) -> float:
        """计算异常成本
        
        Args:
            dept_id: 责任部门ID
            estimated_hours: 预估工时
            discovery_time: 发现时间
            
        Returns:
            float: 异常成本
        """
        try:
            # 根据发现时间确定年月
            year_month = discovery_time.strftime('%Y-%m')
            
            # 获取部门工时单位成本
            unit_cost = DeptMonthlyUnitCost.get_unit_cost(dept_id, year_month)
            
            # 计算异常成本
            exception_cost = estimated_hours * unit_cost
            
            return round(exception_cost, 2)
            
        except Exception as e:
            logger.error(f"计算异常成本失败: {str(e)}")
            return 0
