layui.use(['table', 'form', 'layer', 'upload', 'laydate', 'jquery'], function() {
    const table = layui.table;
    const form = layui.form;
    const layer = layui.layer;
    const upload = layui.upload;
    const laydate = layui.laydate;
    const $ = layui.jquery;

    const MODULE_PATH = '/system/yg_info/social_insurance/';

    // 初始化日期选择器
    laydate.render({
        elem: '#filterMonth',
        type: 'month',
        format: 'yyyy-MM'
    });

    laydate.render({
        elem: '#singleMonthInput',
        type: 'month',
        format: 'yyyy-MM',
        value: new Date()
    });

    // 渲染表格
    function renderTable() {
        table.render({
            elem: '#socialInsuranceTable',
            url: MODULE_PATH + 'data',
            page: true,
            cols: [[
                {field: 'id', width: 80, title: 'ID', sort: true},
                {field: 'employee_name', width: 120, title: '员工姓名'},
                {field: 'employee_id', width: 120, title: '工号'},
                {field: 'dept_name', width: 150, title: '部门'},
                {field: 'social_insurance_deduction', width: 140, title: '社保单位扣款', templet: function(d) {
                    return '¥' + (d.social_insurance_deduction || 0).toFixed(2);
                }},
                {field: 'housing_fund_deduction', width: 140, title: '公积金单位扣款', templet: function(d) {
                    return '¥' + (d.housing_fund_deduction || 0).toFixed(2);
                }},
                {field: 'month', width: 100, title: '月份'},
                {field: 'remark', title: '备注', minWidth: 150},
                {field: 'create_at', width: 160, title: '创建时间'},
                {title: '操作', toolbar: '#barDemo', width: 100, align: 'center'}
            ]],
            skin: 'line',
            toolbar: '#toolbarDemo',
            text: {none: '暂无社保数据'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports']
        });
    }

    // 初始化表格
    renderTable();

    // 工具栏事件
    table.on('toolbar(socialInsuranceTable)', function(obj) {
        switch(obj.event) {
            case 'refresh':
                renderTable();
                updateStatistics();
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(socialInsuranceTable)', function(obj) {
        const data = obj.data;
        switch(obj.event) {
            case 'del':
                layer.confirm('确定删除这条社保数据吗？', function(index) {
                    $.ajax({
                        url: MODULE_PATH + 'remove/' + data.id,
                        type: 'DELETE',
                        success: function(result) {
                            if (result.success) {
                                layer.msg(result.msg, {icon: 1});
                                renderTable();
                                updateStatistics();
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
                break;
        }
    });

    // 查询按钮事件
    $('#searchBtn').click(function() {
        const employee_name = $('#filterEmployeeName').val();
        const filter_type = $('#filterType').val();
        const dept_id = $('#filterDept').val();
        const month = $('#filterMonth').val();

        table.reload('socialInsuranceTable', {
            where: {
                employee_name: employee_name,
                filter_type: filter_type,
                dept_id: dept_id,
                month: month
            },
            page: {
                curr: 1
            }
        });

        // 同时更新统计卡片
        const filterParams = {
            employee_name: employee_name,
            filter_type: filter_type,
            dept_id: dept_id,
            month: month
        };

        // 移除空值参数
        Object.keys(filterParams).forEach(key => {
            if (!filterParams[key]) {
                delete filterParams[key];
            }
        });

        updateStatistics(filterParams);
    });

    // 重置按钮事件
    $('#resetBtn').click(function() {
        $('#filterEmployeeName').val('');
        $('#filterType').val('');
        $('#filterDept').val('');
        $('#filterMonth').val('');
        form.render('select');
        renderTable();

        // 重置统计卡片为全部数据
        updateStatistics();
    });

    // 单个录入表单提交
    form.on('submit(singleSubmit)', function(data) {
        $.ajax({
            url: MODULE_PATH + 'save',
            type: 'POST',
            data: JSON.stringify(data.field),
            contentType: 'application/json',
            success: function(result) {
                if (result.success) {
                    layer.msg(result.msg, {icon: 1});
                    $('#singleForm')[0].reset();
                    form.render();
                    renderTable();
                    updateStatistics();
                } else {
                    layer.msg(result.msg, {icon: 2});
                }
            }
        });
        return false;
    });

    // 文件上传
    upload.render({
        elem: '#uploadBtn',
        url: MODULE_PATH + 'import',
        accept: 'file',
        exts: 'xlsx|xls',
        before: function(obj) {
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.success) {
                layer.alert(res.msg, {icon: 1}, function() {
                    renderTable();
                    updateStatistics();
                    layer.closeAll();
                });
            } else {
                layer.alert(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.closeAll('loading');
            layer.msg('上传失败', {icon: 2});
        }
    });

    // 下载模板
    $('#downloadTemplate').click(function(e) {
        e.preventDefault();

        // 创建下载链接
        const downloadUrl = MODULE_PATH + 'template/download';

        // 创建临时链接进行下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        layer.msg('模板下载中...', {icon: 1, time: 2000});
    });

    // 更新统计信息（支持筛选参数）
    function updateStatistics(filterParams) {
        const params = filterParams || {};

        $.ajax({
            url: MODULE_PATH + 'statistics',
            type: 'GET',
            data: params,
            success: function(result) {
                if (result.success) {
                    const data = result.data;

                    // 更新统计卡片数据
                    $('#totalEmployees').text(data.total_employees || 0);
                    $('#currentMonth').text(data.current_month || '-');
                    $('#totalSocialInsurance').text('¥' + (data.total_social_insurance || 0).toFixed(2));
                    $('#totalHousingFund').text('¥' + (data.total_housing_fund || 0).toFixed(2));

                    // 更新卡片标题，显示是否为筛选状态
                    if (data.is_filtered) {
                        $('.card-change').each(function() {
                            const originalText = $(this).data('original-text') || $(this).text();
                            if (!$(this).data('original-text')) {
                                $(this).data('original-text', originalText);
                            }
                            $(this).text('筛选结果 - ' + originalText);
                            $(this).css('color', '#1890ff');
                        });
                    } else {
                        $('.card-change').each(function() {
                            const originalText = $(this).data('original-text');
                            if (originalText) {
                                $(this).text(originalText);
                                $(this).css('color', '');
                            }
                        });
                    }

                    // 调试信息
                    console.log('统计数据更新:', data);

                    // 更新最后更新时间（如果有对应的元素）
                    if ($('#lastUpdate').length > 0) {
                        $('#lastUpdate').text(data.last_update || '暂无数据');
                    }

                    // 更新当月员工数（如果有对应的元素）
                    if ($('#currentMonthEmployees').length > 0) {
                        $('#currentMonthEmployees').text(data.current_month_employees || 0);
                    }
                } else {
                    console.error('获取统计数据失败:', result.msg);
                    // 设置默认值
                    $('#totalEmployees').text('0');
                    $('#currentMonth').text(new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0'));
                    $('#totalSocialInsurance').text('¥0.00');
                    $('#totalHousingFund').text('¥0.00');
                }
            },
            error: function() {
                console.error('获取统计数据请求失败');
                // 设置默认值
                $('#totalEmployees').text('0');
                $('#currentMonth').text(new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0'));
                $('#totalSocialInsurance').text('¥0.00');
                $('#totalHousingFund').text('¥0.00');
            }
        });
    }

    // 页面加载时更新统计信息
    updateStatistics();

    // 获取当前筛选参数的辅助函数
    function getCurrentFilterParams() {
        const params = {
            employee_name: $('#filterEmployeeName').val(),
            filter_type: $('#filterType').val(),
            dept_id: $('#filterDept').val(),
            month: $('#filterMonth').val()
        };

        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (!params[key]) {
                delete params[key];
            }
        });

        return params;
    }

    // 页面加载完成后自动查询数据
    $(document).ready(function() {
        renderTable();
        updateStatistics();
    });
});
