<!DOCTYPE html>
<html>
<head>
    <title>导入其他成本</title>
    {% include 'system/common/header.html' %}
    <style>
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }

        .layui-card {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .upload-tips {
            background: #f8f9fa;
            border-left: 4px solid #009688;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .upload-tips h3 {
            margin: 0 0 10px 0;
            color: #009688;
            font-size: 16px;
        }

        .upload-tips p {
            margin: 5px 0;
            color: #666;
            line-height: 1.6;
        }

        .upload-form {
            padding: 20px 0;
        }

        .batch-import-tips {
            color: #FF5722;
            margin-top: 10px;
        }

        .import-result {
            margin-top: 15px;
            display: none;
        }

        .import-result .success {
            color: #5FB878;
        }

        .import-result .error {
            color: #FF5722;
        }

        .file-name {
            margin-top: 5px;
            color: #1E9FFF;
        }

        .layui-tab-content {
            padding-top: 20px;
        }

        .template-link {
            margin-top: 10px;
            margin-bottom: 20px;
        }

        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .layui-btn-normal {
            background: linear-gradient(135deg, #1E9FFF 0%, #1890ff 100%);
            border: none;
        }

        .layui-btn-normal:hover {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .layui-btn-primary {
            border: 1px solid #d9d9d9;
            color: #666;
        }

        .layui-btn-primary:hover {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }

        .layui-form-label {
            font-weight: 500;
            color: #333;
        }

        .layui-input, .layui-select, .layui-textarea {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-input:focus, .layui-select:focus, .layui-textarea:focus {
            border-color: #1E9FFF;
            box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
        }

        .layui-tab-title li {
            font-weight: 500;
        }

        .layui-tab-title .layui-this {
            color: #1E9FFF;
        }

        .layui-upload-drag {
            border-radius: 8px;
            border: 2px dashed #d9d9d9;
            transition: all 0.3s ease;
        }

        .layui-upload-drag:hover {
            border-color: #1E9FFF;
            background-color: #f0f8ff;
        }

        .layui-upload-drag .layui-icon {
            color: #1E9FFF;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">导入其他成本</div>
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="importTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">手动导入</li>
                    <li>批量导入</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 手动导入选项卡 -->
                    <div class="layui-tab-item layui-show">
                        <div class="upload-tips">
                            <h3>导入说明：</h3>
                            <p>1. 支持单个项目其他成本的导入</p>
                            <p>2. 导入后会自动更新项目的月度成本统计</p>
                            <p>3. 同一项目在同一月份多次导入会累加成本金额</p>
                            <p>4. 其他成本包括：差旅费、外协费、培训费、维修费等非BOM和人工成本</p>
                        </div>

                        <div class="upload-form">
                            <form class="layui-form" lay-filter="import-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">选择项目</label>
                                    <div class="layui-input-block">
                                        <select name="projectId" lay-verify="required" lay-filter="projectSelect">
                                            <option value="">请选择项目</option>
                                            {% for project in projects %}
                                            <option value="{{ project.id }}">{{ project.project_code }} - {{ project.project_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属部门</label>
                                    <div class="layui-input-block">
                                        <select name="deptId" lay-verify="required" lay-filter="deptSelect">
                                            <option value="">请选择部门</option>
                                            {% for dept in depts %}
                                            <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属年月</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="yearMonth" id="yearMonth" lay-verify="required" placeholder="请选择年月" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">成本类型</label>
                                    <div class="layui-input-block">
                                        <select name="costType" lay-verify="required" lay-filter="costTypeSelect">
                                            <option value="">请选择成本类型</option>
                                            <option value="travel">差旅费</option>
                                            <option value="outsource">外协费</option>
                                            <option value="training">培训费</option>
                                            <option value="repair">维修费</option>
                                            <option value="management">管理费</option>
                                            <option value="transportation">运杂费</option>
                                            <option value="other">其他费用</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">成本金额</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="amount" lay-verify="required|number" placeholder="请输入成本金额" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">上传文件</label>
                                    <div class="layui-input-block">
                                        <div class="layui-upload-drag" id="fileUpload">
                                            <i class="layui-icon layui-icon-upload"></i>
                                            <p>点击上传，或将文件拖拽到此处（可选）</p>
                                            <div class="layui-hide" id="uploadPreview">
                                                <hr>
                                                <img src="" alt="上传成功后渲染" style="max-width: 100%">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注说明</label>
                                    <div class="layui-input-block">
                                        <textarea name="remark" placeholder="请输入备注说明" class="layui-textarea"></textarea>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn" lay-submit lay-filter="submit-import">立即导入</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 批量导入选项卡 -->
                    <div class="layui-tab-item">
                        <div class="upload-tips">
                            <h3>批量导入说明：</h3>
                            <p>1. 支持从Excel文件批量导入多个项目的其他成本</p>
                            <p>2. 请确保Excel文件包含以下列：项目类型、项目编号、部门名称、月份、成本类型、成本金额</p>
                            <p>3. 导入后会自动更新项目的月度成本统计</p>
                            <p>4. 同一项目在同一月份多次导入会累加成本金额</p>
                            <p>5. 成本类型包括：差旅费、外协费、培训费、维修费、管理费、运杂费、其他费用</p>
                            <p>6. 部门名称必须与系统中的部门名称完全一致</p>
                        </div>

                        <div class="template-link">
                            <a href="/system/project_cost/api/import/template/download?type=other" class="layui-btn layui-btn-primary">
                                <i class="layui-icon layui-icon-template"></i> 下载导入模板
                            </a>
                        </div>

                        <div class="upload-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择文件</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload-drag" id="batchFileUpload">
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传Excel文件，或将文件拖拽到此处</p>
                                        <div class="layui-hide" id="batchUploadPreview">
                                            <hr>
                                            <img src="" alt="上传成功后渲染" style="max-width: 100%">
                                        </div>
                                    </div>
                                    <div class="batch-import-tips">
                                        <p>支持的文件格式：.xlsx, .xls</p>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="batchImportBtn">开始批量导入</button>
                                </div>
                            </div>

                            <div class="import-result" id="importResult">
                                <h3>导入结果：</h3>
                                <div class="layui-card">
                                    <div class="layui-card-body">
                                        <p>总计处理：<span id="totalCount">0</span> 条数据</p>
                                        <p class="success">成功导入：<span id="successCount">0</span> 条</p>
                                        <p class="error">导入失败：<span id="errorCount">0</span> 条</p>
                                        <div id="errorDetails" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'laydate', 'upload', 'layer', 'jquery', 'element'], function() {
        let form = layui.form;
        let laydate = layui.laydate;
        let upload = layui.upload;
        let layer = layui.layer;
        let $ = layui.jquery;
        let element = layui.element;

        // 文件对象
        let uploadFile = null;
        let batchUploadFile = null;

        // 初始化年月选择器
        laydate.render({
            elem: '#yearMonth',
            type: 'month',
            format: 'yyyy-MM'
        });

        // 单个文件上传
        upload.render({
            elem: '#fileUpload',
            url: '/system/project_cost/api/upload',
            accept: 'file',
            exts: 'xlsx|xls|csv|pdf|doc|docx',
            auto: false, // 不自动上传
            choose: function(obj) {
                // 将每次选择的文件追加到文件队列
                obj.preview(function(index, file, result) {
                    uploadFile = file;

                    // 显示文件名
                    $('#uploadPreview').removeClass('layui-hide').find('img').attr('src', '/static/system/admin/images/file.png');
                    // 移除旧的文件名显示
                    $('#uploadPreview').siblings('.file-name').remove();
                    // 添加新的文件名
                    $('#uploadPreview').after('<p class="file-name">' + file.name + '</p>');

                    layer.msg('文件已选择，点击"立即导入"按钮完成导入', {icon: 1});
                });
            }
        });

        // 批量文件上传
        upload.render({
            elem: '#batchFileUpload',
            accept: 'file',
            exts: 'xlsx|xls',
            auto: false, // 不自动上传
            choose: function(obj) {
                // 将每次选择的文件追加到文件队列
                obj.preview(function(index, file, result) {
                    batchUploadFile = file;

                    // 显示文件名
                    $('#batchUploadPreview').removeClass('layui-hide').find('img').attr('src', '/static/system/admin/images/file.png');
                    // 移除旧的文件名显示
                    $('#batchUploadPreview').siblings('.file-name').remove();
                    // 添加新的文件名
                    $('#batchUploadPreview').after('<p class="file-name">' + file.name + '</p>');

                    layer.msg('Excel文件已选择，点击"开始批量导入"按钮完成导入', {icon: 1});
                });
            }
        });

        // 单个表单提交
        form.on('submit(submit-import)', function(data) {
            // 构建FormData
            let formData = new FormData();
            formData.append('project_id', data.field.projectId);
            formData.append('dept_id', data.field.deptId);
            formData.append('year_month', data.field.yearMonth);
            formData.append('cost_type', data.field.costType);
            formData.append('amount', data.field.amount);
            formData.append('remark', data.field.remark);

            // 如果选择了文件，则添加到表单数据
            if(uploadFile) {
                formData.append('file', uploadFile);
            }

            // 显示加载中
            let loadIndex = layer.load(2);

            // 发起请求
            $.ajax({
                url: '/system/project_cost/api/import/other',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(res) {
                    layer.close(loadIndex);

                    if (res.success) {
                        layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                            // 关闭当前窗口并刷新父窗口表格
                            const index = parent.layer.getFrameIndex(window.name);
                            parent.layui.table.reload('project-table');
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('导入失败，请重试', {icon: 2});
                }
            });

            return false;
        });

        // 批量导入按钮点击事件
        $('#batchImportBtn').on('click', function() {
            if (!batchUploadFile) {
                layer.msg('请先选择Excel文件', {icon: 2});
                return;
            }

            // 构建FormData
            let formData = new FormData();
            formData.append('file', batchUploadFile);

            // 显示加载中
            let loadIndex = layer.load(2);

            // 发起请求
            $.ajax({
                url: '/system/project_cost/api/import/other/excel',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(res) {
                    layer.close(loadIndex);

                    // 显示导入结果区域
                    $('#importResult').show();

                    if (res.code === 200) {
                        // 更新导入结果统计
                        $('#totalCount').text(res.data.total);
                        $('#successCount').text(res.data.success);
                        $('#errorCount').text(res.data.error);

                        // 显示错误详情（如果有）
                        let errorDetails = $('#errorDetails');
                        errorDetails.empty();

                        if (res.data.error > 0 && res.data.error_details) {
                            errorDetails.append('<h4>错误详情：</h4>');
                            let errorList = $('<ul class="layui-text"></ul>');

                            res.data.error_details.forEach(function(error) {
                                errorList.append('<li>' + error + '</li>');
                            });

                            errorDetails.append(errorList);
                        }

                        // 如果全部成功，显示成功消息并在2秒后关闭窗口
                        if (res.data.error === 0) {
                            layer.msg('导入成功', {icon: 1, time: 2000}, function() {
                                // 刷新父窗口表格并关闭当前窗口
                                const index = parent.layer.getFrameIndex(window.name);
                                parent.layui.table.reload('project-table');
                                parent.layer.close(index);
                            });
                        } else {
                            layer.msg('部分数据导入成功，请查看详情', {icon: 0});
                        }
                    } else {
                        // 显示错误消息
                        $('#totalCount').text('0');
                        $('#successCount').text('0');
                        $('#errorCount').text('0');
                        $('#errorDetails').html('<div class="layui-text">' + res.msg + '</div>');

                        layer.msg('导入失败', {icon: 2});
                    }
                },
                error: function(xhr) {
                    layer.close(loadIndex);

                    // 显示导入结果区域
                    $('#importResult').show();

                    // 显示错误信息
                    $('#totalCount').text('0');
                    $('#successCount').text('0');
                    $('#errorCount').text('0');
                    $('#errorDetails').html('<div class="layui-text">服务器错误，请稍后重试</div>');

                    layer.msg('导入失败，请重试', {icon: 2});
                }
            });
        });
    });
</script>
</html>