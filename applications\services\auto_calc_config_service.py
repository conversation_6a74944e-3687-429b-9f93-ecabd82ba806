"""
自动计算配置管理服务
"""
import datetime
from flask import current_app
from applications.extensions import db
from applications.models.admin_dict import DictType, DictData
from applications.models.auto_calc_log import AutoCalcLog


class AutoCalcConfigService:
    """自动计算配置管理服务类"""
    
    TYPE_CODE = 'auto_labor_calc_config'
    
    # 配置项键名
    CONFIG_KEYS = {
        'enabled': 'enabled',
        'interval': 'interval',
        'hour': 'hour',
        'minute': 'minute',
        'delay': 'delay'
    }

    # 默认配置值
    DEFAULT_CONFIG = {
        'enabled': '1',  # 1-启用, 0-禁用
        'interval': '1', # 执行间隔天数
        'hour': '11',    # 执行小时
        'minute': '30',  # 执行分钟
        'delay': '5'     # 项目间延迟秒数
    }

    @classmethod
    def init_config_data(cls):
        """初始化配置数据到字典表"""
        try:
            # 检查是否在应用上下文中
            from flask import has_app_context
            if not has_app_context():
                # 如果不在应用上下文中，返回True但不执行初始化
                # 这样可以避免启动时的上下文错误
                return True

            # 检查字典类型是否存在
            dict_type = DictType.query.filter_by(type_code=cls.TYPE_CODE).first()
            if not dict_type:
                # 创建字典类型
                dict_type = DictType(
                    type_name='自动计算配置',
                    type_code=cls.TYPE_CODE,
                    description='项目成本自动计算相关配置',
                    enable=1
                )
                db.session.add(dict_type)
                db.session.flush()

            # 检查并创建配置项
            config_items = [
                {'key': 'enabled', 'label': '启用状态', 'value': '1', 'remark': '1-启用, 0-禁用'},
                {'key': 'interval', 'label': '执行间隔', 'value': '1', 'remark': '间隔天数，1表示每天执行'},
                {'key': 'hour', 'label': '执行小时', 'value': '11', 'remark': '0-23'},
                {'key': 'minute', 'label': '执行分钟', 'value': '30', 'remark': '0-59'},
                {'key': 'delay', 'label': '项目间延迟', 'value': '5', 'remark': '延迟秒数，建议5-10秒'}
            ]

            for item in config_items:
                existing = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=item['key']
                ).first()

                if not existing:
                    dict_data = DictData(
                        data_label=item['label'],
                        data_value=item['key'],
                        type_code=cls.TYPE_CODE,
                        is_default=0,
                        enable=1,
                        remark=f"{item['value']}|{item['remark']}"  # 格式：当前值|说明
                    )
                    db.session.add(dict_data)

            db.session.commit()
            current_app.logger.info("自动计算配置数据初始化完成")
            return True

        except Exception as e:
            try:
                db.session.rollback()
            except:
                pass
            # 如果有应用上下文，记录错误；否则静默处理
            try:
                current_app.logger.error(f"初始化配置数据失败: {str(e)}")
            except:
                pass
            return False

    @classmethod
    def get_config(cls, key=None):
        """获取配置值"""
        try:
            if key:
                # 获取单个配置
                dict_data = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=key,
                    enable=1
                ).first()
                
                if dict_data and dict_data.remark:
                    # 从remark中提取当前值（格式：当前值|说明）
                    current_value = dict_data.remark.split('|')[0]
                    return current_value
                else:
                    return cls.DEFAULT_CONFIG.get(key)
            else:
                # 获取所有配置
                dict_data_list = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    enable=1
                ).all()
                
                config = {}
                for data in dict_data_list:
                    if data.remark:
                        current_value = data.remark.split('|')[0]
                        config[data.data_value] = current_value
                
                # 补充默认值
                for key, default_value in cls.DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = default_value
                
                return config
                
        except Exception as e:
            current_app.logger.error(f"获取配置失败: {str(e)}")
            return cls.DEFAULT_CONFIG.get(key) if key else cls.DEFAULT_CONFIG

    @classmethod
    def update_config(cls, key, value, operator_id=None, operator_name=None, 
                     ip_address=None, user_agent=None):
        """更新配置值"""
        try:
            # 获取原值
            old_value = cls.get_config(key)
            
            # 验证配置值
            if not cls._validate_config(key, value):
                raise ValueError(f"配置值 {value} 对于 {key} 无效")
            
            # 更新数据库
            dict_data = DictData.query.filter_by(
                type_code=cls.TYPE_CODE,
                data_value=key
            ).first()
            
            if dict_data:
                # 更新现有记录
                remark_parts = dict_data.remark.split('|') if dict_data.remark else ['', '']
                remark_parts[0] = str(value)  # 更新当前值
                dict_data.remark = '|'.join(remark_parts)
                dict_data.update_time = datetime.datetime.now()
            else:
                # 创建新记录
                dict_data = DictData(
                    data_label=cls._get_config_label(key),
                    data_value=key,
                    type_code=cls.TYPE_CODE,
                    is_default=0,
                    enable=1,
                    remark=f"{value}|{cls._get_config_remark(key)}"
                )
                db.session.add(dict_data)
            
            db.session.commit()
            
            # 记录配置变更日志
            AutoCalcLog.log_config_change(
                operator_id=operator_id,
                operator_name=operator_name,
                config_key=key,
                old_value=old_value,
                new_value=value,
                description=f"配置项 {cls._get_config_label(key)} 从 {old_value} 更改为 {value}",
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            current_app.logger.info(f"配置更新成功: {key} = {value}")
            return True
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新配置失败: {str(e)}")
            raise

    @classmethod
    def _validate_config(cls, key, value):
        """验证配置值"""
        try:
            if key == 'enabled':
                return value in ['0', '1']
            elif key == 'interval':
                interval = int(value)
                return 1 <= interval <= 30  # 支持1-30天间隔
            elif key == 'hour':
                hour = int(value)
                return 0 <= hour <= 23
            elif key == 'minute':
                minute = int(value)
                return 0 <= minute <= 59
            elif key == 'delay':
                delay = int(value)
                return 1 <= delay <= 60
            return False
        except (ValueError, TypeError):
            return False

    @classmethod
    def _get_config_label(cls, key):
        """获取配置项标签"""
        labels = {
            'enabled': '启用状态',
            'interval': '执行间隔',
            'hour': '执行小时',
            'minute': '执行分钟',
            'delay': '项目间延迟'
        }
        return labels.get(key, key)

    @classmethod
    def _get_config_remark(cls, key):
        """获取配置项说明"""
        remarks = {
            'enabled': '1-启用, 0-禁用',
            'interval': '间隔天数，1表示每天执行',
            'hour': '0-23',
            'minute': '0-59',
            'delay': '延迟秒数，建议5-10秒'
        }
        return remarks.get(key, '')

    @classmethod
    def is_enabled(cls):
        """检查是否启用自动计算"""
        return cls.get_config('enabled') == '1'

    @classmethod
    def get_schedule_time(cls):
        """获取调度时间"""
        hour = int(cls.get_config('hour'))
        minute = int(cls.get_config('minute'))
        return hour, minute

    @classmethod
    def get_delay_seconds(cls):
        """获取延迟秒数"""
        return int(cls.get_config('delay'))

    @classmethod
    def get_interval_days(cls):
        """获取执行间隔天数"""
        return int(cls.get_config('interval'))

    @classmethod
    def batch_update_config(cls, config_updates, operator_id=None, operator_name=None):
        """批量更新配置值（只记录一条汇总日志）"""
        try:
            changes = []

            for key, value in config_updates.items():
                # 获取原值
                old_value = cls.get_config(key)

                # 验证配置值
                if not cls._validate_config(key, value):
                    raise ValueError(f"配置值 {value} 对于 {key} 无效")

                # 更新数据库
                dict_data = DictData.query.filter_by(
                    type_code=cls.TYPE_CODE,
                    data_value=key
                ).first()

                if dict_data:
                    # 更新现有记录
                    remark_parts = dict_data.remark.split('|') if dict_data.remark else ['', '']
                    remark_parts[0] = str(value)  # 更新当前值
                    dict_data.remark = '|'.join(remark_parts)
                    dict_data.update_time = datetime.datetime.now()
                else:
                    # 创建新记录
                    dict_data = DictData(
                        data_label=cls._get_config_label(key),
                        data_value=key,
                        type_code=cls.TYPE_CODE,
                        is_default=0,
                        enable=1,
                        remark=f"{value}|{cls._get_config_remark(key)}"
                    )
                    db.session.add(dict_data)

                # 记录变更信息
                if old_value != str(value):
                    label = cls._get_config_label(key)
                    changes.append(f"{label}: {old_value} → {value}")

            db.session.commit()

            # 只记录一条汇总日志
            if changes:
                AutoCalcLog.log_config_change(
                    operator_id=operator_id,
                    operator_name=operator_name,
                    config_key='batch_update',
                    old_value=None,
                    new_value=None,
                    description=f"批量更新配置: {'; '.join(changes)}"
                )

            current_app.logger.info(f"批量配置更新成功: {config_updates}")
            return True

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量更新配置失败: {str(e)}")
            raise
