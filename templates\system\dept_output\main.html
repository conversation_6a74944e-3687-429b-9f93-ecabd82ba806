<!DOCTYPE html>
<html>

<head>
    <title>部门单位产出分析</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}" />
    <style>
        .layui-col-md3 {
            width: 20%;
        }

        .layui-col-md4 {
            width: 25%;
        }

        .layui-card {
            margin-bottom: 15px;
        }

        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }

        .layui-card-body {
            padding: 15px;
        }

        .layui-table {
            margin-top: 0;
        }

        .layui-form-label {
            position: relative;
            float: left;
            display: block;
            padding: 9px 5px;
            width: 80px;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }

        .layui-input-block {
            margin-left: 90px;
            min-height: 36px;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            color: white;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 20px;
            opacity: 0.9;
        }

        .unit-output-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .revenue-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .expense-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .project-card {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .filter-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .export-btn {
            margin-left: 10px;
        }

        /* 简洁的悬停效果 */
        .layui-btn:hover {
            opacity: 0.9;
            transition: opacity 0.3s ease;
        }

        .layui-card {
            transition: box-shadow 0.3s ease;
        }

        .layui-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* 加载状态优化 - 确保居中 */
        .layui-layer-loading {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
        }

        .layui-layer-loading .layui-layer-content {
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            text-align: center;
        }

        /* 项目表格样式优化 */
        .layui-table {
            border-radius: 6px;
            overflow: hidden;
        }

        .layui-table thead tr {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .layui-table tbody tr:hover {
            background-color: #f0f8ff !important;
            transition: background-color 0.2s ease;
        }

        /* 分页样式优化 */
        .layui-laypage {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
        }

        .layui-laypage a, .layui-laypage span {
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .layui-laypage a:hover {
            background-color: #1E9FFF;
            color: white;
            transform: translateY(-1px);
        }

        /* 项目状态标签样式 */
        .layui-badge {
            border-radius: 12px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <!-- 筛选区域 -->
        <div class="layui-card">
            <div class="layui-card-header">筛选条件</div>
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="searchForm">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md2">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择部门</label>
                                <div class="layui-input-block">
                                    <select name="dept_id" lay-filter="deptSelect">
                                        <option value="">全部部门</option>
                                        {% for dept in depts %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目类型</label>
                                <div class="layui-input-block">
                                    <select name="project_type" lay-filter="projectTypeSelect">
                                        <option value="">全部类型</option>
                                        {% for category in project_categories %}
                                        <option value="{{ category.dept_name }}">{{ category.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目编号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="project_code" placeholder="输入项目编号" autocomplete="off"
                                        class="layui-input">
                                </div>
                            </div>
                        </div>
                        <!-- <div class="layui-col-md2">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目状态</label>
                                <div class="layui-input-block">
                                    <select name="project_status">
                                        <option value="">全部状态</option>
                                        {% for status in project_status_options %}
                                        <option value="{{ status.data_value }}">{{ status.data_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div> -->
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <div class="layui-input-block" style="margin-left: 10px; text-align: left;">
                                    <button type="button" class="layui-btn" lay-submit lay-filter="search">
                                        <i class="layui-icon layui-icon-search"></i> 查询
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-normal" id="targetManageBtn">
                                        <i class="layui-icon layui-icon-set"></i> 目标值管理
                                    </button>
                                    <!-- <button type="button" class="layui-btn layui-btn-warm" id="participationManageBtn">
                                        <i class="layui-icon layui-icon-chart-screen"></i> 产出比例统计
                                    </button> -->
                                    <button type="button" class="layui-btn layui-btn-danger" id="clearCacheBtn">
                                        <i class="layui-icon layui-icon-refresh-1"></i> 刷新缓存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 部门数据展示区域 -->
        <div id="deptDataContainer" style="display: none;">
            <!-- 这里将动态生成各部门的数据卡片和表格 -->
        </div>




    </div>

    <!-- 引入必要的JS库 -->
    <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <script>
        layui.use(['form', 'layer', 'laypage'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 项目状态映射（从后端获取）
            let projectStatusMap = {};

            // 获取项目状态字典数据
            function loadProjectStatusDict() {
                $.ajax({
                    url: '/system/dept_output/api/project_status_dict',
                    type: 'GET',
                    timeout: 5000,
                    success: function(response) {
                        if (response.success && response.data) {
                            response.data.forEach(function(item) {
                                projectStatusMap[item.data_value] = item.data_label;
                            });
                            console.log('✅ 项目状态字典加载成功');
                        } else {
                            console.warn('⚠️ 项目状态字典数据格式异常');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.warn('⚠️ 获取项目状态字典失败，将使用默认状态显示');
                        // 设置默认状态映射
                        projectStatusMap = {
                            '0': '未开始',
                            '1': '生产中',
                            '2': '调试中',
                            '3': '入库完成',
                            '4': '发货中',
                            '5': '项目完成'
                        };
                    }
                });
            }

            // 项目状态名称转换函数
            function getProjectStatusName(status) {
                return projectStatusMap[status] || `状态${status}`;
            }

            // 获取项目状态对应的颜色
            function getProjectStatusColor(status) {
                var colorMap = {
                    '1': '#FFB800',  // 未开始 - 橙色
                    '2': '#1E9FFF',  // 生产中 - 蓝色
                    '3': '#FF5722',  // 发货中 - 深橙色
                    '4': '#009688',  // 入库完成 - 青色
                    '5': '#1E9FFF',  // 验收完成 - 绿色
                    '6': '#9C27B0',   // 质保完成 - 紫色
                    '7': '#00BCD4',
                    '8': '#673AB7',
                    '9': '#673AB7',
                    '10': '#E91E63',
                    '11': '#E91E63',
                    '12': '#4CAF50'
                        
                };
                return colorMap[status] || '#999999';
            }

            // 页面加载完成后初始化
            setTimeout(function () {
                loadProjectStatusDict(); // 加载项目状态字典
                queryData(); // 查询数据
            }, 500);

            // 查询数据函数
            function queryData() {
                var field = form.val('searchForm');

                // 显示居中的加载状态
                var loadIndex = layer.load(1, {
                    shade: [0.3, '#000'],
                    shadeClose: false,
                    time: 0,
                    offset: 'auto'  // 确保居中显示
                });

                // 隐藏之前的数据
                document.getElementById('deptDataContainer').style.display = 'none';

                // 记录开始时间
                var startTime = Date.now();

                // 发送查询请求
                $.ajax({
                    url: '/system/dept_output/dept_data',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        dept_id: field.dept_id ? parseInt(field.dept_id) : null,
                        project_type: field.project_type || null,
                        project_code: field.project_code || null,
                        project_status: field.project_status || null
                    }),
                    timeout: 30000, // 30秒超时
                    success: function (response) {
                        var endTime = Date.now();
                        var duration = ((endTime - startTime) / 1000).toFixed(1);

                        // 关闭加载状态
                        layer.close(loadIndex);

                        if (response.success === true) {
                            console.log('返回数据:', response.data);
                            renderDeptData(response.data);
                            showDataSections();

                            // 显示简洁的完成提示（只有耗时超过2秒才显示）
                            if (duration > 2) {
                                layer.msg(`数据计算完成 (${duration}s)`, {
                                    icon: 1,
                                    time: 2000
                                });
                            }
                        } else {
                            layer.msg(response.msg || '查询失败', {
                                icon: 2,
                                time: 3000
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        // 关闭加载状态
                        layer.close(loadIndex);

                        var errorMsg = '网络错误，请稍后重试';

                        if (status === 'timeout') {
                            errorMsg = '查询超时，请缩小查询范围或稍后重试';
                        } else if (xhr.status === 500) {
                            errorMsg = '服务器内部错误，请联系管理员';
                        } else if (xhr.status === 0) {
                            errorMsg = '网络连接中断，请检查网络连接';
                        }

                        layer.msg(errorMsg, {
                            icon: 2,
                            time: 4000
                        });
                    }
                });
            }

            // 查询表单提交
            form.on('submit(search)', function (data) {
                queryData();
                return false;
            });

            // 目标值管理按钮点击事件
            $('#targetManageBtn').click(function () {
                layer.open({
                    type: 2,
                    title: '部门产出目标值管理',
                    area: ['90%', '90%'],
                    content: '/system/dept_output/target',
                    end: function () {
                        // 关闭弹窗后刷新数据，以显示最新的目标值对比
                        queryData();
                    }
                });
            });

            // 产出比例统计按钮点击事件
            $('#participationManageBtn').click(function () {
                layer.open({
                    type: 2,
                    title: '部门产出比例统计',
                    area: ['95%', '90%'],
                    content: '/system/dept_output/participation',
                    end: function () {
                        // 关闭弹窗后刷新数据，以显示最新的比例分摊计算结果
                        queryData();
                    }
                });
            });

            // 刷新缓存按钮点击事件
            $('#clearCacheBtn').click(function () {
                layer.confirm('确定要清理缓存吗？清理后将重新计算所有数据。', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    var loadingIndex = layer.load(1, {
                        shade: [0.3, '#000']
                    });

                    $.ajax({
                        url: '/system/dept_output/clear_cache',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({}),
                        success: function(res) {
                            layer.close(loadingIndex);
                            console.log('清理缓存响应:', res);
                            if (res.success === true) {
                                layer.msg('缓存清理成功！正在重新查询数据...', {
                                    icon: 1,
                                    time: 2000
                                });
                                // 清理缓存后自动重新查询数据
                                setTimeout(function() {
                                    queryData();
                                }, 500);
                            } else {
                                layer.msg(res.msg || '缓存清理失败', {
                                    icon: 2,
                                    time: 3000
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            layer.close(loadingIndex);
                            console.error('清理缓存请求失败:', xhr, status, error);
                            console.error('响应状态:', xhr.status);
                            console.error('响应文本:', xhr.responseText);

                            var errorMsg = '网络错误，缓存清理失败';
                            if (xhr.status === 403) {
                                errorMsg = '权限不足，无法清理缓存';
                            } else if (xhr.status === 404) {
                                errorMsg = '清理缓存接口不存在';
                            } else if (xhr.responseText) {
                                try {
                                    var errorRes = JSON.parse(xhr.responseText);
                                    errorMsg = errorRes.msg || errorMsg;
                                } catch (e) {
                                    // 解析失败，使用默认错误信息
                                }
                            }

                            layer.msg(errorMsg, {
                                icon: 2,
                                time: 4000
                            });
                        }
                    });

                    layer.close(index);
                });
            });

            // 更新关键指标
            function updateMetrics(data) {
                document.getElementById('unitOutputValue').textContent = data.unit_output.toFixed(4);
                document.getElementById('revenueValue').textContent = (data.total_project_price / 10000).toFixed(2);
                document.getElementById('expenseValue').textContent = (data.total_expense / 10000).toFixed(2);
                document.getElementById('projectCountValue').textContent = data.project_count;
            }



            // 显示数据区域
            function showDataSections() {
                document.getElementById('deptDataContainer').style.display = 'block';
            }

            // 项目表格分页渲染函数
            function renderProjectTable(deptId, projects, currentPage, pageSize) {
                currentPage = currentPage || 1;
                pageSize = pageSize || 10;

                var startIndex = (currentPage - 1) * pageSize;
                var endIndex = startIndex + pageSize;
                var currentPageProjects = projects.slice(startIndex, endIndex);

                var tableHtml = `
                    <table class="layui-table" lay-size="sm" style="margin: 0;">
                        <thead>
                            <tr style="background-color: #f8f9fa;">
                                <th style="text-align: center; font-weight: bold;">项目类型</th>
                                <th style="text-align: center; font-weight: bold;">项目编号</th>
                                <th style="text-align: center; font-weight: bold;">项目名称</th>
                                <th style="text-align: center; font-weight: bold;">项目状态</th>
                                <th style="text-align: center; font-weight: bold;">关联方式</th>
                            </tr>
                        </thead>
                        <tbody>`;

                if (currentPageProjects && currentPageProjects.length > 0) {
                    currentPageProjects.forEach(function (project, index) {
                        // 获取项目状态名称
                        var statusName = getProjectStatusName(project.project_status);

                        // 获取状态对应的颜色
                        var statusColor = getProjectStatusColor(project.project_status);

                        // 交替行背景色
                        var rowBg = index % 2 === 0 ? '' : 'background-color: #fafafa;';

                        tableHtml += `
                            <tr style="${rowBg}">
                                <td style="text-align: center;">${project.project_type || '-'}</td>
                                <td style="text-align: center; font-family: monospace;">${project.project_code || '-'}</td>
                                <td style="text-align: left; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${project.project_name || '-'}">${project.project_name || '-'}</td>
                                <td style="text-align: center;"><span class="layui-badge" style="background-color: ${statusColor};">${statusName}</span></td>
                                <td style="text-align: center;"><span class="layui-badge layui-bg-blue">${project.relation_type || '员工参与'}</span></td>
                            </tr>`;
                    });
                } else {
                    tableHtml += '<tr><td colspan="5" style="text-align: center; padding: 30px; color: #999;">暂无项目数据</td></tr>';
                }

                tableHtml += `
                        </tbody>
                    </table>`;

                document.getElementById('project-table-' + deptId).innerHTML = tableHtml;
            }

            // 项目分页控件初始化函数
            function initProjectPagination(deptId, projects, pageSize) {
                pageSize = pageSize || 10;
                var totalCount = projects ? projects.length : 0;

                var paginationContainer = document.getElementById('project-pagination-' + deptId);

                if (totalCount <= pageSize) {
                    // 如果数据不足一页，显示简单的数据统计
                    paginationContainer.innerHTML = `
                        <div style="text-align: center; color: #999; font-size: 12px; padding: 10px;">
                            共 ${totalCount} 个项目
                        </div>`;
                    return;
                }

                layui.use(['laypage'], function () {
                    var laypage = layui.laypage;

                    laypage.render({
                        elem: 'project-pagination-' + deptId,
                        count: totalCount,
                        limit: pageSize,
                        limits: [5, 10, 15, 20, 30],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                        theme: '#1E9FFF',
                        jump: function (obj, first) {
                            renderProjectTable(deptId, projects, obj.curr, obj.limit);
                            if (!first) {
                                // 重新初始化分页控件以更新每页条数
                                initProjectPagination(deptId, projects, obj.limit);
                            }
                        }
                    });
                });
            }

            // 渲染部门数据
            function renderDeptData(deptResults) {
                var container = document.getElementById('deptDataContainer');
                container.innerHTML = '';

                if (!deptResults || deptResults.length === 0) {
                    container.innerHTML = '<div class="layui-card"><div class="layui-card-body" style="text-align: center; padding: 50px;">暂无数据</div></div>';
                    return;
                }

                deptResults.forEach(function (dept) {
                    var deptHtml = `
                        <div class="layui-card" style="margin-bottom: 20px;">
                            <div class="layui-card-header" style="background: #f8f9fa; font-weight: bold;">
                                ${dept.dept_name} - 部门产出分析
                            </div>
                            <div class="layui-card-body">
                                <!-- 关键指标 -->
                                <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">
                                   <div class="layui-col-md4">
                                        <div class="metric-card unit-output-card">
                                            <div class="metric-value">${dept.unit_output.toFixed(2)}</div>
                                            <div class="metric-label">实际单位产出</div>
                                        </div>
                                    </div>
                                       <div class="layui-col-md4">
                                        <div class="metric-card revenue-card">
                                            <div class="metric-value">${dept.conversion_score !== null ? dept.conversion_score.toFixed(1) : '-'}</div>
                                            <div class="metric-label">单位成本产出考核得分</div>
                                        </div>
                                    </div>
                                  
                                    <div class="layui-col-md4">
                                        <div class="metric-card revenue-card">
                                            <div class="metric-value">${(dept.total_project_price / 10000).toFixed(2)}</div>
                                            <div class="metric-label">项目总价格 (万元)</div>
                                        </div>
                                    </div>
                                
                                         <div class="layui-col-md4">
                                        <div class="metric-card expense-card">
                                            <div class="metric-value">${(dept.total_expense / 10000).toFixed(2)}</div>
                                            <div class="metric-label">总开支 (万元)</div>
                                        </div>
                                    </div>
                              
                                </div>

                                <!-- 详细数据 -->
                                <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">
                                          <div class="layui-col-md4">
                                        <div class="metric-card target-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                            <div class="metric-value">${dept.target_output || '-'}</div>
                                            <div class="metric-label">目标单位产出</div>
                                        </div>
                                    </div>
                                 <div class="layui-col-md4">
                                        <div class="metric-card difference-card" style="background: ${dept.difference >= 0 ? 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'};">
                                            <div class="metric-value">${dept.difference ? (dept.difference >= 0 ? '+' : '') + dept.difference.toFixed(2) : '-'}</div>
                                            <div class="metric-label">目标差异</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <div class="metric-card achievement-card" style="background: ${dept.achievement_rate >= 100 ? 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' : 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'};">
                                            <div class="metric-value">${dept.achievement_rate ? dept.achievement_rate.toFixed(1) + '%' : '-'}</div>
                                            <div class="metric-label">目标完成率</div>
                                        </div>
                                    </div>
                                        <div class="layui-col-md4">
                                        <div class="metric-card project-card">
                                            <div class="metric-value">${dept.project_count}</div>
                                            <div class="metric-label">项目数量</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 项目明细表格 -->
                                <div class="layui-card">
                                    <div class="layui-card-header">项目明细</div>
                                    <div class="layui-card-body">
                                        <div id="project-table-${dept.dept_id}">
                                            <!-- 项目表格将通过JavaScript动态生成 -->
                                        </div>
                                        <div id="project-pagination-${dept.dept_id}" style="margin-top: 20px; text-align: center; border-top: 1px solid #f0f0f0; padding-top: 15px;">
                                            <!-- 分页控件将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>`;

                    container.innerHTML += deptHtml;

                    // 为当前部门初始化项目分页
                    setTimeout(function () {
                        // console.log('初始化部门分页:', dept.dept_id, '项目数量:', dept.projects ? dept.projects.length : 0);
                        renderProjectTable(dept.dept_id, dept.projects, 1, 10);
                        initProjectPagination(dept.dept_id, dept.projects, 10);
                    }, 100);
                });
            }






        });
    </script>
</body>

</html>