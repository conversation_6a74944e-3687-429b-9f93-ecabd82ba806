<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>社保导入管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='system/component/layui/css/layui.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='system/component/pear/css/pear.css') }}">
    <style>
        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            background: #f5f7fa;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
      
        /* 主容器样式 */
        .main-container {
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面标题样式 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .page-header .icon {
            margin-right: 15px;
            font-size: 32px;
        }

        .page-header .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 16px;
            font-weight: 400;
        }

        /* 统计卡片样式 */
        .stats-section {
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stats-card.primary { --card-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stats-card.success { --card-color: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stats-card.warning { --card-color: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card.info { --card-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .stats-card .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 24px;
            color: white;
            background: var(--card-color);
        }

        .stats-card .card-title {
            font-size: 14px;
            color: #8b949e;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .stats-card .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .stats-card .card-change {
            font-size: 12px;
            color: #10b981;
        }

        /* 主内容区域 */
        .content-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        /* 选项卡样式重设计 */
        .custom-tabs {
            border-bottom: 1px solid #e5e7eb;
        }

        .custom-tabs .tab-nav {
            display: flex;
            background: #f9fafb;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .custom-tabs .tab-nav li {
            flex: 1;
            text-align: center;
        }

        .custom-tabs .tab-nav li a {
            display: block;
            padding: 20px 30px;
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .custom-tabs .tab-nav li.active a,
        .custom-tabs .tab-nav li a:hover {
            color: #667eea;
            background: white;
            border-bottom-color: #667eea;
        }

        .custom-tabs .tab-content {
            padding: 30px;
        }

        /* 表单样式优化 */
        .form-section {
            background: #f9fafb;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 筛选条件一行布局样式 */
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: end;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 140px;
            flex: 1;
        }

        .filter-label {
            font-weight: 500;
            margin-bottom: 6px;
            color: #374151;
            font-size: 13px;
            white-space: nowrap;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            height: 36px;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .filter-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .filter-input:hover {
            border-color: #9ca3af;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
        }

        .filter-buttons .btn {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-buttons .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }

        .filter-buttons .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .filter-buttons .btn-secondary {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            color: #374151;
        }

        .filter-buttons .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .filter-group {
                min-width: 120px;
            }
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .filter-group {
                min-width: auto;
            }

            .filter-buttons {
                justify-content: center;
                margin-top: 10px;
            }
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* 上传区域样式 */
        .upload-section {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #9ca3af;
            margin-bottom: 16px;
        }

        .upload-text {
            color: #6b7280;
            margin-bottom: 20px;
        }

        /* 提示信息样式 */
        .tips-section {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .tips-title {
            color: #1e40af;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tips-list li {
            color: #1e40af;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .tips-list li::before {
            content: '';
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .tips-list li:nth-child(5) {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 8px 0;
            color: #92400e;
        }

        /* 表格样式优化 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .page-header {
                padding: 20px;
            }

            .page-header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>
                <i class="layui-icon layui-icon-dollar icon"></i>
                社保导入管理
            </h1>
            <div class="subtitle">管理员工社保和公积金扣款数据，支持单个录入和批量导入</div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stats-card primary">
                    <div class="card-icon">
                        <i class="layui-icon layui-icon-user"></i>
                    </div>
                    <div class="card-title">总员工数</div>
                    <div class="card-value" id="totalEmployees">-</div>
                    <div class="card-change">已录入社保数据的员工</div>
                </div>

                <div class="stats-card warning">
                    <div class="card-icon">
                        <i class="layui-icon layui-icon-date"></i>
                    </div>
                    <div class="card-title">当前月份</div>
                    <div class="card-value" id="currentMonth">-</div>
                    <div class="card-change">当前统计月份</div>
                </div>

                <div class="stats-card info">
                    <div class="card-icon">
                        <i class="layui-icon layui-icon-dollar"></i>
                    </div>
                    <div class="card-title">社保总额</div>
                    <div class="card-value" id="totalSocialInsurance">-</div>
                    <div class="card-change">当月社保扣款总额</div>
                </div>

                <div class="stats-card success">
                    <div class="card-icon">
                        <i class="layui-icon layui-icon-home"></i>
                    </div>
                    <div class="card-title">公积金总额</div>
                    <div class="card-value" id="totalHousingFund">-</div>
                    <div class="card-change">当月公积金扣款总额</div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="content-section">
            <!-- 自定义选项卡 -->
            <div class="custom-tabs">
                <ul class="tab-nav">
                    <li class="active">
                        <a href="#" data-tab="list">
                            <i class="layui-icon layui-icon-table"></i>
                            社保数据列表
                        </a>
                    </li>
                    <li>
                        <a href="#" data-tab="single">
                            <i class="layui-icon layui-icon-add-1"></i>
                            单个录入
                        </a>
                    </li>
                    <li>
                        <a href="#" data-tab="batch">
                            <i class="layui-icon layui-icon-upload"></i>
                            批量导入
                        </a>
                    </li>
                </ul>

                <div class="tab-content">
                    <!-- 社保数据列表 -->
                    <div class="tab-panel active" id="listPanel">
                        <!-- 筛选条件 -->
                        <div class="form-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="filter-label">员工姓名</label>
                                    <input type="text" name="employee_name" id="filterEmployeeName" placeholder="请输入员工姓名" class="filter-input">
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">筛选类型</label>
                                    <select name="filter_type" id="filterType" class="filter-input" lay-search>
                                        <option value="">全部</option>
                                        <option value="social_insurance">仅社保</option>
                                        <option value="housing_fund">仅公积金</option>
                                        <option value="both">社保+公积金</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">部门</label>
                                    <select name="dept_id" id="filterDept" class="filter-input" lay-search>
                                        <option value="">选择部门</option>
                                        {% for dept in depts %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">月份</label>
                                    <input type="text" name="month" id="filterMonth" placeholder="YYYY-MM" class="filter-input">
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">操作</label>
                                    <div class="filter-buttons">
                                        <button class="btn btn-primary" id="searchBtn">
                                            <i class="layui-icon layui-icon-search"></i>
                                            查询
                                        </button>
                                        <button class="btn btn-secondary" id="resetBtn">
                                            <i class="layui-icon layui-icon-refresh"></i>
                                            重置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="table-container">
                            <table class="layui-hide" id="socialInsuranceTable" lay-filter="socialInsuranceTable"></table>
                        </div>

                        <!-- 表格工具栏 -->
                        <script type="text/html" id="toolbarDemo">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="refresh">
                                    <i class="layui-icon layui-icon-refresh"></i> 刷新
                                </button>
                            </div>
                        </script>

                        <!-- 行工具栏 -->
                        <script type="text/html" id="barDemo">
                            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                        </script>
                    </div>

                    <!-- 单个录入 -->
                    <div class="tab-panel" id="singlePanel" style="display: none;margin-top: 40px;margin-bottom: 60px;">
                        <div class="form-section">
                            <h3 style="margin-bottom: 20px; color: #374151; font-weight: 600;">
                                <i class="layui-icon layui-icon-add-1"></i>
                                新增社保数据
                            </h3>

                            <form class="layui-form" id="singleForm" lay-filter="singleForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">员工姓名</label>
                                        <input type="text" name="employee_name" placeholder="请输入员工姓名"
                                               lay-verify="required" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">工号</label>
                                        <input type="text" name="employee_id" placeholder="请输入工号"
                                               lay-verify="required" class="form-input">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">部门</label>
                                        <select name="dept_id" class="form-input" lay-verify="required" lay-search>
                                            <option value="">请选择部门</option>
                                            {% for dept in depts %}
                                            <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">月份</label>
                                        <input type="text" name="month" placeholder="YYYY-MM"
                                               lay-verify="required" class="form-input" id="singleMonthInput">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">社保单位扣款</label>
                                        <input type="number" name="social_insurance_deduction" placeholder="请输入社保单位扣款"
                                               lay-verify="required|number" class="form-input"
                                               step="0.01" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">公积金单位扣款</label>
                                        <input type="number" name="housing_fund_deduction" placeholder="请输入公积金单位扣款"
                                               lay-verify="required|number" class="form-input"
                                               step="0.01" min="0">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">备注</label>
                                        <input type="text" name="remark" placeholder="请输入备注信息"
                                               class="form-input">
                                    </div>
                                </div>

                                <div style="margin-top: 30px; display: flex; gap: 15px;">
                                    <button class="btn btn-primary" lay-submit lay-filter="singleSubmit">
                                        <i class="layui-icon layui-icon-ok"></i>
                                        保存社保数据
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="layui-icon layui-icon-refresh"></i>
                                        重置表单
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 批量导入 -->
                    <div class="tab-panel" id="batchPanel" style="display: none;">
                        <!-- 导入说明 -->
                        <div class="tips-section">
                            <div class="tips-title">
                                <i class="layui-icon layui-icon-tips"></i>
                                批量导入说明
                            </div>
                            <ul class="tips-list">
                                <li>📋 支持从Excel文件批量导入员工社保数据</li>
                                <li>📊 请确保Excel文件包含以下列：员工姓名、工号、部门、社保单位扣款、公积金单位扣款、月份、备注（可选）</li>
                                <li>🏢 部门名称必须与系统中的部门名称完全一致</li>
                                <li>📅 月份格式：YYYY-MM（如：2024-01），金额字段必须为数字</li>
                                <li>⚠️ <strong>重要：同一员工同一月份的数据将被覆盖，请谨慎操作</strong></li>
                                <li>📝 建议先下载模板，按照格式填写数据后再导入</li>
                                <li>📈 导入完成后会显示详细的新增/更新统计信息</li>
                            </ul>
                        </div>

                        <!-- 模板下载 -->
                        <div style="text-align: center; margin: 30px 0;">
                            <button type="button" class="btn btn-secondary" id="downloadTemplate">
                                <i class="layui-icon layui-icon-template"></i>
                                下载导入模板
                            </button>
                        </div>

                        <!-- 上传区域 -->
                        <div class="upload-section">
                            <div class="upload-icon">
                                <i class="layui-icon layui-icon-file-b" style="color: #667eea;"></i>
                            </div>
                            <div class="upload-text">
                                <h3 style="margin: 0 0 10px 0; color: #374151;">📁 拖拽Excel文件到此处或点击选择</h3>
                                <p style="margin: 0; color: #6b7280;">支持 .xlsx 和 .xls 格式，同员工同月份数据将被覆盖</p>
                                <p style="margin: 8px 0 0 0; color: #9ca3af; font-size: 12px;">文件将立即处理，无需等待上传列表</p>
                            </div>
                            <button type="button" class="btn btn-primary" id="uploadBtn" style="margin-top: 20px;">
                                <i class="layui-icon layui-icon-upload-drag"></i>
                                选择Excel文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <script>
        // 自定义选项卡切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabLinks = document.querySelectorAll('.tab-nav a');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有活动状态
                    tabLinks.forEach(l => l.parentElement.classList.remove('active'));
                    tabPanels.forEach(p => p.style.display = 'none');

                    // 添加当前活动状态
                    this.parentElement.classList.add('active');
                    const targetTab = this.getAttribute('data-tab');
                    document.getElementById(targetTab + 'Panel').style.display = 'block';
                });
            });
        });
    </script>
    <script src="{{ url_for('static', filename='system/YG/social_insurance.js') }}"></script>
</body>
</html>
