<!DOCTYPE html>
<html>
<head>
    <title>钉钉日志管理</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .search-form .layui-form-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .search-form .layui-form-label {
            width: 80px;
            height: 38px;
            line-height: 38px;
            padding: 0 10px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }
        .search-form .layui-input-block {
            margin-left: 90px;
            flex: 1;
            display: flex;
            align-items: center;
        }
        .search-form .layui-input,
        .search-form .layui-select,
        .search-form select {
            height: 38px;
            line-height: 38px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.3s;
            width: 100%;
        }
        .search-form .layui-input:focus,
        .search-form .layui-select:focus,
        .search-form select:focus {
            border-color: #009688;
        }
        .search-form .btn-group {
            display: flex;
            align-items: center;
            gap: 10px;
            height: 38px;
        }
        .search-form .btn-group .layui-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 15px;
            margin: 0;
        }
        .table-container {
            background: #fff;
            padding: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .page-header {
            background: #fff;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
            border-left: 3px solid #009688;
        }
        .page-header h2 {
            margin: 0;
            font-size: 16px;
            color: #333;
            font-weight: 400;
        }
        .btn-group {
            text-align: right;
            margin-top: 0;
        }
        .btn-group .layui-btn {
            margin-left: 8px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-group .layui-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 表格响应式样式 */
        .table-container {
            overflow-x: auto;
            min-width: 800px;
        }

        .layui-table {
            min-width: 100%;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .table-container {
                min-width: 600px;
            }

            .search-form .layui-col-md3 {
                width: 50% !important;
                margin-bottom: 15px;
            }

            .search-form .btn-group .layui-btn {
                padding: 0 10px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .search-form .layui-col-md3 {
                width: 100% !important;
            }
        }
    </style>
</head>

<body class="pear-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h2><i class="layui-icon layui-icon-list"></i> 钉钉日志管理</h2>
      <!--   <div style="float: right; margin-top: -30px;">
            <a href="/system/dindin/failed-logs" class="layui-btn layui-btn-danger layui-btn-sm">
                <i class="layui-icon layui-icon-close-fill"></i> 失败日志管理
            </a>
        </div> -->
    </div>

    <!-- 搜索区域 -->
    <div class="search-form">
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">开始时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="start_date" id="start_date" placeholder="请选择开始时间" class="layui-input" readonly>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">结束时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="end_date" id="end_date" placeholder="请选择结束时间" class="layui-input" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">日志模板</label>
                        <div class="layui-input-block">
                            <select name="template_name" id="template_name" lay-filter="template_name" lay-search>
                                <option value="">请选择日志模板</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userid" id="userid" placeholder="请输入用户ID" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 0; padding: 0; margin: 0;"></label>
                        <div class="layui-input-block btn-group" style="margin-left: 0;">
                            <button type="button" class="layui-btn layui-btn-primary" id="search-btn">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="button" class="layui-btn layui-btn-danger" id="save-logs-btn">
                                <i class="layui-icon layui-icon-download-circle"></i> 保存到本地
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
        <table class="layui-hide" id="dingtalk-logs-table" lay-filter="dingtalk-logs-table"></table>
    </div>

<!-- 日志详情模板 -->
<script type="text/html" id="log-detail-tpl">
    {% raw %}
    <div style="padding: 15px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 80px;">报告ID:</label>
            <div class="layui-input-block" style="margin-left: 90px; line-height: 38px;">
                {{ d.report_id }}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 80px;">创建时间:</label>
            <div class="layui-input-block" style="margin-left: 90px; line-height: 38px;">
                {{ d.create_time }}
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title">
            <legend>日志内容</legend>
        </fieldset>
        <table class="layui-table" lay-size="sm">
            <thead>
                <tr>
                    <th style="width: 30%;">字段名</th>
                    <th>字段值</th>
                </tr>
            </thead>
            <tbody>
                {{# layui.each(d.contents, function(index, item){ }}
                <tr>
                    <td style="font-weight: bold; color: #009688;">{{ item.key }}</td>
                    <td>{{ item.value }}</td>
                </tr>
                {{# }); }}
            </tbody>
        </table>
    </div>
    {% endraw %}
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="toolbar-tpl">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>

{% include 'system/common/footer.html' %}

<script>
layui.use(['table', 'form', 'layer', 'laytpl', 'jquery', 'laydate'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;
    var laydate = layui.laydate;

    // 日期处理函数
    function formatDate(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return year + '-' + month + '-' + day;
    }

    function formatDateTimeForAPI(dateStr) {
        return dateStr + 'T00:00:00';
    }

    function formatEndDateTimeForAPI(dateStr) {
        return dateStr + 'T23:59:59';
    }

    // 获取今天的日期
    var today = new Date();
    var todayStr = formatDate(today);

    // 初始化日期选择器
    laydate.render({
        elem: '#start_date',
        type: 'date',
        format: 'yyyy-MM-dd',
        value: todayStr,
        max: todayStr,
        done: function(value, date, endDate){
            // 当开始日期改变时，更新结束日期的最小值
            laydate.render({
                elem: '#end_date',
                type: 'date',
                format: 'yyyy-MM-dd',
                value: $('#end_date').val() || todayStr,
                min: value,
                max: todayStr
            });
        }
    });

    laydate.render({
        elem: '#end_date',
        type: 'date',
        format: 'yyyy-MM-dd',
        value: todayStr,
        max: todayStr
    });

    // 初始化表格
    table.render({
        elem: '#dingtalk-logs-table',
        url: '/system/dindin/logs',
        method: 'GET',
        where: {
            start_time: formatDateTimeForAPI(todayStr),
            end_time: formatEndDateTimeForAPI(todayStr)
        },
        cols: [[
            {field: 'report_id', title: '报告ID', width: '25%', minWidth: 150, sort: true},
            {field: 'creator_name', title: '创建人', width: '15%', minWidth: 100},
            {field: 'create_time', title: '创建时间', width: '25%', minWidth: 160, sort: true},
            {field: 'contents', title: '内容数量', width: '15%', minWidth: 80, align: 'center', templet: function(d){
                var count = d.contents ? d.contents.length : 0;
                return '<span class="layui-badge layui-bg-blue">' + count + '</span>';
            }},
            {title: '操作', toolbar: '#toolbar-tpl', width: '20%', minWidth: 100, align: 'center'}
        ]],
        page: true,
        limit: 15,
        limits: [15, 30, 50, 100],
        loading: true,
        text: {
            none: '暂无相关数据'
        },
        done: function(res, curr, count){
            // 表格渲染完成回调
            if(res.code !== 0){
                layer.msg(res.msg || '数据加载失败', {icon: 2});
            }
        }
    });

    // 自动加载模板功能
    function loadTemplates() {
        var userid = $('#userid').val();
        var params = {};
        if(userid) {
            params.userid = userid;
        }

        $.ajax({
            url: '/system/dindin/templates',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(res){
                if(res.code === 0 && res.data){
                    // 清空现有选项
                    $('#template_name').empty();
                    $('#template_name').append('<option value="">请选择日志模板</option>');

                    // 过滤并添加有效的模板选项
                    var validTemplates = 0;
                    var hasWorkLog = false;
                    $.each(res.data, function(index, template){
                        // 检查模板数据是否有效
                        if(template.template_name && template.template_name !== 'null' && template.template_name.trim() !== '') {
                            var isSelected = template.template_name === '工作日志' ? ' selected' : '';
                            if(template.template_name === '工作日志') {
                                hasWorkLog = true;
                            }
                            $('#template_name').append(
                                '<option value="' + template.template_name + '" data-id="' + template.template_id + '"' + isSelected + '>' +
                                template.template_name +
                                '</option>'
                            );
                            validTemplates++;
                        }
                    });

                    // 重新渲染select
                    form.render('select');

                    if(validTemplates > 0 && hasWorkLog) {
                        console.log('模板加载成功，已默认选择"工作日志"');
                    }
                }
            },
            error: function(){
                console.log('模板加载失败');
            }
        });
    }

    // 搜索功能
    $('#search-btn').on('click', function(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var template_name = $('#template_name').val();
        var userid = $('#userid').val();

        // 验证日期
        if(!start_date || !end_date) {
            layer.msg('请选择开始时间和结束时间', {icon: 2});
            return;
        }

        if(start_date > end_date) {
            layer.msg('开始时间不能晚于结束时间', {icon: 2});
            return;
        }

        var where = {
            start_time: formatDateTimeForAPI(start_date),
            end_time: formatEndDateTimeForAPI(end_date)
        };

        if(template_name) {
            where.template_name = template_name;
        }
        if(userid) {
            where.userid = userid;
        }

        table.reload('dingtalk-logs-table', {
            where: where,
            page: {
                curr: 1
            }
        });
    });



    // 保存日志到本地功能
    $('#save-logs-btn').on('click', function(){
        var btn = $(this);
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        var template_name = $('#template_name').val();
        var userid = $('#userid').val();

        // 验证日期
        if(!start_date || !end_date) {
            layer.msg('请选择开始时间和结束时间', {icon: 2});
            return;
        }

        if(start_date > end_date) {
            layer.msg('开始时间不能晚于结束时间', {icon: 2});
            return;
        }

        // 确认操作
        layer.confirm('确定要将当前筛选条件下的日志保存到本地数据库吗？', {
            icon: 3,
            title: '确认保存'
        }, function(index){
            btn.addClass('layui-btn-disabled').text('保存中...');

            var requestData = {
                start_time: formatDateTimeForAPI(start_date),
                end_time: formatEndDateTimeForAPI(end_date)
            };

            if(template_name) {
                requestData.template_name = template_name;
            }
            if(userid) {
                requestData.userid = userid;
            }

            $.ajax({
                url: '/system/dindin/save-logs',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                dataType: 'json',
                success: function(res){
                    if(res.success){
                        layer.msg(res.msg || '保存成功', {icon: 1, time: 3000});

                        // 显示详细结果
                        if(res.data && res.data.details) {
                            var details = res.data.details;
                            var successCount = res.data.success_count || 0;
                            var failedCount = res.data.failed_count || 0;

                            layer.open({
                                type: 1,
                                title: '保存结果详情',
                                area: ['600px', '400px'],
                                content: '<div style="padding: 15px;">' +
                                        '<p><strong>总计：</strong>' + (successCount + failedCount) + ' 条</p>' +
                                        '<p><strong>成功：</strong><span style="color: green;">' + successCount + '</span> 条</p>' +
                                        '<p><strong>失败：</strong><span style="color: red;">' + failedCount + '</span> 条</p>' +
                                        '<hr>' +
                                        '<div style="max-height: 250px; overflow-y: auto;">' +
                                        details.map(function(item) {
                                            var status = item.success ? '<span style="color: green;">✓</span>' : '<span style="color: red;">✗</span>';
                                            return '<p>' + status + ' ' + item.creator_name + ': ' + item.message + '</p>';
                                        }).join('') +
                                        '</div>' +
                                        '</div>',
                                maxmin: true,
                                shadeClose: true
                            });
                        }
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('请求失败', {icon: 2});
                },
                complete: function(){
                    btn.removeClass('layui-btn-disabled').html('<i class="layui-icon layui-icon-download-circle"></i> 保存到本地');
                }
            });

            layer.close(index);
        });
    });

    // 监听工具条
    table.on('tool(dingtalk-logs-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            // 使用layui模板引擎渲染详情内容
            var getTpl = $('#log-detail-tpl').html();
            var view = laytpl(getTpl).render(data);

            layer.open({
                type: 1,
                title: '日志详情 - ' + data.creator_name,
                area: ['700px', '500px'],
                content: view,
                maxmin: true,
                shadeClose: true,
                shade: 0.3
            });
        }
    });

    // 监听模板选择
    form.on('select(template_name)', function(data){
        // 模板选择变化时的处理逻辑
        console.log('选择的模板：', data.value);
    });

    // 页面加载完成后自动加载模板
    loadTemplates();
});
</script>

</body>
</html>