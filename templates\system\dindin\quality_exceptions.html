<!DOCTYPE html>
<html>
<head>
    <title>质量异常单管理</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .search-form .layui-form-item {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .search-form .layui-form-label {
            width: 100px;
            height: 38px;
            line-height: 38px;
            padding: 0 10px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }
        .search-form .layui-input-block {
            margin-left: 30px;
            flex: 1;
            display: flex;
            align-items: center;
        }
        .search-form .layui-input,
        .search-form .layui-select,
        .search-form select {
            height: 38px;
            line-height: 38px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.3s;
            width: 100%;
        }
        .search-form .layui-input:focus,
        .search-form .layui-select:focus,
        .search-form select:focus {
            border-color: #009688;
        }
        .operation-buttons {
            background: #fff;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
            text-align: center;
        }
        .operation-buttons .layui-btn {
            margin: 0 5px;
        }
        .status-tag {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: #fff;
            display: inline-block;
        }
        .status-new { background-color: #1890ff; }
        .status-running { background-color: #faad14; }
        .status-completed { background-color: #52c41a; }
        .status-terminated { background-color: #f5222d; }
        .status-canceled { background-color: #d9d9d9; color: #666; }
        .table-container {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
        }
        .stats-card h3 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .stats-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="layui-fluid" style="margin-top: 20px;">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="stats-card">
                    <h3 id="total-count">0</h3>
                    <p>总异常单数</p>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <h3 id="pending-count">0</h3>
                    <p>待审批</p>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <h3 id="running-count">0</h3>
                    <p>审批中</p>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                    <h3 id="completed-count">0</h3>
                    <p>已完成</p>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="search-form">
            <form class="layui-form" lay-filter="search-form">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">审批模板</label>
                            <div class="layui-input-block">
                                <input type="text" name="process_code_display" value="质量异常单" class="layui-input" readonly style="background-color: #f5f5f5; cursor: not-allowed;">
                                <input type="hidden" name="process_code" value="">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">审批状态</label>
                            <div class="layui-input-block">
                                <input type="text" name="status_display" value="已完成" class="layui-input" readonly style="background-color: #f5f5f5; cursor: not-allowed;">
                                <input type="hidden" name="status" value="COMPLETED">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开始日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="start_time" class="layui-input" id="start-date" placeholder="开始日期">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">结束日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="end_time" class="layui-input" id="end-date" placeholder="结束日期">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 操作按钮 -->
        <div class="operation-buttons">
            <button class="layui-btn layui-btn-normal" id="query-btn">
                <i class="layui-icon layui-icon-search"></i> 查询异常单
            </button>
            <button class="layui-btn layui-btn-warm" id="sync-btn">
                <i class="layui-icon layui-icon-download-circle"></i> 同步到本地
            </button>
            <!-- <button class="layui-btn layui-btn-primary" id="export-btn">
                <i class="layui-icon layui-icon-export"></i> 导出数据
            </button>
            <button class="layui-btn layui-btn-primary" id="refresh-templates-btn">
                <i class="layui-icon layui-icon-refresh"></i> 刷新模板
            </button> -->
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="layui-hide" id="quality-exceptions-table" lay-filter="quality-exceptions-table"></table>
        </div>
    </div>

    <!-- 表格状态模板 -->
    <script type="text/html" id="status-template">
        {% raw %}
        {{#  var status = d.status || 'UNKNOWN'; }}
        {{#  if(status === 'NEW'){ }}
            <span class="status-tag status-new">新建</span>
        {{#  } else if(status === 'RUNNING'){ }}
            <span class="status-tag status-running">审批中</span>
        {{#  } else if(status === 'COMPLETED'){ }}
            <span class="status-tag status-completed">已完成</span>
        {{#  } else if(status === 'TERMINATED'){ }}
            <span class="status-tag status-terminated">已终止</span>
        {{#  } else if(status === 'CANCELED'){ }}
            <span class="status-tag status-canceled">已取消</span>
        {{#  } else { }}
            <span class="status-tag" style="background-color: #ccc;">{{status === 'UNKNOWN' ? '未知状态' : status}}</span>
        {{#  } }}
        {% endraw %}
    </script>

    <!-- 创建时间模板 -->
    <script type="text/html" id="create-time-template">
        {% raw %}
        {{#  var timeValue = d.create_time || ''; }}
        {{#  if(timeValue && timeValue !== '' && timeValue !== '-'){ }}
            {{timeValue}}
        {{#  } else { }}
            -
        {{#  } }}
        {% endraw %}
    </script>

    <!-- 完成时间模板 -->
    <script type="text/html" id="finish-time-template">
        {% raw %}
        {{#  var timeValue = d.finish_time || ''; }}
        {{#  if(timeValue && timeValue !== '' && timeValue !== '-'){ }}
            {{timeValue}}
        {{#  } else { }}
            -
        {{#  } }}
        {% endraw %}
    </script>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="operation-template">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">
            <i class="layui-icon layui-icon-about"></i> 详情
        </a>
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="sync">
            <i class="layui-icon layui-icon-download-circle"></i> 同步
        </a>
    </script>

    {% include 'system/common/footer.html' %}

    <script>
        // 全局函数：图片预览功能
        function previewImage(url) {
            layui.use('layer', function() {
                var layer = layui.layer;
                layer.photos({
                    photos: {
                        "title": "图片预览",
                        "id": 123,
                        "data": [{
                            "alt": "图片",
                            "pid": 1,
                            "src": url,
                            "thumb": url
                        }]
                    },
                    anim: 5
                });
            });
        }

        layui.use(['table', 'form', 'laydate', 'layer', 'jquery'], function() {
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            var $ = layui.jquery;

            // 缓存查询到的数据，避免同步时重复调用API
            var cachedQualityExceptions = null;

            // 初始化日期选择器
            laydate.render({
                elem: '#start-date',
                type: 'date',
                format: 'yyyy-MM-dd'
            });

            laydate.render({
                elem: '#end-date',
                type: 'date',
                format: 'yyyy-MM-dd'
            });

            // 设置上个月的日期范围
            function setLastMonthDateRange() {
                var now = new Date();
                var lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                var lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

                var startDate = lastMonth.getFullYear() + '-' +
                    String(lastMonth.getMonth() + 1).padStart(2, '0') + '-' +
                    String(lastMonth.getDate()).padStart(2, '0');
                var endDate = lastMonthEnd.getFullYear() + '-' +
                    String(lastMonthEnd.getMonth() + 1).padStart(2, '0') + '-' +
                    String(lastMonthEnd.getDate()).padStart(2, '0');

                $('#start-date').val(startDate);
                $('#end-date').val(endDate);
            }

            // 渲染表格
            var tableIns = table.render({
                elem: '#quality-exceptions-table',
                data: [], // 初始空数据
                page: true,
                limit: 15,
                limits: [15, 30, 50, 100],
                loading: false, // 明确禁用初始加载动画
                cols: [[
                    {field: 'process_instance_id', title: '审批实例ID', width: 200, align: 'center'},
                    {field: 'title', title: '异常单标题', minWidth: 170, align: 'center'},
                    {field: 'originator_userid', title: '发起人', width: 120, align: 'center'},
                    {field: 'status', title: '审批状态', width: 130, align: 'center', templet: '#status-template'},
                    {field: 'create_time', title: '创建时间', width: 160, align: 'center'},
                    {field: 'finish_time', title: '完成时间', width: 160, align: 'center'},
                    {title: '操作', width: 200, align: 'center', toolbar: '#operation-template'}
                ]],
                text: {
                    none: '暂无数据，请先选择审批模板并点击查询'
                }
            });

            // 加载审批模板列表并设置质量异常单的process_code
            function loadApprovalTemplates() {
                $.ajax({
                    url: '/system/dindin/api/approval-templates',
                    type: 'GET',
                    success: function(res) {
                        if (res.code === 0 && res.data) {
                            // 查找质量异常单的process_code
                            var qualityTemplate = res.data.find(function(template) {
                                return template.name === '质量异常单';
                            });

                            if (qualityTemplate) {
                                $('input[name="process_code"]').val(qualityTemplate.process_code);
                                console.log('设置质量异常单process_code:', qualityTemplate.process_code);
                            } else {
                                console.warn('未找到质量异常单模板');
                                layer.msg('未找到质量异常单模板', {icon: 2});
                            }
                        } else {
                            layer.msg('加载审批模板失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('加载审批模板失败', {icon: 2});
                    }
                });
            }

            // 查询质量异常单
            function queryQualityExceptions() {
                var formData = form.val('search-form');

                if (!formData.process_code) {
                    layer.msg('请先加载审批模板', {icon: 2});
                    return;
                }

                if (!formData.start_time || !formData.end_time) {
                    layer.msg('请选择开始和结束日期', {icon: 2});
                    return;
                }

                var requestData = {
                    process_code: formData.process_code,
                    status: formData.status,
                    start_time: formData.start_time,
                    end_time: formData.end_time
                };

                var loadingIndex = layer.load(2, {shade: [0.1, '#fff']});

                $.ajax({
                    url: '/system/dindin/api/quality-exceptions',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    success: function(res) {
                        layer.close(loadingIndex);

                        // 验证响应格式
                        if (typeof res !== 'object' || res === null) {
                            console.error('响应格式错误:', res);
                            layer.msg('服务器响应格式错误', {icon: 2});
                            return;
                        }

                        if (res.code === 0) {
                            // 验证数据格式
                            var data = res.data || [];
                            var count = res.count || 0;

                            if (!Array.isArray(data)) {
                                console.error('数据格式错误，期望数组:', data);
                                data = [];
                            }

                            // 清理和格式化数据
                            var cleanedData = data.map(function(item) {
                                return {
                                    process_instance_id: item.process_instance_id || '',
                                    title: item.title || '无标题',
                                    originator_userid: item.originator_userid || '未知',
                                    status: item.status || 'UNKNOWN',
                                    create_time: item.create_time || '-',
                                    finish_time: item.finish_time || '-',
                                    business_id: item.business_id || '',
                                    originator_dept_id: item.originator_dept_id || '',
                                    originator_dept_name: item.originator_dept_name || '',
                                    form_component_values: item.form_component_values || []
                                };
                            });

                            console.log('清理后的数据:', cleanedData.slice(0, 2)); // 只打印前2条用于调试

                            // 缓存原始数据用于同步（避免重复调用API）
                            cachedQualityExceptions = data;
                            console.log('已缓存', cachedQualityExceptions.length, '条原始数据用于同步');

                            // 更新统计数据
                            updateStatistics(cleanedData);

                            // 重新渲染表格
                            table.reload('quality-exceptions-table', {
                                data: cleanedData,
                                page: {
                                    curr: 1
                                }
                            });

                            layer.msg('查询成功，共找到 ' + count + ' 条记录', {icon: 1});
                        } else {
                            var errorMsg = res.msg || '查询失败';
                            console.error('查询失败:', errorMsg);
                            layer.msg(errorMsg, {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadingIndex);
                        console.error('AJAX请求失败:', {
                            status: xhr.status,
                            statusText: xhr.statusText,
                            responseText: xhr.responseText,
                            error: error
                        });

                        var errorMsg = '查询失败，请稍后重试';
                        if (xhr.status === 500) {
                            errorMsg = '服务器内部错误';
                        } else if (xhr.status === 404) {
                            errorMsg = '请求地址不存在';
                        } else if (xhr.status === 403) {
                            errorMsg = '权限不足';
                        }

                        layer.msg(errorMsg, {icon: 2});
                    }
                });
            }

            // 同步质量异常单到本地数据库
            function syncQualityExceptions() {
                // 检查是否有缓存的数据
                if (!cachedQualityExceptions || cachedQualityExceptions.length === 0) {
                    layer.msg('请先查询数据再进行同步', {icon: 2});
                    return;
                }

                layer.confirm('确定要同步 ' + cachedQualityExceptions.length + ' 条质量异常单到本地数据库吗？', {
                    icon: 3,
                    title: '确认同步'
                }, function(index) {
                    layer.close(index);

                    // 直接发送缓存的数据，避免重复调用钉钉API
                    var requestData = {
                        instances: cachedQualityExceptions
                    };

                    var loadingIndex = layer.load(2, {shade: [0.1, '#fff']});

                    $.ajax({
                        url: '/system/dindin/api/quality-exceptions/sync-direct',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(requestData),
                        success: function(res) {
                            layer.close(loadingIndex);

                            // 验证响应格式
                            if (typeof res !== 'object' || res === null) {
                                console.error('同步响应格式错误:', res);
                                layer.msg('服务器响应格式错误', {icon: 2});
                                return;
                            }

                            if (res.success) {
                                var msg = res.msg || '同步成功';
                                var data = res.data || {};

                                // 显示详细的同步结果
                                var detailMsg = msg;
                                if (data.total !== undefined) {
                                    detailMsg += `（总计: ${data.total}, 成功: ${data.success_count}, 失败: ${data.failed_count}）`;
                                }

                                layer.msg(detailMsg, {icon: 1, time: 3000});
                                console.log('同步成功:', data);

                                // 直接使用同步返回的数据更新表格，避免重复调用API
                                if (data.instances && Array.isArray(data.instances)) {
                                    // 清理和格式化数据
                                    var cleanedData = data.instances.map(function(item) {
                                        return {
                                            process_instance_id: item.process_instance_id || '',
                                            title: item.title || '无标题',
                                            originator_userid: item.originator_userid || '未知',
                                            status: item.status || 'UNKNOWN',
                                            create_time: item.create_time || '-',
                                            finish_time: item.finish_time || '-',
                                            business_id: item.business_id || '',
                                            originator_dept_id: item.originator_dept_id || '',
                                            originator_dept_name: item.originator_dept_name || '',
                                            form_component_values: item.form_component_values || []
                                        };
                                    });

                                    console.log('使用同步返回的数据更新表格:', cleanedData.length, '条记录');

                                    // 更新统计数据
                                    updateStatistics(cleanedData);

                                    // 重新渲染表格
                                    table.reload('quality-exceptions-table', {
                                        data: cleanedData,
                                        page: {
                                            curr: 1
                                        }
                                    });
                                } else {
                                    // 如果没有返回实例数据，则重新查询
                                    console.log('同步返回数据中没有实例信息，重新查询');
                                    queryQualityExceptions();
                                }
                            } else {
                                var errorMsg = res.msg || '同步失败';
                                console.error('同步失败:', errorMsg);
                                layer.msg(errorMsg, {icon: 2});
                            }
                        },
                        error: function(xhr, status, error) {
                            layer.close(loadingIndex);
                            console.error('同步AJAX请求失败:', {
                                status: xhr.status,
                                statusText: xhr.statusText,
                                responseText: xhr.responseText,
                                error: error
                            });

                            var errorMsg = '同步失败，请稍后重试';
                            if (xhr.status === 500) {
                                errorMsg = '服务器内部错误';
                            } else if (xhr.status === 404) {
                                errorMsg = '请求地址不存在';
                            } else if (xhr.status === 403) {
                                errorMsg = '权限不足';
                            }

                            layer.msg(errorMsg, {icon: 2});
                        }
                    });
                });
            }

            // 更新统计数据
            function updateStatistics(data) {
                if (!Array.isArray(data)) {
                    console.error('updateStatistics: 数据不是数组', data);
                    data = [];
                }

                var total = data.length;
                var pending = data.filter(function(item) {
                    return item && item.status === 'NEW';
                }).length;
                var running = data.filter(function(item) {
                    return item && item.status === 'RUNNING';
                }).length;
                var completed = data.filter(function(item) {
                    return item && item.status === 'COMPLETED';
                }).length;

                $('#total-count').text(total);
                $('#pending-count').text(pending);
                $('#running-count').text(running);
                $('#completed-count').text(completed);

                console.log('统计数据更新:', { total, pending, running, completed });
            }

            // 导出数据
            function exportData() {
                var formData = form.val('search-form');

                if (!formData.process_code) {
                    layer.msg('请先查询数据后再导出', {icon: 2});
                    return;
                }

                layer.msg('导出功能开发中...', {icon: 0});
            }

            // 事件绑定
            $('#query-btn').click(queryQualityExceptions);
            $('#sync-btn').click(syncQualityExceptions);
            $('#export-btn').click(exportData);
            $('#refresh-templates-btn').click(loadApprovalTemplates);

            // 表格行操作事件
            table.on('tool(quality-exceptions-table)', function(obj) {
                var data = obj.data;
                var layEvent = obj.event;

                if (layEvent === 'detail') {
                    // 显示详情
                    showExceptionDetail(data);
                } else if (layEvent === 'sync') {
                    // 单个同步
                    syncSingleException(data);
                }
            });

            // 检测是否为图片URL
            function isImageUrl(url) {
                if (!url || typeof url !== 'string') return false;

                // 清理URL，移除可能的多余字符
                url = url.trim();

                // 检查是否包含钉钉图片域名或常见图片扩展名
                var imagePatterns = [
                    /static\.dingtalk\.com/i,
                    /media\.dingtalk\.com/i,
                    /\.jpg$/i,
                    /\.jpeg$/i,
                    /\.png$/i,
                    /\.gif$/i,
                    /\.bmp$/i,
                    /\.webp$/i,
                    /https?:\/\/.*\.(jpg|jpeg|png|gif|bmp|webp)/i
                ];

                return imagePatterns.some(function(pattern) {
                    return pattern.test(url);
                });
            }

            // 解析图片URL（支持JSON数组格式）
            function parseImageUrls(value) {
                if (!value) return [];

                var urls = [];

                try {
                    // 尝试解析JSON数组
                    if (value.trim().startsWith('[') && value.trim().endsWith(']')) {
                        var parsed = JSON.parse(value);
                        if (Array.isArray(parsed)) {
                            urls = parsed;
                        }
                    } else {
                        // 普通字符串，按逗号分隔
                        urls = value.split(',');
                    }
                } catch (e) {
                    // JSON解析失败，按逗号分隔处理
                    urls = value.split(',');
                }

                // 清理每个URL
                return urls.map(function(url) {
                    if (!url) return '';

                    // 移除首尾空格和引号
                    url = url.trim().replace(/^["']|["']$/g, '');

                    // 如果URL不是以http开头，可能需要添加协议
                    if (url.startsWith('//')) {
                        url = 'https:' + url;
                    }

                    return url;
                }).filter(function(url) {
                    return url && url.length > 0;
                });
            }



            // 显示异常单详情
            function showExceptionDetail(data) {
                var content = '<div style="padding: 20px; line-height: 1.8;">';
                content += '<p><strong>审批实例ID：</strong>' + (data.process_instance_id || '-') + '</p>';
                content += '<p><strong>业务ID：</strong>' + (data.business_id || '-') + '</p>';
                content += '<p><strong>标题：</strong>' + (data.title || '-') + '</p>';
                content += '<p><strong>发起人：</strong>' + (data.originator_userid || '-') + '</p>';
                content += '<p><strong>发起部门：</strong>' + (data.originator_dept_id || '-') + '</p>';
                content += '<p><strong>审批状态：</strong>' + getStatusText(data.status) + '</p>';
                content += '<p><strong>创建时间：</strong>' + (data.create_time || '-') + '</p>';
                content += '<p><strong>完成时间：</strong>' + (data.finish_time || '-') + '</p>';

                if (data.form_component_values && data.form_component_values.length > 0) {
                    content += '<p><strong>表单数据：</strong></p>';
                    content += '<div style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 10px;">';
                    data.form_component_values.forEach(function(item) {
                        if (item.name && item.value) {
                            // 检查是否为图片URL
                            if (isImageUrl(item.value)) {
                                content += '<p style="margin: 5px 0;"><strong>' + item.name + '：</strong></p>';
                                content += '<div style="margin: 10px 0;">';

                                // 解析图片URL（支持JSON数组格式）
                                var imageUrls = parseImageUrls(item.value);
                                console.log('解析后的图片URLs:', imageUrls); // 调试日志

                                if (imageUrls.length > 0) {
                                    imageUrls.forEach(function(url) {
                                        if (url) {
                                            console.log('显示图片URL:', url); // 调试日志
                                            content += '<img src="' + url + '" style="max-width: 200px; max-height: 200px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="previewImage(\'' + url + '\')" onerror="this.style.display=\'none\'; this.nextSibling.style.display=\'block\';" />';
                                            content += '<div style="display: none; padding: 10px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; margin: 5px; color: #666;">图片加载失败: ' + url + '</div>';
                                        }
                                    });
                                } else {
                                    content += '<div style="padding: 10px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; margin: 5px; color: #666;">无法解析图片URL: ' + item.value + '</div>';
                                }
                                content += '</div>';
                            } else {
                                content += '<p style="margin: 5px 0;"><strong>' + item.name + '：</strong>' + item.value + '</p>';
                            }
                        }
                    });
                    content += '</div>';
                }

                content += '</div>';

                layer.open({
                    type: 1,
                    title: '质量异常单详情',
                    area: ['600px', '500px'],
                    content: content,
                    btn: ['关闭'],
                    btn1: function(index) {
                        layer.close(index);
                    }
                });
            }

            // 单个异常单同步
            function syncSingleException(data) {
                layer.confirm('确定要同步这条异常单到本地数据库吗？', {
                    icon: 3,
                    title: '确认同步'
                }, function(index) {
                    layer.close(index);

                    var loadingIndex = layer.load(2, {shade: [0.1, '#fff']});

                    // 这里可以调用单个同步的API
                    setTimeout(function() {
                        layer.close(loadingIndex);
                        layer.msg('同步成功', {icon: 1});
                    }, 1000);
                });
            }

            // 获取状态文本
            function getStatusText(status) {
                var statusMap = {
                    'NEW': '新建',
                    'RUNNING': '审批中',
                    'COMPLETED': '已完成',
                    'TERMINATED': '已终止',
                    'CANCELED': '已取消'
                };
                return statusMap[status] || status || '未知';
            }

            // 页面加载时初始化
            loadApprovalTemplates();
            setLastMonthDateRange();
        });
    </script>

</body>
</html>
