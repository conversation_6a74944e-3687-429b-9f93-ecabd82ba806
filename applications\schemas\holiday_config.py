from marshmallow import Schema, fields, validate, validates, ValidationError, pre_load
from datetime import datetime


class HolidayConfigSchema(Schema):
    """节假日配置序列化模型"""
    
    id = fields.Integer(dump_only=True)
    holiday_date = fields.Date(required=True, format='%Y-%m-%d')
    holiday_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    holiday_type = fields.String(
        required=True,
        validate=validate.OneOf(['legal', 'makeup', 'weekend', 'company_rest', 'company_work'])
    )
    overtime_rate = fields.Decimal(
        required=True,
        places=1
    )
    year = fields.Integer(required=True)
    description = fields.String(allow_none=True, validate=validate.Length(max=500))
    status = fields.Integer(validate=validate.OneOf([0, 1]), missing=1)
    created_by = fields.Integer(dump_only=True)
    created_at = fields.DateTime(dump_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_by = fields.Integer(dump_only=True)
    updated_at = fields.DateTime(dump_only=True, format='%Y-%m-%d %H:%M:%S')
    
    # 额外的显示字段
    holiday_type_text = fields.String(dump_only=True)
    status_text = fields.String(dump_only=True)

    @validates('holiday_date')
    def validate_holiday_date(self, value):
        """验证节假日日期"""
        if value < datetime(2020, 1, 1).date():
            raise ValidationError('节假日日期不能早于2020年')
        if value > datetime(2050, 12, 31).date():
            raise ValidationError('节假日日期不能晚于2050年')

    @validates('overtime_rate')
    def validate_overtime_rate(self, value):
        """验证加班倍率"""
        valid_rates = [1.5, 2.0, 3.0]
        if float(value) not in valid_rates:
            raise ValidationError('加班倍率只能是1.5、2.0或3.0')


class HolidayConfigQuerySchema(Schema):
    """节假日配置查询参数序列化模型"""

    year = fields.Integer(allow_none=True)
    holiday_type = fields.String(
        validate=validate.OneOf(['legal', 'makeup', 'weekend', 'company_rest', 'company_work']),
        allow_none=True,
        missing=None
    )
    keyword = fields.String(validate=validate.Length(max=100), allow_none=True)
    status = fields.Integer(validate=validate.OneOf([0, 1]), allow_none=True)
    page = fields.Integer(missing=1)
    limit = fields.Integer(missing=10)

    @pre_load
    def preprocess_data(self, data, **kwargs):
        """预处理数据，将空字符串转换为None"""
        if isinstance(data, dict):
            processed_data = data.copy()
            if 'holiday_type' in processed_data and processed_data['holiday_type'] == '':
                processed_data['holiday_type'] = None
            return processed_data
        return data


class HolidayOperationLogSchema(Schema):
    """节假日操作日志序列化模型"""
    
    id = fields.Integer(dump_only=True)
    operation_type = fields.String(dump_only=True)
    operation_type_text = fields.String(dump_only=True)
    holiday_date = fields.Date(dump_only=True, format='%Y-%m-%d')
    old_data = fields.Raw(dump_only=True)
    new_data = fields.Raw(dump_only=True)
    operator_id = fields.Integer(dump_only=True)
    operator_name = fields.String(dump_only=True)
    operation_time = fields.DateTime(dump_only=True, format='%Y-%m-%d %H:%M:%S')
    ip_address = fields.String(dump_only=True)
    user_agent = fields.String(dump_only=True)
    remark = fields.String(dump_only=True)


class HolidayImportSchema(Schema):
    """节假日批量导入参数序列化模型"""

    year = fields.Integer(required=True)
    override_existing = fields.Boolean(missing=False)
    import_type = fields.String(
        validate=validate.OneOf(['holidays_lib', 'manual']),
        missing='holidays_lib'
    )


class HolidayBatchOperationSchema(Schema):
    """节假日批量操作参数序列化模型"""
    
    ids = fields.List(fields.Integer(), required=True, validate=validate.Length(min=1))
    operation = fields.String(
        required=True, 
        validate=validate.OneOf(['enable', 'disable', 'delete'])
    )
