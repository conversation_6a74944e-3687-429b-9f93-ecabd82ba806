<!DOCTYPE html>
<html>
<head>
    <title>项目回款情况一览表</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <style>
        /* CSS变量定义 */
        :root {
            --payment-rate-low: #F56C6C;       /* 低于30%：红色 */
            --payment-rate-medium: #E6A23C;    /* 30%-60%：橙色 */
            --payment-rate-high: #F6D365;      /* 60%-80%：黄色 */
            --payment-rate-excellent: #67C23A; /* 80%以上：绿色 */
        }

        /* 全局样式 */
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            width: 100%;
            overflow-x: hidden;
        }

        .pear-container {
            width: 100%;
            padding: 0;
            box-sizing: border-box;
        }

        /* 卡片样式优化 */
        .layui-card {
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            overflow: hidden;
        }

        .layui-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            transform: translateY(-5px);
        }

        .layui-card-body {
            padding: 20px;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* 页面标题样式 */
        .page-title {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #333;
            padding: 10px 0;
            position: relative;
            display: inline-block;
        }

        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: #009688;
            border-radius: 3px;
        }

        /* 统计卡片样式 */
        .stat-card {
            text-align: center;
            padding: 20px 10px;
            position: relative;
            overflow: hidden;
            height: 180px; /* 固定高度 */
            min-height: 180px; /* 最小高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        /* 自定义的五等分列 */
        .layui-col-md2-4 {
            width: 20%;
        }

        .stat-card-prepayment {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .stat-card-delivery {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .stat-card-acceptance {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .stat-card-warranty {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card-progress {
            background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
            color: white;
        }

        .stat-icon {
            margin-bottom: 15px;
        }

        .stat-icon i {
            font-size: 36px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 70px;
            height: 70px;
            line-height: 70px;
            display: inline-block;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 12px 0 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-bottom: 5px;
        }

        /* 进度环样式优化 */
        .progress-ring {
            position: relative;
            display: inline-block;
            width: 100px;
            height: 100px;
            text-align: center;
            margin: 0;
        }

        .progress-ring .circle {
            transform: rotate(-90deg);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            width: 100px;
            height: 100px;
        }

        .progress-ring .circle-bg {
            fill: none;
            stroke: rgba(255, 255, 255, 0.3);
            stroke-width: 8;
        }

        .progress-ring .circle-progress {
            fill: none;
            stroke: #ffffff; /* 默认颜色，将在JS中根据付款率动态修改 */
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 314.159;
            stroke-dashoffset: calc(314.159 - (314.159 * var(--progress) / 100));
            transition: stroke-dashoffset 1.5s cubic-bezier(0.34, 1.56, 0.64, 1), stroke 0.5s ease;
        }

        .progress-ring .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 22px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .progress-ring .progress-label {
            position: absolute;
            top: 70%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        /* 付款率卡片特殊样式 */
        /* .stat-card-progress {
            padding-top: 15px;
            padding-bottom: 15px;
        } */

        /* 付款状态标签样式 */
        .payment-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            color: #fff;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
            line-height: 18px;
            margin: 2px 0;
        }

        .payment-status.status-unpaid {
            background-color: #F56C6C;
            color: #fff;
        }

        .payment-status.status-paid {
            background-color: #67C23A;
            color: #fff;
        }

        .payment-status.status-required {
            background-color: #E6A23C;
            color: #fff;
        }

        .payment-status.status-partial {
            background-color: #409EFF;
            color: #fff;
        }

        .payment-status.status-none {
            background-color: #C0C4CC;
            color: #fff;
        }

        /* 验收激励样式 */
        .acceptance-incentive {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .acceptance-incentive.incentive-positive {
            background-color: #67C23A;
            color: #fff;
        }

        .acceptance-incentive.incentive-negative {
            background-color: #F56C6C;
            color: #fff;
        }

        .acceptance-incentive.incentive-neutral {
            background-color: #E4E7ED;
            color: #606266;
        }

        /* 质保天数状态标签样式 */
        .warranty-days {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            min-width: 80px;
            text-align: center;
            line-height: 18px;
            margin: 2px 0;
        }

        .warranty-days.days-remaining {
            background-color: #67C23A;
            color: #fff;
        }

        .warranty-days.days-overdue {
            background-color: #F56C6C;
            color: #fff;
        }

        .warranty-days.days-today {
            background-color: #E6A23C;
            color: #fff;
        }

        .warranty-days.days-pending {
            background-color: #909399;
            color: #fff;
            font-style: italic;
        }

        .warranty-days.days-none {
            background-color: #C0C4CC;
            color: #fff;
        }

        /* 项目状态基础样式 - 增加选择器优先级和!important标记 */
        .layui-table-view .layui-table .project-status,
        .project-status {
            display: inline-block !important;
            padding: 6px 10px !important;
            border-radius: 3px !important;
            font-size: 13px !important;
            font-weight: bold !important;
            color: #fff !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            text-align: center !important;
            min-width: 80px !important;
            transition: all 0.3s ease !important;
            -webkit-transition: all 0.3s ease !important;
            -moz-transition: all 0.3s ease !important;
            text-shadow: 0 1px 1px rgba(0,0,0,0.3) !important;
            margin: 0 auto !important;
            line-height: 1.5 !important;
            white-space: normal !important;
            word-break: break-word !important;
            height: auto !important;
            max-width: 130px !important;
        }

        /* 鼠标悬停效果 */
        .layui-table-view .layui-table .project-status:hover,
        .project-status:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
            -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
            -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
        }

        /* 未开始 - 橙黄色 */
        .layui-table-view .layui-table .project-status.status-pending,
        .project-status.status-pending {
            background-color: #FF9800 !important;
            color: #fff !important;
            border-left: 3px solid #E65100 !important;
        }

        /* 生产中 - 绿色 */
        .layui-table-view .layui-table .project-status.status-production,
        .project-status.status-production {
            background-color: #4CAF50 !important;
            color: #fff !important;
            border-left: 3px solid #1B5E20 !important;
        }

        /* 入库完成 - 浅绿色 */
        .layui-table-view .layui-table .project-status.status-warehouse,
        .project-status.status-warehouse {
            background-color: #8BC34A !important;
            color: #fff !important;
            border-left: 3px solid #33691E !important;
        }

        /* 发货完成 - 蓝色 */
        .layui-table-view .layui-table .project-status.status-shipping,
        .project-status.status-shipping {
            background-color: #2196F3 !important;
            color: #fff !important;
            border-left: 3px solid #0D47A1 !important;
        }

        /* 安装完成 - 青色 */
        .layui-table-view .layui-table .project-status.status-installation,
        .project-status.status-installation {
            background-color: #00BCD4 !important;
            color: #fff !important;
            border-left: 3px solid #006064 !important;
        }

        /* 验收完成 - 粉色 */
        .layui-table-view .layui-table .project-status.status-acceptance,
        .project-status.status-acceptance {
            background-color: #E91E63 !important;
            color: #fff !important;
            border-left: 3px solid #880E4F !important;
        }

        /* 项目已完成 - 深灰色 */
        .layui-table-view .layui-table .project-status.status-completed,
        .project-status.status-completed {
            background-color: #455A64 !important;
            color: #fff !important;
            border-left: 3px solid #263238 !important;
        }

        /* 可点击数字样式 */
        .clickable-count {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }

        .clickable-count:hover {
            transform: scale(1.1);
        }

        /* 筛选栏样式 */
        .filter-container {
            background-color: white;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }

        .filter-container .layui-input,
        .filter-container .layui-select {
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            padding: 0 15px;
            transition: all 0.3s;
        }

        .filter-container .layui-input:focus,
        .filter-container .layui-select:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.2);
        }

        .filter-container .layui-btn {
            height: 40px;
            line-height: 40px;
            padding: 0 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .filter-container .layui-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* 表格样式优化 */
        .layui-table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            width: 100%;
        }

        .layui-table thead tr {
            background-color: #009688;
        }

        .layui-table thead th {
            color: white;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 15px;
            border-bottom: 2px solid rgba(0, 0, 0, 0.05);
        }

        .layui-table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .layui-table tbody td {
            padding: 16px 15px;
            vertical-align: middle;
            height: 24px;
            line-height: 24px;
        }

        /* 增加表格行高 */
        .layui-table-cell {
            height: auto;
            line-height: 24px;
            padding: 8px 15px;
            position: relative;
            box-sizing: border-box;
        }

        /* 表格容器样式 */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-bottom: 20px;
        }

        /* 确保表格视图占满容器 */
        .layui-table-view {
            width: 100% !important;
        }

        /* 固定列样式优化 */
        .layui-table-fixed {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 按钮样式优化 */
        .layui-btn {
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .layui-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* 弹窗样式优化 */
        .layui-layer {
            border-radius: 12px;
            overflow: hidden;
        }

        .layui-layer-title {
            background-color: #009688;
            color: white;
            font-weight: 600;
            padding: 12px 20px;
            height: auto;
        }

        /* 响应式设计 */
        @media screen and (max-width: 992px) {
            .layui-col-md2-4 {
                width: 50%;
            }

            .stat-card {
                padding: 15px 10px;
            }

            .stat-icon i {
                font-size: 32px;
                width: 60px;
                height: 60px;
                line-height: 60px;
            }

            .stat-number {
                font-size: 28px;
            }

            .stat-title {
                font-size: 14px;
            }

            .progress-ring {
                width: 100px;
                height: 100px;
            }

            .progress-ring .progress-text {
                font-size: 22px;
            }

            .progress-ring .progress-label {
                font-size: 14px;
            }
        }

        @media screen and (max-width: 576px) {
            .layui-col-md2-4 {
                width: 100%;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-icon i {
                font-size: 36px;
                width: 70px;
                height: 70px;
                line-height: 70px;
            }

            .stat-number {
                font-size: 32px;
            }

            .progress-ring {
                width: 120px;
                height: 120px;
            }

            .progress-ring .progress-text {
                font-size: 24px;
            }
        }
    </style>
</head>
<body class="pear-container">
    <!-- 页面标题和操作按钮 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h2 class="page-title">项目回款情况一览表</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="layui-row layui-col-space15" style="width: 100%; margin: 0; clear: both;">
        <!-- 预付款未付项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-prepayment">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-rmb"></i>
                    </div>
                    <div class="stat-title">预付款未付项目</div>
                    <div class="stat-number clickable-count" id="prepayment-unpaid-count" data-type="prepayment">--</div>
                </div>
            </div>
        </div>

        <!-- 发货款未付项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-delivery">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-cart"></i>
                    </div>
                    <div class="stat-title">发货款未付项目</div>
                    <div class="stat-number clickable-count" id="delivery-payment-unpaid-count" data-type="delivery_payment">--</div>
                </div>
            </div>
        </div>

        <!-- 验收款未付项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-acceptance">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-ok-circle"></i>
                    </div>
                    <div class="stat-title">验收款未付项目</div>
                    <div class="stat-number clickable-count" id="acceptance-payment-unpaid-count" data-type="acceptance_payment">--</div>
                </div>
            </div>
        </div>

        <!-- 质保金未付项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-warranty">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-auz"></i>
                    </div>
                    <div class="stat-title">质保金未付项目</div>
                    <div class="stat-number clickable-count" id="warranty-payment-unpaid-count" data-type="warranty_payment">--</div>
                </div>
            </div>
        </div>

        <!-- 付款率 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-progress">
                    <div class="progress-ring">
                        <svg class="circle" width="100" height="100">
                            <circle class="circle-bg" cx="50" cy="50" r="42"></circle>
                            <circle class="circle-progress" cx="50" cy="50" r="42" style="--progress: 0"></circle>
                        </svg>
                        <div class="progress-text" id="paid-percentage">0%</div>
                        <div class="progress-label">付款率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目列表区域 -->
    <div class="layui-card" style="width: 100%; clear: both; float: none;margin-top: 20px;">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-list" style="margin-right: 8px;"></i>项目回款列表
        </div>
        <div class="layui-card-body">
            <!-- 筛选栏 -->
            <div class="filter-container">
                <div class="layui-form layui-row layui-col-space15">
                    <div class="layui-col-md3 layui-col-sm6">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: auto; padding-left: 0;">项目状态</label>
                            <div class="layui-input-block" style="margin-left: 80px;">
                                <select id="status-filter" name="status" lay-filter="status">
                                    <option value="">全部状态</option>
                                    {% for status in project_status_options %}
                                    <option value="{{ status.data_value }}">{{ status.data_label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6 layui-col-sm6">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: auto; padding-left: 0;">关键词</label>
                            <div class="layui-input-block" style="margin-left: 60px;">
                                <input type="text" id="search-input" placeholder="搜索项目名称/编号" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm12" style="text-align: right;">
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 项目表格 -->
            <div class="table-container" style="width: 100%; overflow-x: auto;">
                <table id="payment-table" lay-filter="payment-table"></table>
            </div>
        </div>
    </div>

    <!-- 表格工具栏模板 -->
    <script type="text/html" id="payment-bar">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="detail">
            <i class="layui-icon layui-icon-form"></i> 详情
        </button>
    </script>

    <!-- 付款状态模板 -->
    <script type="text/html" id="paymentStatusTpl">
        {% raw %}
        {{# if(d.prepayment_status === 0) { }}
            <span class="payment-status status-unpaid">未回款</span>
        {{# } else if(d.prepayment_status === 1) { }}
            <span class="payment-status status-paid">已回款</span>
        {{# } else if(d.prepayment_status === 2) { }}
            <span class="payment-status status-required">未到期</span>
        {{# } else if(d.prepayment_status === 3) { }}
            <span class="payment-status status-partial">部分回款</span>
        {{# } }}
        {% endraw %}
    </script>

    <!-- 项目状态模板 -->
    <script type="text/html" id="projectStatusTpl">
        {% raw %}
        {{# if(d.status === 0) { }}
            <span class="project-status status-pending">未开始</span>
        {{# } else if(d.status === 1) { }}
            <span class="project-status status-production">生产中</span>
        {{# } else if(d.status === 3) { }}
            <span class="project-status status-warehouse">入库完成</span>
        {{# } else if(d.status === 5) { }}
            <span class="project-status status-shipping">发货完成</span>
        {{# } else if(d.status === 6) { }}
            <span class="project-status status-installation">安装调试中</span>
        {{# } else if(d.status === 7) { }}
            <span class="project-status status-installation">安装调试完成</span>
        {{# } else if(d.status === 10) { }}
            <span class="project-status status-acceptance">验收中</span>
        {{# } else if(d.status === 12) { }}
            <span class="project-status status-completed">项目已完成</span>
        {{# } else { }}
            <span class="project-status status-pending">{{ d.status_name }}</span>
        {{# } }}
        {% endraw %}
    </script>
</body>

<script>
    layui.use(['table', 'layer', 'jquery', 'form'], function() {
        let table = layui.table;
        let layer = layui.layer;
        let $ = layui.jquery;
        let form = layui.form;

        // 币种转换为符号的函数
        function getCurrencySymbol(currency) {
            if (!currency) return '¥'; // 默认人民币符号

            switch(currency) {
                case '人民币':
                    return '¥';
                case '美元':
                    return '$';
                case '欧元':
                    return '€';
                case '英镑':
                    return '£';
                case '日元':
                    return '¥';
                case '韩元':
                    return '₩';
                case '港币':
                    return 'HK$';
                default:
                    return currency; // 如果没有匹配的符号，返回原币种文本
            }
        }

        // 加载项目统计数据
        function loadStatistics() {
            $.ajax({
                url: '/system/project_payment/statistics',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let data = response.data;

                        // 更新各类型未付款项目统计数据
                        $('#prepayment-unpaid-count').text(data.prepayment_unpaid_count);
                        $('#delivery-payment-unpaid-count').text(data.delivery_payment_unpaid_count);
                        $('#acceptance-payment-unpaid-count').text(data.acceptance_payment_unpaid_count);
                        $('#warranty-payment-unpaid-count').text(data.warranty_payment_unpaid_count);

                        // 更新付款率
                        $('#paid-percentage').text(data.paid_percentage + '%');
                        $('.circle-progress').css('--progress', data.paid_percentage);
                    } else {
                        layer.msg('获取统计数据失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('获取统计数据失败', {icon: 2});
                }
            });
        }

        // 获取未付款项目列表
        function getUnpaidProjects(paymentType, callback) {
            $.ajax({
                url: '/system/project_payment/api/unpaid_projects',
                type: 'GET',
                data: {
                    payment_type: paymentType
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data);
                    } else {
                        layer.msg('获取未付款项目列表失败: ' + response.message, {icon: 2});
                        callback([]);
                    }
                },
                error: function() {
                    layer.msg('获取未付款项目列表失败', {icon: 2});
                    callback([]);
                }
            });
        }

        // 在页面加载时调用统计函数
        loadStatistics();

        // 渲染表单元素
        form.render();

        // 未付款项目数点击事件
        $('.clickable-count').on('click', function() {
            const count = parseInt($(this).text());
            if (isNaN(count) || count === 0) {
                layer.msg('当前没有未付款项目', {icon: 1});
                return;
            }

            const paymentType = $(this).data('type');
            let paymentTitle = '';

            // 根据付款类型设置标题
            if (paymentType === 'prepayment') {
                paymentTitle = '预付款';
            } else if (paymentType === 'delivery_payment') {
                paymentTitle = '发货款';
            } else if (paymentType === 'acceptance_payment') {
                paymentTitle = '验收款';
            } else if (paymentType === 'warranty_payment') {
                paymentTitle = '质保金';
            }

            // 显示加载中
            const loadingIndex = layer.load(2);

            // 获取未付款项目列表
            getUnpaidProjects(paymentType, function(projects) {
                layer.close(loadingIndex);

                if (!projects || projects.length === 0) {
                    layer.msg('没有找到未付款项目', {icon: 2});
                    return;
                }

                // 构建未付款项目列表HTML
                let content = `
                    <div class="layui-card">
                        <div class="layui-card-header">${paymentTitle}未付项目列表</div>
                        <div class="layui-card-body">
                            <table id="unpaid-project-table" lay-filter="unpaid-project-table"></table>
                        </div>
                    </div>
                `;

                // 显示弹窗
                const index = layer.open({
                    type: 1,
                    title: `<i class="layui-icon layui-icon-alert" style="margin-right: 8px;"></i>${paymentTitle}未付项目详情（共 ${projects.length} 个）`,
                    area: ['85%', '75%'],
                    skin: 'layui-layer-molv', // 使用内置的墨绿主题
                    shade: 0.3,
                    anim: 1, // 弹出动画
                    content: content,
                    success: function(layero, index) {
                        // 定义操作栏模板
                        const operationBar = `
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-xs layui-btn-normal view-detail" lay-event="detail">
                                    <i class="layui-icon layui-icon-form"></i> 查看详情
                                </button>
                            </div>
                        `;

                        // 渲染表格
                        table.render({
                            elem: '#unpaid-project-table',
                            data: projects,
                            page: true,
                            limit: 10,
                            limits: [10, 20, 50, 100],
                            lineStyle: 'height: 60px;', // 设置行高
                            cellMinWidth: 100, // 设置单元格最小宽度
                            cols: [[
                                {field: 'project_code', title: '项目编号', align: 'center', templet: function(d) {
                                    if (d.project_type_name) {
                                        return '<span style="font-weight: 600;">' + d.project_type_name + ' ' + d.project_code + '</span>';
                                    } else {
                                        return '<span style="font-weight: 600;">' + d.project_code + '</span>';
                                    }
                                }},
                                {field: 'project_name', title: '项目名称', align: 'center'},
                                {field: 'status_name', title: '项目状态', align: 'center'},
                                {field: 'price', title: '项目价格', align: 'center', templet: function(d) {
                                    return getCurrencySymbol(d.currency) + ' ' + d.price;
                                }},
                                {field: 'expected_amount', title: '应收金额', align: 'center'},
                                {field: 'actual_amount', title: '实际回款', align: 'center'},
                                {title: '操作', toolbar: operationBar, align: 'center', width: 130}
                            ]],
                            done: function(res, curr, count) {
                                // 自定义样式
                                $(layero).find('th').css({'background-color': '#009688', 'color': '#fff', 'font-weight': '600'});

                                // 增加表格行高
                                $(layero).find('.layui-table-body tr').css({
                                    'height': '60px'
                                });

                                // 调整单元格内容显示
                                $(layero).find('.layui-table-cell').css({
                                    'height': 'auto',
                                    'line-height': '24px',
                                    'padding': '8px 15px',
                                    'white-space': 'normal'
                                });
                            }
                        });

                        // 表格工具栏事件
                        table.on('tool(unpaid-project-table)', function(obj) {
                            const data = obj.data;
                            const layEvent = obj.event;

                            if (layEvent === 'detail') {
                                // 查看详情
                                // 构建带有付款类型参数的URL
                                var paymentType = $(this).closest('.layui-card').find('.layui-card-header').text().trim();
                                var url = '/system/project_payment/detail/' + data.id;
                                if (paymentType.indexOf('预付款') !== -1) {
                                    url += '?payment_type=prepayment';
                                } else if (paymentType.indexOf('发货款') !== -1) {
                                    url += '?payment_type=delivery_payment';
                                } else if (paymentType.indexOf('验收款') !== -1) {
                                    url += '?payment_type=acceptance_payment';
                                } else if (paymentType.indexOf('质保金') !== -1) {
                                    url += '?payment_type=warranty_payment';
                                }

                                layer.open({
                                    type: 2,
                                    title: '<i class="layui-icon layui-icon-form" style="margin-right: 8px;"></i>项目回款详情',
                                    shade: 0.3,
                                    maxmin: true, // 允许最大化和最小化
                                    area: ['92%', '92%'],
                                    skin: 'layui-layer-molv', // 使用内置的墨绿主题
                                    anim: 1, // 弹出动画
                                    content: url,
                                    success: function(layero, index) {
                                        // 弹窗加载完成后的回调
                                        layer.full(index); // 自动最大化
                                    },
                                    end: function() {
                                        // 弹窗关闭后刷新表格数据
                                        table.reload('payment-table');
                                        // 刷新统计数据
                                        loadStatistics();
                                        // 刷新未付款项目列表
                                        var paymentType = '';
                                        if (url.indexOf('prepayment') !== -1) {
                                            paymentType = 'prepayment';
                                        } else if (url.indexOf('delivery_payment') !== -1) {
                                            paymentType = 'delivery_payment';
                                        } else if (url.indexOf('acceptance_payment') !== -1) {
                                            paymentType = 'acceptance_payment';
                                        } else if (url.indexOf('warranty_payment') !== -1) {
                                            paymentType = 'warranty_payment';
                                        }

                                        if (paymentType) {
                                            getUnpaidProjects(paymentType, function(projects) {
                                                table.reload('unpaid-project-table', {
                                                    data: projects
                                                });
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            });
        });

        // 初始化项目表格
        table.render({
            elem: '#payment-table',
            url: '/system/project_payment/data',
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            lineStyle: 'height: 60px;', // 设置行高
            cellMinWidth: 100, // 设置单元格最小宽度
            cols: [[
                {title: '项目编号', field: 'project_code', align: 'center', width: 180, fixed: 'left', templet: function(d) {
                    if (d.project_type_name) {
                        return '<span style="font-weight: 600;">' + d.project_type_name + '' + d.project_code + '</span>';
                    } else {
                        return '<span style="font-weight: 600;">' + d.project_code + '</span>';
                    }
                }},
                {title: '项目名称', field: 'project_name', align: 'center', minWidth: 200},
                {title: '项目状态', field: 'project_status', align: 'center', width: 150, templet: function(d) {
                    // 状态样式映射
                    const statusClassMap = {
                        0: 'status-pending',    // 未开始
                        1: 'status-production', // 生产中
                        3: 'status-warehouse',  // 入库完成
                        5: 'status-shipping',   // 发货完成
                        6: 'status-installation', // 安装调试中
                        7: 'status-installation', // 安装调试完成
                        10: 'status-acceptance', // 验收中
                        12: 'status-completed'  // 项目已完成
                    };

                    // 状态名称映射
                    const statusNameMap = {
                        0: '未开始',
                        1: '生产中',
                        3: '入库完成',
                        5: '发货完成',
                        6: '安装调试中',
                        7: '安装调试完成',
                        10: '验收中',
                        12: '项目已完成'
                    };

                    // 获取状态值和状态名称
                    const statusValue = d.project_status !== undefined ? d.project_status : (d.status !== undefined ? d.status : 0);
                    const statusName = statusNameMap[statusValue] || d.status_name || '未知状态';
                    const statusClass = statusClassMap[statusValue] || 'status-pending';

                    return '<span class="project-status ' + statusClass + '">' + statusName + '</span>';
                }},
                {title: '项目价格', field: 'price', align: 'center', width: 150, templet: function(d) {
                    return '<span style="font-weight: 600; color: #333;">' + getCurrencySymbol(d.currency) + ' ' + d.price + '</span>';
                }},
                {title: '预付款(实收/应收)', align: 'center', width: 150, templet: function(d) {
                    return '<div style="font-weight: 500;"><span style="color: ' + (d.actual_prepayment > 0 ? '#67C23A' : '#999') + ';">' +
                           d.actual_prepayment + '</span> / ' + d.prepayment + '</div>';
                }},
                {title: '预付款状态', field: 'prepayment_status_display', align: 'center', width: 200, templet: function(d) {
                    var statusText = d.prepayment_status_display || '未知状态';
                    var statusClass = 'status-unpaid'; // 默认样式

                    if (d.prepayment_status === 0) {
                        statusClass = 'status-unpaid';
                    } else if (d.prepayment_status === 1) {
                        statusClass = 'status-paid';
                    } else if (d.prepayment_status === 2) {
                        statusClass = 'status-required';
                    } else if (d.prepayment_status === 3) {
                        statusClass = 'status-partial';
                    } else if (d.prepayment_status === 4) {
                        statusClass = 'status-none';
                    }

                    return '<span class="payment-status ' + statusClass + '">' + statusText + '</span>';
                }},
                {title: '发货款(实收/应收)', align: 'center', width: 150, templet: function(d) {
                    return '<div style="font-weight: 500;"><span style="color: ' + (d.actual_delivery_payment > 0 ? '#67C23A' : '#999') + ';">' +
                           d.actual_delivery_payment + '</span> / ' + d.delivery_payment + '</div>';
                }},
                {title: '发货款状态', field: 'delivery_payment_status_display', align: 'center', width: 200, templet: function(d) {
                    var statusText = d.delivery_payment_status_display || '未知状态';
                    var statusClass = 'status-unpaid'; // 默认样式

                    if (d.delivery_payment_status === 0) {
                        statusClass = 'status-unpaid';
                    } else if (d.delivery_payment_status === 1) {
                        statusClass = 'status-paid';
                    } else if (d.delivery_payment_status === 2) {
                        statusClass = 'status-required';
                    } else if (d.delivery_payment_status === 3) {
                        statusClass = 'status-partial';
                    } else if (d.delivery_payment_status === 4) {
                        statusClass = 'status-none';
                    }

                    return '<span class="payment-status ' + statusClass + '">' + statusText + '</span>';
                }},
                {title: '验收款(实收/应收)', align: 'center', width: 150, templet: function(d) {
                    return '<div style="font-weight: 500;"><span style="color: ' + (d.actual_acceptance_payment > 0 ? '#67C23A' : '#999') + ';">' +
                           d.actual_acceptance_payment + '</span> / ' + d.acceptance_payment + '</div>';
                }},
                {title: '验收款状态', field: 'acceptance_payment_status_display', align: 'center', width: 200, templet: function(d) {
                    var statusText = d.acceptance_payment_status_display || '未知状态';
                    var statusClass = 'status-unpaid'; // 默认样式

                    if (d.acceptance_payment_status === 0) {
                        statusClass = 'status-unpaid';
                    } else if (d.acceptance_payment_status === 1) {
                        statusClass = 'status-paid';
                    } else if (d.acceptance_payment_status === 2) {
                        statusClass = 'status-required';
                    } else if (d.acceptance_payment_status === 3) {
                        statusClass = 'status-partial';
                    } else if (d.acceptance_payment_status === 4) {
                        statusClass = 'status-none';
                    }

                    return '<span class="payment-status ' + statusClass + '">' + statusText + '</span>';
                }},
                {title: '验收款激励', field: 'acceptance_incentive_display', align: 'center', width: 180, templet: function(d) {
                    var incentiveText = d.acceptance_incentive_display || '0元 (未计算)';
                    var incentiveClass = 'incentive-neutral'; // 默认样式

                    if (d.acceptance_incentive_amount > 0) {
                        incentiveClass = 'incentive-positive';
                    } else if (d.acceptance_incentive_amount < 0) {
                        incentiveClass = 'incentive-negative';
                    }

                    return '<span class="acceptance-incentive ' + incentiveClass + '">' + incentiveText + '</span>';
                }},
                {title: '质保金(实收/应收)', align: 'center', width: 150, templet: function(d) {
                    return '<div style="font-weight: 500;"><span style="color: ' + (d.actual_warranty_payment > 0 ? '#67C23A' : '#999') + ';">' +
                           d.actual_warranty_payment + '</span> / ' + d.warranty_payment + '</div>';
                }},
                {title: '质保金状态', field: 'warranty_payment_status_display', align: 'center', width: 200, templet: function(d) {
                    var statusText = d.warranty_payment_status_display || '未知状态';
                    var statusClass = 'status-unpaid'; // 默认样式

                    if (d.warranty_payment_status === 0) {
                        statusClass = 'status-unpaid';
                    } else if (d.warranty_payment_status === 1) {
                        statusClass = 'status-paid';
                    } else if (d.warranty_payment_status === 2) {
                        statusClass = 'status-required';
                    } else if (d.warranty_payment_status === 3) {
                        statusClass = 'status-partial';
                    } else if (d.warranty_payment_status === 4) {
                        statusClass = 'status-none';
                    }

                    return '<span class="payment-status ' + statusClass + '">' + statusText + '</span>';
                }},
                {title: '质保天数', field: 'warranty_days_display', align: 'center', width: 130, templet: function(d) {
                    if (d.warranty_days_display === '未验收') {
                        return '<span class="warranty-days days-pending">未验收</span>';
                    } else if (d.warranty_days_display === '-') {
                        return '<span class="warranty-days days-none">-</span>';
                    } else if (d.warranty_days_display === '今日到期') {
                        return '<span class="warranty-days days-today">今日到期</span>';
                    } else if (d.warranty_days_display.includes('剩余')) {
                        return '<span class="warranty-days days-remaining">' + d.warranty_days_display + '</span>';
                    } else if (d.warranty_days_display.includes('已超期')) {
                        return '<span class="warranty-days days-overdue">' + d.warranty_days_display + '</span>';
                    } else {
                        return '<span class="warranty-days days-none">' + d.warranty_days_display + '</span>';
                    }
                }},
                {title: '操作', toolbar: '#payment-bar', align: 'center', fixed: 'right', width: 120}
            ]],
            skin: 'line',
            text: {none: '暂无项目回款信息'},
            done: function() {
                // 表格加载完成后的回调
                $('th').css({
                    'background-color': '#009688',
                    'color': '#fff',
                    'font-weight': '600',
                    'font-size': '14px'
                });

                // 确保表格容器宽度正确
                $('.table-container').css({
                    'width': '100%',
                    'overflow-x': 'auto'
                });

                // 确保表格在容器中正确显示
                $('.layui-table-view').css({
                    'width': '100%'
                });

                // 增加表格行高
                $('.layui-table-body tr').css({
                    'height': '60px'
                });

                // 调整单元格内容显示
                $('.layui-table-cell').css({
                    'height': 'auto',
                    'line-height': '24px',
                    'padding': '8px 15px',
                    'white-space': 'normal',
                    'word-break': 'break-word'
                });

                // 特别处理项目状态列的单元格样式
                $('.layui-table-cell .project-status').parent().css({
                    'height': 'auto',
                    'white-space': 'normal',
                    'word-break': 'break-word',
                    'text-align': 'center'
                });
            }
        });

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            let statusValue = $('#status-filter').val();
            let searchKeyword = $('#search-input').val();

            table.reload('payment-table', {
                where: {
                    status: statusValue,
                    keyword: searchKeyword
                },
                page: {
                    curr: 1
                }
            });
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            $('#status-filter').val('');
            $('#search-input').val('');
            form.render('select');

            table.reload('payment-table', {
                where: {
                    status: '',
                    keyword: ''
                },
                page: {
                    curr: 1
                }
            });
        });

        // 监听表格行工具条事件
        table.on('tool(payment-table)', function(obj) {
            let data = obj.data;
            let layEvent = obj.event;

            if (layEvent === 'detail') {
                // 查看详情
                layer.open({
                    type: 2,
                    title: '<i class="layui-icon layui-icon-form" style="margin-right: 8px;"></i>项目回款详情',
                    shade: 0.3,
                    maxmin: true, // 允许最大化和最小化
                    area: ['92%', '92%'],
                    skin: 'layui-layer-molv', // 使用内置的墨绿主题
                    anim: 1, // 弹出动画
                    content: '/system/project_payment/detail/' + data.id,
                    success: function(layero, index) {
                        // 弹窗加载完成后的回调
                        layer.full(index); // 自动最大化
                    },
                    end: function() {
                        // 弹窗关闭后刷新表格数据
                        table.reload('payment-table');
                        // 刷新统计数据
                        loadStatistics();
                    }
                });
            }
        });

        // 回车键触发搜索
        $('#search-input').on('keypress', function(e) {
            if(e.which === 13) {
                $('#search-btn').click();
            }
        });
    });
</script>
</html>